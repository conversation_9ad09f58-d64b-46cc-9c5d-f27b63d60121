<?xml version="1.0" encoding="utf-8" ?>
<truEngine-layout xmlns:te="http://www.truengine.sk">

<?php
	@{godoc} = @{doc};
	if(substr(@{godoc}, 0, 12) == '.layouts/en_') {
		@{godoc} = '.layouts/sk_' . substr(@{godoc}, 12);
	}
	else if(substr(@{godoc}, 0, 12) == '.layouts/cz_') {
		@{godoc} = '.layouts/sk_' . substr(@{godoc}, 12);
	}

	callStack_gClass::includePrepare_gFunc();

	$includeVars_pVar = array();
	foreach(vars::$vars as $k=>$v) {
		$includeVars_pVar[$k] = $v;
	}
	callStack_gClass::includeEnd_gFunc(@{godoc}, $includeVars_pVar);	

?>

</truEngine-layout>