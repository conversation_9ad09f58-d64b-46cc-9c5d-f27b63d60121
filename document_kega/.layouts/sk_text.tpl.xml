<?xml version="1.0" encoding="utf-8" ?>
<truEngine-layout xmlns:te="http://www.truengine.sk">&lt;?xml version="1.0" encoding="utf-8"?&gt;
&lt;!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN"
	"http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"&gt;
<html te:xmlns="http://www.w3.org/1999/xhtml" xml:lang="sk">
	<head>
		<te:include name="/sk/components/layout-head" layout="text" />
	</head>
	<body onload="teInit();"><a name="top"></a>
		<div id="pageWrapper">

			<hr class="hide" />
			<div id="masthead" class="inside" style="border:none;">
				<te:if test="main_gClass::getConfigVar_gFunc('test', 'main')">
					<div style="background-color:#ffaaaa">
						<h1><a href="/">OPUS SAPIENTIAE - TESTOVACIA VERZIA</a></h1>
					</div>
				<te:else />
					<h1><a href="/">OPUS SAPIENTIAE</a></h1>
				</te:if>
				<te:include name="/sk/components/layout-languages" />
			</div>
			<hr class="hide" />
			<div class="hnav">

			<te:include name="/sk/components/layout-top-menu" menupath="@@{menupath}" />

			</div>
			<div id="outerColumnContainer">
				<div id="innerColumnContainer">
					<hr class="hide" />
					<div id="leftColumn">
						<div class="inside">
						</div>
					</div>
					<hr class="hide" />
					<div id="rightColumn">
						<div class="inside">
							<a href="/chyba">Help</a>

<div class="vnav">
	<te:include name="/sk/components/layout-side-menu"  menupath="@{menupath}"/>
</div>
						</div>
					</div>
					<div id="contentColumn">

						<hr class="hide" />
						<a name="skipToContent" id="skipToContent"></a>
						<div class="inside">
							<te:include name="/sk/components/check-isic" />
							<te:include name="/sk/components/layout-content" />
						</div>
					</div>
					<div class="clear mozclear"></div>
				</div>
			</div>
			<div class="hide" id="nsFooterClear"><!-- for NS4's sake --></div>
			<hr class="hide" />
			<div id="footer" class="inside">
<div id="disclaimer">
      		<te:include name="/sk/components/layout-disclaimer-sk" /><br />
      		<te:include name="/sk/components/layout-disclaimer-en" />
</div>
<p style="margin:0;">
	&amp;copy; truEngine. <a href="/truEngine" style="color:#000;">Generuje TruEngine</a>.<br />
	<span style="color:#bb9;"><?php
	$version = main_gClass::getVersion_gFunc();
	echo 'Last updated on ' . date('F j, Y', strtotime($version[0])) . ' ';
	echo '(build ' . $version[1] . ')';
	?></span>
</p>

			</div>
			<hr class="hide" />
		</div>
		<te:include name="/sk/components/layout-layout-menu" layout="basic" />
	</body>

</html>
</truEngine-layout>
