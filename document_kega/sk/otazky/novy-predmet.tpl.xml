<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:source name="test_add_predmet" varname="form" value_id="@{request:value_id}">
	<te:source-param name="submit_button_title_add">@L{Pridať predmet}</te:source-param>
	<te:source-param name="submit_button_title_update">@L{Aktualizovať predmet}</te:source-param>
</te:source>

<te:include name="/sk/components/form" form="@@{form}" />

<te:if test="@{form:hidden_fields:value_id:value}">
	<te:content-set name="title" value="@L{Editovať predmet}" />
<te:else />
	<te:content-set name="title" value="@L{Nový predmet}" />
</te:if>


<te:if test="@{form:error_code} == 'ok'">
		<ul>

			<li style="padding-top:30px;"><a href="/otazky/predmety">@L{Zoznam predmetov}</a></li>
			<li style="padding-top:30px;"><a href="/otazky/novy-predmet">@L{Nový predmet}</a></li>
		</ul>
</te:if>

<script type="text/javascript">
	var tr = $j('fieldset tr'); 
	tr.each(function (index) {
		if($j(tr[index]).attr('id').indexOf('tr_tree_') == 0) {
			$j('.td_input', $j(tr[index])).css('border-bottom', '1px dashed #ddd');
			$j('.td_input', $j(tr[index])).css('padding-bottom', '10px');
		}
	});
</script>


</truEngine-document>
