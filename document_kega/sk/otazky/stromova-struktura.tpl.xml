<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">


<te:content-set name="title" value="@L{St<PERSON>ová štruktúra}" />

<te:source name="questions_tree" varname="tree" />
<?php
//dd(@{tree:_struct:childs});
function writeTree_gFunc($treeData_pVar, $level_pVar = 0)
{
	$lTypes_pVar = array('DISC','CIRCLE','SQUARE','DISC','CIRCLE','SQUARE');

	echo '<ul type="' . $lTypes_pVar[$level_pVar] . '" style="display:' . ($level_pVar>1?'none':'block') . '">';

	$afterChild_pVar = true;

	foreach($treeData_pVar as $data_pVar) {
		if($afterChild_pVar) {
			 $liStyle_pVar = ' style="margin-top:10px"';
			 $afterChild_pVar = false;
		}
		else {
			$liStyle_pVar = '';
		}

		echo '<li'.$liStyle_pVar.'>';
		if(isset($data_pVar['childs']) && count($data_pVar['childs'])) {
			echo '<a href="#" onclick="hideUnhideTreeChilds(this.parentNode); return(false);">+</a> ';
		}

		switch($level_pVar) {
			case 0:
				$href="editovat-modul";
				break;
			case 1:
				$href="editovat-studijny-program";
				break;
			case 2:
				$href="editovat-predmet";
				break;
			case 3:
				$href="editovat-problematiku";
				break;
			case 4:
				$href="editovat-podkategoriu";
				break;
		}
		$href = main_gClass::makeUrl_gFunc('/otazky/' . $href . '?value_id=' . $data_pVar['enum_id']);
		if($data_pVar['enum_id']) {
			echo '<a href="'.$href.'">edit</a> ';
		}

		$href = '/otazky/otazky';
		if(isset($data_pVar['filter'])) {
			$href .= '?filter=';
			foreach($data_pVar['filter'] as $k=>$v) {
				$href .= urlencode('&'.$k . '=' . $v);
			}
		}
		$href = main_gClass::makeUrl_gFunc($href);

		echo '<a ';

		echo ' href="'.$href.'" ';
		if(isset($data_pVar['dummy']) && $data_pVar['dummy']) {
			echo ' style="color:red;" ';
		}

		echo '>';

		echo '<span ';

		if(isset($data_pVar['stats']) && isset($data_pVar['stats']['total'])) {
			$title = '@L{Celkovo}: '. $data_pVar['stats']['total']['n_questions'] . '<br >';
			$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sk: ' . $data_pVar['stats']['total']['n_questions_sk'] . '<br >';
			$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;en: ' . $data_pVar['stats']['total']['n_questions_en'] . '<br >';
			$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cz: ' . $data_pVar['stats']['total']['n_questions_cz'] . '<br >';
			$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{obtiažnosť}: ' . $data_pVar['stats']['total']['obtiaznost'] . '<br >';
			$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{chybovosť}: ' . $data_pVar['stats']['total']['nekorektnost'] . '<br >';
			$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{aktuálnosť}: ' . $data_pVar['stats']['total']['aktualnost'] . '<br >';

			if(isset($data_pVar['stats']['active'])) {
				$title .= '<br />@L{OTÁZKY}: '. $data_pVar['stats']['active']['n_questions'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sk: ' . $data_pVar['stats']['active']['n_questions_sk'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;en: ' . $data_pVar['stats']['active']['n_questions_en'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cz: ' . $data_pVar['stats']['active']['n_questions_cz'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{obtiažnosť}: ' . $data_pVar['stats']['active']['obtiaznost'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{chybovosť}: ' . $data_pVar['stats']['active']['nekorektnost'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{aktuálnosť}: ' . $data_pVar['stats']['active']['aktualnost'] . '<br >';

			}
			if(isset($data_pVar['stats']['waiting'])) {
				$title.= '<br />@L{PREDOTÁZKY}: '. $data_pVar['stats']['waiting']['n_questions'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sk: ' . $data_pVar['stats']['waiting']['n_questions_sk'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;en: ' . $data_pVar['stats']['waiting']['n_questions_en'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cz: ' . $data_pVar['stats']['waiting']['n_questions_cz'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{obtiažnosť}: ' . $data_pVar['stats']['waiting']['obtiaznost'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{chybovosť}: ' . $data_pVar['stats']['waiting']['nekorektnost'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@L{aktuálnosť}: ' . $data_pVar['stats']['waiting']['aktualnost'] . '<br >';
			}
			if(isset($data_pVar['stats']['rejected'])) {
				$title.= '<br />@L{ZAMIETNUTÉ}: '. $data_pVar['stats']['rejected']['n_questions'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sk: ' . $data_pVar['stats']['rejected']['n_questions_sk'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;en: ' . $data_pVar['stats']['rejected']['n_questions_en'] . '<br >';
				$title .= '&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;cz: ' . $data_pVar['stats']['rejected']['n_questions_cz'] . '<br >';
			}


//, $data_pVar['stats']['total']['n_questions_en'], $data_pVar['stats']['total']['obtiaznost'], $data_pVar['stats']['total']['nekorektnost']));

			echo ' title="'.$title.'" class="tipz"';
		}
		echo '>';

		echo $data_pVar['sk_enum_field_name_item'];
		if(isset($data_pVar['stats']) && isset($data_pVar['stats']['total'])) {
			$sumSkChildsStatus_pVar = 0;
			$sumSkStatus_pVar = '';

			$sumEnChildsStatus_pVar = 0;
			$sumEnStatus_pVar = '';

			$sumCzChildsStatus_pVar = 0;
			$sumCzStatus_pVar = '';

			$sumTotalChildsStatus_pVar = 0;
			$sumTotalStatus_pVar = '';

			if(isset($data_pVar['childs']) && count($data_pVar['childs'])) {
				foreach($data_pVar['childs'] as $ch_pVar) {
					if(isset($ch_pVar['stats'])) {
						$sumSkChildsStatus_pVar += $ch_pVar['stats']['total']['n_questions_sk'];
						$sumEnChildsStatus_pVar += $ch_pVar['stats']['total']['n_questions_en'];
						$sumCzChildsStatus_pVar += $ch_pVar['stats']['total']['n_questions_cz'];
						$sumTotalChildsStatus_pVar += $ch_pVar['stats']['total']['n_questions'];
					}
				}

			    if($data_pVar['stats']['total']['n_questions_sk'] != $sumSkChildsStatus_pVar) {
				    $sumSkStatus_pVar = ' (' . ($data_pVar['stats']['total']['n_questions_sk'] - $sumSkChildsStatus_pVar) . ')';
			    }
			    if($data_pVar['stats']['total']['n_questions_en'] != $sumEnChildsStatus_pVar) {
				    $sumEnStatus_pVar = ' (' . ($data_pVar['stats']['total']['n_questions_en'] - $sumEnChildsStatus_pVar) . ')';
			    }
			    if($data_pVar['stats']['total']['n_questions_cz'] != $sumCzChildsStatus_pVar) {
				    $sumCzStatus_pVar = ' (' . ($data_pVar['stats']['total']['n_questions_cz'] - $sumCzChildsStatus_pVar) . ')';
			    }
			    if($data_pVar['stats']['total']['n_questions_cz'] != $sumCzChildsStatus_pVar) {
				    $sumCzStatus_pVar = ' (' . ($data_pVar['stats']['total']['n_questions_cz'] - $sumCzChildsStatus_pVar) . ')';
			    }
			    if($data_pVar['stats']['total']['n_questions'] != $sumTotalChildsStatus_pVar) {
				    $sumTotalStatus_pVar = ' (' . ($data_pVar['stats']['total']['n_questions'] - $sumTotalChildsStatus_pVar) . ')';
			    }
			}

			$lngstr_pVar = '';
			switch(main_gClass::getLanguage_gFunc()) {
				case 'sk':
					$lngstr_pVar = $data_pVar['stats']['total']['n_questions_sk'] . $sumSkStatus_pVar;
					break;
				case 'en':
					$lngstr_pVar = $data_pVar['stats']['total']['n_questions_en'] . $sumEnStatus_pVar;
					break;
				case 'cz':
					$lngstr_pVar = $data_pVar['stats']['total']['n_questions_cz'] . $sumCzStatus_pVar;
					break;
			}


			echo ' ';
			echo '(';
			//echo implode(' / ', array($data_pVar['stats']['total']['n_questions_sk'] . $sumSkStatus_pVar, $data_pVar['stats']['total']['n_questions_en'] . $sumEnStatus_pVar, $data_pVar['stats']['total']['obtiaznost'], $data_pVar['stats']['total']['nekorektnost']));
			echo implode(' / ', array($data_pVar['stats']['total']['n_questions'] . $sumTotalStatus_pVar, $lngstr_pVar));
			echo ')';
		}
		echo  '</span></a>';
		if(isset($data_pVar['childs']) && count($data_pVar['childs'])) {
			writeTree_gFunc($data_pVar['childs'], $level_pVar + 1);
			$afterChild_pVar = true;
		}
		echo '</li>';
	}
	echo '</ul>';
}

writeTree_gFunc(@{tree:_struct:childs});

?>

<script type="text/javascript">
function hideUnhideTreeChilds(parent)
{
/*
	var ul = parent.getElementsByTagName("ul");

	for (var i = 0; i &lt; ul.length; i++) {
    	if(ul[i].style.display == 'none') {
    		ul[i].style.display = 'block';
    	}
    	else {
    		ul[i].style.display = 'none';
    	}
    }
    */
    for(var i = 0; i &lt; parent.childNodes.length; i++) {
    	if(parent.childNodes[i].tagName != 'UL' &amp;&amp; parent.childNodes[i].tagName != 'ul') {
    		continue;
    	}
    	if(parent.childNodes[i].style.display == 'none') {
    		parent.childNodes[i].style.display = 'block';
    	}
    	else {
    		parent.childNodes[i].style.display = 'none';
    	}
    }
}

</script>

</truEngine-document>
