<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

	<te:content-set name="title" value="@L{Prihlásiť sa}" />

	<te:source name="session" varname="session" />

	<?php
		if(!isset(@{_login_follow})) {
			@{_login_follow} = '/@{lng}/log-in';
		}
	?>

	<!--x hlaska po o<PERSON>, teda ak je definovana premenna _logout_msg -->
	<te:if test="isset(@{_logout_msg}) &amp;&amp; @{_logout_msg} === 'logged_out'">
		<div class="msg_info">
			<te:string id="Boli ste úspešne odhlásený zo systému." />
		</div>
	</te:if>

	<!--x ak nie je prihlaseny, zobraz<PERSON> pri<PERSON> formular, alebo nejaku hlasku -->
	<te:xaccess xaccess="s_logged_on">
	  <!--x formular zobrazim iba ak nie je disabled -->
      <form action="@{_login_follow}" method="POST" te:if="empty(@{_login_form_disabled})" id="form_login" onsubmit="return(validateAndSubmit(loginFormItems));">
      	<script type="text/javascript">
      		var loginFormItems = new Array();
      		loginFormItems[0] = new Array();
//      		loginFormItems[0]['err_bgcolor'] = '#FF0000';
//      		loginFormItems[0]['err_color'] = '#aa0000';
      		loginFormItems[1] = new Array();
      		loginFormItems[1]['id'] = 'form_login_login';
      		loginFormItems[1]['match'] = /^[a-z0-9\.]+(@[a-z0-9\.]+)?$/i;
      		loginFormItems[1]['message'] = '<te:string id="Musíte zadať prihlasovacie meno.\n E-mailová adresa." />';
      		loginFormItems[2] = new Array();
      		loginFormItems[2]['id'] = 'form_login_passwd';
      		loginFormItems[2]['match'] = /^.{@{_login_pass_min_len},200}$/i;
      		loginFormItems[2]['message'] = '<te:string id="Musíte zadať heslo.\nMinimálne @{_login_pass_min_len} znakov." />';
      		loginFormItems[3] = new Array();
      		loginFormItems[3]['id'] = 'form_login_submit';
      		loginFormItems[3]['disable'] = true;
      		loginFormItems[3]['value'] = 'Prihlasujem...';
      	</script>
      	<fieldset>
			<te:if test="isset(@{_login_timeouted}) &amp;&amp; @{_login_timeouted}">
<!--x				<div>
					Po dlhšej nečinnosti bola Vaša session odhlásená. Musíte znovu zadať prihlasovacie meno a heslo.<br />
					Ak chcete pokračovať vo vykonaní požiadavky, ktorá bola prerušená týmto hlásením, označte tento checkbox
					<te:if test="!empty(@{_login_restore_checked})">
						<input type="checkbox" name="restore_session" checked="checked" />.
					</te:if>
					<te:if test="!empty(@{_login_restore_checked})">
						<input type="checkbox" name="restore_session" />.
					</te:if>

					<input type="checkbox" name="restore_session" />.
				</div> -->
			</te:if>

          <table class="form_table">
            <tbody>
	            <tr>
	              <td class="td_label"><label for="form_login_login"><te:string id="Prihlasovacie meno:" /></label></td>
	              <td><input name="login" id="form_login_login" type="text" class="input_text" /><img src="/images/icons/info_small.png" class="info_icon tipz" title="@L{Zadajte prihlasovacie meno.::Môže obsahovať malé a veľké písmená, číslice, bodku,&lt;br /&gt;alebo to môže byť e-mailová adresa.}" /></td>
	            </tr>
	            <tr>
	              <td class="td_label"><label for="form_login_passwd"><te:string id="Heslo:" /></label></td>
	              <td><input name="password" id="form_login_passwd" type="password" class="input_text" /><img src="/images/icons/info_small.png" class="info_icon tipz" title="Zadajte heslo.::Minimálne @{_login_pass_min_len} znaky." /></td>
	            </tr>
	            <tr>
	              <td></td>
	              <td class="td_submit">
	              	<div>
	                	<input type="submit" value="@L{Prihlásiť}" id="form_login_submit" class="input_submit" />
	                </div>
	              </td>
	            </tr>
          </tbody></table>
        </fieldset>
      </form>
	  <div class="msg_info">
	  <?php @{url1} = main_gClass::makeUrl_gFunc('/log-in/nove-heslo');
	  		@{url2} = main_gClass::makeUrl_gFunc('/log-in/nove-heslo');
	  ?>
	  	<te:string id="Ak si nepamätáte heslo, môžete si &lt;a href='@{url1}'&gt;vygenerovať nové&lt;/a&gt;." />
	  </div>

	</te:xaccess>

	<!--x zobrazenie hlasky po pokuse o prihlasenie sa -->
	<te:if test="isset(@{_login_msg})">
		<te:if test="@{_login_msg} === 'logged_in'">
			<div class="msg_ok">
				<te:string id="Boli ste úspešne prihlásený do systému." />
				<te:redirect location="/" />
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'no_loginname_or_password'">
			<div class="msg_error">
				<te:string id="Musíte zadať prihlasovacie meno a heslo. Minimálny počet znakov v&amp;nbsp;hesle je&amp;nbsp;@{_login_pass_min_len}." />
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'no_isicsrn_or_name'">
			<div class="msg_error">
				<te:string id="Musíte zadať sériové číslo ISIC karty, meno a priezvisko." />
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'bad_login_or_password'">
			<te:if test="isset(@{_login_disabled_timeout})">
				<div class="msg_error">
					<te:string id="Zadali ste nesprávny login alebo heslo." />
				</div>
			<te:else />
				<div class="msg_error">
					<te:string id="Zadali ste nesprávny login alebo heslo." /><br />
					<?php @{url} = main_gClass::makeUrl_gFunc('/log-in/nove-heslo'); ?>
					<te:string id="Ak ste zabudli heslo, možete požiadať o &lt;a href='@{url}'&gt;vygenerovanie nového hesla&lt;/a&gt;." />
				</div>
			</te:if>
		</te:if>

		<te:if test="@{_login_msg} === 'user_disabled'">
			<div class="msg_error">
				<te:string id="Používateľ &lt;strong&gt;@{_login_username}&lt;/strong&gt; má zakázané prilásenie. Kontaktujte administrátora." />
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'user_temporary_disabled_timeout'">
			<div class="msg_error">
				Používateľ <strong>@{_login_username}</strong> má dočasne zakázané prilásenie.<br />
				@L{Pravdepodobne ste zadali nesprávne heslo.}<br />
				Bude sa možné prihlásiť po @{_login_disabled_timeout} minútach.<br />
				Pre skoršie odblokovanie kontaktujte administrátora alebo možete požiadať o <a href="/log-in/nove-heslo">vygenerovanie nového hesla</a>.
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'user_temporary_disabled'">
			<div class="msg_error">
				Používateľ <strong>@{_login_username}</strong> má dočasne zakázané prilásenie.<br />
				@L{Pravdepodobne ste viac krát zadali nesprávne heslo.}<br />
				Pre odblokovanie kontaktujte administrátora alebo možete požiadať o <a href="/log-in/nove-heslo">vygenerovanie nového hesla</a>.
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'unallowed_ip_for_user'">
			<div class="msg_error">
				Používateľ <strong>@{_login_username}</strong> nemá povolený prístup z IP adresy @{_login_ip}.<br />
				Prihláste sa z povolenej IP adresy a pridajte si IP @{_login_ip} do zoznamu, ak potrebujete.
				@L{Ak sa neviete prihlásiť zo žiadnej IP adresy, kontaktujte administrátora.}
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'key_required_for_user'">
			<div class="msg_error">
				Aby ste sa mohli prihlásiť ako používateľ <strong>@{_login_username}</strong>, musíte mať vo svojom
				prehliadači nainštalovaný 128 bitový kľúč (32 znakový hexadecimálny reťazec) zhodný s kľúčom používateľa.<br />
				<a href="#">@L{inštalovať kľúč na tento počítač}</a>
				<!--x
					pred instalaciou si musi v systeme vygenerovat docasny kluc, ktory ma obmedzenu casovu platnost,
					max. do nainstalovania klucu na pc.
					Docasny kluc vlozi do instalacneho formulara (z pc, na ktory chce kluc instalovat), prihlasi sa do systemu
					zadanim mena a hesla. Vygeneruje sa mu novy kluc a nainstaluje na pc. Docasny kluc stratil platnost.
					(cize nemoze byt zneuzity pre vygenerovanie dalsieho kluca).
					Jeden pouzivatel moze vlastnit viacero klucov, na jednom pc moze byt instalovanych viacero klucov
					roznych pouzivatelov. Z kluca sa neda zistit, ktoremu pouzivatelovi patri.
					Pouzivatel nemoze mat ten isty kluc na dvoch roznych PC.
				-->
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'unallowed_key_for_user'">
			<div class="msg_error">
				Aby ste sa mohli prihlásiť ako používateľ <strong>@{_login_username}</strong>, musíte mať vo svojom
				prehliadači nainštalovaný 128 bitový kľúč (32 znakový hexadecimálny reťazec) zhodný s kľúčom používateľa.<br />
				<a href="#">@L{inštalovať kľúč na tento počítač}</a>
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'ip_error'">
			<div class="msg_error">
				@L{Došlo k zmene IP adresy. Z bezpečnostných dôvodov bola Vaša session zrušená.} Musíte sa znovu <a href="@{_login_follow}">prihlásiť</a>.
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'agent_error'">
			<div class="msg_error">
				@L{Došlo k zmene nastavení Vášho prehliadača. Z bezpečnostných dôvodov bola Vaša session zrušená.} Musíte sa znovu <a href="@{_login_follow}">prihlásiť</a>.
			</div>
		</te:if>

		<te:if test="@{_login_msg} === 'pcid_error'">
			<div class="msg_error">
				@L{Došlo k zmene nastavení Váho prehliadača. Z bezpečnostných dôvodov bola Vaša session zrušená.} Musíte sa znovu <a href="@{_login_follow}">prihlásiť</a>.
			</div>
		</te:if>

	</te:if>

</truEngine-document>
