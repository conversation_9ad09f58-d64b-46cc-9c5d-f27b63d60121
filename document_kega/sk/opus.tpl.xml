<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
    <te:content-set name="title" value="" />

    <?php
        $language = main_gClass::getLanguage_gFunc();
        $storageFilename = 'homepage-text-' . $language . '.html';
        $html = \Illuminate\Support\Facades\Storage::exists($storageFilename) ? Illuminate\Support\Facades\Storage::get($storageFilename):'';
        $html = str_replace("\n", '<br />', $html);
        $html .='<br /><br /><hr />';
        echo $html;
        ?>


    <te:livewire component="homepage" formClass="\App\Livewire\Homepage" />


</truEngine-document>
