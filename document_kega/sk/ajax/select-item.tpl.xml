<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:ajax-request>
	<?php
		$items_pVar = main_gClass::getSessionData_gFunc('selected_items', array());
		$items_unset_pVar = array();
		if(strlen(@{request:item_type})) {
			if(!isset($items_pVar[@{request:item_type}])) {
				$items_pVar[@{request:item_type}] = array();
			}
			if(@{request:select} === 'true') {
				$itemIDS_pVar = explode(',', @{request:item_id});
				foreach($itemIDS_pVar as $item_id_pVar) {
					$item_id_pVar = intval(trim($item_id_pVar));
					$items_pVar[@{request:item_type}][$item_id_pVar] = true;
				}
				ksort($items_pVar[@{request:item_type}], SORT_NUMERIC);
			}
			elseif(@{request:select} === 'clear') {
				$items_unset_pVar = $items_pVar[@{request:item_type}];
				unset($items_pVar[@{request:item_type}]);
			}
			else {
				$itemIDS_pVar = explode(',', @{request:item_id});
				foreach($itemIDS_pVar as $item_id_pVar) {
					$item_id_pVar = intval(trim($item_id_pVar));
					unset($items_pVar[@{request:item_type}][$item_id_pVar]);
				}
			}
			ksort($items_pVar);
			main_gClass::setPhpSessionVar_gFunc('selected_items', $items_pVar);
		}
		
		$json_pVar = new json_gClass();
		@{json_value} = $json_pVar->json_encode_gFunc($items_pVar);
		@{json_value2} = $json_pVar->json_encode_gFunc($items_unset_pVar);
	?>
<result>1</result>
<items>@{json_value}</items>
<unsetitems>@{json_value2}</unsetitems>
</te:ajax-request>
	

</truEngine-document>
