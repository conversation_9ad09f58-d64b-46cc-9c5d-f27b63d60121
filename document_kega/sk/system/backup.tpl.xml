<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_superadmin">
<te:content-set name="title" value="@L{Vytvorenie zálohy}" />

<te:source name="backup_continue" varname="backup" session="@{request:backup_session}" />

<te:if test="@{backup:result} == 'none'">
	<te:source name="backup_form" varname="form">
			<te:source-param name="submit_button_title">@L{Zálohovať}</te:source-param>
	</te:source>
	
	<te:if test="@{form:error_code} != 'ok'">
		<div class="msg_warning">
			Aby vytvorenie zálohy prebehlo korektne, <strong>zablokujte prístup do databázy</strong> pre&amp;nbsp;ostatných pou<PERSON> (<a href="/system/technicka-odstavka">technická odstávka</a>). V opačnom prípade sa nedá zaručiť korektné zálohovanie dát, a to ani v prípade, že počas zálohovania neboli vygenerované židne chybové hlásenia!
		</div>
	</te:if>
	
	<te:include name="/sk/components/form" form="@@{form}" />
	
	<te:if test="@{form:error_code} == 'ok'">
		<?php @{backup} = @{form}; ?>
	</te:if>
</te:if>

<te:if test="@{backup:result} == 'continue'">
	continue...
	@{backup:session}
</te:if>

<te:if test="@{backup:result} == 'done'">
	<te:if test="@{backup:target} === 'download_only' || @{backup:target} === 'download_and_save'">
		<te:layout-insert name="none" />
		<te:header name="Content-type" value="application/octet-stream" />
		<?php @{date} = date('Ymd_His'); ?>
		<te:header name="Content-disposition" value="attachment; filename=&quot;backup_@{date}.dat&quot;" />
	<te:else />
		@L{Záloha bola vytvorená.}
	</te:if>
</te:if>

<te:if test="@{backup:result} == 'error'">
	error...
</te:if>

</truEngine-document>