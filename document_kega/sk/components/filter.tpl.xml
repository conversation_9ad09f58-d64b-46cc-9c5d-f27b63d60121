<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php

	if(!isset(@{html_id})) {
		@{html_id} = 'default';
	}

	if(!isset(@{onsubmit})) {
		@{onsubmit} = '';
	}

	if(!isset(@{filter_settings})) {
		@{filter:settings} = htmlspecialchars_decode(@{request:filter});
	}
	else {
		@{filter:settings} = htmlspecialchars_decode(@{filter_settings});
	}

	@{filter:sort} = htmlspecialchars_decode(@{request:zoradit});
	@{filter:params} = '';

	if(!empty(@{filter:settings})) {
		@{filter:params} .= '&filter=' . urlencode(@{filter:settings});
	}
	if(!empty(@{filter:sort})) {
		@{filter:params} .= '&zoradit=' . urlencode(@{filter:sort});
	}

	if(!isset(@{filter:default})) {
		@{filter:default} = '';
	}


	if(empty(@{filter:settings})) {
		@{filter_id} = '_empty';
	}
	else {
		@{filter_id} = '';
	}

	if(!isset(@{read_only}) || @{read_only} !== 'true') {
		@{read_only} = false;
	}
	else {
		@{read_only} = true;
	}

	@{restore_filter} = session_gClass::getSessionSettingsDetail_gFunc('filter-@{doc}');
	if(!@{restore_filter}) {
		@{restore_filter} = array();
	}

	if(@{request:filter} === NULL && @{request:zoradit} === NULL) {
		if(@{restore_filter} && isset(@{restore_filter}['_default'])) {
			$tmp = @{filter:default};
			@{filter} = @{restore_filter}['_default'];
			@{filter:default} = $tmp;
		}
	}

	if(@{request:save-filter} == 1) {
		@{restore_filter}[@{request:filter-name}] = @{filter};
		session_gClass::saveSessionSettingsDetail_gFunc('filter-@{doc}', @{restore_filter});
	}

	@{restore_filter}['_default'] = @{filter};
	session_gClass::saveSessionSettingsDetail_gFunc('filter-@{doc}', @{restore_filter});

	if(!empty(@{filter:default})) {
		parse_str(@{filter:settings}, $tmp1);
		parse_str(@{filter:default}, $tmp2);
		foreach($tmp2 as $k=>$v) {
			if(!isset($tmp1[$k])) {
				$tmp1[$k]=$v;
			}
		}
		$tmpx = '';
		foreach($tmp1 as $k=>$v)
		{
			$tmpx .= '&';
			$tmpx .= $k . '=' . $v;
		}
		@{filter:settings} = $tmpx;
	}

?>

<te:source name="searchFilter" varname="data" items="@{item_type}" request="@{filter:settings}" html_id="@{html_id}" />

<div id="filter_container@{filter_id}@{html_id}" class="card" te:print="no">
	<div class="card-header">
		<h3 class="card-title">
			<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M4 4h16v2.172a2 2 0 0 1 -.586 1.414l-4.414 4.414v7l-6 2v-9l-4.48 -4.928a2 2 0 0 1 -.52 -1.345v-2.113z"/></svg>
			<te:string id="Filter" />
		</h3>
		<div class="card-actions">
			<a href="#" class="btn btn-outline-primary btn-sm" onclick="filter_show_hide_settings('@{html_id}'); return(false);" te:if="@{read_only} !== true">
				<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/><path d="M9 12a3 3 0 1 0 6 0a3 3 0 0 0 -6 0"/></svg>
				Nastavenia
			</a>
		</div>
	</div>

	<div class="card-body">
		<te:if test="count(@{restore_filter}) > 1">
			<div class="mb-3">
				<label class="form-label">Uložené filtre:</label>
				<div class="btn-list">
					<te:for each="@{restore_filter} as @{filter-name}=>@{named-filter}">
						<te:if test="@{filter-name} != '_default'">
							<a href="@{doc}?named-filter=1@{named-filter:params}" class="btn btn-outline-secondary btn-sm">@{filter-name}</a>
						</te:if>
					</te:for>
				</div>
			</div>
		</te:if>

		<te:if test="@{read_only} !== true">
			<div id="filter@{html_id}" class="mb-3"></div>
			<te:else />
			<div id="filter@{html_id}" style="display:none"></div>
		</te:if>

		<div id="filter_settings@{html_id}" style="display:none;" class="mb-3"></div>

		<te:if test="@{read_only} === true || !empty(@{onsubmit})">
			<input type="hidden" name="filter" id="filter_submit@{html_id}" value="@{filter:settings}" />
			<input type="hidden" name="zoradit" id="filter_sort@{html_id}" value="@{filter:sort}" />
			<te:if test="!empty(@{onsubmit})">
				<button type="button" class="btn btn-primary" onclick="filter_set_request('@{html_id}'); @{onsubmit}">
					<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M4 4h16v2.172a2 2 0 0 1 -.586 1.414l-4.414 4.414v7l-6 2v-9l-4.48 -4.928a2 2 0 0 1 -.52 -1.345v-2.113z"/></svg>
					@L{Filtrovať}
				</button>
			</te:if>
		<te:else />
			<form method="get" onsubmit="filter_set_request('@{html_id}');" action="@{doc}">
				<input type="hidden" name="filter" id="filter_submit@{html_id}" value="@{filter:settings}" />
				<input type="hidden" name="zoradit" id="filter_sort@{html_id}" value="@{filter:sort}" />
				<div class="btn-list">
					<button type="submit" class="btn btn-primary">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M4 4h16v2.172a2 2 0 0 1 -.586 1.414l-4.414 4.414v7l-6 2v-9l-4.48 -4.928a2 2 0 0 1 -.52 -1.345v-2.113z"/></svg>
						@L{Filtrovať}
					</button>
					<button type="button" class="btn btn-outline-secondary" onclick="$j('#filter-save-code').show().replaceAll(this); return(false);">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/><path d="M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0"/><path d="M14 4l0 4l-4 0l0 -4"/></svg>
						@L{Uložiť filter}
					</button>
				</div>
			</form>
		</te:if>

		<div id="filter-save-code" style="display:none;" class="mt-3">
			<div class="row g-2">
				<div class="col">
					<input type="text" name="filter-name" class="form-control" placeholder="@L{Názov filtra}" value="" />
				</div>
				<div class="col-auto">
					<input type="hidden" name="save-filter" id="save-filter" value="0" />
					<button type="submit" class="btn btn-success" onclick="$j('#save-filter').val(1);">
						<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M6 4h10l4 4v10a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2v-12a2 2 0 0 1 2 -2"/><path d="M12 14m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0"/><path d="M14 4l0 4l-4 0l0 -4"/></svg>
						@L{Uložiť a filtrovať}
					</button>
				</div>
			</div>
		</div>
	</div>
</div>


	<script type="text/javascript">
	newFilter('@{html_id}');

	@{data:js}
	</script>
</div>

<te:if test="isset(@{last_name_index})">
	<div style="font-size:12px; padding:3px;" te:print="no">
		 <?php
		 	for($i_pVar = ord('A'); $i_pVar <= ord('Z'); $i_pVar++) {
			echo '<a href="#" onclick="sL(\'' . strtolower(chr($i_pVar)) . '\'); return(false);">' . chr($i_pVar) . '</a> ';
		}
	 ?>
		 <script type="text/javascript">
		 function sL(value)
		 {
			var url = '<?php echo main_gClass::makeUrl_gFunc(@{doc}); ?>';

			if(url.indexOf('?') >= 0) {
				url += '&amp;';
			}
			else {
				url += '?';
			}

			x = document.getElementById('filter_sort@{html_id}' );
			if(x) {
				url += 'zoradit=' + x.value;
			}
			if(filters['@{html_id}'].filter_params) {
				url += '&amp;filter=' + escape(filters['@{html_id}'].filter_params + '&amp;last_name=LIKE(' + value + '%)');
			}
			else {
				url += '&amp;filter=' + escape('&amp;last_name=LIKE(' + value + '%)');
			}


			document.location = url;
		 }
		 </script>
	</div>
</te:if>
&amp;nbsp;&amp;nbsp;
</truEngine-document>
