<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
<?php 
	if(substr(@{data}, 0, 7) == 'http://'
	|| substr(@{data}, 0, 8) == 'https://') {
		if(in_array(substr(@{data}, -4), array('.jpg', '.png', '.gif')) !== false) {
			echo '<a href="@{data}" target="_blank"><img src="@{data}" style="max-width:100px; max-height:100px;" /></a>';
			if(isset(@{container})) {
			?>
					<script type="text/javascript">$j(function() { $j('#@{container}').addClass('media'); });</script>
				<?php
			}
		}
		else {
			echo '<a href="@{data}" target="_blank">@{data}</a>';
		}
	}
	elseif(strtolower(substr(@{data}, 0, 5)) == '&lt;a') {
		echo htmlspecialchars_decode(@{data});
	}
	else {
		echo @{data};
	}

?>
</truEngine-document>
