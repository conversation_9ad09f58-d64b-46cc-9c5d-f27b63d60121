<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:if test="@{form:visibility}=='visible'">
	<te:if test="isset(@{form:vars:submit_button_title})">
		<input type="submit" class="button" value="@{form:vars:submit_button_title}" />
	<te:else />
		<input type="submit" class="button" value="@L{Odoslať}" />
	</te:if>
	
	&lt;/form&gt;
	
	<te:if test="isset(@{form:js}) &amp;&amp; !empty(@{form:js})">
		<script type="text/javascript">
			@{form:js}
		</script>
	</te:if>
	<script type="text/javascript">
			$j(document).ready(function() {
				var tr = $j('.form_table tr');
				tr.each(function(i) {
					showFieldLng('', $j(tr[i]).attr('id'));
				});
			});	
	</script>
</te:if>

</truEngine-document>
