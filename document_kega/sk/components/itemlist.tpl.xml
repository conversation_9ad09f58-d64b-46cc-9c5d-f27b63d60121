<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{items_name})) {
	@{items_name} = 'item';
}
?>

<script type="text/javascript">
	function show@{items_name}ListDetails(id, show) {
		$('#more_' + id).slideToggle();
		$('#a_show_' + id).toggle();
		$('#a_hide_' + id).toggle();
	}
</script>

<div class="row row-cards">
	<te:for each="@{items} as @{k_item}=>@{item}">
		<div class="col-12">
			<div class="card">
				<div class="card-header">
					<div class="row align-items-center w-100">
						<div class="col">
							<h3 class="card-title">
								@{item:titul_pred} @{item:first_name} @{item:last_name} @{item:titul_za}
							</h3>
						</div>
						<div class="col-auto">
							<div class="btn-list">
								<button type="button" class="btn btn-outline-primary btn-sm" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 1); return(false);" id="a_show_@{item:item_id}_@{k_item}" title="Viac detailov">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m12 5l0 14"/><path d="m5 12l14 0"/></svg>
								</button>
								<button type="button" class="btn btn-outline-secondary btn-sm" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 0); return(false);" id="a_hide_@{item:item_id}_@{k_item}" style="display:none" title="Menej detailov">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m5 12l14 0"/></svg>
								</button>
							</div>
						</div>
					</div>
				</div>

				<div id="more_@{item:item_id}_@{k_item}" class="card-body" style="display:none">
					<div class="row">
						<div class="col-md-6">
							<div class="mb-3">
								<label class="form-label"><te:string id="Rola" />:</label>
								<div class="form-control-plaintext">@{item:user_role}</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</te:for>
</div>

</truEngine-document>
