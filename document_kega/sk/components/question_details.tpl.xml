<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php @{edit_enabled}=false; ?>
<?php
	if(!isset(@{display})) {
		@{display} = array('navigation', 'answers', 'languages', 'source', 'autor', 'zaradenie', 'obtiaznost');
	}
	else {
		@{display} = explode(',', @{display});
	}

	if(!isset(@{keywords})) {
		@{keywords} = false;
	}
?>


<te:source name="list_questions" varname="question" filter="item_id=@{question_id}" />
<?php @{info} = @{question:_info}; @{question} = reset(@{question}); ?>
<p>
	<strong><te:string id="Znenie otázky" />:</strong><br />
	<te:if test="!in_array('languages', @{display})">
				@{question:otazka}<br />
		<te:else />
				<te:for each="@{info:languages} as @{xlng}">
					&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;[@{xlng}] <?php echo @{question}[@{xlng} . '_otazka']; ?><br />
				</te:for>
	</te:if>
</p>

<te:if test="in_array('source', @{display})">
	<p>
		<strong>@L{Literatúra}:</strong> @{question:literatura}
	</p>
</te:if>


<te:if test="in_array('autor', @{display})">
	<p>
		<strong>@L{Autor}:</strong> <te:include name="/sk/components/autor" autor="@{question:autorske_prava}" />
	</p>
</te:if>

<te:if test="in_array('zaradenie', @{display})">
	<p>
		<strong>@L{Zaradenie}:</strong> <te:include name="/sk/components/zaradenie" question="@{question}" />
	</p>
</te:if>

<te:if test="in_array('obtiaznost', @{display})">
	<p>
		<strong>@L{Obtiažnosť}:</strong> @{question:obtiaznost}
	</p>
</te:if>

<te:if test="@{keywords} !== false">
	<div id="qkeywords">
		<strong>@L{Kľúčové slová}:</strong><br />
		<table>
		<te:for each="@{info:languages} as @{xlng}">
			<?php @{value} = @{question}['@{xlng}_keywords']; ?>
			<tr><td>&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;&amp;nbsp;[@{xlng}] @L{Kľúčové slová}</td><td><input type="text" name="q-keywords-@{xlng}" id="q-keywords-@{xlng}" value="@{value}" style="width:300px;" /></td></tr>
		</te:for>
		</table>
	</div>
	
	<script type="text/javascript">
	$j(function() {
		$j('#qkeywords').insertBefore('#@{keywords} > input:first');
	});
	</script>
	
<script type="text/javascript">
// autocomplete script
document.addEvent('domready', function() {
  
 
	var inputWord2 = $('q-keywords-sk'); 
	new Autocompleter.Request.JSON(inputWord2, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_questions-sk_keywords', {
		'multiple': true,
		'selectFirst': true,
		'selectMode': false,
		'minLength': 2
	});
	
	var inputWord3 = $('q-keywords-en'); 
	new Autocompleter.Request.JSON(inputWord3, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_questions-en_keywords', {
		'multiple': true,
		'selectFirst': true,
		'selectMode': false,
		'minLength': 2
	});

	var inputWord4 = $('q-keywords-cz'); 
	new Autocompleter.Request.JSON(inputWord4, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_questions-cz_keywords', {
		'multiple': true,
		'selectFirst': true,
		'selectMode': false,
		'minLength': 2
	});
		 
});
</script>

	
</te:if>

<table te:if="isset(@{question:answers.item_id}) &amp;&amp; in_array('answers', @{display})">
	<?php @{ID} = ord('A'); ?>
	<te:for each="@{question:answers.item_id} as @{aitem_key}=>@{aitem_value}">
		<te:if test="@{aitem_value}">
			<tr>
				<?php
					@{answer_id} = @{question:answers.item_id}[@{aitem_key}];
					@{IDX} = chr(@{ID});
				?>
				<td class="label" style="padding-top:10px"><strong><span title="@{answer_id}">@{IDX} (@{answer_id})</span></strong> <small><?php echo @{question:answers.spravnost}[@{aitem_key}]; ?></small></td>
				<td style="padding-top:10px"><?php echo @{question:answers.sk_odpoved}[@{aitem_key}]; ?>
					<te:if test="isset(@{edit_enabled}) &amp;&amp; @{edit_enabled}">
						<br /><a href="/otazky/editovat-odpoved?otazka=@{question_id}&amp;item_id=@{answer_id}">@L{Editovať odpoveď}</a>
					</te:if>
				</td>
			</tr>
		</te:if>
		<?php @{ID}++; ?>
	</te:for>
</table>

<te:if test="in_array('navigation', @{display})">
	<p>
		<div te:access="s_test_edit_question">
			<a href="/otazky/editovat-otazku/&amp;item_id=@{question:item_id}"><te:string id="Editovať otázku" /></a>
		</div>
		<div te:access="s_test_edit_answer">
			<a href="/otazky/odpovede-multiedit-2/&amp;otazka=@{question:item_id}"><te:string id="Editovať opovede" /></a>
		</div>
	</p>
	<br />
</te:if>
<?php @{otazka} = @{question}; ?>
</truEngine-document>