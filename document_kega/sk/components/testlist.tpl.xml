<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{items_name})) {
	@{items_name} = 'item';
}
?>

<script type="text/javascript">
	function show@{items_name}ListDetails(id, show) {
		$j('#more_' + id).slideToggle();
		$j('#ifoto_' + id).slideToggle();
		$j('#a_show_' + id).toggle();
		$j('#a_hide_' + id).toggle();
	}
</script>

<div class="row row-cards">
	<te:for each="@{items} as @{k_item}=>@{item}">
		<div class="col-12" te:if="@{k_item:0} != '_' &amp;&amp; @{k_item} !== 'filter'">
			<div class="card">
				<div class="card-header">
					<div class="row align-items-center w-100">
						<div class="col">
							<h3 class="card-title">
								<?php @{item_name} = @{item}[@{item:language} . '_name']; ?>
								@{item_name}
							</h3>
							<div class="text-muted">
								<span class="badge bg-secondary me-2">
									<img src="/images/icons/@{item:language}.jpg" alt="@{item:language}" title="@{item:language}" class="me-1" style="width: 16px; height: 12px;" />
									@{item:language}
								</span>
								<te:if test="@{item:smer} != -1">
									<span class="badge bg-outline me-2">Smer @{item:smer}</span>
								</te:if>
								<te:if test="@{item:rocnik} != -1">
									<span class="badge bg-outline me-2">@{item:rocnik}</span>
								</te:if>
								<span class="badge bg-primary me-2">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M8 8a3.5 3 0 0 1 3.5 -3h1a3.5 3 0 0 1 3.5 3a3 3 0 0 1 1 3v1a5 5 0 0 1 -4 5h-4a5 5 0 0 1 -4 -5v-1a3 3 0 0 1 1 -3"/><path d="M8 11a1 1 0 0 1 2 0v1a1 1 0 0 1 -2 0v-1z"/><path d="M14 11a1 1 0 0 1 2 0v1a1 1 0 0 1 -2 0v-1z"/></svg>
									@{item:settings:pocet_otazok} <te:string id="otázok" />
								</span>
								<te:if test="isset(@{item:cas}) &amp;&amp; !empty(@{item:cas})">
									<span class="badge bg-warning">
										<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 8l0 4l2 2"/><path d="M3.05 11a9 9 0 1 1 .5 4m-.5 5v-5h5"/></svg>
										@{item:cas} min
									</span>
								</te:if>
							</div>
						</div>
						<div class="col-auto">
							<div class="btn-list">
								<button type="button" class="btn btn-outline-primary btn-sm" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 1); return(false);" id="a_show_@{item:item_id}_@{k_item}" title="Viac detailov">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m12 5l0 14"/><path d="m5 12l14 0"/></svg>
								</button>
								<button type="button" class="btn btn-outline-secondary btn-sm" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 0); return(false);" id="a_hide_@{item:item_id}_@{k_item}" style="display:none" title="Menej detailov">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m5 12l14 0"/></svg>
								</button>
							</div>
						</div>
					</div>
				</div>

				<te:if test="!empty(@{item:description})">
					<div class="card-body">
						<?php @{item_description} = @{item}[@{item:language} . '_description']; ?>
						<p class="text-muted">@{item_description}</p>
					</div>
				</te:if>
				<div id="more_@{item:item_id}_@{k_item}" class="card-body border-top" style="display:none">
					<div class="row">
						<div class="col-md-6">
							<div class="mb-3">
								<label class="form-label"><te:string id="Počet otázok" />:</label>
								<div class="form-control-plaintext">@{item:pocet_otazok}</div>
							</div>

							<div class="mb-3">
								<label class="form-label"><te:string id="Spôsob testovania" />:</label>
								<div class="form-control-plaintext">@{item:settings:sposob_testovania}</div>
							</div>
						</div>

						<div class="col-md-6">
							<div class="mb-3">
								<label class="form-label"><te:string id="Ukázať odpovede" />:</label>
								<div class="form-control-plaintext">@{item:settings:ukazat_odpovede}</div>
							</div>

							<div class="mb-3">
								<label class="form-label"><te:string id="Aktuálnosť" />:</label>
								<div class="form-control-plaintext">@{item:aktualnost}</div>
							</div>
						</div>
					</div>

					<div class="btn-list">
						<a te:access="s_test_test_run" href="/testy/start?test_id=@{item:item_id}" class="btn btn-primary">
							<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M7 4v16l13 -8z"/></svg>
							<te:string id="Spustiť skúšanie" />
						</a>
						<a te:access="s_test_generate_print" href="/testy/generovat-tlacove-predlohy?test_id=@{item:item_id}" class="btn btn-outline-secondary">
							<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M17 17h2a2 2 0 0 0 2 -2v-4a2 2 0 0 0 -2 -2h-14a2 2 0 0 0 -2 2v4a2 2 0 0 0 2 2h2"/><path d="M17 9v-4a2 2 0 0 0 -2 -2h-6a2 2 0 0 0 -2 2v4"/><path d="M7 13m0 2a2 2 0 0 1 2 -2h6a2 2 0 0 1 2 2v4a2 2 0 0 1 -2 2h-6a2 2 0 0 1 -2 -2z"/></svg>
							<te:string id="Generovať tlačové predlohy" />
						</a>
						<te:if test="!isset(@{instances}) || @{instances} == 'yes'">
							<a te:access="s_test_list_instances" href="/testy/zoznam-testovani?test_id=@{item:item_id}" class="btn btn-outline-info">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M9 5h-2a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-12a2 2 0 0 0 -2 -2h-2"/><path d="M9 3m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v0a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z"/><path d="M9 12l2 2l4 -4"/></svg>
								<te:string id="Zoznam testovaní" />
							</a>
						</te:if>
						<te:if test="!@{item:instances}">
							<a te:access="s_test_delete_test" href="/@{doc}?delete_id=@{item:item_id}" class="btn btn-outline-danger" onclick="return(confirm('@L{Naozaj chcete zmazat test?}'));">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M4 7l16 0"/><path d="M10 11l0 6"/><path d="M14 11l0 6"/><path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/><path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/></svg>
								<te:string id="Zmazať test" />
							</a>
						<te:else />
							<a te:access="s_test_delete_test_data" href="/@{doc}?delete_id=@{item:item_id}&amp;delete_data=true" class="btn btn-outline-danger" onclick="return(confirm('@L{Naozaj chcete zmazat test aj s jeho testovaniami?}'));">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M4 7l16 0"/><path d="M10 11l0 6"/><path d="M14 11l0 6"/><path d="M5 7l1 12a2 2 0 0 0 2 2h8a2 2 0 0 0 2 -2l1 -12"/><path d="M9 7v-3a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v3"/></svg>
								<te:string id="Zmazať test s dátami" />
							</a>
						</te:if>
						<te:if test="isset(@{item:settings:halloffame}) &amp;&amp; @{item:settings:halloffame} == 'yes'">
							<a te:access="s_test_hall_of_fame" href="/testy/hall-of-fame?test_id=@{item:item_id}" class="btn btn-outline-warning">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 6l4 6l5 -4l-2 10h-14l-2 -10l5 4z"/></svg>
								<te:string id="Hall of fame" />
							</a>
						</te:if>
						<a te:access="s_test_literatura" href="/literatura/resers?test_id=@{item:item_id}" class="btn btn-outline-secondary">
							<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M3 19a9 9 0 0 1 9 0a9 9 0 0 1 9 0"/><path d="M3 6a9 9 0 0 1 9 0a9 9 0 0 1 9 0"/><path d="M3 6l0 13"/><path d="M12 6l0 13"/><path d="M21 6l0 13"/></svg>
							<te:string id="Rešerš literatúry" />
						</a>
					</div>
				</div>
			</div>
		</div>
	</te:for>
</div>

</truEngine-document>
