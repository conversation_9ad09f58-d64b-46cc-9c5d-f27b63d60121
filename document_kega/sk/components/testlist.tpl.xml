<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{items_name})) {
	@{items_name} = 'item';
}
?>

<script type="text/javascript">
	function show@{items_name}ListDetails(id, show)
	{
		$j('#more_' + id).slideToggle();
		$j('#ifoto_' + id).slideToggle();
		$j('#a_show_' + id).toggle();
		$j('#a_hide_' + id).toggle();
	}
</script>

<te:for each="@{items} as @{k_item}=>@{item}">
	<div class="itemlist" te:if="@{k_item:0} != '_' &amp;&amp; @{k_item} !== 'filter'">
		<div class="nav">
			<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 1); return(false);" id="a_show_@{item:item_id}_@{k_item}" title="Viac detailov">+</a>
			<a href="#" onclick="show@{items_name}ListDetails(@{item:item_id}+'_'+@{k_item}, 0); return(false);" id="a_hide_@{item:item_id}_@{k_item}" style="display:none" title="Menej detailov">--</a>
		</div>
		<table cellpadding="0" cellspacing="0">
			<tr>
				<td class="label"><te:string id="Názov testu" />:</td>
				<td>
					<?php @{item_name} = @{item}[@{item:language} . '_name'];  ?>
					@{item_name}
				</td>
			</tr>
			<tr>
				<td class="label"><te:string id="Nastavenia" />:</td>
				<td>
					<img src="/images/icons/@{item:language}.jpg" alt="@{item:language}" title="@{item:language}" />&amp;nbsp;
					<te:if test="@{item:smer} != -1"> / smer @{item:smer}</te:if>
					<te:if test="@{item:rocnik} != -1"> / @{item:rocnik}</te:if>
					/ <te:string id="otázok" /> @{item:settings:pocet_otazok}
					<te:if test="isset(@{item:cas}) &amp;&amp; !empty(@{item:cas})">
						/ <te:string id="čas" /> @{item:cas} min
					</te:if> 
						
				</td>
			</tr>
			<tr te:if="!empty(@{item:description})">
				<td class="label"><te:string id="Popis testu" />:</td>
				<td>
					<?php @{item_description} = @{item}[@{item:language} . '_description'];  ?>
					@{item_description}
				</td>
			</tr>
		</table>
		<div id="more_@{item:item_id}_@{k_item}" style="display:none">
			<table cellpadding="0" cellspacing="0">
				<tr>
					<td class="label"><te:string id="Počet otázok" />:</td>
					<td>@{item:pocet_otazok}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Spôsob testovania" />:</td>
					<td>@{item:settings:sposob_testovania}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Ukázať odpovede" />:</td>
					<td>@{item:settings:ukazat_odpovede}</td>
				</tr>
				<tr>
					<td class="label"><te:string id="Aktuálnosť" />:</td>
					<td>@{item:aktualnost}</td>
				</tr>
				<tr>
					<td></td>
					<td>
							<a te:access="s_test_test_run" href="/testy/start?test_id=@{item:item_id}" style="margin-right:50px"><te:string id="Spustiť skúšanie" /></a>
							<a te:access="s_test_generate_print" href="/testy/generovat-tlacove-predlohy?test_id=@{item:item_id}" style="margin-right:50px"><te:string id="Generovať tlačové predlohy" /></a>
							<te:if test="!isset(@{instances}) || @{instances} == 'yes'">
								<a te:access="s_test_list_instances" href="/testy/zoznam-testovani?test_id=@{item:item_id}" style="margin-right:50px"><te:string id="Zoznam testovaní" /></a>
							</te:if>
							<te:if test="!@{item:instances}">
								<a te:access="s_test_delete_test" href="/@{doc}?delete_id=@{item:item_id}" style="margin-right:50px" onclick="return(confirm('@L{Naozaj chcete zmazat test?}'));"><te:string id="Zmazať test" /></a>
								<te:else />
								<a te:access="s_test_delete_test_data" href="/@{doc}?delete_id=@{item:item_id}&amp;delete_data=true" style="margin-right:50px" onclick="return(confirm('@L{Naozaj chcete zmazat test aj s jeho testovaniami?}'));"><te:string id="Zmazať test s dátami" /></a>
							</te:if>
							<te:if test="isset(@{item:settings:halloffame}) &amp;&amp; @{item:settings:halloffame} == 'yes'">
								<a te:access="s_test_hall_of_fame" href="/testy/hall-of-fame?test_id=@{item:item_id}"><te:string id="Hall of fame" /></a>
							</te:if>
							<a te:access="s_test_literatura" href="/literatura/resers?test_id=@{item:item_id}"><te:string id="Rešerš literatúry" /></a>
					</td>
				</tr>
			</table>
		</div>

		<div class="clear"></div>
	</div>
</te:for>

</truEngine-document>
