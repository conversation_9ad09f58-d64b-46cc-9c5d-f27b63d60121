<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<div te:print="no" class="d-flex align-items-center justify-content-between">
	<?php
	if(!isset(@{params})) {
		@{params} = '';
	}

	if(!isset(@{extra_params})) {
		@{extra_params} = '';
	}

	if(!isset(@{_order_by})) {
		@{_order_by} = '';
	}
	?>

	<div class="d-flex align-items-center">
		<?php if(@{pager:totalPages} > 1) { ?>
			<nav aria-label="Stránkovanie">
				<ul class="pagination pagination-sm m-0">
					<?php if(@{pager:currentPage} <= 1) { ?>
						<li class="page-item disabled">
							<span class="page-link">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m15 6l-6 6l6 6"/></svg>
								Predchádzajúca
							</span>
						</li>
					<?php } else { ?>
						<li class="page-item">
							<a class="page-link" href="<?php echo main_gClass::makeUrl_gFunc('/@{doc}?page='.(@{pager:currentPage}-1)).'@{params}@{extra_params}'; ?>">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m15 6l-6 6l6 6"/></svg>
								Predchádzajúca
							</a>
						</li>
					<?php } ?>

					<?php for($i=0; $i < @{pager:totalPages}; $i++) { ?>
						<?php if(($i + 1) == @{pager:currentPage}) { ?>
							<li class="page-item active">
								<span class="page-link"><?php echo $i+1; ?></span>
							</li>
						<?php } else { ?>
							<li class="page-item">
								<a class="page-link" href="<?php echo main_gClass::makeUrl_gFunc('/@{doc}?page='.($i+1)).'@{params}@{extra_params}'; ?>"><?php echo $i+1; ?></a>
							</li>
						<?php } ?>
					<?php } ?>

					<?php if(@{pager:currentPage} >= @{pager:totalPages}) { ?>
						<li class="page-item disabled">
							<span class="page-link">
								Ďalšia
								<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m9 6l6 6l-6 6"/></svg>
							</span>
						</li>
					<?php } else { ?>
						<li class="page-item">
							<a class="page-link" href="<?php echo main_gClass::makeUrl_gFunc('/@{doc}?page='.(@{pager:currentPage}+1)).'@{params}@{extra_params}'; ?>">
								Ďalšia
								<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m9 6l6 6l-6 6"/></svg>
							</a>
						</li>
					<?php } ?>
				</ul>
			</nav>
		<?php } ?>
	</div>

	<div class="d-flex align-items-center gap-3">
		<?php
			@{current_order} = explode('/', @{_order_by});
			if(!isset(@{current_order:1})) { @{current_order:1} = 'ASC'; }
			@{display_type} = @{pager:totalItems} > 1 ? 'inline':'none';
			if(@{pager:totalItems} <= 0) {
				?>
					<script type="text/javascript">
						var fce = document.getElementById('filter_container_empty');
						if(fce) {
							fce.style.display='none';
						}
					</script>
					<?php
				}
		?>

		<te:if test="isset(@{order_fields})">
			<?php
				if(!isset($GLOBALS['pager_id'])) {
					$GLOBALS['pager_id'] = 0;
				}
				$GLOBALS['pager_id'] ++;
				@{ctag} = 'pager_id_' . $GLOBALS['pager_id'];
			?>

			<script type="text/javascript">
				function sob_@{ctag}() {
					var sel1 = document.getElementById('sort_sel1_@{ctag}');
					var sel2 = document.getElementById('sort_sel2_@{ctag}');

					var order_params = '';
					order_params = order_params +  $j('#sort_sel2_@{ctag}').val();
					order_params = order_params +  '/' + $j('#sort_sel1_@{ctag}').val();

					$j('#filter_sort').val(order_params);

					var url = '<?php echo main_gClass::makeUrl_gFunc('/' . @{doc}); ?>';

					if(url.indexOf('?') &gt;= 0) {
						url += '&amp;';
					}
					else {
						url += '?';
					}

					url += 'zoradit=' + order_params;

					if(filters['default'].filter_params) {
						url += '&amp;filter=' + escape(filters['default'].filter_params);
					}

					url += '@{extra_params}';
					document.location = url;
				}
			</script>

			<div class="d-flex align-items-center gap-2">
				<span class="text-muted">@L{Radiť}</span>
				<select class="form-select form-select-sm" onchange="sob_@{ctag}();" id="sort_sel1_@{ctag}">
					<option te:if="@{current_order:1} == 'ASC'" value="ASC" selected="selected">@L{vzostupne}</option>
					<option te:if="@{current_order:1} == 'DESC'" value="DESC" selected="selected">@L{zostupne}</option>
					<option te:if="@{current_order:1} != 'ASC'" value="ASC">@L{vzostupne}</option>
					<option te:if="@{current_order:1} != 'DESC'" value="DESC">@L{zostupne}</option>
				</select>
				<span class="text-muted">@L{podľa}</span>
				<select class="form-select form-select-sm" onchange="sob_@{ctag}();" id="sort_sel2_@{ctag}">
					<option></option>
					<te:for each="@{order_fields} as @{kfield}=>@{vfield}">
						<option te:if="@{current_order:0} == @{kfield}" value="@{kfield}" selected="selected">@{vfield}</option>
						<option te:if="@{current_order:0} != @{kfield}" value="@{kfield}">@{vfield}</option>
					</te:for>
				</select>
			</div>
		</te:if>

		<te:if test="isset(@{select}) &amp;&amp; is_array(@{select})">
			@{select:html}
		</te:if>

		<span class="badge bg-secondary">@{pager:totalItems}</span>
	</div>
</div>

</truEngine-document>
