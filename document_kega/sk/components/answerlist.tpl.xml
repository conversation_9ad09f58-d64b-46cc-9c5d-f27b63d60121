<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<div class="row row-cards">
	<te:for each="@{answers} as @{k_answer}=>@{answer}">
		<div class="col-12">
			<div class="card">
				<div class="card-body">
					<div class="row align-items-center">
						<div class="col-auto">
							<span class="badge bg-primary"><?php echo chr(65 + @{k_answer}); ?></span>
						</div>
						<div class="col">
							<div class="text-truncate">
								@{answer:text}
							</div>
						</div>
						<div class="col-auto">
							<te:if test="@{answer:correct}">
								<span class="badge bg-success">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10"/></svg>
									Správne
								</span>
							<te:else />
								<span class="badge bg-danger">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M18 6l-12 12"/><path d="m6 6l12 12"/></svg>
									Nesprávne
								</span>
							</te:if>
						</div>
					</div>
				</div>
			</div>
		</div>
	</te:for>
</div>

</truEngine-document>
