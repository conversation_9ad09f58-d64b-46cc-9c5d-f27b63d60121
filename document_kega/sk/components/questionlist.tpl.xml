<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php if(!isset(@{select})) { @{select} = false; } ?>

<script type="text/javascript">
    function showQuestionlistDetails(id, show)
    {
        $('#more_' + id).slideToggle();
        $('#ifoto_' + id).slideToggle();
        $('#a_show_' + id).toggle();
        $('#a_hide_' + id).toggle();
    }
</script>

    <style>
        .a_show, .a_hide { padding-right: 0px; }
        .badge.bg-success {
            color:rgb(55, 65, 81);
            background-color:rgba(102, 187, 106, 0.5) !important;
        }
        .badge.bg-danger {
            color:rgb(55, 65, 81);
            background-color:rgba(245, 80, 55, 0.5) !important;
        }
    </style>

<div class="row row-cards">
    <div class="space-y-4">
        <te:for each="@{questions} as @{k_question}=>@{question}">
            <div class="col-12" te:if="substr(@{k_question}, 0, 1) != '_'">
                <div class="card" id="ql_@{question:item_id}">
                    <div class="card-header">
                        <div class="row align-items-center" style="width:100%">
                            <div class="col">
                                <h3 class="card-title">
                                    @{question:item_id}. <te:include name="/sk/components/content-data" data="@{question:otazka}" />
                                </h3>
                                <div class="text-muted small" te:print="no">
                                    Autor: <te:include name="/sk/components/autor" autor="@{question:autorske_prava}" />
                                </div>
                            </div>
                            <div class="col-auto">
                                <div class="btn-list">
                                    <te:if test="@{select:status}">
                                        <input type="checkbox" class="form-check-input msel_test_questions" id="msel_test_questions_@{question:item_id}" onclick="msel_check('test_questions', @{question:item_id}, this.checked)" />
                                    </te:if>
                                    <button type="button" class="btn btn-outline-primary btn-sm a_show" onclick="showQuestionlistDetails(@{question:item_id}, 1); return(false);" id="a_show_@{question:item_id}" title="Viac detailov">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m12 5l0 14"/><path d="m5 12l14 0"/></svg>
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary btn-sm a_hide" onclick="showQuestionlistDetails(@{question:item_id}, 0); return(false);" id="a_hide_@{question:item_id}" style="display:none" title="Menej detailov">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m5 12l14 0"/></svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="card-body">
                        <te:if test="count(@{question:otazka_media})">
                            <script type="text/javascript">$(function() { $('#ql_@{question:item_id}').addClass('media'); });</script>
                            <div class="mb-3">
                                <?php @{src} = isset(@{question:otazka_media:0}) ? @{question:otazka_media:0:src} : '' ; ?>
                                <?php @{thumbnail} = isset(@{question:otazka_media:0:th1_}) ? @{question:otazka_media:0:th1_:src} : '' ; ?>
                                <te:include name="/sk/components/movie" src="@{src}" thumbnail="@{thumbnail}" />
                            </div>
                        </te:if>

                        <div class="btn-list">
                            <te:choose test="@{question:status}">
                                <te:when case="active">
                                    <te:if test="@{question:owner_id}==@{session:user_id}">
                                        <a te:access="s_test_edit_question|s_test_edit_question_owned" class="btn btn-primary" href="/otazky/editovat-otazku/?item_id=@{question:item_id}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/><path d="m20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/><path d="m16 5l3 3"/></svg>
                                            <te:string id="Editovať otázku" />
                                        </a>
                                        <te:else />
                                        <a te:access="s_test_edit_question" class="btn btn-primary" href="/otazky/editovat-otazku/?item_id=@{question:item_id}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/><path d="m20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/><path d="m16 5l3 3"/></svg>
                                            <te:string id="Editovať otázku" />
                                        </a>
                                    </te:if>

                                    <a te:access="s_system_admin" class="btn btn-secondary" href="/moj-opus/single-questions/new/?question_id=@{question:item_id}">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M4 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M7 17l0 .01"/><path d="M14 4m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M7 7l0 .01"/><path d="M4 14m0 1a1 1 0 0 1 1 -1h4a1 1 0 0 1 1 1v4a1 1 0 0 1 -1 1h-4a1 1 0 0 1 -1 -1z"/><path d="M17 7l0 .01"/><path d="M14 14l3 0"/><path d="M20 14l0 .01"/><path d="M14 17l6 0"/><path d="M14 20l6 0"/><path d="M17 17l0 3"/><path d="M20 17l0 3"/></svg>
                                        <te:string id="Vygenerovať `single question` QR kod" />
                                    </a>
                                </te:when>
                                <te:when case="waiting">
                                    <te:if test="@{question:owner_id}==@{session:user_id}">
                                        <a te:access="s_test_edit_xquestion|s_test_edit_xquestion_owned" class="btn btn-warning" href="/otazky/editovat-predotazku/?item_id=@{question:item_id}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/><path d="m20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/><path d="m16 5l3 3"/></svg>
                                            <te:string id="Editovať predotázku" />
                                        </a>
                                        <te:else />
                                        <a te:access="s_test_edit_xquestion" class="btn btn-warning" href="/otazky/editovat-predotazku/?item_id=@{question:item_id}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/><path d="m20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/><path d="m16 5l3 3"/></svg>
                                            <te:string id="Editovať predotázku" />
                                        </a>
                                    </te:if>
                                </te:when>
                                <te:when case="rejected">
                                    <te:if test="@{question:owner_id}==@{session:user_id}">
                                        <a te:access="s_test_edit_xquestion|s_test_edit_xquestion_owned" class="btn btn-danger" href="/otazky/editovat-predotazku/?item_id=@{question:item_id}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/><path d="m20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/><path d="m16 5l3 3"/></svg>
                                            <te:string id="Editovať predotázku" />
                                        </a>
                                        <te:else />
                                        <a te:access="s_test_edit_xquestion" class="btn btn-danger" href="/otazky/editovat-predotazku/?item_id=@{question:item_id}">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/><path d="m20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/><path d="m16 5l3 3"/></svg>
                                            <te:string id="Editovať predotázku" />
                                        </a>
                                    </te:if>
                                </te:when>
                            </te:choose>

                            <a te:access="s_test_show_comments" class="btn btn-outline-primary" href="/otazky/komentare-k-otazke?item_id=@{question:item_id}">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M8 9h8"/><path d="M8 13h6"/><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/></svg>
                                <te:string id="Komentáre" />
                            </a>
                            <a href="/otazky/log-zmien-otazky?item_id=@{question:item_id}" class="btn btn-outline-secondary">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 8l0 4l2 2"/><path d="M3.05 11a9 9 0 1 1 .5 4m-.5 5v-5h5"/></svg>
                                <te:string id="Log zmien" />
                            </a>
                        </div>

                        <te:if test="@{question:status} == 'waiting'">
                            <te:if test="!@{question:garant_id} || @{question:garant_id} == @{session:user_id}">
                                <div class="mt-3 pt-3 border-top">
                                    <div class="btn-list">
                                        <a te:access="s_test_accept_xquestion" class="btn btn-success" id="link_accept_@{question:item_id}" href="/otazky/akceptovat-otazku?item_id=@{question:item_id}" target="_blank" onclick="return(acceptQuestion(@{question:item_id}))">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path d="m9 12l2 2l4 -4"/></svg>
                                            <te:string id="Akceptovať predotázku" />
                                        </a>
                                        <button te:access="s_test_reject_xquestion" class="btn btn-danger" id="link_reject_@{question:item_id}" onclick="return(rejectQuestion(@{question:item_id}))">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path d="m5.7 5.7l12.6 12.6"/></svg>
                                            <te:string id="Zamietnúť predotázku" />
                                        </button>
                                    </div>
                                    <div te:access="s_test_reject_xquestion" id="block_reject_@{question:item_id}" class="mt-3" style="display:none">
                                        <form method="get" action="/otazky/zamietnut-otazku?item_id=@{question:item_id}" target="_blank" id="form_reject_go_@{question:item_id}">
                                            <div class="mb-3">
                                                <label class="form-label"><strong><te:string id="Dôvod zamietnutia" />:</strong></label>
                                                <textarea name="reason" id="textarea_reject_@{question:item_id}" class="form-control" rows="3"></textarea>
                                            </div>
                                            <button type="button" id="link_reject_go_@{question:item_id}" class="btn btn-danger" onclick="rejectQuestionSubmit(@{question:item_id}); this.parentNode.submit(); return(false);">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 12m-9 0a9 9 0 1 0 18 0a9 9 0 1 0 -18 0"/><path d="m5.7 5.7l12.6 12.6"/></svg>
                                                <te:string id="Zamietnúť predotázku" />
                                            </button>
                                            <input type="submit" value="Zamietnúť predotázku" style="display:none" />
                                        </form>
                                    </div>
                                </div>
                            </te:if>
                        </te:if>

                        <div id="more_@{question:item_id}" class="mt-4 pt-3 border-top" style="display:none">
                            <div class="row mb-3">
                                <div class="col-12">
                                    <label class="form-label d-inline"><te:string id="Zaradenie" />:</label>
                                    <span class="ms-2"><te:include name="/sk/components/zaradenie" question="@{question}" /></span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label class="form-label"><te:string id="Literatúra" />:</label>
                                        <div class="form-control-plaintext">
                                            @{question:literatura} <?php
                                                if(isset(@{question:literatura_strana}) && !empty(@{question:literatura_strana})) {
                                                    echo ' (' . string_gClass::interval(@{question:literatura_strana}) . ')';
                                                };
                                            ?>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6" te:access="s_test_edit_question">
                                    <div class="mb-3">
                                        <label class="form-label"><te:string id="Index obtiažnosti/nekorektnosti" />:</label>
                                        <div class="form-control-plaintext">@{question:obtiaznost} / @{question:nekorektnost}</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"><te:string id="Počet správnych/nesprávnych zodpovedaní" />:</label>
                                        <div class="form-control-plaintext">@{question:spravne} / @{question:nespravne}</div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label"><te:string id="Počet správnych/nesprávnych zodpovedaní v teste" />:</label>
                                        <div class="form-control-plaintext">@{question:spravne_test} / @{question:nespravne_test}</div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <h5>Odpovede</h5>
                                <div class="list-group list-group-flush">
                                    <?php @{ID} = ord('A'); ?>
                                    <te:for each="@{question:answers.item_id} as @{aitem_key}=>@{aitem_value}">
                                        <te:if test="@{aitem_value}">
                                            <?php
                                                @{answer_id} = @{question:answers.item_id}[@{aitem_key}];
                                                @{IDX} = chr(@{ID});
                                                @{answer_text} = @{question}['answers.' . @{lng} . '_odpoved'][@{aitem_key}];
                                            ?>
                                            <div class="list-group-item">
                                                <div class="row align-items-center">
                                                    <div class="col-auto">
                                                        <te:if test="@{question:answers.spravnost}[@{aitem_key}] == 'spravne'">
                                                            <span class="badge bg-success" title="@{answer_id}">@{IDX}</span>
                                                        <te:else />
                                                            <span class="badge bg-danger" title="@{answer_id}">@{IDX}</span>
                                                        </te:if>
                                                    </div>
                                                    <div class="col">
                                                        <te:if test="@{question:answers.spravnost}[@{aitem_key}] == 'spravne'">
                                                            <te:include name="/sk/components/content-data" data="@{answer_text}" container="ql_@{question:item_id}" />
                                                        <te:else />
                                                            <span class="text-decoration-line-through text-muted">
                                                                <te:include name="/sk/components/content-data" data="@{answer_text}" container="ql_@{question:item_id}" />
                                                            </span>
                                                        </te:if>

                                                        <?php if(isset(@{question}['answers.odpoved_media'])) { ?>
                                                            <?php
                                                                @{answer_odpoved_media} = @{question}['answers.odpoved_media'][@{aitem_key}];
                                                                @{answer_odpoved_media} = reset(@{answer_odpoved_media});
                                                            ?>
                                                            <te:if test="is_array(@{answer_odpoved_media}) &amp;&amp; isset(@{answer_odpoved_media:src})">
                                                                <script type="text/javascript">$j(function() { $j('#ql_@{question:item_id}').addClass('media'); });</script>
                                                                <div class="mt-2">
                                                                    <?php @{src} = isset(@{answer_odpoved_media}) ? @{answer_odpoved_media:src} : '' ; ?>
                                                                    <?php @{thumbnail} = isset(@{answer_odpoved_media:th1_}) ? @{answer_odpoved_media:th1_:src} : '' ; ?>
                                                                    <te:include name="/sk/components/movie" src="@{src}" thumbnail="@{thumbnail}" />
                                                                </div>
                                                            </te:if>
                                                        <?php } ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php @{ID}++; ?>
                                        </te:if>
                                    </te:for>
                                </div>
                            </div>
                        </div>

                        <te:ajax>
                            <script type="text/javascript" te:if="@{question:status} == 'waiting' &amp;&amp; (!@{question:garant_id} || @{question:garant_id} == @{session:user_id})">
                                window.addEventListener('MyDOMContentLoaded',function () {
                                    $('link_accept_@{question:item_id}').onclick = function() { acceptQuestionAjax(@{question:item_id}); return(false); };
                                });
                                window.addEventListener('MyDOMContentLoaded',function () {
                                    $('link_reject_go_@{question:item_id}').onclick = function() { rejectQuestionAjax(@{question:item_id}); return(false); };
                                });
                            </script>
                        </te:ajax>
                    </div>
                </div>
            </div>
        </te:for>
    </div>
</div>

<te:ajax>
    <script type="text/javascript">
    function acceptQuestionAjax(question_id)
    {
        var req = new Request({
            method: 'get',
            url: $('link_accept_' + question_id).get('href'),
            data: { 'ajax' : 'true' },
            onSuccess: function(responseText, responseXML) {
                if(getAjaxResponse(responseXML, 'result') != '1') {
                    alert('<te:string id="Otázku sa nepodarilo schváliť." />');
                }
                else {
                    acceptQuestion(question_id);
                }
            },
            onFailure: function() {
                alert('<te:string id="Otázku sa nepodarilo schváliť." />');
            }
            }).send();
    }
    function rejectQuestionAjax(question_id)
    {
        var req = new Request({
            method: 'get',
            url: $('link_reject_go_' + question_id).get('href'),
            data: { 'ajax' : 'true', 'reason' : $('textarea_reject_' + question_id).value },
            onSuccess: function(responseText, responseXML) {
                if(getAjaxResponse(responseXML, 'result') != '1') {
                    alert('<te:string id="Otázku sa nepodarilo zamietnúť." />');
                }
                else {
                    rejectQuestionSubmit(question_id);
                }
            },
            onFailure: function() {
                alert('<te:string id="Otázku sa nepodarilo zamietnúť." />');
            }
            }).send();
    }
    </script>
</te:ajax>
</truEngine-document>
