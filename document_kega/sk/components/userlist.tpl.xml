<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{select}) || !is_array(@{select})) {
	@{select} = array();
}
if(!isset(@{select:status})) {
	@{select:status} = false;
}
?>

<script type="text/javascript">
	function showUserlistDetails(id, show)
	{
		$j('#more_' + id).slideToggle();
		$j('#ifoto_' + id).slideToggle();
		$j('#a_show_' + id).toggle();
		$j('#a_hide_' + id).toggle();
	}
</script>

<div class="row row-cards">
	<?php @{n} = 0; @{isic_ok_class} = isset(@{user:isic_ok}) ? @{user:isic_ok} : 'yes'; ?>
	<te:for each="@{users} as @{k_user}=>@{user}">
		<div class="col-12" te:if="substr(@{k_user}, 0 , 1) != '_'">
			<div class="card userlist-@{user:user_role} userlist-isic-@{isic_ok_class}">
				<div class="card-header">
					<div class="row align-items-center w-100">
						<div class="col-auto">
							<span class="avatar avatar-lg">
								<te:if test="is_array(@{user:foto}) &amp;&amp; isset(@{user:foto:0})">
									<img src="@{user:foto:0:th1_:src}" alt="@{user:nick}" />
								<te:else />
									<te:if test="is_array(@{user:foto_rec}) &amp;&amp; isset(@{user:foto_rec:0})">
										<img src="@{user:foto_rec:0:th1_:src}" alt="@{user:nick}" />
									<te:else />
										<img src="/images/icons/nofoto.jpg" alt="@{user:nick}" />
									</te:if>
								</te:if>
							</span>
						</div>
						<div class="col">
							<h3 class="card-title">
								<?php
									@{right}='s_users_edit_user';
									if(@{user:user_role} == 'superuser') {
										@{right}='s_users_edit_superadmin';
									}
									if(@{user:user_role} == 'admin') {
										@{right}='s_users_edit_admin';
									}
									if(@{user:owner_id} == @{session:user_id}) {
										if(@{user:user_role} != 'superuser') {
											@{right} .= '_owned';
										}
									}
									if(@{user:status} == 'request') {
										@{right} = 's_users_accept_request';
									}
								?>
								<te:access access="@{right}">
									<te:if test="@{user:status} == 'request'">
										<a href="/pouzivatelia/editovat-registraciu?item_id=@{user:item_id}" te:print="no" class="text-reset">@{user:nick}</a>
										<span te:print="yes">@{user:nick}</span>
									<te:else />
										<a href="/pouzivatelia/editovat-pouzivatela?item_id=@{user:item_id}" te:print="no" class="text-reset">@{user:nick}</a>
										<span te:print="yes">@{user:nick}</span>
									</te:if>
								</te:access>
								<te:xaccess xaccess="@{right}">
									@{user:nick}
								</te:xaccess>
								<te:if test="strtolower(@{user:nick})!==strtolower(@{user:login})">
									<small class="text-muted">(@{user:login})</small>
								</te:if>
							</h3>
							<div class="text-muted">
								@{user:titul_pred} @{user:first_name} @{user:last_name} @{user:titul_za}
							</div>
						</div>
						<div class="col-auto">
							<div class="btn-list">
								<te:if test="@{select:status}" te:print="no">
									<input type="checkbox" class="form-check-input" id="msel_users_@{user:item_id}" onclick="msel_check('users', @{user:item_id}, this.checked)" />
								</te:if>
								<button type="button" class="btn btn-outline-primary btn-sm" onclick="showUserlistDetails(@{user:item_id}+'_'+@{k_user}, 1); return(false);" id="a_show_@{user:item_id}_@{k_user}" title="@L{Viac detailov}">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m12 5l0 14"/><path d="m5 12l14 0"/></svg>
								</button>
								<button type="button" class="btn btn-outline-secondary btn-sm" onclick="showUserlistDetails(@{user:item_id}+'_'+@{k_user}, 0); return(false);" id="a_hide_@{user:item_id}_@{k_user}" style="display:none" title="@L{Menej detailov}">
									<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m5 12l14 0"/></svg>
								</button>
							</div>
						</div>
					</div>
				</div>

				<div class="card-body">
					<div class="row">
						<div class="col-md-6">
							<te:if test="isset(@{user:email})">
								<div class="mb-3">
									<label class="form-label"><te:string id="Email" />:</label>
									<div class="form-control-plaintext">
										<te:if test="!empty(@{user:email})">
											@{user:email}
											<span id="mstatus-@{user:item_id}">
												<te:if test="isset(@{user:email_check_status}) &amp;&amp; @{user:email_check_status}!=='checked'">
													<span class="badge bg-danger ms-2">
														<te:choose test="@{user:email_check_status}">
															<te:when case="notset"><te:string id="neoverený" /></te:when>
															<te:when case="changed"><te:string id="zmenený" /></te:when>
															<te:when case="protected"><te:string id="schválený" /></te:when>
															<te:otherwise>@{user:email_check_status}</te:otherwise>
														</te:choose>
													</span>
												</te:if>
											</span>
											<a te:access="s_users_accept_request" href="/pouzivatelia/poslat-overovaci-mail?user_id=@{user:item_id}" class="btn btn-sm btn-outline-primary ms-2" onclick="$j.ajax({url: $j(this).attr('href'), context: document.body, success: function(){ $j('#mstatus-@{user:item_id}').html('&lt;span class=badge bg-danger ms-2&gt;neoverený&lt;/span&gt;'); } }); return(false);">
												<svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M3 7a2 2 0 0 1 2 -2h14a2 2 0 0 1 2 2v10a2 2 0 0 1 -2 2h-14a2 2 0 0 1 -2 -2v-10z"/><path d="m3 7l9 6l9 -6"/></svg>
												Poslať overovací mail
											</a>
										</te:if>
									</div>
								</div>
							</te:if>

							<te:if test="isset(@{user:onlinelist}) || array_search('online',@{user:online.session_status}) !== false">
								<div class="mb-3">
									<label class="form-label"><te:string id="Stav" />:</label>
									<div class="form-control-plaintext">
										<te:if test="array_search('online',@{user:online.session_status}) !== false">
											<span class="badge bg-success"><te:string>@{user:session_status_text}</te:string></span>
										<te:else />
											<span class="badge bg-danger"><te:string>@{user:session_status_text}</te:string></span>
										</te:if>
									</div>
								</div>
							</te:if>

							<div class="mb-3">
								<label class="form-label"><te:string id="Rola" />:</label>
								<div class="form-control-plaintext">
									<te:if test="isset(@{user:sk_user_role_enum_name_item})">
										<te:string>@{user:sk_user_role_enum_name_item}</te:string>
									<te:else />
										<te:string>@{user:user_role}</te:string>
									</te:if>
								</div>
							</div>
						</div>

						<div class="col-md-6">
							<te:if test="isset(@{user:user_groups_label})">
								<div class="mb-3">
									<label class="form-label"><te:string id="Skupiny" />:</label>
									<div class="form-control-plaintext">
										<te:if test="empty(@{user:user_groups_label})">
											<span class="text-muted"><te:string id="žiadne" /></span>
										<te:else />
											@{user:user_groups_label}
										</te:if>
									</div>
								</div>
							</te:if>

							<te:if test="isset(@{user:isic})">
								<div class="mb-3">
									<label class="form-label"><te:string id="ISIC" />:</label>
									<div class="form-control-plaintext">@{user:isic}</div>
								</div>
							</te:if>

							<te:if test="isset(@{user:isic_ok})">
								<div class="mb-3">
									<label class="form-label"><te:string id="ISIC overený" />:</label>
									<div class="form-control-plaintext">
										<te:if test="isset(@{user:sk_isic_ok_enum_name_item})">
											<te:string>@{user:sk_isic_ok_enum_name_item}</te:string>
										<te:else />
											<te:string>@{user:isic_ok}</te:string>
										</te:if>
									</div>
								</div>
							</te:if>
						</div>
					</div>
				</div>

				<div id="more_@{user:item_id}_@{k_user}" class="card-body border-top" style="display:none">
					<div class="row">
						<div class="col-md-6">
							<te:if test="@{user:insert_time}">
								<div class="mb-3">
									<label class="form-label"><te:string id="Vytvorený" />:</label>
									<div class="form-control-plaintext">@{user:insert_time}</div>
								</div>
							</te:if>

							<te:if test="@{user:update_time}">
								<div class="mb-3">
									<label class="form-label"><te:string id="Aktualizovaný" />:</label>
									<div class="form-control-plaintext">@{user:update_time}</div>
								</div>
							</te:if>

							<te:if test="@{user:time_last_access}">
								<div class="mb-3">
									<label class="form-label"><te:string id="Posledná aktivita" />:</label>
									<div class="form-control-plaintext">@{user:time_last_access}</div>
								</div>
							</te:if>

							<te:if test="isset(@{user:last_remote_ip}) &amp;&amp; @{user:last_remote_ip}">
								<div class="mb-3">
									<label class="form-label"><te:string id="IP adresa" />:</label>
									<div class="form-control-plaintext">@{user:last_remote_ip}</div>
								</div>
							</te:if>

							<div class="mb-3">
								<label class="form-label"><te:string id="Nastavený autologout" />:</label>
								<div class="form-control-plaintext">@{user:timeout} min</div>
							</div>
						</div>

						<div class="col-md-6">
							<div class="mb-3">
								<label class="form-label"><te:string id="Pohlavie" />:</label>
								<div class="form-control-plaintext">
									<te:if test="isset(@{user:sk_gender_enum_name_item})">
										<te:string>@{user:sk_gender_enum_name_item}</te:string>
									<te:else />
										<te:string>@{user:gender}</te:string>
									</te:if>
								</div>
							</div>

							<div class="mb-3">
								<label class="form-label"><te:string id="Jazyk" />:</label>
								<div class="form-control-plaintext">@{user:default_language}</div>
							</div>

							<te:if test="isset(@{user:onlinelist})">
								<div class="mb-3">
									<label class="form-label"><te:string id="Timeout" />:</label>
									<div class="form-control-plaintext">@{user:time_next_timeout} (@{user:timeout})</div>
								</div>

								<div class="mb-3">
									<label class="form-label"><te:string id="Browser" />:</label>
									<div class="form-control-plaintext">@{user:user_agent}</div>
								</div>

								<div class="mb-3">
									<label class="form-label"><te:string id="PC" />:</label>
									<div class="form-control-plaintext"><small>@{user:pc_str_id}</small></div>
								</div>
							</te:if>
						</div>
					</div>

					<div class="mt-4" te:print="no">
						<div class="btn-list">
							<a href="/pouzivatelia/log-zmien-pouzivatela?item_id=@{user:item_id}" class="btn btn-outline-secondary">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 8l0 4l2 2"/><path d="M3.05 11a9 9 0 1 1 .5 4m-.5 5v-5h5"/></svg>
								<te:string id="Log zmien" />
							</a>
							<a te:access="s_system_superadmin" href="/pouzivatelia/prihlasit-ako?item_id=@{user:item_id}" class="btn btn-outline-warning" onclick="return(confirm('@L{Naozaj sa chcete prihlásiť ako používateľ} @{user:login}?'));">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M14 8v-2a2 2 0 0 0 -2 -2h-7a2 2 0 0 0 -2 2v12a2 2 0 0 0 2 2h7a2 2 0 0 0 2 -2v-2"/><path d="M20 12h-13l3 -3m0 6l-3 -3"/></svg>
								<te:string id="Prihlásiť" />
							</a>
							<a te:access="s_cdouk_checkisic" href="/pouzivatelia/cdo-isic-check?user_id=@{user:item_id}" class="btn btn-outline-info">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M9 12l2 2l4 -4"/><path d="M21 12c-1 0 -3 -1 -3 -3s2 -3 3 -3s3 1 3 3s-2 3 -3 3"/><path d="M21 6c-1 0 -3 -1 -3 -3s2 -3 3 -3s3 1 3 3s-2 3 -3 3"/><path d="M21 18c-1 0 -3 -1 -3 -3s2 -3 3 -3s3 1 3 3s-2 3 -3 3"/><path d="M18 9h-15"/><path d="M18 15h-15"/></svg>
								<te:string id="Overiť ISIC" />
							</a>
							<a te:access="s_system_show_user_groups" href="/pouzivatelia/prava/skupiny-pouzivatela?item_id=@{user:item_id}" class="btn btn-outline-primary">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M10 13a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M8 21v-1a2 2 0 0 1 2 -2h4a2 2 0 0 1 2 2v1"/><path d="M15 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M17 10h2a2 2 0 0 1 2 2v1"/><path d="M5 5a2 2 0 1 0 4 0a2 2 0 0 0 -4 0"/><path d="M3 13v-1a2 2 0 0 1 2 -2h2"/></svg>
								<te:string id="Skupiny" />
							</a>
							<a te:access="s_system_show_user_rights" href="/pouzivatelia/prava/pouzivatel?pouzivatel=@{user:item_id}" class="btn btn-outline-primary">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3"/><path d="M9 12l2 2l4 -4"/></svg>
								<te:string id="Práva" />
							</a>
							<a href="/testy/zoznam-pouzivatelovych-testovani&amp;user_id=@{user:item_id}" class="btn btn-outline-secondary">
								<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M14 3v4a1 1 0 0 0 1 1h4"/><path d="M17 21h-10a2 2 0 0 1 -2 -2v-14a2 2 0 0 1 2 -2h7l5 5v11a2 2 0 0 1 -2 2z"/><path d="M9 9l1 0"/><path d="M9 13l6 0"/><path d="M9 17l6 0"/></svg>
								@L{Zoznam testovaní používateľa}
							</a>
						</div>

						<te:if test="@{user:status} == 'request'">
							<div class="mt-3 pt-3 border-top">
								<div class="btn-list">
									<te:if test="(@{user:email_check_status} == 'checked' || @{user:email_check_status} == 'protected') &amp;&amp; (!isset(@{user:isic}) || @{user:isic_ok} == 'yes')">
										<a te:access="s_users_accept_request" href="/pouzivatelia/akceptovat-registraciu?user_id=@{user:item_id}" class="btn btn-success">
											<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M5 12l5 5l10 -10"/></svg>
											<te:string id="Akceptovať registráciu" />
										</a>
									</te:if>
									<a te:access="s_users_accept_request" href="/pouzivatelia/zamietnut-registraciu?user_id=@{user:item_id}" class="btn btn-danger">
										<svg xmlns="http://www.w3.org/2000/svg" class="icon me-2" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="M18 6l-12 12"/><path d="m6 6l12 12"/></svg>
										<te:string id="Zamietnúť registráciu" />
									</a>
								</div>
							</div>
						</te:if>
					</div>
				</div>
			</div>
		</div>
		<?php @{n}++; ?>
	</te:for>
</div>

</truEngine-document>
