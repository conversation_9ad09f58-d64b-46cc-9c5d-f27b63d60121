<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(!isset(@{select}) || !is_array(@{select})) {
	@{select} = array();
}
if(!isset(@{select:status})) {
	@{select:status} = false;
}
?>

<script type="text/javascript">
	function showUserlistDetails(id, show)
	{
		$j('#more_' + id).slideToggle();
		$j('#ifoto_' + id).slideToggle();
		$j('#a_show_' + id).toggle();
		$j('#a_hide_' + id).toggle();
	}
</script>

<?php @{n} = 0; @{isic_ok_class} = isset(@{user:isic_ok}) ? @{user:isic_ok} : 'yes'; ?>
<te:for each="@{users} as @{k_user}=>@{user}">
	<hr te:if="@{n}" te:print="yes" />
	<div class="userlist userlist-@{user:user_role} userlist-isic-@{isic_ok_class}" te:if="substr(@{k_user}, 0 , 1) != '_'">
		<table cellpadding="0" cellspacing="0" style="width:100%">
			<tr>
				<td valign="top" class="list_foto">
					<te:if test="is_array(@{user:foto}) &amp;&amp; isset(@{user:foto:0})">
						<a te:print="no" href="@{user:foto:0:src}" rel="milkbox" target="_blank"><img src="@{user:foto:0:th1_:src}" style="width:80px;" /></a>
						<img te:print="yes" src="@{user:foto:0:th1_:src}" style="width:80px;" />
					<te:else />
						<te:if test="is_array(@{user:foto_rec}) &amp;&amp; isset(@{user:foto_rec:0})">
							<a te:print="no" href="@{user:foto_rec:0:src}" rel="milkbox" target="_blank"><img src="@{user:foto_rec:0:th1_:src}" style="width:80px;"/></a>
							<img te:print="yes" src="@{user:foto_rec:0:th1_:src}" style="width:80px;"/>
						<te:else />
							<img src="/images/icons/nofoto.jpg" style="width:80px; height:80px;" />
						</te:if>
					</te:if>
					<br />
					<te:if test="is_array(@{user:foto_rec}) &amp;&amp; isset(@{user:foto_rec:0})">
						<a te:print="no" id="ifoto_@{user:item_id}_@{k_user}" style="display:none;" href="@{user:foto_rec:0:src}" rel="milkbox" target="_blank"><img src="@{user:foto_rec:0:th1_:src}" style="width:80px;"/></a>
						<img te:print="yes" src="@{user:foto_rec:0:th1_:src}" />
					<te:else />
						<img id="ifoto_@{user:item_id}_@{k_user}" src="/images/icons/nofoto.jpg" style="width:80px; height:80px; display:none;"/>
					</te:if>
				</td>
				<td valign="top">
					<div class="nav" te:print="no">
						<a href="#" onclick="showUserlistDetails(@{user:item_id}+'_'+@{k_user}, 1); return(false);" id="a_show_@{user:item_id}_@{k_user}" title="@L{Viac detailov}">+</a>
						<a href="#" onclick="showUserlistDetails(@{user:item_id}+'_'+@{k_user}, 0); return(false);" id="a_hide_@{user:item_id}_@{k_user}" style="display:none" title="@L{Menej detailov}">--</a>
						<div te:if="@{select:status}" te:print="no">
							<br /><input type="checkbox" id="msel_users_@{user:item_id}" onclick="msel_check('users', @{user:item_id}, this.checked)" />
						</div>
					</div>

					<table cellpadding="0" cellspacing="0">
						<tr>
							<td class="label"><te:string id="Prezývka/login" />:</td>
							<td>
								<?php
									@{right}='s_users_edit_user';
						if(@{user:user_role} == 'superuser') {
							@{right}='s_users_edit_superadmin';
						}
						if(@{user:user_role} == 'admin') {
							@{right}='s_users_edit_admin';
						}
						if(@{user:owner_id} == @{session:user_id}) {
							if(@{user:user_role} != 'superuser') {
								@{right} .= '_owned';
							}
						}
						if(@{user:status} == 'request') {
							@{right} = 's_users_accept_request';
						}
					?>
						<te:access access="@{right}">
							<te:if test="@{user:status} == 'request'">
								<a href="/pouzivatelia/editovat-registraciu?item_id=@{user:item_id}" te:print="no">@{user:nick}</a>
								<span te:print="yes">@{user:nick}</span>
							<te:else />
								<a href="/pouzivatelia/editovat-pouzivatela?item_id=@{user:item_id}" te:print="no">@{user:nick}</a>
								<span te:print="yes">@{user:nick}</span>
							</te:if>
						</te:access>
								<te:xaccess xaccess="@{right}">
									@{user:nick}
								</te:xaccess>
								<te:if test="strtolower(@{user:nick})!==strtolower(@{user:login})"> (@{user:login})</te:if>
							</td>
						</tr>
						<tr>
							<td class="label"><te:string id="Meno priezvisko" />:</td>
							<td>@{user:titul_pred} @{user:first_name} @{user:last_name} @{user:titul_za}</td>
						</tr>
						<tr te:if="isset(@{user:email})">
							<td class="label"><te:string id="Email" />:</td>
							<td><te:if test="!empty(@{user:email})">@{user:email}
								<span id="mstatus-@{user:item_id}">
									<te:if test="isset(@{user:email_check_status}) &amp;&amp; @{user:email_check_status}!=='checked'">
										(<span style="color:red">
											<te:choose test="@{user:email_check_status}">
											<te:when case="notset"><te:string id="neoverený" /></te:when>
											<te:when case="changed"><te:string id="zmenený" /></te:when>
											<te:when case="protected"><te:string id="schválený" /></te:when>
											<te:otherwise>@{user:email_check_status}</te:otherwise>
											</te:choose>
										</span>)
									</te:if>
								</span>
								<a te:access="s_users_accept_request" href="/pouzivatelia/poslat-overovaci-mail?user_id=@{user:item_id}" onclick="$j.ajax({url: $j(this).attr('href'), context: document.body, success: function(){ $j('#mstatus-@{user:item_id}').html('( &lt;span style=color:red&gt;neoverený&lt;/span&gt; )'); } }); return(false);">Poslať overovací mail</a>
								</te:if>
							</td>
						</tr>
						<te:if test="isset(@{user:onlinelist}) || array_search('online',@{user:online.session_status}) !== false">
							<te:if test="array_search('online',@{user:online.session_status}) !== false">
								<tr>
									<td class="label"><te:string id="Stav" />:</td>
									<td style="color:#00CC00"><te:string>@{user:session_status_text}</te:string></td>
								</tr>
							<te:else />
								<tr>
									<td class="label"><te:string id="Stav" />:</td>
									<td style="color:#FF0000"><te:string>@{user:session_status_text}</te:string></td>
								</tr>
							</te:if>
						</te:if>
						<tr>
							<td class="label"><te:string id="Rola" />:</td>
							<td>
								<te:if test="isset(@{user:sk_user_role_enum_name_item})">
									<te:string>@{user:sk_user_role_enum_name_item}</te:string>
								<te:else />
									<te:string>@{user:user_role}</te:string>
								</te:if>
								</td>
						</tr>
						<tr te:if="isset(@{user:user_groups_label})">
							<td class="label"><te:string id="Skupiny" />:</td>
							<td>
								<te:if test="empty(@{user:user_groups_label})">
										<te:string id="žiadne" />
									<te:else />
										@{user:user_groups_label}
								</te:if>
							</td>
						</tr>
						<tr te:if="isset(@{user:isic})">
							<td class="label"><te:string id="ISIC" />:</td>
							<td>@{user:isic}</td>
						</tr>
						<tr te:if="isset(@{user:isic_ok})">
							<td class="label"><te:string id="ISIC overený" />:</td>
							<td>
								<te:if test="isset(@{user:sk_isic_ok_enum_name_item})">
									<te:string>@{user:sk_isic_ok_enum_name_item}</te:string>
								<te:else />
									<te:string>@{user:isic_ok}</te:string>
								</te:if>
								</td>
						</tr>
					</table>

					<div id="more_@{user:item_id}_@{k_user}" style="display:none">
						<table cellpadding="0" cellspacing="0">
							<tr te:if="@{user:insert_time}">
								<td class="label"><te:string id="Vytvorený" />:</td>
								<td>@{user:insert_time}</td>
							</tr>
							<tr te:if="@{user:update_time}">
								<td class="label"><te:string id="Aktualizovaný" />:</td>
								<td>@{user:update_time}</td>
							</tr>
							<tr te:if="@{user:time_last_access}">
								<td class="label"><te:string id="Posledná aktivita" />:</td>
								<td>@{user:time_last_access}</td>
							</tr>
							<tr te:if="isset(@{user:last_remote_ip}) &amp;&amp; @{user:last_remote_ip}">
								<td class="label"><te:string id="IP adresa" />:</td>
								<td>@{user:last_remote_ip}</td>
							</tr>
							<tr>
								<td class="label"><te:string id="Nastavený autologout" />:</td>
								<td>@{user:timeout} min</td>
							</tr>
							<tr>
								<td class="label"><te:string id="Pohlavie" />:</td>
								<td>
									<te:if test="isset(@{user:sk_gender_enum_name_item})">
										<te:string>@{user:sk_gender_enum_name_item}</te:string>
										<te:else />
										<te:string>@{user:gender}</te:string>
									</te:if>
									</td>
							</tr>
							<tr>
								<td class="label"><te:string id="Jazyk" />:</td>
								<td>@{user:default_language}</td>
							</tr>
							<te:if test="isset(@{user:onlinelist})">
								<tr>
									<td class="label"><te:string id="Timeout" />:</td>
									<td>@{user:time_next_timeout} (@{user:timeout})</td>
								</tr>
								<tr>
									<td class="label"><te:string id="Browser" />:</td>
									<td>@{user:user_agent}</td>
								</tr>
								<tr>
									<td class="label"><te:string id="PC" />:</td>
									<td><small>@{user:pc_str_id}</small></td>
								</tr>
							</te:if>
							<tr te:print="no">
								<td></td>
								<td>
									<a href="/pouzivatelia/log-zmien-pouzivatela?item_id=@{user:item_id}"><te:string id="Log zmien" /></a>
									<a te:access="s_system_superadmin" href="/pouzivatelia/prihlasit-ako?item_id=@{user:item_id}" onclick="return(confirm('@L{Naozaj sa chcete prihlásiť ako používateľ} @{user:login}?'));"><te:string id="Prihlásiť" /></a>
									<a te:access="s_cdouk_checkisic" href="/pouzivatelia/cdo-isic-check?user_id=@{user:item_id}"><te:string id="Overiť ISIC" /></a>
									<!-- <a te:access="s_users_generate_check_email" href="/pouzivatelia/overenie-emailu?user_id=@{user:item_id}">Overiť email</a> -->
									<te:if test="@{user:status} == 'request'">
										<br />
										<te:if test="(@{user:email_check_status} == 'checked' || @{user:email_check_status} == 'protected') &amp;&amp; (!isset(@{user:isic}) || @{user:isic_ok} == 'yes')">
											<a te:access="s_users_accept_request" href="/pouzivatelia/akceptovat-registraciu?user_id=@{user:item_id}"><strong style="color:green"><te:string id="Akceptovať registráciu" /></strong></a>
										</te:if>
										<a te:access="s_users_accept_request" href="/pouzivatelia/zamietnut-registraciu?user_id=@{user:item_id}"><strong style="color:red"><te:string id="Zamietnúť registráciu" /></strong></a>
									</te:if>
									<a te:access="s_system_show_user_groups" href="/pouzivatelia/prava/skupiny-pouzivatela?item_id=@{user:item_id}"><te:string id="Skupiny" /></a>
									<a te:access="s_system_show_user_rights" href="/pouzivatelia/prava/pouzivatel?pouzivatel=@{user:item_id}"><te:string id="Práva" /></a>
									<a href="/testy/zoznam-pouzivatelovych-testovani&amp;user_id=@{user:item_id}">@L{Zoznam testovaní používateľa}</a>
								</td>
							</tr>
						</table>
					</div>

				</td>
			</tr>
		</table>
	</div>
	<?php @{n}++; ?>
</te:for>

</truEngine-document>
