<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
	<te:if test="substr('@{src}', -4) == '.swf'">
            <object type="application/x-shockwave-flash" data="@{config:runtime:web_dir}@{src}" width="600" height="330">
                <param name="movie" value="@{src}" />
                <param name="wmode" value="transparent" />
            </object>
		<te:else />
            <te:if test="substr('@{src}', -4) == '.flvXXXX'">
                    <object type="application/x-shockwave-flash" data="http://flv-player.net/medias/player_flv_maxi.swf" width="600" height="330">
                        <param name="movie" value="http://flv-player.net/medias/player_flv_maxi.swf" />
                        <param name="allowFullScreen" value="true" />
                        <?php @{flv} = string_gClass::path_gFunc(@{config:runtime:server_protocol} . '%3A//' . @{config:runtime:server_name} . @{config:runtime:web_dir} . @{src}); ?>
                        <param name="FlashVars" value="flv=@{flv}&amp;width=520&amp;height=330&amp;loop=1&amp;margin=1&amp;showstop=1&amp;showvolume=1&amp;showtime=1&amp;showplayer=always&amp;showloading=always&amp;showfullscreen=1&amp;buffer=10" />
                        <param name="wmode" value="transparent" />
                    </object>
            <te:else />
                <te:if test="substr('@{src}', -4) == '.flv' || substr('@{src}', -4) == '.mp4'">
                    <video width="600" height="300" autoplay="" loop="" muted="">
                        <?php @{src} = str_replace('.flv', '.mp4', 'https://opus.sapientiae.eu@{src}') ?>
                        <source src="@{src}" type="video/mp4" />
                        Your browser does not support the video tag.
                    </video>
                <te:else />
                    <a href="@{src}" rel="milkbox" target="_blank"><img src="@{thumbnail}" /></a>
                </te:if>
            </te:if>
	</te:if>
</truEngine-document>
