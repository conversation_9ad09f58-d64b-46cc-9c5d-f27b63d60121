<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

	<script type="text/javascript">
		var add_category = $('add_category');
		var add_questions = $('add_questions');
		var categories = $('test_templates_categories');
		var category_count = $('category_count');
		if(add_questions) {
			var add_questions_values = $('add_questions_values');
			$j(add_questions).before('&lt;input type="button" value="" id="add_questions" name="add_questions" class="input_text" &gt;');
			$j(add_questions).remove();
			add_questions = $('add_questions');
			add_questions.value = 'Pridať otázky';
			add_questions.onclick = function add_questions() {
				var categories = $('test_templates_categories');
				var add_questions_values = $('add_questions_values');
				categories.value += 'questions ' + add_questions_values.value;
				add_questions_values.value = '';
				categories.value += '\n';
				categories.onkeyup();
			}
		}
		if(add_category &amp;&amp; categories) {
			$j(add_category).before('&lt;input type="button" value="" id="add_category" name="add_category" class="input_text" &gt;');
			$j(add_category).remove();
			add_category = $('add_category');
			add_category.value = 'Pridať kategóriu';
			add_category.onclick = function add_category() {
				var categories = $('test_templates_categories');
				var modul = $('modul');
				var program = $('program');
				var predmet = $('predmet');
				var kategoria = $('kategoria');
				var podkategoria = $('podkategoria');
				
				if(category_count.value &amp;&amp; modul.options[modul.selectedIndex].value != -1) {
					categories.value += category_count.value + ' ';
					
					if(modul.options[modul.selectedIndex].value != -1) {
						categories.value += modul.options[modul.selectedIndex].value;
					}
					categories.value += '/';
					
					if(program.options[program.selectedIndex].value != -1) {
						categories.value += program.options[program.selectedIndex].value;
					}
					categories.value += '/';
					
					if(predmet.options[predmet.selectedIndex].value != -1) {
						categories.value += predmet.options[predmet.selectedIndex].value;
					}
					categories.value += '/';
					
					if(kategoria.options[kategoria.selectedIndex].value != -1) {
						categories.value += kategoria.options[kategoria.selectedIndex].value;
					}
					categories.value += '/';
					
					if(podkategoria.options[podkategoria.selectedIndex].value != -1) {
						categories.value += podkategoria.options[podkategoria.selectedIndex].value;
					}
					
					categories.value += '\n';
					categories.onkeyup();
				}
			}			
		}
	</script>
	
</truEngine-document>
