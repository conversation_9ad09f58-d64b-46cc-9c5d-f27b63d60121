<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">


	<script type="text/javascript">
		$j(function() {
			$j("#bookmarks").tabs({ cookie: { expires: 365 }, event: 'mouseover' });
		});
	</script>


	<te:source name="bookmarks" varname="bookmarks" doc="@{doc}" />
	<te:if test="count(@{bookmarks:top}) || count(@{bookmarks:last})">
		<div id="bookmarks">
			<ul>
				<li><a href="#bookmarks-1"><te:string id="Najpoužívanejšie" /></a></li>
				<li><a href="#bookmarks-2"><te:string id="Posledné" /></a></li>
			</ul>
			
			<div id="bookmarks-1">
				<ul>
					<te:for each="@{bookmarks:top} as @{bookmark}">
						<li><a href="@{bookmark:document}">@{bookmark:text}</a></li>
					</te:for>
				</ul>
			</div>
			<div id="bookmarks-2">
				<ul>
					<te:for each="@{bookmarks:last} as @{bookmark}">
						<li><a href="@{bookmark:document}">@{bookmark:text}</a></li>
					</te:for>
				</ul>				
			</div>
		</div>
	</te:if>
	
</truEngine-document>