<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
if(isset(@{test:data:settings:language})) {
	@{language} = @{test:data:settings:language};
}
else {
	@{language} = main_gClass::getLanguage_gFunc();
}
?>
		<te:if test="(@{test:test_status:edit} || @{test:current_question}) &amp;&amp; isset(@{test:data:settings:cas}) &amp;&amp; @{test:data:settings:cas} &gt; 0">
			<te:include name="/sk/.html-templates/fixed-timer" timer_id="test_timer_@{test:data:id}" save_time="1" time_step="-1" time_start="@{test:data:settings:cas} * 60" onTimeout="$j('#test_timer').val(2); $j('#test_form').ajaxSubmit(); $('test_timer').value=1; alert('Časový limit pre test vypršal.'); $j('#test_form input[type=submit]').click(); return;" />
			<input type="hidden" name="timer" value="0" id="test_timer" />
		</te:if>
		
		<ol te:if="@{test:test_status:display}">
		<te:for each="@{test:questions} as @{question}">
			<?php main_gClass::applyLanguageToArray_gFunc(@{question}, @{language}); ?>
			<li te:if="(!@{test:current_question} &amp;&amp; (!@{test:test_status:edit} || @{test:data:settings:sposob_testovania} == 'vsetko')) || @{test:current_question} == @{question:item_id}" value="@{question:index}">
				<span te:print="no" class="autor">Autor: <te:include name="/sk/components/autor" autor="@{question:autorske_prava}" /></span>
				<te:include name="/sk/components/content-data" data="@{question:otazka}" />
					<a te:access="s_test_report_error" href="#" style="text-decoration:none; color:#aaaaaa; font-size:9px" te:print="no" onclick="testReportError(this, @{question:item_id}, 0); return(false);" title="Kliknite, ak chcete nahlásiť chybu alebo pridať komentár">E</a>
					<a te:access="s_test_edit_from_test" href="/otazky/editovat-otazku/&amp;item_id=@{question:item_id}" style="text-decoration:none; color:#aaaaaa; font-size:9px" te:print="no" target="_blank" title="Kliknite, ak chcete vykonať úpravy">U</a>
				<te:if test="is_array(@{question:otazka_media})">
					<br />
					<div te:print="no">
						<?php @{src} = isset(@{question:otazka_media:0}) ? @{question:otazka_media:0:src} : '' ; ?>
						<?php @{thumbnail} = isset(@{question:otazka_media:th1_}) ? @{question:otazka_media:th1_:src} : '' ; ?>
						<te:include name="/sk/components/movie" src="@{src}" thumbnail="@{thumbnail}" />
					</div>					
					<img te:print="yes" src="@{question:otazka_media:th1_:src}" />
				</te:if>
				<?php 
					@{answers_ids} = explode(',', @{question:db_answer_ids});
					@{err} = 0;
				?>
				<ol type="lower-alpha">
					<te:for each="@{answers_ids} as @{answer_id}">
						<li>
							<?php @{answer} = @{test:answers}[@{answer_id}];
								  main_gClass::applyLanguageToArray_gFunc(@{answer}, @{language});
							?>
							
							<te:if test="@{test:test_status:edit}">
								<input te:print="no" type="hidden" name="question_@{test:data:id}_@{question:item_id}_@{answer_id}" value="1" />
								<input te:print="no" te:if="isset(@{answer:checked}) &amp;&amp; @{answer:checked}" type="checkbox" name="question_@{test:data:id}_flag_@{question:item_id}_@{answer_id}" id="question_@{test:data:id}_flag_@{question:item_id}_@{answer_id}" checked="checked" />
								<input te:print="no" te:if="!isset(@{answer:checked}) || !@{answer:checked}" type="checkbox" name="question_@{test:data:id}_flag_@{question:item_id}_@{answer_id}" id="question_@{test:data:id}_flag_@{question:item_id}_@{answer_id}" />
								<label for="question_@{test:data:id}_flag_@{question:item_id}_@{answer_id}"><te:include name="/sk/components/content-data" data="@{answer:odpoved}" /></label>
							</te:if>
							
							<te:if test="!@{test:test_status:edit} &amp;&amp; (!isset(@{test:test_status:print}) || !@{test:test_status:print})">
								<input te:print="no" te:if="isset(@{answer:checked}) &amp;&amp; @{answer:checked}" type="checkbox" checked="checked" disabled="disabled" />
								<input te:print="no" te:if="!isset(@{answer:checked}) || !@{answer:checked}" type="checkbox" disabled="disabled" />
								<te:if test="isset(@{answer:OK})">
									<te:if test="@{language} == 'sk'">
										<te:if test="@{answer:OK} === true">
												<te:include name="/sk/components/content-data" data="@{answer:odpoved}" />
												<te:if test="@{answer:spravnost} == 'spravne'"> <te:string id="(označená/správne)" /></te:if>
												<te:if test="@{answer:spravnost} == 'nespravne'"> <te:string id="(neoznačená/správne)" /></te:if>
											<te:else />
												<span style="color:red;">
													<te:include name="/sk/components/content-data" data="@{answer:odpoved}" />
													<te:if test="@{answer:spravnost} == 'spravne'"> <te:string id="(neoznačená/nesprávne)" /></te:if>
													<te:if test="@{answer:spravnost} == 'nespravne'"> <te:string id="(označená/nesprávne)" /></te:if>
													<te:if test="!empty(@{answer:odpoved_vysvetlenie})">
														<div class="odpoved_vysvetlenie">@L{Vysvetlenie}: @{answer:odpoved_vysvetlenie}</div>
													</te:if>
													<?php @{err}++; ?>
												</span>
										</te:if>
										<te:else />
										<te:if test="@{answer:OK} === true">
												<te:include name="/sk/components/content-data" data="@{answer:odpoved}" />
												<te:if test="@{answer:spravnost} == 'spravne'"> <te:string id="(marked/correct)" /></te:if>
												<te:if test="@{answer:spravnost} == 'nespravne'"> <te:string id="(unmarked/correct)" /></te:if>
											<te:else />
												<span style="color:red;">
													<te:include name="/sk/components/content-data" data="@{answer:odpoved}" />
													<te:if test="@{answer:spravnost} == 'spravne'"> <te:string id="(unmarked/incorrect)" /></te:if>
													<te:if test="@{answer:spravnost} == 'nespravne'"> <te:string id="(marked/incorrect)" /></te:if>
													<te:if test="!empty(@{answer:odpoved_vysvetlenie})">
														<div class="odpoved_vysvetlenie">@L{Vysvetlenie}: @{answer:odpoved_vysvetlenie}</div>
													</te:if>
													<?php @{err}++; ?>
												</span>
										</te:if>										
									</te:if>
									<te:else />
										<span style="color:#aaaaaa;"><te:include name="/sk/components/content-data" data="@{answer:odpoved}" /></span>
										<te:if test="!empty(@{answer:odpoved_vysvetlenie})">
											<div class="odpoved_vysvetlenie">@L{Vysvetlenie}: @{answer:odpoved_vysvetlenie}</div>
										</te:if>
										<?php @{err}++; ?>
								</te:if>
							</te:if>
							
							<te:if test="!@{test:test_status:edit} &amp;&amp; isset(@{test:test_status:print}) &amp;&amp; @{test:test_status:print}">
								<te:include name="/sk/components/content-data" data="@{answer:odpoved}" />
							</te:if>
							
								<a te:access="s_test_report_error" href="#" style="text-decoration:none; color:#aaaaaa; font-size:9px" te:print="no" onclick="testReportError(this, @{question:item_id}, @{answer_id}); return(false);" title="Kliknite, ak chcete nahlásiť chybu, alebo pridať komentár">E</a>
								<a te:access="s_test_edit_from_test" href="/otazky/odpovede-multiedit/&amp;otazka=@{question:item_id}" style="text-decoration:none; color:#aaaaaa; font-size:9px" te:print="no" target="_blank" title="Kliknite, ak chcete vykonať úpravy">U</a>
							
							<te:if test="is_array(@{answer:odpoved_media})">
								<br />
								<div te:print="no">
									<?php @{src} = isset(@{answer:odpoved_media:0}) ? @{answer:odpoved_media:0:src} : '' ; ?>
									<?php @{thumbnail} = isset(@{answer:odpoved_media:th1_}) ? @{answer:odpoved_media:th1_:src} : '' ; ?>
									<te:include name="/sk/components/movie" src="@{src}" thumbnail="@{thumbnail}" />
								</div>					
								<img te:print="yes" src="@{answer:odpoved_media:th1_:src}" style="border:0px;" />
								
							</te:if>
						</li>
					</te:for>
					<br />
				</ol>
				<te:if test="!empty(@{question:vysvetlenie})">
					<div class="vysvetlenie">@L{Vysvetlenie otázky}: @{question:vysvetlenie}</div>
				</te:if>				
			</li>
		</te:for>
		</ol>

</truEngine-document>
