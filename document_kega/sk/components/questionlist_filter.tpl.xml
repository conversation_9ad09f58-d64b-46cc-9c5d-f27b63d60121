<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<form action="/@{doc}" method="POST">
	<te:for each="@{filter:fields} as @{field_key}=>@{field}">
		<select name="@{field}">
				<option value="*"></option>
				<te:if test="isset(@{filter:params}[@{field}]) &amp;&amp; empty(@{filter:params}[@{field}])">
					<option selected="selected" value="">-</option>
				<te:else />
					<option value="">-</option>
				</te:if>
			<?php @{values} = @{filter:tree:_values}[@{field_key}]; ?>
			<te:for each="@{values} as @{v}">
				<te:if test="isset(@{filter:params}[@{field}]) &amp;&amp; @{filter:params}[@{field}] == @{v:enum_field_value}">
						<option selected="selected" value="@{v:enum_field_value}">@{v:sk_enum_field_name_item}</option>
					<te:else />
						<option value="@{v:enum_field_value}">@{v:sk_enum_field_name_item}</option>
				</te:if>
			</te:for>
		</select>
	</te:for>
	<input type="submit" value="&amp;gt;" />
</form>


</truEngine-document>
