<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php

if(!isset(@{select}) || !is_array(@{select})) {
	@{select} = array();
}

if(!isset(@{select:status})) {
	@{select:status} = false;
}

if(!isset(@{select:settings})) {
	@{select:settings} = array();
}

@{select:html} = '
		<input type="checkbox" title="@L{Označiť všetky položky na stránke}" onclick="msel_selectAllItems(this.checked);" class="msel_check_all" />
		 <div style="display:none" class="msel">@L{Označené položky}:';
$tmp = '<select onchange="if(this.selectedIndex) { document.location=this.options[this.selectedIndex].value; }">
				<option value=""></option>
';

$n = 0;
foreach(@{select:settings} as $v_pVar) {
	if(isset($v_pVar['location'])) {
		$tmp .= '<option value="' . main_gClass::makeUrl_gFunc($v_pVar['location']) . '">' . $v_pVar['label'] . '</option>';
		$n++;
	}
}


$json_pVar = new json_gClass();
$tmp .= '
		 	</select>';
if($n) {
	@{select:html} .= $tmp;
}

@{select:html} .= '
			<span style="display:none" id="msel_unselect">@L{zrušiť}</span>';
if(isset(@{select:display})) {
	@{select:html} .= '<span style="display:none" id="msel_display">' . $json_pVar->json_encode_gFunc(@{select:display}) . '</span>';
}
@{select:html} .= '<div class="msel_data"></div></div>
';

foreach(@{select:settings} as $k_pVar=>$v_pVar) {
	if(!isset($v_pVar['access']) || session_gClass::userHasRightsInfo_gFunc($v_pVar['access'])) {
		@{select:status} = true;
	}
}


?>


</truEngine-document>
