<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:if test="@{form:error_code} == 'error'">

	<te:for each="@{form:fieldsets} as @{fieldset}">
			<te:for each="@{fieldset:fields} as @{field}">
				<te:if test="@{field:error_code}!='ok'">
					<?php @{form:workspace:n_error_fields}++; ?>
				</te:if>
			</te:for>
	</te:for>
	<te:if test="@{form:workspace:n_error_fields}">
		<div class="msg_error"><a name="@{form:form_id}_err"></a>
			<te:string id="Formulár nie je správne vyplnený. Nesprávne sú vyplnené nasledujúce polia:" /><br />
			<te:set ref="@{n}" value="0" />
			<te:for each="@{form:fieldsets} as @{fieldset}">
				<te:for each="@{fieldset:fields} as @{field}">
					<te:if test="@{field:error_code}!='ok'">
						<te:if test="@{n}">,</te:if>
						<a href="#" title="@{field:error_message}" onclick="document.getElementById('@{field:name}').focus(); return(false);">@{field:label}</a>
						<?php @{n}++; ?>
					</te:if>
				</te:for>
			</te:for>
			<script type="text/javascript">
				document.location="#@{form:form_id}_err";
			</script>
		</div>
	</te:if>
</te:if>

<te:if test="@{form:visibility}=='visible'">
	&lt;form id="@{form:form_id}" 
	<te:for each="@{form:attributes} as @{kattr}=>@{attr}">
		@{kattr}="@{attr}"
	</te:for>
	enctype="multipart/form-data"&gt;
</te:if>
	@{form:hidden_fields_code}
</truEngine-document>
