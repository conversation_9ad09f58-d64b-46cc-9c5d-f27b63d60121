<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<te:ajax-request>
	<?php @{filter} =  htmlspecialchars_decode(@{request:filter}); ?>
	<te:source name="list_items" varname="items" filter="@{filter}" _type="@{request:type}" _order_by="@{request:order_by}" />
	<data>
		<te:for each="@{items} as @{item}">
			<te:if test="isset(@{item:item_id})">
				<item>
					<?php @{label} = @{item}[@{request:label}]; ?>
					<id>@{item:item_id}</id><label>@{label}</label>
				</item>
			</te:if>
		</te:for>
	</data>
<result>1</result>
<te:else />

<?php
	if(!isset(@{item_selector_id})) {
		@{item_selector_id} = 'item_selector';
	}
?>

	<div>
	
	<te:include name="/sk/components/filter" filter="@@{filter}" item_type="@{type}" doc="/@{doc}" filter_settings="@{filter_settings}" html_id="_item_selector@{item_selector_id}"  onsubmit="item_selector_refresh@{item_selector_id}();" />
	<te:source name="list_items" varname="items" filter="@{filter:settings}" _type="@{type}" _order_by="@{order_by}" />

	<table>
		<tr>
			<td>
				<select size="4" style="width:300px" multiple="multiple" id="select_@{item_selector_id}">
					<te:for each="@{items} as @{item}">
						<te:if test="isset(@{item:item_id})">
							<option value="@{item:item_id}">
							<?php echo @{item}[@{label}]; ?>
							</option>
						</te:if>
					</te:for>
				</select>
			</td>
			<td>
				<input type="button" value="&gt;&gt;" onclick="itemSelectorMoveRight('@{item_selector_id}');" /><br />
				<input type="button" value="&lt;&lt;" onclick="itemSelectorMoveLeft('@{item_selector_id}');" />
			</td>
			<td>
				<select name="@{item_selector_id}[]" size="4" style="width:300px" multiple="multiple" id="result_@{item_selector_id}">
				</select>
			</td>
		</tr>
	</table>
</div>

<script type="text/javascript">
function item_selector_refresh@{item_selector_id}()
{
	filter_hide_settings('_item_selector@{item_selector_id}');
	itemSelectorRefresh('@{item_selector_id}', '@{type}', '@{order_by}', '@{label}');
}
</script>

</te:ajax-request>
	

</truEngine-document>
