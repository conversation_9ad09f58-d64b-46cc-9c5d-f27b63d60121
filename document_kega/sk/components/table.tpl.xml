<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
    if(isset(@{_sortable})) {
        @{_sortable} = explode(',', @{_sortable});
    }
    else {
        @{_sortable} = array();
    }

    if(isset(@{_order_by})) {
        @{_order_by} = explode('/', @{_order_by});
    }

    @{tableid} = uniqid('table_');
?>

<te:set ref="@{last_group}" value="x" />
<te:set ref="@{current_group}" value="" />

<div class="table-responsive">
<table class="table table-vcenter card-table" id="@{tableid}">
        <?php @{x}=0; ?>
        <te:for each="@{table:rows} as @{row}">
        <te:if test="isset(@{group}) &amp;&amp; isset(@{row}[@{group}])">
            <?php @{current_group} = @{row}[@{group}]['value']; ?>
        </te:if>
        <te:if test="@{last_group} != @{current_group}">
            <te:if test="@{x}">&lt;/tbody&gt;</te:if>
            <thead>
                <tr te:if="isset(@{group_title})">
                    <?php @{col_count} = count(@{table:columns});; ?>
                    <th colspan="@{col_count}"><h3 class="m-0"><?php echo @{row}[@{group_title}]['value']; ?></h3></th>
                </tr>
                <tr>
                    <te:for each="@{table:columns} as @{column_id}=>@{column_data}">
                        <te:choose test="@{column_id}">
                            <te:when case="currency"></te:when>
                            <te:when case="mj"></te:when>
                            <te:when case="product_id"></te:when>
                            <te:otherwise>
                                <te:if test="in_array(@{column_id}, @{_sortable})">
                                        <te:if test="isset(@{_order_by}) &amp;&amp; (@{_order_by:0} == @{column_id} &amp;&amp; @{_order_by:1} == 'ASC')">
                                            <th class="text-muted">
                                                <a href="/@{doc}?zoradit=@{column_id}/DESC" class="text-reset">
                                                    @{column_data:label}
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m6 15l6 -6l6 6"/></svg>
                                                </a>
                                            </th>
                                        <te:else />
                                            <th class="text-muted">
                                                <a href="/@{doc}?zoradit=@{column_id}/ASC" class="text-reset">
                                                    @{column_data:label}
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m6 9l6 6l6 -6"/></svg>
                                                </a>
                                            </th>
                                        </te:if>
                                <te:else />
                                        <th class="text-muted">@{column_data:label}</th>
                                </te:if>
                            </te:otherwise>
                        </te:choose>
                    </te:for>
                </tr>
            </thead>
            &lt;tbody&gt;
            <te:set ref="@{last_group}" value="@{current_group}" />
        </te:if>

        <?php
            @{x}++;
            @{attrs} = array();
            if(isset(@{key})) {
                @{attrs:id} = @{row}[@{key}]['value'];
            }
        ?>
        <tr te:attrs="@{attrs}">
            <te:for each="@{table:columns} as @{column_id}=>@{column_data}">
                <td>
                    <te:if test="isset(vars::$vars['@{column_id}'])">
                        <?php
                            $col_data = vars::$vars['@{column_id}'];

                            foreach(@{row} as $column_id=>$column) {
                                $col_data = str_replace('%%'.$column_id.'%%', @{row}[$column_id]['data'], $col_data);
                            }

                            if(isset(@{table:format}) && isset(@{table:format}[@{column_id}])) {
                                $col_data = call_user_func(@{table:format}[@{column_id}], $col_data, @{row});
                            }
                            echo $col_data;
                        ?>
                    <te:else />
                        <?php
                            $col_data = @{row}[@{column_id}]['data'];
                            if(isset(@{table:format}) && isset(@{table:format}[@{column_id}])) {
                                error_gClass::disableReporting_gFunc();
                                $col_data = call_user_func(@{table:format}[@{column_id}], $col_data, @{row});
                                error_gClass::enableReporting_gFunc();
                            }
                            echo $col_data;
                         ?>
                    </te:if>
                </td>
            </te:for>
        </tr>
        </te:for>
        <te:if test="@{x}">&lt;/tbody&gt;</te:if>
</table>
</div>

<te:if test="isset(@{uisort})">
    <te:if test="empty(@{_order_by})">
        <script type="text/javascript">
            $j(function() {
                $j("#@{tableid} tbody").sortable({
                        helper: function(e, ui) {
                            ui.children().each(function() {
                                $j(this).width($j(this).width());
                            });
                            return ui;
                        },
                        cursor:'move',
                        placeholder:'placeholder',
                        start: function(event, ui) {
                            ui.placeholder.css('height', ui.item.height() + 'px');
                        },

                        update: function(e, ui) {
                            $el = $j(ui.item);
                            $el.effect("highlight",{},1000);
                            href = '/';
                            $j(this).sortable("refresh");
                            params = new Object();
                            params.doc = '@{lng}/ajax/sortable';
                            params.portlet = '@{uisort}';
                            params.sorted = '';
                            params.ajax = 'true';

                            var rows = $j("#@{tableid} tbody tr");
                            rows.each(function(index) {
                                params.sorted += $j(rows[index]).attr('id') + ',' ;
                            });

                            $j.ajax({
                                type: 'GET',
                                url: href,
                                data: params
                            });
                        }

                    }).disableSelection();
            });
        </script>
        <div class="alert alert-info">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m12 9v4"/><path d="m10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z"/><path d="m12 16h.01"/></svg>
            @L{Poradie riadkov tabuľky môžete zmeniť ich presunom myšou.}
        </div>
    <te:else />
        <div class="alert alert-warning">
            <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="m0 0h24v24H0z" fill="none"/><path d="m12 9v4"/><path d="m10.363 3.591l-8.106 13.534a1.914 1.914 0 0 0 1.636 2.871h16.214a1.914 1.914 0 0 0 1.636 -2.87l-8.106 -13.536a1.914 1.914 0 0 0 -3.274 0z"/><path d="m12 16h.01"/></svg>
            @L{Ak chcete zmeniť štandardné usporiadanie položiek v zoznamoch, zobrazte tabuľku v štandardnom usporiadaní}. (<a href="/@{doc}" class="alert-link">@L{zobraziť}</a>) @L{Potom budete môcť položky tabuľky meniť presúvaním myšou.}
        </div>
    </te:if>
</te:if>

</truEngine-document>
