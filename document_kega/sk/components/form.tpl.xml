<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
@{form:workspace} = array();
@{form:workspace:classes} = array();
@{form:workspace:classes:main} = 'type1';
@{form:workspace:classes:price} = 'type2';
@{form:workspace:classes:discounts} = 'type2';
@{form:workspace:classes:properties} = 'type3';
@{form:workspace:classes:media} = 'type1';
@{form:workspace:classes:sk} = 'type2';
@{form:workspace:classes:en} = 'type3';
@{form:workspace:classes:accessories} = 'type3';

@{form:workspace:classes:address_invoice} = 'type2';
@{form:workspace:classes:address_delivery} = 'type3';

@{form:workspace:nSUFFIX} = '';

@{form:workspace:n_error_fields} = 0;

if(isset(@{multi}) && @{multi}) {
	@{form:workspace:multi} = true;
	@{form:workspace:last_prefix} = '';
	@{form:workspace:item_type} = @{form:vars:itemtype};
	@{form:workspace:show_links} = false;
	@{form:workspace:nSuffixValue} = 0;
}
else {
	@{form:workspace:multi} = false;
}

?>
<script type="text/javascript">
	var lng_fields = new Object();
</script>
<te:include name="/sk/components/formpart-open" form="@@{form}" />


	<te:for each="@{form:fieldsets} as @{fieldset}">
		<te:if test="count(@{fieldset:fields})">
			<te:if test="isset(@{form:workspace:last_prefix}) &amp;&amp; @{form:workspace:last_prefix}!==@{fieldset:prefix}">
				<?php
					if(!@{form:workspace:show_links}) {
						if(isset(@{form:hidden_fields}[(@{fieldset:prefix}).@{form:workspace:item_type}.'_active_formpart'])
							&& !@{form:hidden_fields}[(@{fieldset:prefix}).@{form:workspace:item_type}.'_active_formpart']['value']) {
							@{form:workspace:show_links} = true;
						}
					}
			?>
				<te:if test="!empty(@{form:workspace:last_prefix})">
					<te:if test="@{form:workspace:show_links}">
						<a href="#" onclick="showBlock('@{fieldset:prefix}', 'block', '@{fieldset:prefix}_link', '@{fieldset:prefix}@{form:workspace:item_type}_active_formpart', 1); return(false);" id="@{fieldset:prefix}_link" style="float:right;" class="@{fieldset:prefix}_link multiform-addlink">Pridat dalsiu prazdnu možnosť</a><br />
						<a href="#" onclick="showBlock('@{fieldset:prefix}', 'block', '@{fieldset:prefix}_link', '@{fieldset:prefix}@{form:workspace:item_type}_active_formpart', 1); copyBlockData('@{fieldset:prefix}'); return(false);" id="@{fieldset:prefix}_link" style="float:right;" class="@{fieldset:prefix}_link multiform-addlink">Pridat dalsiu klonovanú možnosť</a>
					</te:if>
					<!--x uzatvaram predchadzajuci div -->
					&lt;/div&gt;
				</te:if>
				<te:if test="@{form:hidden_fields}[@{fieldset:prefix}.@{form:workspace:item_type}.'_active_formpart']['value'] || empty(@{form:workspace:last_prefix})">
					&lt;div id="@{fieldset:prefix}"  class="multiFormPart" &gt;
				<te:else />
					&lt;div style="display:none;" id="@{fieldset:prefix}" &gt;
				</te:if>
				<?php
					@{form:workspace:last_prefix} = @{fieldset:prefix};
				@{form:workspace:nSuffixValue}++;
				@{form:workspace:nSUFFIX} = ' (' . @{form:workspace:nSuffixValue} . ')';
			?>
			</te:if>

			<te:include name="/sk/components/formpart-fieldset" form="@@{form}" fieldset="@{fieldset:name}" />

		</te:if>
	</te:for>

	<te:if test="isset(@{form:workspace:last_prefix}) &amp;&amp; !empty(@{form:workspace:last_prefix})">
		&lt;/div&gt;
	</te:if>


<te:include name="/sk/components/formpart-close" form="@@{form}" />

</truEngine-document>
