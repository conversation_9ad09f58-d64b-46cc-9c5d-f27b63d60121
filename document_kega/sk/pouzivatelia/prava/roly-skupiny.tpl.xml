<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_system_show_group_roles">

<te:source name="list_groups_roles" varname="data" group_id="@{request:skupina}" columns="&amp;nbsp;,role_id,role_name" pager="50,@{request:page}" delete="@{request:delete}" role_id_add="@{request:role_id_add}" />

<te:content-set name="title" value="Roly v skupine @{data:_title}" />

<p te:access="s_system_add_group_role" te:if="@{data:_this_group:group_type_id}==1">
	<a href="#" onclick="$('add_role_to_group').style.display='block'; return(false);"><te:string id="Pridať rolu do skupiny @{data:_title}" /></a>
	<div id="add_role_to_group" style="display:none;">
		<form action="/pouzivatelia/prava/roly-skupiny&amp;skupina=@{request:skupina}" method="post">
			@L{Pridať rolu}
			<select name="role_id_add">
				<option></option>
				<te:for each="@{data:_roles} as @{role}">
					<option value="@{role:name}">@{role:title}</option>
				</te:for>
			</select> <te:string id="do skupiny @{data:_title}" />
			<input type="submit" value="@L{Pridať rolu}" />
		</form>
	</div>
</p>


<te:include name="/sk/components/table" table="@@{data}">
	<te:include-param name="&amp;nbsp;">
		<a href="/pouzivatelia/prava/roly-skupiny&amp;skupina=@{request:skupina}&amp;delete=%%role_id%%" onclick="return(confirm('Naozaj chcete zmazať rolu %%role_name%% zo skupiny @{data:_title}?'));">@L{Zmazať}</a>
	</te:include-param>
</te:include>

<p>
<a href="/pouzivatelia/prava/skupiny">@L{Zoznam skupín}</a>
</p>

<te:content-set name="pdf_icon" value="@{REQUEST_URI}" />
</truEngine-document>