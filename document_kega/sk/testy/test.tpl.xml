<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
<te:content-set name="title" value="@L{Test}" />

<te:source name="test" varname="test" test_id="@{request:test_id}" />

<te:if test="@{test:test_status:display} &amp;&amp; (@{test:test_status:edit} || @{test:current_question})">
	<form action="/@{doc}" method="POST" id="test_form">
		<te:include name="/sk/components/testquestions" test="@@{test}" />
		<input type="hidden" name="test_id" value="@{test:data:id}" />
		<te:if test="@{test:current_question}">
			<p>
				@L{Celkový počet otázok v teste:} @{test:data:settings:pocet_otazok}<br />
			</p>
			<input type="submit" value="@L{Pokračovať v teste}" />
			<te:else />
			<input type="submit" value="@L{Odoslať test}" />
		</te:if>
	</form>
</te:if>

<te:if test="@{test:test_status:display} &amp;&amp; !@{test:test_status:edit} &amp;&amp; !@{test:current_question}">
	<div class="msg_info">
		<div  te:print="no">@L{Test bol ukončený.}</div>
		<te:if test="isset(@{test:status})">
			<te:string id="Počet bodov @{test:status:ok} z celkového počtu @{test:status:total}. (@{test:status:status} %)" />
			<te:if test="isset(@{test:data:settings:hranica_uspesnosti})">
				<te:if test="@{test:data:settings:hranica_uspesnosti} &lt;= @{test:status:status}">
					<span style="color:green"><strong>@L{Absolvovanie testu úspešné}.</strong></span>
					<te:else />
					<span style="color:red"><strong>@L{Absolvovanie testu neúspešné}.</strong></span>
				</te:if>
				<te:if test="isset(@{test:data:settings:excelentne})">
					<te:if test="@{test:data:settings:excelentne} &lt;= @{test:status:status}">
						<span style="color:green"><strong> @L{Výsledok splnil kritérium excelentnosti}.</strong></span>
						<te:else />
						<span style="color:red"><strong> @L{Výsledok nespĺňa kritérium excelentnosti}.</strong></span>
					</te:if>
				</te:if>
			</te:if>
			<br />

            <?php
                $job = new \App\RemoteJobs\AiTestJob(@{test:data:id});
                $jobId = $job->dispatch();
                @{test:data:jobId} = $jobId;
            ?>

            <div class="spinner-border text-primary" role="status" id="@{test:data:jobId}-waiting">
                <span class="sr-only"></span>
            </div>
            <div id="@{test:data:jobId}-message"></div>

            <script type="text/javascript">

                document.addEventListener('MyDOMContentLoaded', function() {
                    var xhr = new XMLHttpRequest();
                    xhr.open('GET', '/sk/job/@{test:data:jobId}', true);
                    xhr.onload = function() {
                        if (xhr.status &gt;= 200 &amp;&amp; xhr.status &lt; 300) {
                            var response = JSON.parse(xhr.responseText);
                            if (response.status === 'success') {
                                document.getElementById('@{test:data:jobId}-waiting').style.display = 'none';
                                document.getElementById('@{test:data:jobId}-message').innerHTML = '&lt;br /&gt;' + response.message;
                            }
                        } else {
                            document.getElementById('@{test:data:jobId}-waiting').style.display = 'none';
                        }
                    };
                    xhr.onerror = function() {
                        document.getElementById('@{test:data:jobId}-waiting').style.display = 'none';
                    };
                    xhr.send();
                });

            </script>

		</te:if>
	</div>
	<div te:print="yes">
		<table><tr>
		<td width="100px">
			<?php @{user_foto} = session_gClass::getUserDetailStatic_gFunc(@{test:data:user:item_id}, 'foto_thumb'); ?>
			<img src="/?doc=@{user_foto}" />
		</td>
		<td>
			@L{Meno:} <strong>@{test:data:user:titul_pred} @{test:data:user:first_name} @{test:data:user:first_name} @{test:data:user:titul_za}</strong><br />
			@L{ISIC:} <strong>@{test:data:user:isic}</strong><br />
			@L{Smer:} @{test:data:user:smer}<br />
			@L{Ročník:} @{test:data:user:rocnik}<br />
			@L{IP adresa:} @{test:data:ip}<br />
			@L{Dátum a čas:} <?php echo date('j.n.Y H:i', strtotime('@{test:data:time_first}')); ?><br />
		</td>
		</tr></table>
	</div>
	<te:if test="isset(@{test:questions})">
		<te:include name="/sk/components/testquestions" test="@@{test}" />
	</te:if>
	<te:content-set name="pdf_icon" value="/@{doc}?test_id=@{request:test_id}" />
</te:if>

<te:if test="isset(@{test:data}) &amp;&amp; @{test:data:official} == 'no'">
	<div te:print="no" style="display:none; position:absolute; left:0px; top:0px; background-color:#aaaaaa; padding:20px;" id="test_report_error">
		<strong>@L{Nahlásenie chybnej otázky:}</strong>
		<p>
		</p>
		<input type="hidden" name="question_id" id="test_report_question_id" value="0" />
		<input type="hidden" name="answer_id" id="test_report_answer_id" value="0" />
		@L{Komentár}:<br />
		<textarea name="comment" style="width:300px" class="input_textarea" id="test_report_comment"></textarea>
		<br />
		<select id="test_report_type">
			<option value=""></option>
			<option value="comment">@L{komentár}</option>
			<option value="nespravne">@L{nesprávne}</option>
			<option value="nezrozumitelnost">@L{nezrozumiteľné}</option>
			<option value="preklep">@L{preklep}</option>
			<option value="duplicita">@L{duplicita}</option>
		</select>
		<div>
			<input type="button" value="@L{Nahlásiť}" onclick="testReportErrorSubmit();" />
			<input type="button" value="@L{Zrušiť}" onclick="$('test_report_error').style.display='none';" />
		</div>

	</div>
</te:if>
</truEngine-document>
