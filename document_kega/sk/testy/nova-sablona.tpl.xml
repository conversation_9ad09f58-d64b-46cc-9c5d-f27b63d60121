<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk" te:access="s_test_add_template|s_test_edit_template">


	<te:source name="test_addtemplate" varname="form" item_id="@{request:item_id}">
		<te:source-param name="submit_button_title_add">@L{Pridať test}</te:source-param>
		<te:source-param name="submit_button_title_update">@L{Aktualizovať test}</te:source-param>
	</te:source>
	
	<te:include name="/sk/components/form" form="@@{form}" />
	
	<te:if test="@{form:hidden_fields:test_templates_item_id:value}">
		<te:content-set name="title" value="@L{Editovať test}" />
	<te:else />
		<te:content-set name="title" value="@L{Nový test}" />
	</te:if>
	
	<te:if test="@{form:error_code} == 'ok'">
		OK
	</te:if>
	
	<te:include name="/sk/components/questioncategories_javascript" />

	<script type="text/javascript">
	document.addEvent('domready', function() {
		if($('test_templates_categories')) {
			var div = document.createElement('div');
			var txt = document.createTextNode("@L{Okrem hlavnej náplne skúšanej problematiky je nevyhnutné zadať minimálne 30% otázok z predchádzajúcich súvisiacich problematík.}");
			div.appendChild(txt);
			div.className = 'msg_info';
			$('test_templates_categories').parentNode.insertBefore( div, $('test_templates_categories').nextSibling ); 
		}
		
		timepicker('#test_templates_time_start');
		timepicker('#test_templates_time_stop');
		
		$j('#test_templates_sposob_testovania').change(function() {
			showHideUkazatOdpovede();
		});
		
		$j('#test_templates_typ_hodnotenia').change(function() {
			showHideMaxPocetChybnychOdpovedi();
		});
		
	});
	
	function showHideUkazatOdpovede()
	{
		if($j('#test_templates_sposob_testovania').val() == 'vsetko') {
			$j($j('#test_templates_ukazat_odpovede option')[0]).hide();
		}
		else {
			$j($j('#test_templates_ukazat_odpovede option')[0]).show();
		}
	}
	
	function showHideMaxPocetChybnychOdpovedi()
	{
		if($j('#test_templates_typ_hodnotenia').val() == 'inverzny') {
			$j('#tr_test_templates_max_errors').show();
		}
		else {
			$j('#tr_test_templates_max_errors').hide();
		}	
	}
	
	showHideUkazatOdpovede();
	showHideMaxPocetChybnychOdpovedi();
	</script>


	<script type="text/javascript">
	// autocomplete script
	document.addEvent('domready', function() {
	
		var inputWord1 = $('test_templates_sk_preferred_keywords'); 
	//	var indicator = inputWord1.getPrevious().getElement('.autocompleter-loading');
	//	indicator.setStyle('display', 'none');
		new Autocompleter.Request.JSON(inputWord1, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_templates-sk_preferred_keywords', {
	//		'indicator': indicator,
			'multiple': true,
			'selectFirst': true,
			'selectMode': false,
			'minLength': 2
		});
		
		var inputWord2 = $('test_templates_en_preferred_keywords'); 
	//	var indicator = inputWord2.getPrevious().getElement('.autocompleter-loading');
	//	indicator.setStyle('display', 'none');
		new Autocompleter.Request.JSON(inputWord2, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_templates-en_preferred_keywords', {
	//		'indicator': indicator,
			'multiple': true,
			'selectFirst': true,
			'selectMode': false,
			'minLength': 2
		});
		
		var inputWord3 = $('test_templates_preferred_authors'); 
	//	var indicator = inputWord3.getPrevious().getElement('.autocompleter-loading');
	//	indicator.setStyle('display', 'none');
		new Autocompleter.Request.JSON(inputWord3, '@{config:runtime:web_dir}index.php?doc=@{config:runtime:language}/ajax/autocomplete&amp;ajax=true&amp;ajaxFormat=json&amp;type=items-test_templates-preferred_authors', {
	//		'indicator': indicator,
			'multiple': true,
			'selectFirst': true,
			'selectMode': false,
			'minLength': 2
		});
	 
	});
	</script>
	
</truEngine-document>