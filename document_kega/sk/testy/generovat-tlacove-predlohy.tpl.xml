<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">
<te:content-set name="title" value="@L{Generovanie tlačových predlôh}" />


	<te:source name="test_generate_print" varname="form" test_id="@{request:test_id}" template_id="@{request:template_id}">
		<te:source-param name="submit_button_title_add">@L{Generovať predlohy}</te:source-param>
	</te:source>

	<te:include name="/sk/components/form" form="@@{form}" />

    <te:if test="@{form:error_code} != 'ok'">
        <?php
            $template_id = @{request:template_id};
            $test_id = @{request:test_id};
            $showMessage = false;

            if($template_id) {
                $template = \Illuminate\Support\Facades\DB::table('kega_items_test_templates__data')->where('item_id', $template_id)->first();
                if($template && $template->media === 'yes') {
                    $showMessage = true;
                }
            }

            if($test_id) {
                $test = \Illuminate\Support\Facades\DB::table('kega_tests')->where('kega_tests.item_id', $test_id)
                    ->join('kega_items_test_templates__data', 'kega_tests.source_template_id', '=', 'kega_items_test_templates__data.item_id')
                ->first();
                if($test && $test->media === 'yes') {
                    $showMessage = true;
                }
            }
        ?>

        <?php if($showMessage): ?>
            <div class="msg_error">
                Test môže obsahovať animácie a videá. Na tlačové predlohy sa tieto prvky nebudú zobrazovať.
            </div>
        <?php endif; ?>
    </te:if>


	<te:if test="@{form:error_code} == 'ok'">
		<te:layout-replace name="sk_pdf" />
		<?php @{version} = 0; ?>
		<te:for each="@{form:test_data} as @{test_data}">
			<?php @{start_version} = @{version}; ?>
			<te:for each="@{test_data} as @{test}">
				<?php @{version}++; ?>
				<tcpdf method="startPageGroup" te:print="yes" /><?php
				?><tcpdf method="AddPage" te:print="yes" /><?php
				?><h1>@{test:data:sk_name}</h1>
				@L{Meno:} ............................................... @L{Podpis:} ..................................................<br />
				@L{Smer:} ............................................... @L{Ročník:} ................................................<br />
				@L{ISIC:} ................................................<br />
				<font size="8px">@L{verzia:} @{version}</font><br />
				<hr />
				<div te:if="!empty(@{test:data:sk_description})">
					@{test:data:sk_description}
					<hr />
				</div>
				@L{Správne odpovede z testu označiť sem, do tabuľky odpovedí:}<br />
				<table border="1" cellpadding="1px">
					<?php @{n} = 0; ?>
					<tr>
					<te:for each="@{test:questions} as @{question}">
						<?php @{n}++; ?>
						<td align="right" width="20px">@{n}.</td><td width="108px"><span style="color:white;">&amp;nbsp;</span>
							<?php @{na} = 0; @{nanswers} = count(explode(',', @{question:db_answer_ids})); ?>
							<te:while test="@{na} &lt; @{nanswers}">
								<?php @{na}++; ?>
								<span><?php echo chr(ord('A') + @{na} - 1); ?></span>
								<span style="color:white">&amp;nbsp;</span>
							</te:while>
						</td>
						<te:if test="!(@{n}%4)">&lt;/tr&gt;&lt;tr&gt;</te:if>
					</te:for>
					<te:if test="(@{n}%4)">&lt;/tr&gt;&lt;tr&gt;</te:if>
					<td colspan="10" width="512px"></td>
					</tr>
				</table>
				<te:include name="/sk/components/testquestions" test="@@{test}" />
			</te:for>

			<tcpdf method="startPageGroup" te:print="yes" /><?php
			?><tcpdf method="AddPage" te:print="yes" />
			<hr />
			<?php @{version} = @{start_version}; ?>
			<te:for each="@{test_data} as @{test}">
				<?php @{version}++; ?>
				@{test:data:sk_name} - <font size="8px"> @L{verzia} @{version}</font><br />
				<table border="1" cellpadding="1px">
					<?php @{n} = 0; ?>
					<tr>
					<te:for each="@{test:questions} as @{question}">
						<?php @{n}++; ?>
						<td align="right" width="20px">@{n}.</td><td width="108px"><span style="color:white;">&amp;nbsp;</span>
							<?php
								@{na} = 0;
							@{answers} = explode(',', @{question:db_answer_ids});
							@{nanswers} = count(@{answers});

						?>
							<te:while test="@{na} &lt; @{nanswers}">
								<?php
									@{answer_data} = @{test:answers}[@{answers}[@{na}]];
								@{na}++;
							?>
								<te:if test="@{answer_data:spravnost} === 'spravne'">
									<span><?php echo chr(ord('A') + @{na} - 1); ?></span>
									<te:else />
									<span style="color:white">X</span>
								</te:if>
								<span style="color:white">&amp;nbsp;</span>
							</te:while>
						</td>
						<te:if test="!(@{n}%4)">&lt;/tr&gt;&lt;tr&gt;</te:if>
					</te:for>
					<te:if test="(@{n}%4)">&lt;/tr&gt;&lt;tr&gt;</te:if>
					<td colspan="10" width="512px"></td>
					</tr>
				</table>
				<br />
				<hr />
			</te:for>
		</te:for>

		<tcpdf method="setPage" params="2" te:print="yes" />
 		<tcpdf method="deletePage" params="1" te:print="yes" />
	</te:if>

</truEngine-document>
