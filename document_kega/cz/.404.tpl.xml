<?xml version="1.0" encoding="utf-8" ?>
<truEngine-document xmlns:te="http://www.truengine.sk">

<?php
	@{godoc} = @{doc};
	if(strlen(@{doc}) == 2) {
		@{tmp_doc} = @{doc} . '/';
	}
	else {
		@{tmp_doc} = @{doc};
	}
	if(substr(@{tmp_doc}, 0, 3) == 'cz/') {
		@{godoc} = 'sk/' . substr(@{tmp_doc}, 3);
	}

	callStack_gClass::includePrepare_gFunc();

	$includeVars_pVar = array();
	foreach(vars::$vars as $k=>$v) {
		$includeVars_pVar[$k] = $v;
	}
	callStack_gClass::includeEnd_gFunc(@{godoc}, $includeVars_pVar);	

?>
	<te:result value="200" />

</truEngine-document>