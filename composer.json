{"name": "havala/kega", "type": "project", "description": "Opus SapientiAE", "keywords": [], "license": "proprietary", "require": {"php": "^8.2", "dompdf/dompdf": "^2.0", "guzzlehttp/guzzle": "^7.9", "laravel/framework": "^10.10", "laravel/sanctum": "^3.3", "laravel/tinker": "^2.8", "livewire/livewire": "^3.3", "mixedtype/mixedtype": "dev-main", "mixedtype/tracker": "dev-main", "openai-php/client": "^0.10.3", "sentry/sentry-laravel": "^4.1", "simplesoftwareio/simple-qrcode": "^4.2", "tecnickcom/tcpdf": "^6.6"}, "require-dev": {"beyondcode/laravel-dump-server": "^1.9", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "classmap": ["truEngine/.lib", "truEngine/classes"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php artisan mixedtype:setup"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true, "repositories": [{"type": "path", "url": "vendor-libs/mixedtype/*", "options": {"symlink": true}}, {"type": "path", "url": "../mixedtype-tracker/", "options": {"symlink": true}}, {"type": "vcs", "url": "https://github.com/mixedtype/tracker.git"}]}