<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__maintainance_pVar'])) return(true);
$GLOBALS['file__maintainance_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}

class maintainance_gFunc
{
	static private $maintainance_max_exec_time_pVar = 120; // 120 s
	static private $max_slow_query_live_pVar = 2678400; // 31 dni
	static private $slow_query_def_pVar = 3;
	static private $max_error_live_pVar = 2678400; // 31 dni

	static private $nLines_pVar = 0;
	static private $ignoreTags_pVar = array('RIGHTS_OK',
											'RIGHTS_FAIL',
											'SERVER_NAME',
											'REMOTE_ADDR',
											'HTTP_ACCEPT',
											'HTTP_ACCEPT_ENCODING',
											'HTTP_ACCEPT_LANGUAGE',
											'HTTP_ACCEPT_CHARSET',
											'LOAD_MODULE',
											'mySQL_transaction_start',
											'mySQL_transaction_commit',
											'TEMPLATE_RULE',
											'mySQL_transaction_rollback',
											'RESTORED_CACHED_RIGHTS',
											'SESSION_DESTROYED_SESSION_ID');

	static function run_maintainance_gFunc()
	{
		set_time_limit(min(self::$maintainance_max_exec_time_pVar + 120, 300));
		log_gClass::logQueriesOff_gFunc();

    	self::$slow_query_def_pVar = intval(main_gClass::getConfigVar_gFunc('slow_query_time', 'debug'))/1000.0;
    	if(!self::$slow_query_def_pVar) {
    		self::$slow_query_def_pVar = 3;
    	}

		$ret_pVar = true;
		if($ret_pVar) {
			$ret_pVar = self::parseLog_gFunc();
		}
		if($ret_pVar) {
			$ret_pVar = self::processLog_gFunc();
		}

		echo 'done('.intval($ret_pVar).')';

		$xlevel_pVar = main_gClass::getInputInteger_gFunc('maintanceredirect', main_gClass::SRC_REQUEST_pVar, 0);
		if(!$ret_pVar && $xlevel_pVar > 0) {
			$xlevel_pVar--;
			echo '<script type="text/javascript">document.location=\'/sk/?maintainance=1&maintanceredirect=' . $xlevel_pVar . '\';</script>';
		}
		log_gClass::logQueriesOn_gFunc();

		return($ret_pVar);
	}

	static private function parseLog_gFunc()
	{
        $dirName_pVar= main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        if(empty($dirName_pVar)) {
                return(false);
        }
        $dirName_pVar .= '.log/';

        $ret_pVar = true;

        $max_life_time_pVar = time(0) - 3600;

		$d = dir($dirName_pVar);
		while (false !== ($entry_pVar = $d->read())) {
			if(empty($entry_pVar) || $entry_pVar[0] == '.') {
				continue;
			}
			if(!is_dir($dirName_pVar . $entry_pVar)) {
				continue;
			}

			$nSkipped_pVar = 0;
			$d2 = dir($dirName_pVar . $entry_pVar);
			while (false !== ($entry2_pVar = $d2->read())) {
				if($entry2_pVar == '.' || $entry2_pVar == '..') {
					continue;
				}
				if(self::timeExceeded_gFunc()) {
					$ret_pVar = false;
					break 2;
				}
				$t = intval($entry2_pVar);
				if($t) {
					if($t < $max_life_time_pVar) {
						if(self::parseLogFile_gFunc($dirName_pVar . $entry_pVar . '/' . $entry2_pVar)) {
							unlink($dirName_pVar . $entry_pVar . '/' . $entry2_pVar);
							continue;
						}
						else {
							$ret_pVar = false;
						}
					}
				}
				$nSkipped_pVar++;
			}
			$d2->close();
			if(!$nSkipped_pVar) {
				rmdir($dirName_pVar . $entry_pVar);
			}
		}
		$d->close();

		return($ret_pVar);
	}

	static private function parseLogFile_gFunc($fileName_pVar)
	{
		if(filesize($fileName_pVar) > 3500000) {
			error_gClass::error_gFunc(__FILE__, __LINE__, string_gClass::get('str__maintainance_file_too_large_sVar', $fileName_pVar), array(), true);
			return(false);
		}
		$lines_pVar = file($fileName_pVar, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);

		self::$nLines_pVar += count($lines_pVar);
		$lastK_pVar = false;
		foreach($lines_pVar as $k_pVar=>$line_pVar) {
			$lines_pVar[$k_pVar] = $line_pVar = str_replace(chr(0xa8), LF, str_replace(chr(0xb8), CR, $line_pVar));
			if($line_pVar[0] == chr(0xb7)) {
				if($lastK_pVar !== false) {
					$line_pVar = substr($line_pVar, 1);
					$lines_pVar[$lastK_pVar] .= $line_pVar;
					unset($lines_pVar[$k_pVar]);
				}
				continue;
			}
			$lastK_pVar = $k_pVar;
		}
		$sql_pVar = 'INSERT INTO `%tlog` (`request_id`, `time_sec`, `time_usec`, `tag`, `value`) VALUES ';
		$sql_str_pVar = array();
		$sql_data_pVar = array();
		$request_id_pVar = basename($fileName_pVar);
		foreach($lines_pVar as $line_pVar) {
			if(strpos($line_pVar, 'RESTORED_CACHED_RIGHTS:') !== false) {
				$line_pVar = str_replace('):', '', $line_pVar);
				$line_pVar = str_replace('(RESTORED_CACHED_RIGHTS:', '(RESTORED_CACHED_RIGHTS):', $line_pVar);
			}

			if(!preg_match_all('/^(\d{10})[\.,](\d{6}) \(([a-zA-Z0-9_\-:.\\\ \/]+)\):(.*)/', $line_pVar, $matches_pVar)) {
				error_gClass::error_gFunc(__FILE__, __LINE__, string_gClass::get('str__maintainance_parse_error_sVar', $line_pVar));
				unset($lines_pVar);
				return(false);
			}

			if(array_search($matches_pVar[3][0], self::$ignoreTags_pVar) !== false) {
				continue;
			}

			$sql_str_pVar[] = '(%s, %d, %d, %s, %s)';
			$sql_data_pVar[] = $request_id_pVar;
			$sql_data_pVar[] = $matches_pVar[1][0];
			$sql_data_pVar[] = $matches_pVar[2][0];
			$sql_data_pVar[] = $matches_pVar[3][0];
			$sql_data_pVar[] = $matches_pVar[4][0];
		}

		if(count($sql_str_pVar)) {
			$sql_pVar .= implode(', ', $sql_str_pVar);
			db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar);
		}
		unset($lines_pVar);
		return(true);
	}

	static private function processLog_gFunc()
	{
		$current_time_pVar = time();
		$ret_pVar = true;

		$delete_requests_pVar = array();
		$mTU__stats_documents_per_day_pVar = new mTU__stats_documents_per_day_gClass();
		$mTU__stats_documents_per_month_pVar = new mTU__stats_documents_per_month_gClass();
		$mTU__stats_documents_per_week_pVar = new mTU__stats_documents_per_week_gClass();
		$mTU__stats_per_day_pVar = new mTU__stats_per_day_gClass();
		$mTU__stats_per_month_pVar = new mTU__stats_per_month_gClass();
		$mTU__stats_per_hour_pVar = new mTU__stats_per_hour_gClass();

		while(1) {
			if(self::timeExceeded_gFunc()) {
				$ret_pVar = false;
				break;
			}
			if(count($delete_requests_pVar)) {
				error_gClass::fatal_gFunc(__FILE__, __LINE__, '');
			}
			$sql_pVar = 'SELECT `request_id` FROM `%tlog` GROUP BY `request_id` ORDER BY `request_id` LIMIT 0,500';
			$requests_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
			if(!count($requests_pVar)) {
				break;
			}

			foreach($requests_pVar as $request_pVar) {
				$err_doc_pVar = false;
				$request_pVar = $request_pVar['request_id'];
				$sql_pVar = 'SELECT * FROM `%tlog` WHERE `request_id` = %s';
				$tmpdata_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $request_pVar);
				$data_pVar = array();
				$last_record_pVar = array();
				foreach($tmpdata_pVar as $v_pVar) {
					if(!isset($data_pVar[$v_pVar['tag']])) {
						$data_pVar[$v_pVar['tag']] = array();
					}
					$v_pVar['value'] = trim($v_pVar['value']);
					$tag_pVar = $v_pVar['tag'];
					if(substr($tag_pVar, 0, 9) == 'DB_QUERY-') {
						$tag_pVar = 'DB_QUERY';
						$file_pVar = substr($v_pVar['tag'], 9);
						$file_pVar = str_replace(' ', '_', trim($file_pVar));
						$v_pVar['value'] = array('file'=>$file_pVar, 'value'=>$v_pVar['value']);
					}
					$data_pVar[$tag_pVar][] = $v_pVar;
					$last_record_pVar = $v_pVar;
				}
				unset($tmpdata_pVar);

				if(!isset($data_pVar['TIME_START']) || !isset($data_pVar['QUERY'])) {
					error_gClass::error_gFunc(__FILE__, __LINE__, string_gClass::get('str__maintainance_no_required_tag_sVar'), array(), true);
					$sql_pVar = 'DELETE FROM `%tlog` WHERE `request_id` = %s';
					db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $request_pVar);
					continue;
				}

				$result_pVar = array('request_id' => $request_pVar);

				$time_pVar = intval($data_pVar['TIME_START'][0]['value']);
				$result_pVar['date'] = date('Y-m-d H:i:s', $time_pVar);
				$result_pVar['year'] = date('y', $time_pVar);
				$result_pVar['month'] = date('m', $time_pVar);
				$result_pVar['day'] = date('z', $time_pVar) + 1;
				$result_pVar['week'] = date('W', $time_pVar);
				$result_pVar['hour'] = date('H', $time_pVar);



				// najskor detekujem dokument:
				$query_pVar = htmlspecialchars_decode($data_pVar['QUERY'][0]['value']);
				$query_params_pVar = array();
				parse_str($query_pVar, $query_params_pVar);
				if(isset($query_params_pVar['doc'])) {
					$result_pVar['document'] = $query_params_pVar['doc'];
				}
				else {
					$result_pVar['document'] = $query_pVar;
				}
				$result_pVar['document'] = trim($result_pVar['document']);
				if(substr($result_pVar['document'], -1) == '/') {
					$result_pVar['document'] = substr($result_pVar['document'], 0, -1);
					$result_pVar['document'] = trim($result_pVar['document']);
				}

				// detekcia nedokonceneho konca
				if(!isset($data_pVar['END'])) {
					$result_pVar['end'] = false;
				}
				else {
					$result_pVar['end'] = true;
				}

				// vypocitam time_total (hodnota je trochu vacsia ako je v logu
				if($result_pVar['end']) {
					// nastavim si cas kedy skoncil
					$end_pVar = floatval($data_pVar['END'][0]['time_sec'] . '.' . $data_pVar['END'][0]['time_usec']);
				}
				else {
					$end_pVar = floatval($last_record_pVar['time_sec'] . '.' . $last_record_pVar['time_usec']);
				}
				$result_pVar['time_total'] = $end_pVar - floatval(str_replace(',', '.', $data_pVar['TIME_START'][0]['value']));
				if(isset($data_pVar['SLEEPING'])) {
					foreach($data_pVar['SLEEPING'] as $v_pVar) {
						$result_pVar['time_total'] -= floatval(trim($v_pVar['value']));
					}
				}

				if(isset($data_pVar['USER_AGENT'])) {
					$result_pVar['user_agent'] = $data_pVar['USER_AGENT'][0]['value'];
				}
				else {
					$result_pVar['user_agent'] = '';
				}

				$result_pVar['http_referer'] = '';
				if(isset($data_pVar['HTTP_REFERER'])) {
					$tmp_pVar = main_gClass::getServerVar_gFunc('SERVER_NAME');
					if(substr($data_pVar['HTTP_REFERER'][0]['value'], 0, strlen($tmp_pVar) + 8) != 'http://' . $tmp_pVar . '/'
					&& substr($data_pVar['HTTP_REFERER'][0]['value'], 0, strlen($tmp_pVar) + 9) != 'https://' . $tmp_pVar . '/') {
						$result_pVar['http_referer'] = $data_pVar['HTTP_REFERER'][0]['value'];
					}
				}

				if(isset($data_pVar['TOTAL_BYTES'])) {
					$result_pVar['total_bytes'] = $data_pVar['TOTAL_BYTES'][0]['value'];
				}
				else {
					$result_pVar['total_bytes'] = 0;
				}
				if(isset($data_pVar['MEMORY_USAGE'])) {
					$result_pVar['memory_usage'] = $data_pVar['MEMORY_USAGE'][0]['value'];
				}
				else {
					$result_pVar['memory_usage'] = 0;
				}

				// nastavim system call
				$systemCall_pVar = 0;
				if(isset($data_pVar['SYSTEM_DOCUMENT']) && $data_pVar['SYSTEM_DOCUMENT'][0]['value'] == 1) {
					$systemCall_pVar = 1;
				}
				$result_pVar['system_document'] = $systemCall_pVar;

				///// redirect
				if(isset($data_pVar['SEO_REDIRECT1'])
				   || isset($data_pVar['LANGUAGE_REDIRECT1'])
				   || isset($data_pVar['SEO_REDIRECT2'])
				   || isset($data_pVar['LANGUAGE_REDIRECT2'])) {
				   	$result_pVar['redirect'] = true;
				}
				else {
					$result_pVar['redirect'] = false;
				}

				if(isset($data_pVar['SEND_MAIL'])) {
					$result_pVar['send_mail'] = count($data_pVar['SEND_MAIL']);
				}
				else {
					$result_pVar['send_mail'] = 0;
				}

				if(isset($data_pVar['LOGIN_DISABLING_TIMEOUT'])) {
					$result_pVar['login_disabling_timeout'] = 1;
				}
				else {
					$result_pVar['login_disabling_timeout'] = 0;
				}

				if(isset($data_pVar['SESSION_ERROR_IP'])) {
					$result_pVar['session_error_ip'] = 1;
				}
				else {
					$result_pVar['session_error_ip'] = 0;
				}

				if(isset($data_pVar['SESSION_ERROR_AGENT'])) {
					$result_pVar['session_error_agent'] = 1;
				}
				else {
					$result_pVar['session_error_agent'] = 0;
				}


				// document_id
				$sql_pVar = 'SELECT `document_id`, `system_call` FROM `%tmaintainance__documents` WHERE `document_name` = %s';
				$document_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['document']));
				if($document_pVar === true) {
					$document_id_pVar = db_public_gClass::insert_gFunc('INSERT INTO `%tmaintainance__documents` (`document_name`, `system_call`) VALUES (%s, %d)', __FILE__, __LINE__, array($result_pVar['document'], $systemCall_pVar), true);
					$document_pVar = array('document_id'=>$document_id_pVar, 'system_call'=>$systemCall_pVar);
				}

				//// QUERIES
				$result_pVar['query_count'] = 0;
				$result_pVar['query_duration'] = 0;
				$result_pVar['query_bytes'] = 0;
				$result_pVar['query_level_0_001'] = 0;
				$result_pVar['query_level_0_01'] = 0;
				$result_pVar['query_level_0_1'] = 0;
				$result_pVar['query_level_1'] = 0;
				$result_pVar['query_level_3'] = 0;

				$result_pVar['db_query'] = array();
				if(isset($data_pVar['DB_QUERY'])) {
					$sql_pVar = 'INSERT INTO `%tmaintainance__queries_slow` (`time_executed`, `duration`, `source`, `query`, `document_id`) VALUES ';
					$sql_str_pVar = array();
					$sql_data_pVar = array();
					foreach($data_pVar['DB_QUERY'] as $v_pVar) {
						$tmp_pVar = array();
						if(!preg_match('/\s*\((\d+[\.,]\d+E?[-+]?\d?\d?)\)\s*(.*)/', $v_pVar['value']['value'], $tmp_pVar)) {
							echo $v_pVar['value']['value'];
							continue;
						}
						$tmp_pVar[1] = str_replace(',', '.', $tmp_pVar[1]);
						$tmp_pVar[1] = floatval($tmp_pVar[1]);
						$tmp_pVar[2] = trim($tmp_pVar[2]);

						$result_pVar['query_duration'] += $tmp_pVar[1];
						$result_pVar['query_bytes'] += strlen($tmp_pVar[2]);
						if($tmp_pVar[1] < 0.001) {
							$result_pVar['query_level_0_001']++;
						}
						if($tmp_pVar[1] < 0.01) {
							$result_pVar['query_level_0_01']++;
						}
						if($tmp_pVar[1] < 0.1) {
							$result_pVar['query_level_0_1']++;
						}
						if($tmp_pVar[1] < 1) {
							$result_pVar['query_level_1']++;
						}
						if($tmp_pVar[1] < 3) {
							$result_pVar['query_level_3']++;
						}
						$result_pVar['query_count']++;

						if($tmp_pVar[1] > self::$slow_query_def_pVar) {
							if($v_pVar['time_sec'] + self::$max_slow_query_live_pVar > $current_time_pVar) {
								$sql_str_pVar[] = '(%s, %f, %s, %s, %d)';
								$sql_data_pVar[] = date('Y-m-d H:i:s', $v_pVar['time_sec']);
								$sql_data_pVar[] = $tmp_pVar[1];
								$sql_data_pVar[] = $v_pVar['value']['file'];
								$sql_data_pVar[] = $tmp_pVar[2];
								$sql_data_pVar[] = $document_pVar['document_id'];
								//$result_pVar['db_query'][] = array('file'=>$v_pVar['value']['file'], 'duration'=>$tmp_pVar[1], 'query'=>$tmp_pVar[2], 'executed'=>$v_pVar['time_sec']);
							}
						}
					}
					if(count($sql_str_pVar)) {
						$sql_pVar .= implode(', ', $sql_str_pVar);
						db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar);
					}
				}

				if(isset($data_pVar['DB_QUERIES'])) {
					foreach($data_pVar['DB_QUERIES'] as $v_pVar) {
						$tmp_pVar = explode('(', $v_pVar['value']);
						$tmp_pVar = explode(')', $tmp_pVar[1]);
						$tmp_pVar = explode(';', $tmp_pVar[0]);

						$result_pVar['query_count'] += intval(trim($tmp_pVar[1]));
						$result_pVar['query_duration'] += floatval(trim($tmp_pVar[0]));
						$result_pVar['query_bytes'] += intval(trim($tmp_pVar[2]));
					}
				}

				if(isset($data_pVar['DB_BQUERY'])) {
					$tmp_pVar = array();
					foreach($data_pVar['DB_BQUERY'] as $v_pVar) {
						$vv_pVar = trim($v_pVar['value'], CR.LF.'() ');
						$vv_pVar = explode('/', $vv_pVar);

						if(!isset($tmp_pVar[$vv_pVar[0]])) {
							$tmp_pVar[$vv_pVar[0]] = array('last_time_executed'=>'', 'count'=>0, 'duration'=>0, 'params_example'=>'', 'source'=>'', 'query'=>'', 'md5'=>'');
						}

						$tmp_pVar[$vv_pVar[0]]['last_time_executed'] = date('Y-m-d H:i:s', intval($v_pVar['time_sec']));
						$tmp_pVar[$vv_pVar[0]]['count'] += intval($vv_pVar[1]);
						$tmp_pVar[$vv_pVar[0]]['duration'] += floatval($vv_pVar[2]);
						$tmp_pVar[$vv_pVar[0]]['source'] = base64_decode($vv_pVar[3]);
						$tmp_pVar[$vv_pVar[0]]['query'] = base64_decode($vv_pVar[4]);
						$tmp_pVar[$vv_pVar[0]]['params_example'] = $vv_pVar[5];
						$tmp_pVar[$vv_pVar[0]]['md5'] = $vv_pVar[0];
					}

					foreach($tmp_pVar as $v_pVar) {
						$sql_pVar = 'UPDATE `%tmaintainance__queries_base` SET ';
						$sql_pVar .= '`last_time_executed` = %s, `count` = `count` + %d, `duration` = `duration` + %f, `params_example` = %s, `source` = %s, `query` = %s WHERE `md5` = %s';
						$tmp_res_pVar = db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $v_pVar, true);
						if(!$tmp_res_pVar) {
							db_public_gClass::insertData_gFunc('%tmaintainance__queries_base', '%s, %d, %f, %s, %s, %s, %s', $v_pVar, __FILE__, __LINE__);
						}
					}
				}


				//// ERRORS
				$result_pVar['err_fatal'] = 0;
				$result_pVar['err_error'] = 0;
				$result_pVar['err_warning'] = 0;
				$result_pVar['err_notice'] = 0;
				$result_pVar['err_docs'] = 0;

				if(!$err_doc_pVar && !$result_pVar['end']) {
					$result_pVar['err_docs']++;
					$err_doc_pVar = true;
				}

				foreach(error_gClass::$errortype_pVar as $v_pVar) {
					if(isset($data_pVar[$v_pVar])) {
						foreach($data_pVar[$v_pVar] as $vv_pVar) {
							switch($vv_pVar['tag']) {
								case 'Notice':
								case 'User Notice':
									$result_pVar['err_notice']++;
									break;
								case 'Warning':
								case 'User Warning':
									$result_pVar['err_warning']++;
									break;
								case 'Error':
								case 'User Error':
									$result_pVar['err_error']++;
									break;
								default: // fatal
									$result_pVar['err_fatal']++;
									break;
							}

							if(!$err_doc_pVar) {
								$result_pVar['err_docs']++;
								$err_doc_pVar = true;
							}
							$err_time_pVar = substr($vv_pVar['value'], 0, 19);
							$err_time_pVar = strtotime($err_time_pVar);
							if(!$err_time_pVar) {
								$err_time_pVar = intval($data_pVar['TIME_START'][0]['value']);
								$err_text_pVar = $vv_pVar['value'];
							}
							else {
								$err_text_pVar = substr($vv_pVar['value'], 20);
							}

							if($err_time_pVar + self::$max_error_live_pVar > $current_time_pVar) {
								$err_time_pVar = date('Y-m-d H:i:s', $err_time_pVar);
								$sql_pVar = 'INSERT INTO `%tmaintainance__errors` (`time_executed`, `error_type`, `error`, `document_id`)
												VALUES(%s, %s, %s, %d)';
								db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($err_time_pVar, $vv_pVar['tag'], $err_text_pVar, $document_pVar['document_id']));
							}
						}
					}
				}

				// unique documents
				if(isset($data_pVar['PCID'])) {
					$result_pVar['pcid'] = $data_pVar['PCID'][0]['value'];
				}
				else {
					$result_pVar['pcid'] = '';
				}

				if(isset($data_pVar['SESSION_USER_ID'])) {
					$result_pVar['user_id'] = $data_pVar['SESSION_USER_ID'][0]['value'];
				}
				else {
					$result_pVar['user_id'] = 0;
				}

				$tmp_time_pVar = strtotime(sprintf('20%02d-01-01 00:00:00', $result_pVar['year'])) + ($result_pVar['day'] - 1) * 3600 * 24;
				$day_from_pVar = $result_pVar['day'] - (date('N', $tmp_time_pVar) - 1);
				$day_till_pVar = $day_from_pVar + 6;
				$sql_pVar = 'SELECT count(*) FROM `%tmaintainance__access` WHERE `year` = %d AND `day` >= %d AND `day` <= %d AND `user_id` = %d AND document_id=%d AND `pcid` = %s';
				if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $day_from_pVar, $day_till_pVar, $result_pVar['user_id'], $document_pVar['document_id'], $result_pVar['pcid']))) {
					$result_pVar['unique_doc_per_week'] = 1;
				}
				else {
					$result_pVar['unique_doc_per_week'] = 0;
				}

				$tmp_time2_pVar = strtotime(sprintf('20%02d-%02d-01 00:00:00', $result_pVar['year'], date('n', $tmp_time_pVar)));
				$day_from_pVar = date('z', $tmp_time2_pVar) + 1;
				$day_till_pVar = $day_from_pVar + date('t', $tmp_time2_pVar) - 1;
				$sql_pVar = 'SELECT count(*) FROM `%tmaintainance__access` WHERE `year` = %d AND `day` >= %d AND `day` <= %d AND `user_id` = %d AND document_id=%d AND `pcid` = %s';
				if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $day_from_pVar, $day_till_pVar, $result_pVar['user_id'], $document_pVar['document_id'], $result_pVar['pcid']))) {
					$result_pVar['unique_doc_per_month'] = 1;
				}
				else {
					$result_pVar['unique_doc_per_month'] = 0;
				}
				$sql_pVar = 'SELECT count(*) FROM `%tmaintainance__access` WHERE `year` = %d AND `day` >= %d AND `day` <= %d AND `user_id` = %d AND `pcid` = %s';
				if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $day_from_pVar, $day_till_pVar, $result_pVar['user_id'], $result_pVar['pcid']))) {
					$result_pVar['unique_per_month'] = 1;
				}
				else {
					$result_pVar['unique_per_month'] = 0;
				}

				$sql_pVar = 'SELECT count(*) FROM `%tmaintainance__access` WHERE `year` = %d AND `day` = %d AND `user_id` = %d AND `pcid` = %s';
				if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['day'], $result_pVar['user_id'], $result_pVar['pcid']))) {
					$result_pVar['unique_per_day'] = 1;
				}
				else {
					$result_pVar['unique_per_day'] = 0;
				}
				$sql_pVar = 'SELECT count(*) FROM `%tmaintainance__access` WHERE `year` = %d AND `day` = %d AND `user_id` = %d AND document_id=%d AND `pcid` = %s';
				if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['day'], $result_pVar['user_id'], $document_pVar['document_id'], $result_pVar['pcid']))) {
					$sql_pVar = 'INSERT INTO `%tmaintainance__access` (`year`, `day`, `user_id`, `document_id`, `pcid`) VALUES (%d, %d, %d, %d, %s)';
					db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['day'], $result_pVar['user_id'], $document_pVar['document_id'], $result_pVar['pcid']));
					$result_pVar['unique_doc_per_day'] = 1;
				}
				else {
					$result_pVar['unique_doc_per_day'] = 0;
				}

				//@TODO: HTTP_REFERER a USER_AGENT by sa tiez mozno dali optimalizovat ako ostatne tabulky. Ale nie tak vyrazne, lebo vysledok potom neulozim jednym update/insert

				// HTTP_REFERER
				if(strlen($result_pVar['http_referer'])) {
					$sql_pVar = 'SELECT count(*) FROM `%tmaintainance__stats_ext_per_month` WHERE `year` = %d AND `month` = %d AND `type` = \'HTTP_REFERER\' AND `tag` = %s';
					if(db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['month'], $result_pVar['http_referer']))) {
						$sql_pVar = 'UPDATE `%tmaintainance__stats_ext_per_month` SET `value` = `value` + 1 WHERE `year` = %d AND `month` = %d AND `type` = \'HTTP_REFERER\' AND `tag` = %s';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['month'], $result_pVar['http_referer']));
					}
					else {
						$sql_pVar = 'INSERT INTO `%tmaintainance__stats_ext_per_month` (`year`, `month`, `type`, `tag`, `value`)
									VALUES(%d, %d, \'HTTP_REFERER\', %s, 1)';
						db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['month'], $result_pVar['http_referer']));
					}
				}

				// USER_AGENT
				if($result_pVar['unique_per_month']) {
					$sql_pVar = 'select count(*) FROM `%tmaintainance__stats_ext_per_month` WHERE `year` = %d AND `month` = %d AND `type` = \'USER_AGENT\' AND `tag` = %s';
					if(db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['month'], $result_pVar['user_agent']), true)) {
						$sql_pVar = 'UPDATE `%tmaintainance__stats_ext_per_month` SET `value` = `value` + 1 WHERE `year` = %d AND `month` = %d AND `type` = \'USER_AGENT\' AND `tag` = %s';
						db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['month'], $result_pVar['user_agent']));
					}
					else {
						$sql_pVar = 'INSERT INTO `%tmaintainance__stats_ext_per_month` (`year`, `month`, `type`, `tag`, `value`)
									VALUES(%d, %d, \'USER_AGENT\', %s, 1)';
						db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['year'], $result_pVar['month'], $result_pVar['user_agent']));
					}
				}

				$result_pVar['document_id'] = $document_pVar['document_id'];
				$result_pVar['system'] = $result_pVar['system_document'];

				// maintainance__stats_documents_per_day
				$mTU__stats_documents_per_day_pVar->update_gFunc($result_pVar);

				// maintainance__stats_documents_per_month
				$mTU__stats_documents_per_month_pVar->update_gFunc($result_pVar);

				// maintainance__stats_documents_per_week
				$mTU__stats_documents_per_week_pVar->update_gFunc($result_pVar);

				// maintainance__stats_per_day
				$mTU__stats_per_day_pVar->update_gFunc($result_pVar);

				// maintainance__stats_per_month
				$mTU__stats_per_month_pVar->update_gFunc($result_pVar);

				// maintainance__stats_per_hour
				$mTU__stats_per_hour_pVar->update_gFunc($result_pVar);

				$delete_requests_pVar[] = $result_pVar['request_id'];

				if(count($delete_requests_pVar) > 100) {
					$sql_pVar = 'DELETE FROM `%tlog` WHERE `request_id` IN (\'' . implode('\', \'', $delete_requests_pVar) . '\')';
					if(db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
						$delete_requests_pVar = array();
					}
				}
				/*
				$sql_pVar = 'DELETE FROM `%tlog` WHERE `request_id` = %s';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['request_id']));
				*/
				if(self::timeExceeded_gFunc()) {
					$ret_pVar = false;
					break 2;
				}
			}
			if(count($delete_requests_pVar)) {
				$sql_pVar = 'DELETE FROM `%tlog` WHERE `request_id` IN (%as)';
				if(db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($delete_requests_pVar))) {
					$delete_requests_pVar = array();
				}
			}
		}

		$mTU__stats_documents_per_day_pVar->flush_gFunc();
		$mTU__stats_documents_per_month_pVar->flush_gFunc();
		$mTU__stats_documents_per_week_pVar->flush_gFunc();
		$mTU__stats_per_day_pVar->flush_gFunc();
		$mTU__stats_per_month_pVar->flush_gFunc();
		$mTU__stats_per_hour_pVar->flush_gFunc();

		if(count($delete_requests_pVar)) {
			$sql_pVar = 'DELETE FROM `%tlog` WHERE `request_id` IN (%as)';
			if(db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($delete_requests_pVar))) {
				$delete_requests_pVar = array();
			}
		}

		$sql_pVar = 'DELETE FROM `%tmaintainance__errors` WHERE `time_executed` < %s';
		db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(date('Y-m-d H:i:s', $current_time_pVar - self::$max_error_live_pVar)));

		$sql_pVar = 'DELETE FROM `%tmaintainance__queries_slow` WHERE `time_executed` < %s
				OR `duration` < %f';
		db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(date('Y-m-d H:i:s', $current_time_pVar - self::$max_slow_query_live_pVar), self::$slow_query_def_pVar));

		if($ret_pVar === true) {
			// v access musi zostat cely minuly mesiac.. teda mozem zmazat vsetko starsie ako 62 dni.
			// ale dame tomu rezervu, takze 70 dni.
			$y_pVar = date('y');
			$d_pVar = date('z') + 1;
			$d_pVar = $d_pVar - 70;
			if($d_pVar <= 0) {
				$y_pVar = $y_pVar - 1;
				$d_pVar = 365 + $d_pVar;
			}
			$sql_pVar = 'DELETE FROM `%tmaintainance__access` WHERE `year` < %d OR (`year` = %d AND `day` < %d)';
			db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($y_pVar, $y_pVar, $d_pVar));
		}

		return($ret_pVar);
	}

	static private function timeExceeded_gFunc()
	{
		return(main_gClass::timeExceeded_gFunc(self::$maintainance_max_exec_time_pVar));
	}
}


class maintainanceTableUpdater_gClass
{
	private $cache_pVar;

	function __construct()
	{
		$this->cache_pVar = array();
	}

	function update_gFunc($data_pVar)
	{
		$keys_pVar = $this->getKeys_gFunc();
		$key_pVar = array();
		foreach($keys_pVar as $v_pVar) {
			$key_pVar[] = $data_pVar[$v_pVar];
		}
		$key_pVar = implode(',', $key_pVar);

		if(!isset($this->cache_pVar[$key_pVar])) {
			$this->cache_pVar[$key_pVar] = array();
			$fields_pVar = $this->getIntFields_gFunc();
			foreach($fields_pVar as $v_pVar) {
				$this->cache_pVar[$key_pVar][$v_pVar] = 0;
			}
			$fields_pVar = $this->getFloatFields_gFunc();
			foreach($fields_pVar as $v_pVar) {
				$this->cache_pVar[$key_pVar][$v_pVar] = 0.0;
			}
		}

		$fields_pVar = $this->getIntFields_gFunc();
		foreach($fields_pVar as $v_pVar) {
			$this->cache_pVar[$key_pVar][$v_pVar] += intval($this->getFieldValue_gFunc($v_pVar, $data_pVar));
		}
		$fields_pVar = $this->getFloatFields_gFunc();
		foreach($fields_pVar as $v_pVar) {
			$this->cache_pVar[$key_pVar][$v_pVar] += floatval($this->getFieldValue_gFunc($v_pVar, $data_pVar));
		}
		return(true);
	}

	function flush_gFunc()
	{
		foreach($this->cache_pVar as $k_pVar=>$v_pVar) {
			// maintainance__stats_documents_per_day
			$sql_str_pVar = array();
			$sql_data_pVar = array();
			$sql_pVar = 'UPDATE `' . $this->getTableName_gFunc() . '` SET ';
			$fields_pVar = $this->getIntFields_gFunc();
			foreach($fields_pVar as $field_pVar) {
				$sql_str_pVar[] = '`' . $field_pVar . '` = `' . $field_pVar . '` + %d ';
				$sql_data_pVar[] = $v_pVar[$field_pVar];
			}
			$fields_pVar = $this->getFloatFields_gFunc();
			foreach($fields_pVar as $field_pVar) {
				$sql_str_pVar[] = '`' . $field_pVar . '` = `' . $field_pVar . '` + %f ';
				$sql_data_pVar[] = $v_pVar[$field_pVar];
			}
			$sql_pVar .= implode(',', $sql_str_pVar);
			$sql_pVar .= ' WHERE ';
			$sql_str_pVar = array();
			$keys_data_pVar = explode(',', $k_pVar);
			$keys_pVar = $this->getKeys_gFunc();
			$keys_data_tmp_pVar = $keys_data_pVar;
			foreach($keys_pVar as $key_pVar) {
				$sql_str_pVar[] = '`' . $key_pVar . '` = %d ';
				$sql_data_pVar[] = array_shift($keys_data_tmp_pVar);
			}
			$sql_pVar .= implode(' AND ', $sql_str_pVar);
			if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar, true)) {
				$sql_str1_pVar = array();
				$sql_str2_pVar = array();
				$sql_data_pVar = array();
				$sql_pVar = 'INSERT INTO `' . $this->getTableName_gFunc() . '` ';
				$fields_pVar = $this->getIntFields_gFunc();
				foreach($fields_pVar as $field_pVar) {
					$sql_str1_pVar[] = '`' . $field_pVar . '`';
					$sql_str2_pVar[] = '%d';
					$sql_data_pVar[] = $v_pVar[$field_pVar];
				}
				$fields_pVar = $this->getFloatFields_gFunc();
				foreach($fields_pVar as $field_pVar) {
					$sql_str1_pVar[] = '`' . $field_pVar . '`';
					$sql_str2_pVar[] = '%f';
					$sql_data_pVar[] = $v_pVar[$field_pVar];
				}
				$keys_data_tmp_pVar = $keys_data_pVar;
				foreach($keys_pVar as $key_pVar) {
					$sql_str1_pVar[] = '`' . $key_pVar . '`';
					$sql_str2_pVar[] = '%d';
					$sql_data_pVar[] = array_shift($keys_data_tmp_pVar);
				}
				$sql_pVar .= '(' . implode(',', $sql_str1_pVar) . ') VALUES (' . implode(',', $sql_str2_pVar) . ')';
				db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar);
			}
		}
		return(true);
	}

	//@TODO: getFieldValue_gFunc mozem u potomkov zrusit, a spravit jednu tu v rodicovi, so vstkymy CASE. Nic sa tym nezmeni, akurat sa zprehladni a skrati kod.
	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		return($data_pVar[$fieldName_pVar]);
	}

	protected function getIntFields_gFunc()
	{
		return(array());
	}

	protected function getFloatFields_gFunc()
	{
		return(array());
	}

	protected function getTableName_gFunc()
	{
		return('table_name');
	}

	protected function getKeys_gFunc()
	{
		return(array());
	}
}

class mTU__stats_documents_per_day_gClass extends maintainanceTableUpdater_gClass
{
	protected function getKeys_gFunc()
	{
		return(array('year', 'day', 'document_id'));
	}

	protected function getIntFields_gFunc()
	{
		return(array('document_redirected', 'document_executed', 'document_executed_unique'));
	}

	protected function getTableName_gFunc()
	{
		return('%tmaintainance__stats_documents_per_day');
	}

	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		switch($fieldName_pVar) {
			case 'document_redirected':
				return($data_pVar['redirect'] ? 1 : 0);
			case 'document_executed':
				return($data_pVar['redirect'] ? 0 : 1);
			case 'document_executed_unique':
				return((!$data_pVar['redirect'] && $data_pVar['unique_doc_per_day']) ? 1 : 0);
			default:
				return($data_pVar[$fieldName_pVar]);
		}
	}
}

class mTU__stats_documents_per_month_gClass extends maintainanceTableUpdater_gClass
{
	protected function getKeys_gFunc()
	{
		return(array('year', 'month', 'document_id'));
	}

	protected function getIntFields_gFunc()
	{
		return(array('document_redirected', 'document_executed', 'document_executed_unique', 'send_mail'));
	}

	protected function getTableName_gFunc()
	{
		return('%tmaintainance__stats_documents_per_month');
	}

	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		switch($fieldName_pVar) {
			case 'document_redirected':
				return($data_pVar['redirect'] ? 1 : 0);
			case 'document_executed':
				return($data_pVar['redirect'] ? 0 : 1);
			case 'document_executed_unique':
				return((!$data_pVar['redirect'] && $data_pVar['unique_doc_per_day']) ? 1 : 0);
			default:
				return($data_pVar[$fieldName_pVar]);
		}
	}
}

class mTU__stats_documents_per_week_gClass extends maintainanceTableUpdater_gClass
{
	protected function getKeys_gFunc()
	{
		return(array('year', 'week', 'document_id'));
	}

	protected function getIntFields_gFunc()
	{
		return(array('document_redirected',
					 'document_executed',
					 'document_executed_unique',
					 'document_memory_sum',
					 'query_executed_sum_level_0_001',
					 'query_executed_sum_level_0_01',
					 'query_executed_sum_level_0_1',
					 'query_executed_sum_level_1',
					 'query_executed_sum_level_3',
					 'query_executed_sum',
					 'query_bytes_sum',
					 'notice_sum',
					 'warning_sum',
					 'error_sum',
					 'fatal_sum',
					 'uncompleted_sum',
					 'doc_error'
		));
	}

	protected function getFloatFields_gFunc()
	{
		return(array('document_duration_sum', 'query_duration_sum'));
	}

	protected function getTableName_gFunc()
	{
		return('%tmaintainance__stats_documents_per_week');
	}

	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		switch($fieldName_pVar) {
			case 'document_redirected':
				return($data_pVar['redirect'] ? 1 : 0);
			case 'document_executed':
				return($data_pVar['redirect'] ? 0 : 1);
			case 'document_executed_unique':
				return((!$data_pVar['redirect'] && $data_pVar['unique_doc_per_day']) ? 1 : 0);
			case 'document_duration_sum':
				return($data_pVar['time_total']);
			case 'document_memory_sum':
				return($data_pVar['memory_usage']);
			case 'query_executed_sum_level_0_001':
				return($data_pVar['query_level_0_001']);
			case 'query_executed_sum_level_0_01':
				return($data_pVar['query_level_0_01']);
			case 'query_executed_sum_level_0_1':
				return($data_pVar['query_level_0_1']);
			case 'query_executed_sum_level_1':
				return($data_pVar['query_level_1']);
			case 'query_executed_sum_level_3':
				return($data_pVar['query_level_3']);
			case 'query_executed_sum':
				return($data_pVar['query_count']);
			case 'query_duration_sum':
				return($data_pVar['query_duration']);
			case 'query_bytes_sum':
				return($data_pVar['query_bytes']);
			case 'notice_sum':
				return($data_pVar['err_notice']);
			case 'warning_sum':
				return($data_pVar['err_warning']);
			case 'error_sum':
				return($data_pVar['err_error']);
			case 'fatal_sum':
				return($data_pVar['err_fatal']);
			case 'uncompleted_sum':
				return($data_pVar['end'] ? 0 : 1);
			case 'doc_error':
				return($data_pVar['err_docs']);
			default:
				return($data_pVar[$fieldName_pVar]);
		}
	}
}

class mTU__stats_per_day_gClass extends maintainanceTableUpdater_gClass
{
	protected function getKeys_gFunc()
	{
		return(array('year', 'day', 'system'));
	}

	protected function getIntFields_gFunc()
	{
		return(array('document_redirected',
					 'document_executed',
					 'document_executed_unique',
					 'document_memory_sum',
					 'document_total_bytes',
					 'query_executed_sum_level_0_001',
					 'query_executed_sum_level_0_01',
					 'query_executed_sum_level_0_1',
					 'query_executed_sum_level_1',
					 'query_executed_sum_level_3',
					 'query_executed_sum',
					 'query_bytes_sum',
					 'notice_sum',
					 'warning_sum',
					 'error_sum',
					 'fatal_sum',
					 'uncompleted_sum',
					 'doc_error'
		));
	}

	protected function getFloatFields_gFunc()
	{
		return(array('document_duration_sum', 'query_duration_sum'));
	}

	protected function getTableName_gFunc()
	{
		return('%tmaintainance__stats_per_day');
	}

	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		switch($fieldName_pVar) {
			case 'document_redirected':
				return($data_pVar['redirect'] ? 1 : 0);
			case 'document_executed':
				return($data_pVar['redirect'] ? 0 : 1);
			case 'document_executed_unique':
				return((!$data_pVar['redirect'] && $data_pVar['unique_doc_per_day']) ? 1 : 0);
			case 'document_duration_sum':
				return($data_pVar['time_total']);
			case 'document_memory_sum':
				return($data_pVar['memory_usage']);
			case 'document_total_bytes':
				return($data_pVar['total_bytes']);
			case 'query_executed_sum_level_0_001':
				return($data_pVar['query_level_0_001']);
			case 'query_executed_sum_level_0_01':
				return($data_pVar['query_level_0_01']);
			case 'query_executed_sum_level_0_1':
				return($data_pVar['query_level_0_1']);
			case 'query_executed_sum_level_1':
				return($data_pVar['query_level_1']);
			case 'query_executed_sum_level_3':
				return($data_pVar['query_level_3']);
			case 'query_executed_sum':
				return($data_pVar['query_count']);
			case 'query_duration_sum':
				return($data_pVar['query_duration']);
			case 'query_bytes_sum':
				return($data_pVar['query_bytes']);
			case 'notice_sum':
				return($data_pVar['err_notice']);
			case 'warning_sum':
				return($data_pVar['err_warning']);
			case 'error_sum':
				return($data_pVar['err_error']);
			case 'fatal_sum':
				return($data_pVar['err_fatal']);
			case 'uncompleted_sum':
				return($data_pVar['end'] ? 0 : 1);
			case 'doc_error':
				return($data_pVar['err_docs']);
			default:
				return($data_pVar[$fieldName_pVar]);
		}
	}
}

class mTU__stats_per_month_gClass extends maintainanceTableUpdater_gClass
{
	protected function getKeys_gFunc()
	{
		return(array('year', 'month', 'system'));
	}

	protected function getIntFields_gFunc()
	{
		return(array('document_redirected',
					 'document_executed',
					 'document_executed_unique',
					 'document_memory_sum',
					 'document_total_bytes',
					 'query_executed_sum_level_0_001',
					 'query_executed_sum_level_0_01',
					 'query_executed_sum_level_0_1',
					 'query_executed_sum_level_1',
					 'query_executed_sum_level_3',
					 'query_executed_sum',
					 'query_bytes_sum',
					 'notice_sum',
					 'warning_sum',
					 'error_sum',
					 'fatal_sum',
					 'uncompleted_sum',
					 'doc_error',
					 'login_disaling_timeout_sum',
					 'send_mail',
					 'session_error_ip_sum',
					 'session_error_agent'
		));
	}

	protected function getFloatFields_gFunc()
	{
		return(array('document_duration_sum', 'query_duration_sum'));
	}

	protected function getTableName_gFunc()
	{
		return('%tmaintainance__stats_per_month');
	}

	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		switch($fieldName_pVar) {
			case 'document_redirected':
				return($data_pVar['redirect'] ? 1 : 0);
			case 'document_executed':
				return($data_pVar['redirect'] ? 0 : 1);
			case 'document_executed_unique':
				return((!$data_pVar['redirect'] && $data_pVar['unique_doc_per_day']) ? 1 : 0);
			case 'document_duration_sum':
				return($data_pVar['time_total']);
			case 'document_memory_sum':
				return($data_pVar['memory_usage']);
			case 'document_total_bytes':
				return($data_pVar['total_bytes']);
			case 'query_executed_sum_level_0_001':
				return($data_pVar['query_level_0_001']);
			case 'query_executed_sum_level_0_01':
				return($data_pVar['query_level_0_01']);
			case 'query_executed_sum_level_0_1':
				return($data_pVar['query_level_0_1']);
			case 'query_executed_sum_level_1':
				return($data_pVar['query_level_1']);
			case 'query_executed_sum_level_3':
				return($data_pVar['query_level_3']);
			case 'query_executed_sum':
				return($data_pVar['query_count']);
			case 'query_duration_sum':
				return($data_pVar['query_duration']);
			case 'query_bytes_sum':
				return($data_pVar['query_bytes']);
			case 'notice_sum':
				return($data_pVar['err_notice']);
			case 'warning_sum':
				return($data_pVar['err_warning']);
			case 'error_sum':
				return($data_pVar['err_error']);
			case 'fatal_sum':
				return($data_pVar['err_fatal']);
			case 'uncompleted_sum':
				return($data_pVar['end'] ? 0 : 1);
			case 'doc_error':
				return($data_pVar['err_docs']);
			case 'login_disaling_timeout_sum':
				return($data_pVar['login_disabling_timeout']);
			case 'session_error_ip_sum':
				return($data_pVar['session_error_ip']);
			default:
				return($data_pVar[$fieldName_pVar]);
		}
	}
}

class mTU__stats_per_hour_gClass extends maintainanceTableUpdater_gClass
{
	protected function getKeys_gFunc()
	{
		return(array('year', 'day', 'hour','system'));
	}

	protected function getIntFields_gFunc()
	{
		return(array('document_redirected',
					 'document_executed',
					 'document_memory_sum',
					 'document_total_bytes',
					 'query_executed_sum_level_0_001',
					 'query_executed_sum_level_0_01',
					 'query_executed_sum_level_0_1',
					 'query_executed_sum_level_1',
					 'query_executed_sum_level_3',
					 'query_executed_sum',
					 'query_bytes_sum'
		));
	}

	protected function getFloatFields_gFunc()
	{
		return(array('document_duration_sum', 'query_duration_sum'));
	}

	protected function getTableName_gFunc()
	{
		return('%tmaintainance__stats_per_hour');
	}

	protected function getFieldValue_gFunc($fieldName_pVar, &$data_pVar)
	{
		switch($fieldName_pVar) {
			case 'document_redirected':
				return($data_pVar['redirect'] ? 1 : 0);
			case 'document_executed':
				return($data_pVar['redirect'] ? 0 : 1);
			case 'document_duration_sum':
				return($data_pVar['time_total']);
			case 'document_memory_sum':
				return($data_pVar['memory_usage']);
			case 'document_total_bytes':
				return($data_pVar['total_bytes']);
			case 'query_executed_sum_level_0_001':
				return($data_pVar['query_level_0_001']);
			case 'query_executed_sum_level_0_01':
				return($data_pVar['query_level_0_01']);
			case 'query_executed_sum_level_0_1':
				return($data_pVar['query_level_0_1']);
			case 'query_executed_sum_level_1':
				return($data_pVar['query_level_1']);
			case 'query_executed_sum_level_3':
				return($data_pVar['query_level_3']);
			case 'query_executed_sum':
				return($data_pVar['query_count']);
			case 'query_duration_sum':
				return($data_pVar['query_duration']);
			case 'query_bytes_sum':
				return($data_pVar['query_bytes']);
			default:
				return($data_pVar[$fieldName_pVar]);
		}
	}
}


class maintainance_stats_per_hour_gClass extends source_gClass
{
	protected function getData()
	{
		$sql_pVar = 'SELECT year, 0 as month, day, hour, document_executed, document_duration_sum, document_memory_sum, document_total_bytes, query_executed_sum, query_duration_sum FROM %tmaintainance__stats_per_hour WHERE `system` = 0';
		$tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

		$ret_pVar['data'] = array();
		$last_pVar = false;
		foreach($tmp_pVar as $v_pVar) {
			$v_pVar['year'] += 2000;
			$time_pVar = mktime($v_pVar['hour'], 59, 59, 1, $v_pVar['day'], $v_pVar['year']);
			$v_pVar['month'] = date('n', $time_pVar);
			$v_pVar['day'] = date('j', $time_pVar);

			if($last_pVar !== false) {
				$time2_pVar = mktime($last_pVar['hour'] + 1, 59, 59, $last_pVar['month'], $last_pVar['day'], $last_pVar['year']);
				if($time_pVar != $time2_pVar) {
					$last_pVar['year'] = date('Y', $time2_pVar);
					$last_pVar['month'] = date('n', $time2_pVar);
					$last_pVar['day'] = date('j', $time2_pVar);
					$last_pVar['hour'] = date('G', $time2_pVar);
					$last_pVar['document_executed'] = 0;
					$last_pVar['document_duration_sum'] = 0;
					$last_pVar['document_memory_sum'] = 0;
					$last_pVar['document_total_bytes'] = 0;
					$last_pVar['query_executed_sum'] = 0;
					$last_pVar['query_duration_sum'] = 0;
					$ret_pVar['data'][] = $last_pVar;

					$time2_pVar = mktime($last_pVar['hour'] + 1, 59, 59, $last_pVar['month'], $last_pVar['day'], $last_pVar['year']);
					if($time_pVar != $time2_pVar) {
						$time2_pVar = mktime($v_pVar['hour'] - 1, 59, 59, $v_pVar['month'], $v_pVar['day'], $v_pVar['year']);
						$last_pVar['year'] = date('Y', $time2_pVar);
						$last_pVar['month'] = date('n', $time2_pVar);
						$last_pVar['day'] = date('j', $time2_pVar);
						$last_pVar['hour'] = date('G', $time2_pVar);
						$ret_pVar['data'][] = $last_pVar;
					}
				}
			}
			$last_pVar = $v_pVar;

			$ret_pVar['data'][] = $v_pVar;
		}

		$ret_pVar['months'] = array();
		foreach($ret_pVar['data'] as $v_pVar) {
			$period_pVar = sprintf('%04d-%02d', $v_pVar['year'], $v_pVar['month']);
			$ret_pVar['months'][$period_pVar . '-01'] = str_replace('-', '/', $period_pVar);
		}

		return($ret_pVar);
	}
}

class maintainance_stats_per_hour extends maintainance_stats_per_hour_gClass
{

}

return(true);
