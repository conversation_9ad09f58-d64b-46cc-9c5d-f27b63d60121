<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__import_pVar'])) return(true);
$GLOBALS['file__import_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}


class import_gClass
{
	protected $fileName_pVar;
	protected $info_pVar;
	
	protected function __construct($fileName_pVar)
	{
		$this->fileName_pVar = $fileName_pVar;
		$this->info_pVar = false;
	}
	
	static public function loadUpoadedFile_gFunc($fileName_pVar)
	{
		$p_pVar = strrpos($fileName_pVar, '.');
		if($p_pVar !== false) {
			$extension_pVar = substr($fileName_pVar, $p_pVar + 1);
			$extension_pVar = strtolower($extension_pVar);
			
			switch($extension_pVar) {
				case 'xml':
					if(modules_gClass::isModuleRegistred_gFunc('import_xml')) {
						$obj_pVar = new import_xml_gClass($fileName_pVar);
						return($obj_pVar);
					}
					break;
				case 'txt':
					if(modules_gClass::isModuleRegistred_gFunc('import_txt')) {
						$obj_pVar = new import_txt_gClass($fileName_pVar);
						return($obj_pVar);
					}
					break;
				case 'csv':
					if(modules_gClass::isModuleRegistred_gFunc('import_csv')) {
						$obj_pVar = new import_csv_gClass($fileName_pVar);
						return($obj_pVar);
					}
					break;
			}
		}
	    
	    return(null);
	}
	
	function getInfo_gFunc()
	{
		if($this->info_pVar !== false) {
			return($this->info_pVar);
		}
		$this->info_pVar = $this->loadInfo_gFunc();
		return($this->info_pVar);
	}
	
	public function import_gFunc()
	{
		log_gClass::logQueriesOff_gFunc();
		
		$this->_import_gFunc();
		
		log_gClass::logQueriesOn_gFunc();
	}
	
	function _import_gFunc()
	{
		/// tuto metodu pretazujem
		return(false);
	}
	
	function _import_data_gFunc($data_pVar)
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_import_data)) {
			return(false);
		}
		if(!modules_gClass::isModuleRegistred_gFunc('items')) {
			return(false);
		}
		//echo '<pre>'; print_r($data_pVar); echo '</pre>';
		if(!isset($data_pVar['data_type']) || empty($data_pVar['data_type'])) {
			return(false);
		}
		if(!isset($data_pVar['data']) || !count($data_pVar['data'])) {
			return(true);
		}
		
		foreach($data_pVar['data'] as $item_pVar) {
			if(isset($item_pVar['item_id'])) {
				unset($item_pVar['item_id']);
			}
			switch($data_pVar['data_type']) {
				case 'users':
					if(!isset($item_pVar['status'])) {
						$item_pVar['status'] = 'active';
					}
					if(!isset($item_pVar['user_role'])) {
						$item_pVar['user_role'] = 'none';
					}
					break;
				case 'test_questions':
					if(!isset($item_pVar['status'])) {
						$item_pVar['status'] = 'waiting';
					}
					if(isset($item_pVar['answers'])) {
						$answers_pVar = $item_pVar['answers'];
						unset($item_pVar['answers']);
					}
					break;
			}
			echo '<pre>'; print_r($item_pVar); echo '</pre>';
			$item_id_pVar = items_gClass::saveOrUpdateItem_gFunc($data_pVar['data_type'], $item_pVar);

			if($item_id_pVar) {
				if($data_pVar['data_type'] == 'test_questions' && isset($answers_pVar)) {
					foreach($answers_pVar as $answer_pVar) {
						if(isset($answer_pVar['item_id'])) {
							unset($answer_pVar['item_id']);
						}
						if(!isset($answer_pVar['status'])) {
							$answer_pVar['status'] = 'active';
						}
						$answer_pVar['test_question'] = $item_id_pVar;
						items_gClass::saveOrUpdateItem_gFunc('test_answers', $answer_pVar);
					}
					unset($answers_pVar);
				}
			}
		}
		
		return(true);
	}

}

class import_form_gClass extends form_gClass {

	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		$this->addFieldset_gFunc('file', 'Súbor');
		$this->addField_gFunc('file', 'file', 'filelist', 'Importovať súbor');

		if(isset($this->params['submit_button_title'])) {
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title'], false);
		}
	}
	
	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		// ulozim
		$this->saveData_gFunc();

		$data_pVar = $this->getFormData_gFunc();

		return($data_pVar);
	}

	protected function saveData_gFunc()
	{
    	$fileInfo_pVar = main_gClass::getInputFile('file');
    	if(!is_array($fileInfo_pVar)) {
    		// subor nebol uploadovany vobec
    		return(false);
    	}

    	if(!is_array($fileInfo_pVar['error'])) {
    		foreach ($fileInfo_pVar as $k_pVar=>$v_pVar) {
    			$fileInfo_pVar[$k_pVar] = array(0 => $v_pVar);
    		}
    	}

    	if(!isset($fileInfo_pVar['error'][0])) {
	    	return(false);
    	}
		$fileInfo_pVar['error']=array(0=>$fileInfo_pVar['error'][0]);

    	$file_error_pVar = $fileInfo_pVar['error'][0];
    	if($file_error_pVar !== UPLOAD_ERR_OK) {
   			return(false);
    	}
		
    	$baseName_pVar = basename($fileInfo_pVar['name'][0]);
    	$p_pVar = strrpos($baseName_pVar, '.');
    	if($p_pVar !== false) {
    		$extension_pVar = substr($baseName_pVar, $p_pVar + 1);
    		$extension_pVar = strtolower($extension_pVar);
    	}
    	else {
    		$extension_pVar = '';
    	}
    	
		// uploadnem subor do tmp adresara, a zapisem do tabulky importov
		$target_dir_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
		$target_dir_pVar .= '.tmp/';
		$target_name_pVar = 'import_' . sprintf('%1.22f', microtime(true)) . '.tmp.' . $extension_pVar;
		
    	if(modules_gClass::isModuleRegistred_gFunc('upload')) {
    		if(upload_gClass::moveUploadedFile_gFunc('file', 0, $target_dir_pVar, $target_name_pVar) === false) {
    			return(false);
       		}
    	}
    	else {
    		if(move_uploaded_file($fileInfo_pVar['tmp_name'][0], $target_dir_pVar . $target_name_pVar) === false) {
    			return(false);
    		}
    	}
	    	
	    // precitam copyright z requestu
	    $copyright_pVar = main_gCLass::getInputString_gFunc('file' . '_copyright');
	    if(empty($copyright_pVar)) {
	    	$copyright_pVar = false;
	    }
	    
	    $import_pVar = import_gClass::loadUpoadedFile_gFunc($target_dir_pVar . $target_name_pVar);
	    $info_pVar = $import_pVar->getInfo_gFunc();

	    $sql_pVar = 'INSERT INTO `%timport_log`
	    				(`user_id`, `import_datetime`, `export_comment`, `export_source`, `tmp_file`, `export_settings`)
	    				VALUES (%d, now(), %s, %s, %s, %s)';
	    $sql_params_pVar = array();
	    $sql_params_pVar[] = session_gClass::getUserDetail_gFunc('user_id');
	    $sql_params_pVar[] = isset($info_pVar['comment'])?$info_pVar['comment']:'';
	    $sql_params_pVar[] = $copyright_pVar;
	    $sql_params_pVar[] = $target_name_pVar;
	    unset($info_pVar['comment']);
	    foreach ($info_pVar as $k_pVar=>$v_pVar)
	    {
	    	$info_pVar[$k_pVar] = $k_pVar . '=' . $v_pVar;
	    }
	    $sql_params_pVar[] = implode(NL, $info_pVar);
	    
	    $import_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar, true);
	    
	    
		$tmp_dir_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
		$tmp_dir_pVar .= '.tmp/';
		
	    $import_pVar = import_gClass::loadUpoadedFile_gFunc($target_dir_pVar . $target_name_pVar);
	    if(!$import_pVar) {
	    	return(false);
	    }
	    $import_pVar->import_gFunc();
	    return(true);
	}

}

class import_form extends import_form_gClass {}


return(true);
