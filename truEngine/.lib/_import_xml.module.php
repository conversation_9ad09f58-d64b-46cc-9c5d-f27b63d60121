<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__import_xml_pVar'])) return(true);
$GLOBALS['file__import_xml_pVar']=true;


class import_xml_gClass extends import_gClass
{
	private $DOMDocumentObject;
	
	protected function __construct($fileName_pVar)
	{
		$this->DOMDocumentObject = null;
		parent::__construct($fileName_pVar);
	}
	
	protected function loadInfo_gFunc()
	{
		$this->info_pVar = array();
		
		$f = fopen($this->fileName_pVar, 'rt');
		if(!$f) {
			return(false);
		}
		
		$str_pVar = '';
		while(1) {
			$str_pVar .= fgets($f, 1000);
			if(feof($f)) {
				break;
			}
			if(stripos($str_pVar, '</truEngine-export-settings>') !== false) {
				break;
			}
		}
		fclose($f);
		
		$pos_pVar = stripos($str_pVar, '</truEngine-export-settings>');
		if($pos_pVar === false) {
			return(false);
		}
		
		$str_pVar = substr($str_pVar, 0, $pos_pVar + 28);
		$str_pVar .= NL . '</truEngine-export>';
		
		$domDoc_pVar = $this->xmlParseString_gFunc($str_pVar);
		if($domDoc_pVar === false) {
			return(false);
		}
		

        $xpath = new DOMXPath($domDoc_pVar);
        $query = '//truEngine-export/truEngine-export-settings/*';
        $settings = $xpath->query($query);
        foreach ($settings as $setting) {
        	$this->info_pVar[$setting->nodeName] = $setting->nodeValue;
        }

        return($this->info_pVar);
	}
	
	public function _import_gFunc()
	{		
		$this->loadInfo_gFunc();
		
		$str_pVar = file_get_contents($this->fileName_pVar);
		$this->DOMDocumentObject = $this->xmlParseString_gFunc($str_pVar);
		unset($str_pVar);
		
		$xpath_pVar = new DOMXPath($this->DOMDocumentObject);

        $query_pVar = '//truEngine-export/truEngine-export-data/_fields/*';
        $recordsFields_pVar = $xpath_pVar->query($query_pVar);
        
        $fields_pVar = array();
        $data_pVar = array('data'=>array(), 'files'=>array());
        foreach($recordsFields_pVar as $rec_pVar) {
        	if($rec_pVar instanceof DOMCdataSection) {
        		$nodeType_pVar = XML_CDATA_SECTION_NODE;
            }
            else {
            	$nodeType_pVar = $rec_pVar->nodeType;
            }
            if($nodeType_pVar === XML_ELEMENT_NODE) {
            	$prefix_pVar = '';
            	$fieldName_pVar = $rec_pVar->nodeName;
            	$p_pVar = strpos($rec_pVar->nodeName, '.');
            	if($p_pVar !== false) {
            		$prefix_pVar = substr($rec_pVar->nodeName, 0, $p_pVar);
            		$fieldName_pVar = substr($rec_pVar->nodeName, $p_pVar + 1);
            	}
            	
            	if(!isset($fields_pVar[$prefix_pVar])) {
            		$fields_pVar[$prefix_pVar] = array();
            	}
            	$fields_pVar[$prefix_pVar][$fieldName_pVar] = $rec_pVar->nodeValue;
            }
		}
		//echo '<pre>'; print_r($fields_pVar); echo '</pre>';
        $query_pVar = '//truEngine-export/truEngine-export-data/*';
        $records_pVar = $xpath_pVar->query($query_pVar);
		
        foreach($records_pVar as $rec_pVar) {
            if($rec_pVar instanceof DOMCdataSection) {
        		$nodeType_pVar = XML_CDATA_SECTION_NODE;
            }
            else {
            	$nodeType_pVar = $rec_pVar->nodeType;
            }
            if($nodeType_pVar !== XML_ELEMENT_NODE) {
            	continue;
            }
            if($rec_pVar->nodeName !== 'rec') {
            	continue;
            }
            
            $recData_pVar = array();
            
        	if($rec_pVar->hasAttributes()) {
        		$attrList_pVar = $rec_pVar->attributes;
        		foreach($attrList_pVar as $attrName_pVar=>$attrValue_pVar) {
        			if(isset($fields_pVar[''][$attrValue_pVar->nodeName])) {
	        			$recData_pVar[$attrValue_pVar->nodeName] = $attrValue_pVar->nodeValue;
        			}
        		}
            }
            
		    if($rec_pVar->firstChild) {
                $crec_pVar =& $rec_pVar->firstChild;
                while(1) {
                    if($crec_pVar instanceof DOMCdataSection) {
                        $cnodeType_pVar = XML_CDATA_SECTION_NODE;
                    }
                    else {
                        $cnodeType_pVar = $crec_pVar->nodeType;
                    }
                    if($cnodeType_pVar === XML_ELEMENT_NODE) {
                    	if(isset($fields_pVar[$crec_pVar->nodeName])) {
                    		// toto je vnoreny element.
                    		$recData_pVar[$crec_pVar->nodeName] = array();
                    		
                    		if($crec_pVar->firstChild) {
                    			$ccrec_pVar =& $crec_pVar->firstChild;
                    			while(1) {
				                    if($ccrec_pVar instanceof DOMCdataSection) {
				                        $ccnodeType_pVar = XML_CDATA_SECTION_NODE;
				                    }
				                    else {
				                        $ccnodeType_pVar = $ccrec_pVar->nodeType;
				                    }
				                    if($ccnodeType_pVar === XML_ELEMENT_NODE) {
				                    	if($ccrec_pVar->nodeName === 'rec') {
					                        $ccData_pVar = array();
				                    	    if($ccrec_pVar->hasAttributes()) {
								        		$attrList_pVar = $ccrec_pVar->attributes;
								        		foreach($attrList_pVar as $attrName_pVar=>$attrValue_pVar) {
								        			if(isset($fields_pVar[$crec_pVar->nodeName][$attrValue_pVar->nodeName])) {
								        				$ccData_pVar[$attrValue_pVar->nodeName] = $attrValue_pVar->nodeValue;
								        			}
								        		}
								            }
								            
											if($ccrec_pVar->firstChild) {
								                $cccrec_pVar =& $ccrec_pVar->firstChild;
								                while(1) {
								                    if($cccrec_pVar instanceof DOMCdataSection) {
								                        $cccnodeType_pVar = XML_CDATA_SECTION_NODE;
								                    }
								                    else {
								                        $cccnodeType_pVar = $cccrec_pVar->nodeType;
								                    }
								                    if($cccnodeType_pVar === XML_ELEMENT_NODE) {
								                   		if(isset($fields_pVar[$crec_pVar->nodeName][$cccrec_pVar->nodeName])) {
								                   			if($fields_pVar[$crec_pVar->nodeName][$cccrec_pVar->nodeName] == 'data') {
					                    						$ccData_pVar[$cccrec_pVar->nodeName] = $cccrec_pVar->nodeValue;
								                   			}
								                   			elseif($fields_pVar[$crec_pVar->nodeName][$cccrec_pVar->nodeName] == 'file') {
							                    				$tmp_pVar = $this->parseFileElement_gFunc($cccrec_pVar);
							                    				if(is_array($tmp_pVar)) {
							                    					$ccData_pVar[$cccrec_pVar->nodeName] = $tmp_pVar['md5'];
							                    					if(isset($tmp_pVar['content'])) {
								                    					$data_pVar['files'][$tmp_pVar['md5']] = $tmp_pVar['content'];
							                    					}
							                    					unset($tmp_pVar);                    					
							                    				}
							                    			}
				                    					}
								                    }
								                    
							                    	if(!$cccrec_pVar->nextSibling) break;
							                    	$cccrec_pVar =& $cccrec_pVar->nextSibling;
								                }
											}
											
				                    	    if(count($ccData_pVar)) {
				                    			$recData_pVar[$crec_pVar->nodeName][] = $ccData_pVar;
				                    		}
				                    	}
				                    }
			                    	if(!$ccrec_pVar->nextSibling) break;
			                    	$ccrec_pVar =& $ccrec_pVar->nextSibling;
                    			}
                    		}
                    	}
                    	else {
                    		if(isset($fields_pVar[''][$crec_pVar->nodeName])) {
                    			if($fields_pVar[''][$crec_pVar->nodeName] == 'data') {
		                    		$recData_pVar[$crec_pVar->nodeName] = $crec_pVar->nodeValue;
                    			}
                    			elseif($fields_pVar[''][$crec_pVar->nodeName] == 'file') {
                    				$tmp_pVar = $this->parseFileElement_gFunc($crec_pVar);
                    				if(is_array($tmp_pVar)) {
                    					$recData_pVar[$crec_pVar->nodeName] = $tmp_pVar['md5'];
                    					if(isset($tmp_pVar['content'])) {
	                    					$data_pVar['files'][$tmp_pVar['md5']] = $tmp_pVar['content'];
                    					}
                    					unset($tmp_pVar);                    					
                    				}
                    			}
                    		}
                    	}
                    }
                    if(!$crec_pVar->nextSibling) break;
                    $crec_pVar =& $crec_pVar->nextSibling;
                }
		    }
		    
		    if(count($recData_pVar)) {
		    	$data_pVar['data'][] = $recData_pVar;
		    }		    
        }

        $data_pVar['data_type'] = $this->info_pVar['data_type'];
        $data_pVar['fields'] = $this->info_pVar['fields'];
        
        //echo '<pre>'; print_r($data_pVar); echo '</pre>';
		
        $this->_import_data_gFunc($data_pVar);
        return(true);        
	}
	
	private function parseFileElement_gFunc($element_pVar)
	{
		$ret_pVar = false;
		if($element_pVar->firstChild) {
			$el_pVar =& $element_pVar->firstChild;
			while(1) {
				if($el_pVar instanceof DOMCdataSection) {
					$nodeType_pVar = XML_CDATA_SECTION_NODE;
				}
				else {
					$nodeType_pVar = $el_pVar->nodeType;
				}
                if($nodeType_pVar === XML_ELEMENT_NODE) {
					if($el_pVar->nodeName == 'content') {
						if($el_pVar->hasAttributes()) {
			        		$attrList_pVar = $el_pVar->attributes;
			        		foreach($attrList_pVar as $attrName_pVar=>$attrValue_pVar) {
			        			if($attrValue_pVar->nodeName == 'md5') {
			        				$ret_pVar = array('md5'=>$attrValue_pVar->nodeValue);
			        			}
			        		}
			        		if(is_array($ret_pVar)) {
			        			$tmp_pVar = trim($el_pVar->nodeValue);
			        			if(!empty($tmp_pVar)) {
			        				$ret_pVar['content'] = $tmp_pVar;
			        			}
			        			return($ret_pVar);
			        		}
			            }
					}
                }
                if(!$el_pVar->nextSibling) break;
                $el_pVar =& $el_pVar->nextSibling;
			}
		}		
        return($ret_pVar);
	}
	
	private function xmlParseString_gFunc($xmlStr_pVar)
	{
        $xmlHeaderOk_pVar = false;
        // skonvertujem na utf-8, pretoze parser robi problemy
        $p0_pVar = strpos($xmlStr_pVar, '<'.'?');
        if($p0_pVar !== false) {
            $p1_pVar = strpos($xmlStr_pVar, '?'.'>', $p0_pVar);
            if($p1_pVar !== false) {
                $p1_pVar += 2;
                $x_pVar = substr($xmlStr_pVar, $p0_pVar, $p1_pVar - $p0_pVar);
                $x_pVar = strtolower($x_pVar);
                if(substr($x_pVar, 0, 5) == '<'.'?xml') {
                    $subpatterns_pVar = array();
                    if(preg_match('/(.*\s*encoding=[\"\']{1}){1}(.*)([\"\']{1}\s*.*){1}/i', $x_pVar, $subpatterns_pVar)) {
                        $xmlHeaderOk_pVar = true;
                        $this->sourceEncoding_pVar = $subpatterns_pVar[2];
                        $x_pVar = $subpatterns_pVar[1] . 'UTF-8' . $subpatterns_pVar[3];
                        if(strtolower($this->sourceEncoding_pVar) !== 'utf-8') {
                        	$xmlStr_pVar = iconv($this->sourceEncoding_pVar, 'UTF-8/'.'/TRANSLIT', $xmlStr_pVar);
                        	$x_pVar = iconv($this->sourceEncoding_pVar, 'UTF-8/'.'/TRANSLIT', $x_pVar);
                        }
                        $xmlStr_pVar = substr($xmlStr_pVar, 0, $p0_pVar) . $x_pVar . substr($xmlStr_pVar, $p1_pVar);
                    }
                }
            }
        }

        if(!$xmlHeaderOk_pVar) {
            error_gClass::error_gFunc(__FILE__,__LINE__, string_gClass::get('str__xml_encoding_sVar'));
            return(false);
        }
        
        $doc = new DOMDocument();
        if(!$doc->loadXML($xmlStr_pVar)) {
            return(false);
        }
        return($doc);
	}

}

return(true);
