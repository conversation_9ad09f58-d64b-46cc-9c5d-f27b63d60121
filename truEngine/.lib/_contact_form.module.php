<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__contact_form_pVar'])) return(true);
$GLOBALS['file__contact_form_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('forms')) {
	return(false);
}

class contact_form_gClass extends form_gClass
{
	function __construct($action_pVar = 'get')
	{
		parent::__construct($action_pVar);
	}

	protected  function handleAction($action_pVar = false)
	{
		if($action_pVar === false) {
			$action_pVar = 'get';
		}

		switch ($action_pVar) {
			case 'get':
				$data_pVar = $this->getData();
				break;
			default:
				$data_pVar = $this->getData();
				break;
		}
		return($data_pVar);
	}

	protected function initForm_gFunc()
	{
		$this->form_init_pVar = true;
		$this->addField_gFunc(0, 'name', 'text', string_gClass::get('str_contactform_label_name_pVar'), true);
		$this->addField_gFunc(0, 'email', 'email', string_gClass::get('str_contactform_label_email_pVar'), true);
		$this->addField_gFunc(0, 'phone', 'text', string_gClass::get('str_contactform_label_phone_pVar'), false);
		$this->addField_gFunc(0, 'company', 'text', string_gClass::get('str_contactform_label_company_pVar'), false);
		$this->addField_gFunc(0, 'subject', 'text', string_gClass::get('str_contactform_label_subject_pVar'), true);
		$this->addField_gFunc(0, 'message', 'textarea', string_gClass::get('str_contactform_label_message_pVar'), true);
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		if(isset($this->params['template'])) {
			$template_pVar = $this->params['template'];
		}
		else {
			$template_pVar = '/' . main_gClass::getLanguage_gFunc() . '/.emails/contact_mail';
		}

		list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = main_gClass::addLanguagePrefixToDocName_gFunc($template_pVar);
		if($prefixEnabled_pVar) {
			$template_pVar = $lngStr_pVar . '/' . $docx_pVar;
		}

		$vars_pVar = array('mail'=>array());
		foreach ($data_pVar['fieldsets']['0']['fields'] as $field_pVar) {
			$vars_pVar['mail'][$field_pVar['name']] = $field_pVar['value'];
		}
		$vars_pVar['mail']['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME');

		$templateContent_pVar = callStack_gClass::getDocContent_gFunc($template_pVar, $vars_pVar);

		$to_pVar = main_gClass::getConfigVar_gFunc('email', 'contacts');
		$from_pVar = $vars_pVar['mail']['email'];
		$subject_pVar = $vars_pVar['mail']['subject'];
		$message_pVar = $templateContent_pVar;
		email_gClass::mailHtml_gFunc($to_pVar, $from_pVar, $subject_pVar, $message_pVar);

		return($data_pVar);
	}

}

class contact_form extends contact_form_gClass
{

}

return(true);
