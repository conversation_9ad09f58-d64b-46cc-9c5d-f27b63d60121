<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__search_pVar'])) return(true);
$GLOBALS['file__search_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}

class search_gClass extends source_gClass
{
	protected function getData()
	{
		if ($this->action_pVar === 'form') {
			if(modules_gClass::isModuleRegistred_gFunc('forms')) {
				$form_pVar = new searchform_gClass('get', 'searchform');

				$form_pVar->handleAction_gFunc();
				return($form_pVar->getFormData_gFunc());
			}
		}
		elseif($this->action_pVar === 'search' || $this->action_pVar === 'get') {
			if(modules_gClass::isModuleRegistred_gFunc('eshop') || modules_gClass::isModuleRegistred_gFunc('kega')) {
				if(main_gClass::getInputString_gFunc('reload', main_gClass::SRC_REQUEST_pVar, false, null) !== null) {
					// obnovim parametre zo session
					$searchCond_pVar = main_gClass::getSessionData_gFunc('search_cond');
					$this->params = string_gClass::stringToArray_gFunc($searchCond_pVar);
				}
				else {
					// ulozim parametre do session

					$searchCond_pVar = string_gClass::arrayToString_gFunc($this->params);
					main_gClass::setPhpSessionVar_gFunc('search_cond',$searchCond_pVar);
				}
				$products_pVar = db_eshop_gClass::searchProducts_gFunc($this->params);
				return($products_pVar);
			}
		}
		return(array());
	}
}

class searchform_gClass extends form_gClass
{
	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		$storedSearchCond_pVar = false;
		if(modules_gClass::isModuleRegistred_gFunc('eshop') && empty($storedSearchCond_pVar)) {
			$this->addField_gFunc('main', 'search-phrase', 'varchar', string_gClass::get('str__search_search_phrase_sVar'), false);
			items_gClass::initFormRef_gFunc('eshop', $this, array(), true);
		}
		if(modules_gClass::isModuleRegistred_gFunc('kega') && empty($storedSearchCond_pVar)) {
			$this->addField_gFunc('main', 'search-phrase', 'varchar', string_gClass::get('str__search_search_phrase_sVar'), false);
			items_gClass::initFormRef_gFunc('test_questions', $this, array(), true);
		}

		/**
		 * Inicializujem formular.
		 * Ale aj ke pritomny parameter request:reload a nastavena hodnota v session, tak zavolam $this->forceOk_gFunc();
		 */
		if(main_gClass::getInputString_gFunc('reload', main_gClass::SRC_REQUEST_pVar, false, null) !== null) {
			$storedSearchCond_pVar = main_gClass::getSessionData_gFunc('search_cond', null);
			if(!empty($storedSearchCond_pVar)) {
				$this->forceOk_gFunc();
				$this->setVar_gFunc('search_cond', $storedSearchCond_pVar);
			}
		}

		$this->setVar_gFunc('submit_button_title', string_gClass::get('str__search_search_submit_sVar'));
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}
		if(main_gClass::getInputString_gFunc('reload', main_gClass::SRC_REQUEST_pVar, false, null) !== null) {
			return($data_pVar);
		}

		$searchCond_pVar = array();
		$searchCond_pVar['_operator'] = 'AND';
		foreach ($data_pVar['fieldsets'] as $fieldset_pVar) {
			foreach ($fieldset_pVar['fields'] as $field_pVar) {
				if(!$this->fieldIsEmpty_gFunc($field_pVar['name'])) {
					$useName_pVar = $this->removeFieldNamePrefix_gFunc($field_pVar['name']);
					if($field_pVar['type'] === 'set') {
						$searchCond_pVar[$useName_pVar] = $field_pVar['multi_value'];
						$searchCond_pVar[$useName_pVar]['_operator'] = 'OR';
						$searchCond_pVar[$useName_pVar]['_key'] = $useName_pVar;
					}
					elseif (substr($field_pVar['type'], -8) === 'Interval') {
						$value_pVar = array();
						if(strlen($field_pVar['multi_value']['min'])) {
							$value_pVar[] = array($useName_pVar=>$field_pVar['multi_value']['min'], '__operator'=>'>=');
						}
						if(strlen($field_pVar['multi_value']['max'])) {
							$value_pVar[] = array($useName_pVar=>$field_pVar['multi_value']['max'], '__operator'=>'<=');
						}
						if(count($value_pVar)) {
							$value_pVar['_operator'] = 'AND';
							$searchCond_pVar[] = $value_pVar;
						}
					}
					elseif($field_pVar['type'] === 'varchar' || $field_pVar['type'] === 'text') {
						if($field_pVar['name'] === 'search-phrase') {
							$searchCond_pVar['search-phrase'] = $field_pVar['value'];
						}
						else {
							$value_pVar = array();
							$value_pVar['__operator'] = 'LIKE';
							$value_pVar[$useName_pVar] = $field_pVar['value'];
							$searchCond_pVar[] = $value_pVar;
						}
					}
					else {
							$searchCond_pVar[$useName_pVar] = $field_pVar['value'];
					}
				}
			}
		}

		$searchCond_pVar = string_gClass::arrayToString_gFunc($searchCond_pVar,true);

		$this->setVar_gFunc('search_cond', $searchCond_pVar);
		$data_pVar = $this->getFormData_gFunc();
		return($data_pVar);
	}
}

class search extends search_gClass
{

}

class searchFilter_gClass extends source_gClass
{
	private $userListAdded_pVar;
	private $filter_users_list;
	
	function __construct($action_pVar = 'get')
	{
		$this->filter_users_list = false;
		parent::__construct($action_pVar);
	}
	
	protected function getData()
	{
		$this->userListAdded_pVar = false;
		$data_pVar = array();
		$data_pVar['js'] = '';

		if(!modules_gClass::isModuleRegistred_gFunc('items'))
		{
		    return($data_pVar);
		}
		if(!isset($this->params['items'])) {
			return($data_pVar);
		}
		
		if(!isset($this->params['html_id'])) {
			return($data_pVar);
		}
		$html_id_pVar = $this->params['html_id'];
		
		if(!isset($this->params['request'])) {
			$defaults_pVar = array();
		}
		else {
			$tmp_pVar = str_replace('%', '%25', $this->params['request']);
			parse_str($tmp_pVar, $defaults_pVar);
		}

		$data_pVar['order_fields'] = array();
		
		$enum_fields_ids_pVar = array();
		$extra_values_pVar =array();
		
		$itemAdapter_pVar = items_gClass::getAdapter_gFunc($this->params['items']);
		$fields_pVar = $itemAdapter_pVar->getItemsFilterFields_gFunc();
		$this->initFields_gFunc($data_pVar, $fields_pVar, $html_id_pVar, $itemAdapter_pVar, $defaults_pVar);
		if($this->params['items'] == 'test_questions') {
			$itemAdapter_pVar = items_gClass::getAdapter_gFunc('test_answers');
			$fields_pVar = $itemAdapter_pVar->getItemsFields_gFunc();
			unset($fields_pVar['status']);
			unset($fields_pVar['owner_id']);
			$this->initFields_gFunc($data_pVar, $fields_pVar, $html_id_pVar, $itemAdapter_pVar, $defaults_pVar, 'answers_');
		}

		
		
		$data_pVar['js'] .= 'filter_display(\''.$html_id_pVar.'\'); filter_set_request(\''.$html_id_pVar.'\'); ';
		
		return($data_pVar);
	}
	
	private function getUserlist_gFunc($cacheEnabled_pVar = true)
	{
		if($this->filter_users_list !== false) {
			return($this->filter_users_list);
		}

		$sql_pVar = 'SELECT * FROM `%titems_users__data` WHERE status<>\'deleted\' AND status<>\'request\' ORDER BY last_name,first_name,nick';
		$this->filter_users_list = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		//$this->filter_users_list = items_gClass::getItems_gFunc('users', array('_order_by'=>'last_name,first_name,nick', 0=>array('__operator'=>'<>', 'status'=>'request')), false);
		return($this->filter_users_list);
	}
	
	private function initFields_gFunc(&$data_pVar, $fields_pVar, $html_id_pVar, $itemAdapter_pVar, $defaults_pVar, $prefix_pVar = '')
	{
		$enum_fields_ids_pVar = array();
		
		foreach($fields_pVar as $field_pVar) {
			$lngPrefix_pVar = ''; //main_gClass::getLanguage_gFunc() . '_';
			$data_pVar['order_fields'][$field_pVar['tag']] = $field_pVar[main_gClass::getLanguage_gFunc().'_name'];
			if(substr($field_pVar['tag'], 0, 8) == 'password') {
				continue;
			}
			elseif($field_pVar['type'] == 'enum') {
				$data_pVar['js'] .= 'filter_add_option(\''.$html_id_pVar.'\', \''. $prefix_pVar . $field_pVar['tag'].'\', \''.$field_pVar['type'].'\', \''.$field_pVar[main_gClass::getLanguage_gFunc().'_name'].'\');';
				$enum_fields_ids_pVar[$prefix_pVar . $field_pVar['tag']] = $field_pVar['field_id'];
			}
			elseif($field_pVar['type'] == 'user_id') {
				//$tmp_users_pVar = items_gClass::getItems_gFunc('users', array('_order_by'=>'last_name,first_name,nick', 0=>array('__operator'=>'<>', 'status'=>'request')));
				$tmp_users_pVar = self::getUserlist_gFunc();
				foreach($tmp_users_pVar as $tmp_user_pVar) {
					if(!isset($tmp_user_pVar['item_id'])) {
						continue;
					}
					$tmp_pVar = array();
		    		if(!empty($tmp_user_pVar['last_name'])) {
		    			$tmp_pVar[] = $tmp_user_pVar['last_name'];
		    		}
					if(!empty($tmp_user_pVar['first_name'])) {
		    			$tmp_pVar[] = $tmp_user_pVar['first_name'];
		    		}
					$tmp_pVar = implode(' ', $tmp_pVar);
										
		    		if(!empty($tmp_user_pVar['titul_pred'])) {
		    			$tmp_pVar .= ', ' .$tmp_user_pVar['titul_pred'];
		    		}
		    		
		    		if(!empty($tmp_pVar)) {
			    		if(!empty($tmp_user_pVar['titul_za'])) {
			    			$tmp_pVar .= ', ' . $tmp_user_pVar['titul_za'];
			    		}
		    		}
		    		$tmp_pVar .= ' ('.$tmp_user_pVar['nick'].')';
		    		$users_pVar[$tmp_user_pVar['item_id']] = $tmp_pVar;
				}
				
				$data_pVar['js'] .= 'filter_add_option(\''.$html_id_pVar.'\', \''.$prefix_pVar . $field_pVar['tag'].'\', \''.$field_pVar['type'].'\', \''.$field_pVar[main_gClass::getLanguage_gFunc().'_name'].'\');';
				
				if(isset($defaults_pVar[$prefix_pVar . $field_pVar['tag']])) {
					$default_value_pVar = explode('|', $defaults_pVar[$prefix_pVar . $field_pVar['tag']]);
					foreach($default_value_pVar as $v_pVar) {
						$data_pVar['js'] .= 'filter_set(\''.$html_id_pVar.'\', \''.$prefix_pVar . $field_pVar['tag'].'\', \''.$v_pVar.'\', true, \''.$users_pVar[$v_pVar].'\', false);';
					}
				}				
				
				if(!$this->userListAdded_pVar && isset($users_pVar)) {
					$data_pVar['js'] .= 'filters[\''.$html_id_pVar.'\'].filter_user_ids = new Object();';
					foreach($users_pVar as $user_id_pVar=>$user_pVar) {
						$data_pVar['js'] .= 'filter_add_enums(\''.$html_id_pVar.'\', \':user_ids\', \''.$user_id_pVar.'\', \''.$user_pVar.'\');';
					}					
					$this->userListAdded_pVar = true;
				}
			}
			elseif($field_pVar['tag'] == 'last_name' && $this->params['items'] == 'users') {
				$data_pVar['js'] .= 'filter_add_option(\''.$html_id_pVar.'\', \''.$prefix_pVar . $field_pVar['tag'].'\', \'enum\', \''.$field_pVar[main_gClass::getLanguage_gFunc().'_name'].'\');';
				for($i_pVar = ord('A'); $i_pVar <= ord('Z'); $i_pVar++) {
					$extra_values_pVar[] = array('enum_field_id'=>$field_pVar['field_id'], 'enum_field_value'=>'LIKE('.strtolower(chr($i_pVar)) . '%)', main_gClass::getLanguage_gFunc().'_enum_field_name_item'=>chr($i_pVar));
				}
				$enum_fields_ids_pVar[$prefix_pVar . $field_pVar['tag']] = $field_pVar['field_id'];
			}
			elseif($field_pVar['tag'] == 'user_groups' && $this->params['items'] == 'users') {
				if(modules_gClass::isModuleRegistred_gFunc('access4_groups')) {
					$data_pVar['js'] .= 'filter_add_option(\''.$html_id_pVar.'\', \''.$prefix_pVar . $field_pVar['tag'].'\', \'enum\', \''.$field_pVar[main_gClass::getLanguage_gFunc().'_name'].'\');';
					$groups_pVar = db_access4_groups_gClass::getGroups_gFunc();
					foreach($groups_pVar as $k_pVar=>$v_pVar) {
						$extra_values_pVar[] = array('enum_field_id'=>$field_pVar['field_id'], 'enum_field_value'=>'LIKE(%,'. $v_pVar['group_id'] . ',%)', main_gClass::getLanguage_gFunc().'_enum_field_name_item'=>$v_pVar['group_name']);
					}
					$enum_fields_ids_pVar[$prefix_pVar . $field_pVar['tag']] = $field_pVar['field_id'];
				}
			}
			elseif($field_pVar['type'] == 'text'
					|| $field_pVar['type'] == 'xtext'
					|| $field_pVar['type'] == 'varchar'
					|| $field_pVar['type'] == 'xvarchar'
				) {
				if($field_pVar['type'][0] == 'x') {
					$lngPrefix_pVar = '';
				}
				$data_pVar['js'] .= 'filter_add_option(\''.$html_id_pVar.'\', \''. $prefix_pVar . $lngPrefix_pVar . $field_pVar['tag'].'\', \''.$field_pVar['type'].'\', \''.$field_pVar[main_gClass::getLanguage_gFunc().'_name'].'\');';
				
				if(isset($defaults_pVar[$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag']])) {
					if(substr($defaults_pVar[$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag']], 0, 6) == 'LIKE(%') {
						$defaults_pVar[$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag']] = substr($defaults_pVar[$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag']], 6, -2);
					}
					//$default_value_pVar = explode('|', $defaults_pVar[$prefix_pVar . $field_pVar['tag']]);
					$data_pVar['js'] .= 'filter_set(\''.$html_id_pVar.'\', \''.$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag'].'\', \'value\', \''.$defaults_pVar[$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag']].'\', \''.$defaults_pVar[$prefix_pVar . $lngPrefix_pVar . $field_pVar['tag']].'\', false);';
				}
			}
		}

		$data_pVar['js'] .= 'filter_add_option(\''.$html_id_pVar.'\', \'selected_items\', \'enum\', \'Označené\');';
		$extra_values_pVar[] = array('enum_field_id'=>0, 'enum_field_value'=>'yes', main_gClass::getLanguage_gFunc().'_enum_field_name_item'=>'iba označené');
		$enum_fields_ids_pVar['selected_items'] = 0;
		
		$values_pVar = array();
		if(count($enum_fields_ids_pVar) > 1) {
			$values_pVar = $itemAdapter_pVar->getEnumFieldsValues_gFunc('enum_field_id in (%ad)', array($enum_fields_ids_pVar));
		}
		$values_pVar = array_merge($values_pVar, $extra_values_pVar);
		foreach($values_pVar as $value_pVar) {
			$field_tag_pVar = array_search($value_pVar['enum_field_id'], $enum_fields_ids_pVar);
			$data_pVar['js'] .= 'filter_add_enums(\''.$html_id_pVar.'\', \''.$field_tag_pVar.'\', \''.$value_pVar['enum_field_value'].'\', \''.$value_pVar[main_gClass::getLanguage_gFunc().'_enum_field_name_item'].'\');';
			if(isset($defaults_pVar[$field_tag_pVar])) {
				$default_value_pVar = explode('|', $defaults_pVar[$field_tag_pVar]);
				if(array_search($value_pVar['enum_field_value'], $default_value_pVar) !== false) {
					$data_pVar['js'] .= 'filter_set(\''.$html_id_pVar.'\', \''.$field_tag_pVar.'\', \''.$value_pVar['enum_field_value'].'\', true, \''.$value_pVar[main_gClass::getLanguage_gFunc().'_enum_field_name_item'].'\', false);';
				}
			}
		}
	}
}

class searchFilter extends searchFilter_gClass {}

return(true);
