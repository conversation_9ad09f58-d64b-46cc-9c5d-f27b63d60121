<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__sitemap_pVar'])) return(true);
$GLOBALS['file__sitemap_pVar']=true;


class sitemapBuilder_gClass
{
	public $siteMap_pVar;
	
	function buildData_gFunc()
	{
		$this->siteMap_pVar = array();
		$doc_root_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
		$this->siteMap_pVar[] = $doc_root_pVar;

		$this->siteMap_pVar['childs'] = $this->processFolder_gFunc($doc_root_pVar);
	}
	
	function processFolder_gFunc($path_pVar, $docPath_pVar = '', $level_pVar = 0)
	{
		$dir_pVar = dir($path_pVar);
		$ret_pVar = array();
		
		$sitemapRules_pVar = array(
			'ignore' => array()
		);
		$sitemapRulesPath_pVar = $path_pVar . '.sitemap';
		if(file_exists($sitemapRulesPath_pVar)) {
			$rules_pVar = file($sitemapRulesPath_pVar);
			if(is_array($rules_pVar)) {
				foreach($rules_pVar as $rule_pVar) {
					$tmp_pVar = explode(' ', $rule_pVar, 2);
					if(count($tmp_pVar) == 2) {
						$tmp_pVar[0] = strtolower(trim($tmp_pVar[0]));
						$tmp_pVar[1] = trim($tmp_pVar[1]);
						$sitemapRules_pVar[$tmp_pVar[0]][] = $tmp_pVar[1];
					}
				}
			}
		}
		
		while (($entry_pVar = $dir_pVar->read()) !== false)
		{
			if(empty($entry_pVar) || $entry_pVar[0] === '.') {
				continue;
			}
			
			if(in_array($entry_pVar, $sitemapRules_pVar['ignore'])) {
				continue;
			}
			
			$is_dir_pVar = is_dir($path_pVar . $entry_pVar);
			$data_pVar = array();
			$data_pVar['fileName'] = $entry_pVar;
			$data_pVar['type'] = $is_dir_pVar ? 'DIR':'DOC';
			
			if($is_dir_pVar) {
				$data_pVar['childs'] = $this->processFolder_gFunc($path_pVar . $entry_pVar . '/', $docPath_pVar . $entry_pVar . '/', $level_pVar + 1);
				
				$data_pVar['title'] = $data_pVar['fileName'];
			}
			else {
				$data_pVar['childs'] = array();
				$info_pVar = $this->read_doc_info_gFunc($path_pVar . $entry_pVar);
				foreach($info_pVar as $k_pVar=>$v_pVar) {
					$data_pVar[$k_pVar] = $v_pVar;
				}
				if(!isset($data_pVar['title']) || empty($data_pVar['title'])) {
					$data_pVar['title'] = $data_pVar['fileName'];
				}
				$data_pVar['src'] = $docPath_pVar . substr($data_pVar['fileName'], 0, -8);
			}
			
			$ret_pVar[$entry_pVar] = $data_pVar;
		}
		$dir_pVar->close();
		return($ret_pVar);
	}
	
    private function read_doc_info_gFunc($fileName_pVar)
    {
    	$elements_pVar = array('rights'=>1, 'title'=>1);
    	$info_pVar = array();
        if(file_exists($fileName_pVar)) {
        	$f_pVar = fopen($fileName_pVar, 'rt');
        	$tmp_pVar = '';
        	while(!feof($f_pVar) && count($elements_pVar)) {
	        	$tmp_pVar .= fread($f_pVar, 500);
	        	if(isset($elements_pVar['rights'])) {
		        	if(preg_match('/.*(<truEngine-document[^>]*>)/i', $tmp_pVar, $matches_pVar)) {
		        		unset($elements_pVar['rights']);
		        	    if(preg_match('/.*<truEngine-document\s[^>]*te:access=["\']([^"\']+)["\'].*/i', $matches_pVar[1], $matches_pVar)) {
		        			$info_pVar['rights'] = $matches_pVar[1];
		        			//@TODO: toto zoptimalizovat -  ak ide o klasicke prava bez podmienok (neobsahuje vseliake znaky), tak nemusim to riesit cez enum, ale rovno volat session_gClass::userHasRightsInfo_gFunc 
		        			$str_pVar = string_gClass::parseAccessString_gFunc($info_pVar['rights']);
		        			$str_pVar = 'if(' . $str_pVar . ') { $enumRes_pVar = true; } else { $enumRes_pVar = false; }';
		        			eval($str_pVar);
		        			if(!$enumRes_pVar) {
		        				fclose($f_pVar);
		        				return(array('access_denied'=>true));
		        			}
		        		}
		        	}
	        	}
	        	
	        	if(isset($elements_pVar['title'])) {		        	
		        	if(preg_match('/.*(<te:content-set\s[^>]*name="title"[^>]*>)/i', $tmp_pVar, $matches_pVar)) {
		        		unset($elements_pVar['title']);
		        		if(preg_match('/.*<te:content-set\s[^>]*value="([^"]*)"[^>]*>/i', $matches_pVar[1], $matches_pVar)) {
		        			$info_pVar['title'] = $matches_pVar[1];
		        		}		        		
		        	}
	        	}
        	}
        	fclose($f_pVar);
        }
        return($info_pVar);
    }
}

class sitemap_gClass extends source_gClass
{
	function getData()
	{
		$siteMap_pVar = new sitemapBuilder_gClass();
		$siteMap_pVar->buildData_gFunc();
		
		return($siteMap_pVar->siteMap_pVar);
	}
}

class sitemap extends sitemap_gClass {}

return(true);
