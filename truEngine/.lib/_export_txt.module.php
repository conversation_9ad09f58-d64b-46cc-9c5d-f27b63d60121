<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__export_txt_pVar'])) return(true);
$GLOBALS['file__export_txt_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('export'))
{
    return(false);
}

class export_txt_gClass extends export_gClass
{

	function __construct($settings_pVar)
	{
		parent::__construct($settings_pVar);
		$this->right_pVar = s_system_export_data_txt;
	}

	protected function export_open_gFunc()
	{
		$this->result_pVar->echo_gFunc('charset: ' . $this->settings_pVar['encoding'] . NL);
		$this->result_pVar->echo_gFunc('data: ' . $this->settings_pVar['data_type'] . NL);
		$this->result_pVar->echo_gFunc('timestamp: ' . $this->settings_pVar['timestamp'] . NL);
		$this->result_pVar->echo_gFunc('language: ' . $this->settings_pVar['languages'] . NL);
		$this->result_pVar->echo_gFunc('comment: ' . $this->comment_pVar . NL);
		return(true);
	}

	protected function export_close_gFunc()
	{
		return(true);
	}

	protected function export_data_gFunc()
	{
		$data_pVar = $this->getData_gFunc();

		$fields_pVar = implode(',', array_keys($data_pVar['_fields']));

		$this->result_pVar->echo_gFunc('fields:' . $fields_pVar . NL);
		$this->writeTxt_gFunc($data_pVar);
		return(true);
	}


	/**
	 * Funkcia zapisuje TXT priamo do BUFFRU.
	 * @param $data_pVar
	 * @return unknown_type
	 */
	private function writeTxt_gFunc(&$data_pVar, $nTabs_pVar = 0, $addPrefix_pVar = '')
	{
		if($nTabs_pVar === 0) {
			$prefixValue_pVar = 0;
		}
		elseif($nTabs_pVar === 1) {
			$prefixValue_pVar = ord('A') - 1;
		}
		elseif($nTabs_pVar === 2) {
			$prefixValue_pVar = '-';
		}
		else {
			$prefixValue_pVar = '*';
		}

		foreach($data_pVar as $k_pVar=>$v_pVar) {
			if(substr($k_pVar, 0, 1) === '_') {
				continue;
			}

			if($nTabs_pVar === 0 || $nTabs_pVar === 1) {
				$prefixValue_pVar++;
			}
			$prefix_tab_pVar = str_repeat(TAB, $nTabs_pVar);
			if($nTabs_pVar === 0) {
				$prefix_pVar = sprintf('%d. ' ,$prefixValue_pVar);
			}
			elseif($nTabs_pVar === 1) {
				$prefix_pVar = chr($prefixValue_pVar) . ') ';
			}
			else {
				$prefix_pVar = $prefixValue_pVar . ' ';
			}

			if($nTabs_pVar === 0) {
				$this->result_pVar->echo_gFunc(NL);
			}

			$nFields_pVar = count($v_pVar);
			foreach($v_pVar as $vv_pVar) {
				if(is_array($vv_pVar)) {
					$nFields_pVar--;
				}
			}
			$i_pVar = 0;
			$str_pVar = '';
			foreach($v_pVar as $kk_pVar=>$vv_pVar) {
				$str_pVar .= $prefix_tab_pVar;
				if(!$i_pVar) {
					$str_pVar .= $prefix_pVar;
				}
				else {
					$str_pVar .= str_repeat(' ', strlen($prefix_pVar));
				}
				if($nFields_pVar != 1) {
					//$str_pVar .= '(' . $kk_pVar . ') ';
				}
				if(!is_array($vv_pVar)) {
					$vv_pVar = trim($vv_pVar);
					if(empty($vv_pVar)) {
						$vv_pVar = '-';
					}
					$str_pVar .= $vv_pVar;
					$this->result_pVar->echo_gFunc(NL . $str_pVar);
				}
				else {
					$this->writeTxt_gFunc($vv_pVar, $nTabs_pVar + 1, str_repeat(' ', strlen($prefix_pVar)));
				}
				$str_pVar = '';
				$i_pVar++;
			}


			if($nTabs_pVar === 0) {
//				$this->result_pVar->echo_gFunc(NL);
			}
		}
	}

}


return(true);
