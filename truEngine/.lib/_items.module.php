<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__items_pVar'])) return(true);
$GLOBALS['file__items_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}



/**
 * FILTER:
 * 		Filter je vlastne polozka s nastavenymi niektorymi vlastnostami. Filter zodpoveda tym polozkam,
 * 		v ktorych sa zhodne.
 * 		Do filtra mozem natavit specialne hodnoty:
 * 			- url - pred spracovanim sa rozlozi na prislusne hodnoty
 * 			- filter - je to retazec v tvare premenna=hodnota&premenna=hodnota&... Tiez sa rozlozi pred spracovanim.
 *
 */

class itemsCustomField
{

}

class itemsCustomField_filelist extends itemsCustomField
{

}

return(true);
