<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__https_pVar'])) return(true);
$GLOBALS['file__https_pVar']=true;

class https_gClass extends truEngineBaseClass_gClass
{
    public static function check_gFunc($redirect_pVar=true)
    {
        if(!main_gClass::getConfigVar_gFunc('enabled', 'https')) return(true);

        if(!main_gClass::getInputBoolean_gFunc('https')) return(true);

        $port_pVar = main_gClass::getConfigVar_gFunc('port', 'https');

        if(main_gClass::getServerVar_gFunc('SERVER_PORT') != $port_pVar) {
        	if($redirect_pVar) {
        	    //main_gClass::phpSessionStart_gFunc();
        	    main_gClass::setPhpSessionVar_gFunc('regenerate_php_session', true);
        	    $location_pVar = 'https://'. main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getServerVar_gFunc('PHP_SELF').'?' . main_gClass::getServerVar_gFunc('QUERY_STRING');
        	    log_gClass::write_gFunc('HTTPS_REDIRECT', $location_pVar);
        		Header('Location: ' . $location_pVar . LF);

        		db_gClass::Close();
        	}
        	else {
        	    return(false);
        	}
        	main_gClass::terminate_gFunc();
        	exit;
        }
        return(true);
    }

    public static function secureSession_gFunc()
    {
        session_set_cookie_params(0, main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar'), null, true, true);
    }

    public static function enabled_gFunc()
    {
        if(!main_gClass::getConfigVar_gFunc('enabled', 'https')) {
            return(false);
        }
        else {
            return(true);
        }
    }
}

return(true);
