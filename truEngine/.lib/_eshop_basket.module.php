<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__eshop_basket_pVar'])) return(true);
$GLOBALS['file__eshop_basket_pVar']=true;

class eshop_basket_gClass extends source_gClass
{
	protected  function handleAction($action_pVar = false)
	{
		if($action_pVar === false) {
			$action_pVar = 'get';
		}

		switch ($action_pVar) {
			case 'get':
				$data_pVar = $this->getData();
				break;
			case 'add':
				$data_pVar = $this->addItems_gFunc();
				break;
			case 'update':
				$data_pVar = $this->updateItems_gFunc();
				break;
			default:
				$data_pVar = $this->getData();
				break;
		}
		$this->setResults_gFunc($data_pVar);
		return($data_pVar);
	}

	protected function getData()
	{
		$basket_data_pVar = $this->getBasketData_gFunc();
		$this->recount_gFunc($basket_data_pVar);
		$this->setResults_gFunc($basket_data_pVar);
		return($basket_data_pVar);
	}

	protected function setResults_gFunc(&$data_pVar)
	{
		if(!isset($data_pVar['result_added'])) {
			$data_pVar['result_added'] = 0;
		}
		if(!isset($data_pVar['result_deleted'])) {
			$data_pVar['result_deleted'] = 0;
		}
		if(!isset($data_pVar['result_updated'])) {
			$data_pVar['result_updated'] = 0;
		}
	}

	protected function updateItems_gFunc()
	{
		// add items
		$n_added_pVar = 0;
		$product_id_pVar = main_gClass::getInputInteger_gFunc('basket_add_item', main_gClass::SRC_REQUEST_pVar, false);
		if($product_id_pVar) {
			$n_added_pVar = $this->addItems_gFunc(false);
		}

		$basket_data_pVar = $this->getBasketData_gFunc();

		// update items
		$n_updated_pVar = 0;
		$n_deleted_pVar = 0;
		$varNames_pVar = main_gClass::getInputVarNames_gFunc(main_gClass::SRC_REQUEST_pVar);
		foreach ($varNames_pVar as $v_pVar) {
			if($v_pVar === 'basket_transport') {
				continue;
			}
			if(substr($v_pVar, 0, 14) != 'basket_update_') {
				continue;
			}
			$item_id_pVar = intval(substr($v_pVar, 14));
			if(!$item_id_pVar) {
				continue;
			}
			if(!isset($basket_data_pVar['rows'][$item_id_pVar])) {
				continue;
			}
			$quantity_pVar = main_gClass::getInputFloat_gFunc($v_pVar, main_gClass::SRC_REQUEST_pVar, 0);
			if(!$quantity_pVar) {
				unset($basket_data_pVar['rows'][$item_id_pVar]);
				$n_deleted_pVar++;
			}
			else {
				$basket_data_pVar['rows'][$item_id_pVar]['quantity'] = $quantity_pVar;
				$n_updated_pVar++;
			}
		}

		// delete items
		foreach ($varNames_pVar as $v_pVar) {
			if($v_pVar === 'basket_delete_all') {
				continue;
			}
			if($v_pVar === 'basket_transport') {
				continue;
			}
			if(substr($v_pVar, 0, 14) != 'basket_delete_') {
				continue;
			}
			$item_id_pVar = intval(substr($v_pVar, 14));
			if(!$item_id_pVar) {
				continue;
			}
			if(!isset($basket_data_pVar['rows'][$item_id_pVar])) {
				continue;
			}
			unset($basket_data_pVar['rows'][$item_id_pVar]);
			$n_deleted_pVar++;
		}

		// delete all
		$delete_all_pVar = main_gClass::getInputString_gFunc('basket_delete_all', main_gClass::SRC_REQUEST_pVar, false, 0);
		if(!empty($delete_all_pVar)) {
			$n_deleted_pVar += count($basket_data_pVar['rows']);
			$basket_data_pVar = $this->deleteBasket_gFunc();
		}

		// update transport
		$basket_transport_pVar = main_gClass::getInputInteger_gFunc('basket_transport', main_gClass::SRC_REQUEST_pVar, false, 0);
		if($basket_transport_pVar > 0) {
			$basket_data_pVar['transport'] = intval($basket_transport_pVar);
		}

		$this->recount_gFunc($basket_data_pVar);
		$this->setBasketData_gFunc($basket_data_pVar);
		//echo '<pre>'; print_r($basket_data_pVar); echo '</pre>';

		if($n_added_pVar) {
			$basket_data_pVar['result_added'] = $n_added_pVar;
		}
		elseif ($n_deleted_pVar) {
			$basket_data_pVar['result_deleted'] = $n_deleted_pVar;
		}
		elseif ($n_updated_pVar) {
			$basket_data_pVar['result_updated'] = $n_updated_pVar;
		}

		return($basket_data_pVar);
	}

	protected function addItems_gFunc($getData_pVar = true)
	{
		$product_id_pVar = main_gClass::getInputInteger_gFunc('basket_add_item', main_gClass::SRC_REQUEST_pVar, false);
		$quantity_pVar = main_gClass::getInputFloat_gFunc('basket_add_quantity', main_gClass::SRC_REQUEST_pVar, false);
		$x = main_gClass::getInputString_gFunc('basket_add_item', main_gClass::SRC_REQUEST_pVar, false);

		if(empty($product_id_pVar) || empty($quantity_pVar)) {
			$product_id_pVar = '';
			$quantity_pVar = '';
			/*
			if($getData_pVar) {
				$basket_data_pVar = $this->getData();
				$basket_data_pVar['result'] = 'get';
				return($basket_data_pVar);
			}
			return(0);
			*/
		}

		$basket_data_pVar = $this->getBasketData_gFunc();

		$product_id_pVar = explode(',', $product_id_pVar);
		$quantity_pVar = explode(',', $quantity_pVar);

		$inputVars_pVar = main_gClass::getInputVarNames_gFunc(main_gClass::SRC_REQUEST_pVar);
		foreach ($inputVars_pVar as $inputVar_gFunc)
		{
			if(substr($inputVar_gFunc, 0, strlen('basket_add_quantity_id_')) !== 'basket_add_quantity_id_') {
				continue;
			}
			$tmpProduct_id_pVar = (int)substr($inputVar_gFunc, strlen('basket_add_quantity_id_'));
			$tmpQuantity_pVar = main_gClass::getInputFloat_gFunc($inputVar_gFunc, main_gClass::SRC_REQUEST_pVar, false);
			if($tmpQuantity_pVar) {
				$product_id_pVar[] = $tmpProduct_id_pVar;
				$quantity_pVar[] = $tmpQuantity_pVar;
			}
		}


		$n_pVar = 0;
		foreach ($product_id_pVar as $k_pVar=>$v_pVar) {
			if(!isset($quantity_pVar[$k_pVar])) {
				continue;
			}
			$p_id_pVar = intval($v_pVar);

			$done_pVar = false;
			foreach ($basket_data_pVar['rows'] as $kk_pVar=>$vv_pVar) {
				if($vv_pVar['product_id'] !== $p_id_pVar) {
					continue;
				}
				$basket_data_pVar['rows'][$kk_pVar]['quantity'] += floatval($quantity_pVar[$k_pVar]);
				$basket_data_pVar['rows'][$kk_pVar]['price'] = $basket_data_pVar['rows'][$kk_pVar]['price_mj'] * $basket_data_pVar['rows'][$kk_pVar]['quantity'];
				if(!$basket_data_pVar['rows'][$kk_pVar]['quantity']) {
					unset($basket_data_pVar['rows'][$kk_pVar]);
				}
				$done_pVar = true;
				break;
			}
			if(!$done_pVar) {
				// polozka este nie je v kosiku
				if(modules_gClass::isModuleRegistred_gFunc('eshop')) {
					$itemData_pVar = eshop_gClass::getItem_gFunc($p_id_pVar);
				}
				else {
					$itemData_pVar = self::getItems_gFunc($p_id_pVar);
					if(count($itemData_pVar)) {
						$itemData_pVar = $itemData_pVar[0];
					}
				}
				if(isset($itemData_pVar) && is_array($itemData_pVar) && count($itemData_pVar)) {
						$item_pVar = array();
						$item_pVar['n'] = 0;
						$item_pVar['product_id'] = $p_id_pVar;
						$item_pVar['product_name'] = $itemData_pVar['name'];
						$item_pVar['quantity'] = floatval($quantity_pVar[$k_pVar]);
						$item_pVar['mj'] = 'ks';
						$item_pVar['price_mj'] = $itemData_pVar['price'];
						$item_pVar['price'] = $item_pVar['price_mj'] * $item_pVar['quantity'];
						$item_pVar['currency'] = $itemData_pVar['currency'];
						$basket_data_pVar['rows'][$p_id_pVar] = $item_pVar;
						$n_pVar++;
				}
			}
		}

		$this->recount_gFunc($basket_data_pVar);
		$this->setBasketData_gFunc($basket_data_pVar);

		if($getData_pVar) {
			if($n_pVar) {
				$basket_data_pVar['result_added'] = $n_pVar;
			}
			return($basket_data_pVar);
		}

		return($n_pVar);
	}

	protected function deleteBasket_gFunc($getData_pVar = true)
	{
		$basket_data_pVar = $this->getBasketData_gFunc();
		$n_pVar = count($basket_data_pVar['rows']);
		$basket_data_pVar = $this->getBasketData_gFunc(true);
		if($getData_pVar) {
			$basket_data_pVar['result_deleted'] = $n_pVar;
			return($basket_data_pVar);
		}
		return($n_pVar);
	}

	protected function recount_gFunc(&$data_pVar)
	{
		$n_pVar = 0;
		$i_pVar = 0;
		$price_pVar = 0;
		foreach ($data_pVar['rows'] as $k_pVar=>$v_pVar)
		{
			$i_pVar++;
			if(modules_gClass::isModuleRegistred_gFunc('currency')) {
				$price_pVar += priceFormat_gClass::convertPrice_gFunc($v_pVar['price_mj'], $v_pVar['currency'], main_gClass::getMainCurrency_gFunc()) * $v_pVar['quantity'];
			}
			else {
				$price_pVar += $v_pVar['price_mj'] * $v_pVar['quantity'];
			}
			$n_pVar += $v_pVar['quantity'];
			$data_pVar['rows'][$k_pVar]['n'] = $i_pVar;
			$data_pVar['rows'][$k_pVar]['price'] = $v_pVar['price_mj'] * $v_pVar['quantity'];
		}

		$transport_price_pVar = 0;
		$items_price_pVar = $price_pVar;
		if($data_pVar['transport']) {
			$transport_price_pVar = $data_pVar['transport_types'][$data_pVar['transport']]['transport_type_price'];
			$price_pVar += $transport_price_pVar;
		}

		$data_pVar['total_price'] = $price_pVar;
		$data_pVar['transport_price'] = $transport_price_pVar;
		$data_pVar['items_price'] = $items_price_pVar;
		$data_pVar['total_quantity'] = $n_pVar;
		$data_pVar['total_items'] = $i_pVar;
		$data_pVar['currency'] = main_gClass::getMainCurrency_gFunc();
	}

	private function setBasketData_gFunc($data_pVar)
	{
		main_gClass::setPhpSessionVar_gFunc('basketData_pVar', $data_pVar, false, false);
	}

	public function getBasketData_gFunc($empty_pVar = false)
	{
		$basket_data_pVar = main_gClass::getSessionData_gFunc('basketData_pVar', false);
		if($empty_pVar || $basket_data_pVar === false) {
			$basket_data_pVar = array();
			$basket_data_pVar['rows'] = array();
			$basket_data_pVar['columns'] = array();
			$basket_data_pVar['columns']['n'] = array('label'=>string_gClass::get('str_eshop_basket_cp_pVar'));
			$basket_data_pVar['columns']['product_id'] = array('label'=>string_gClass::get('str_eshop_basket_kod_pVar'));
			$basket_data_pVar['columns']['product_name'] = array('label'=>string_gClass::get('str_eshop_basket_nazov_pVar'));
			$basket_data_pVar['columns']['quantity'] = array('label'=>string_gClass::get('str_eshop_basket_mnozstvo_pVar'));
			$basket_data_pVar['columns']['mj'] = array('label'=>string_gClass::get('str_eshop_basket_mj_pVar'));
			$basket_data_pVar['columns']['price_mj'] = array('label'=>string_gClass::get('str_eshop_basket_cenamj_pVar'));
			$basket_data_pVar['columns']['price'] = array('label'=>string_gClass::get('str_eshop_basket_cena_pVar'));
			$basket_data_pVar['columns']['currency'] = array('label'=>string_gClass::get('str_eshop_basket_mena_pVar'));

			if(modules_gClass::isModuleRegistred_gFunc('db')) {
				$basket_data_pVar['transport_types'] = db_public_gClass::eshop_getTransportTypes_gFunc(false);
				$basket_data_pVar['payment_types'] = db_public_gClass::eshop_getPaymentTypes_gFunc(false);
			}
			else {
				$basket_data_pVar['transport_types'] = array();
				$basket_data_pVar['payment_types'] = array();
			}
			$basket_data_pVar['transport'] = 0;
			$basket_data_pVar['payment'] = 0;
		}
		main_gClass::setPhpSessionVar_gFunc('basketData_pVar', $basket_data_pVar,false, false);
		//echo '<pre>'; print_r($basket_data_pVar); echo '</pre>';
		return($basket_data_pVar);
	}
	
	function getItems_gFunc($item_id_pVar = 0)
	{
		
		$fName_pVar = main_gClass::getConfigVar_gFunc('products', 'eshop');
		$doc_root_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
		$fName_pVar = $doc_root_pVar . '.files/' . $fName_pVar;
		
		$f_pVar = fopen($fName_pVar, 'rt');
		if(!$f_pVar) {
			return(array());
		}
		
		$ret_pVar = array();
		
		while(!feof($f_pVar)) {
			$data_pVar = fgetcsv($f_pVar, 1024);
			if(!is_array($data_pVar) || !count($data_pVar)) {
				continue;
			}
			if(!is_numeric($data_pVar[0])) {
				continue;
			}
			if($item_id_pVar) {
				if(intval($data_pVar[0]) != intval($item_id_pVar)) {
					continue;
				}
				$tmp_pVar = array();
				$tmp_pVar['item_id'] = intval($data_pVar[0]);
				$tmp_pVar['name'] = $data_pVar[1];
				$tmp_pVar['price'] = $data_pVar[2];
				$tmp_pVar['currency'] = $data_pVar[3];
				$ret_pVar[] = $tmp_pVar;
				break; // nasiel som polozku, mozem to ukoncit
			}
			
			$tmp_pVar = array();
			$tmp_pVar['item_id'] = intval($data_pVar[0]);
			$tmp_pVar['name'] = $data_pVar[1];
			$tmp_pVar['price'] = $data_pVar[2];
			$tmp_pVar['currency'] = $data_pVar[3];
			$ret_pVar[] = $tmp_pVar;
		}
		
		fclose($f_pVar);
		echo 'd';
		return($ret_pVar);
	}
}

class eshop_basket extends eshop_basket_gClass {}

return(true);