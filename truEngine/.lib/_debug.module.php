<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__debug_pVar'])) return(true);
$GLOBALS['file__debug_pVar']=true;


class debug_debug_gClass extends debug_gClass {
    private static $traceArrayRecursion_pVar = 0;
/*
    public static function traceArray_gFunc($array_pVar, $title_pVar = '')
    {
        self::$traceArrayRecursion_pVar = 0;
        self::_traceArray_gFunc($array_pVar, $title_pVar);
    }

	private static function _traceArray_gFunc($array_pVar, $title_pVar = '')
	{
	    if(is_object($array_pVar)) {
            ob_start();
            echo '<pre>';
            print_r($array_pVar);
            echo '</pre>';
            $msg_pVar .= ob_get_contents();
            ob_end_clean();
	    }
	    else if(is_array($array_pVar)) {
	       $array_copy_pVar = $array_pVar;
            $msg_pVar='<table>';

            foreach($array_copy_pVar as $k_pVar=>$v_pVar) {
                $msg_pVar .= '<tr><td valign="top">' . str_replace("\n", '<br>', htmlspecialchars($k_pVar)) . '</td>';
             	$msg_pVar .= '<td valign="top"> => ';
             	if(is_null($v_pVar)) $msg_pVar .= '(null)';
             	if(is_bool($v_pVar)) $msg_pVar .= '(boolean)';
             	if(is_array($v_pVar)) $msg_pVar .= '(array)';
             	if(is_object($v_pVar)) $msg_pVar .= '(object)';
             	if(is_int($v_pVar)) $msg_pVar .= '(integer)';
             	if(is_float($v_pVar)) $msg_pVar .= '(float)';
             	if(is_string($v_pVar)) $msg_pVar .= '(string)';
     	        $msg_pVar .= '</td>';
     	        $msg_pVar .= '<td valign="top">';
     	        if(is_object($v_pVar) || is_array($v_pVar)) {
     		         if($k_pVar == 'GLOBALS') {
     		             $msg_pVar .= 'GLOBALS';
     		         }
     		         else {
     		             self::$traceArrayRecursion_pVar++;
     		             $msg_pVar .= self::_traceArray_gFunc($v_pVar);
     		             self::$traceArrayRecursion_pVar--;
     		         }
     	        }
     	        else {
     		         $msg_pVar .= str_replace("\n", '<br>', htmlspecialchars($v_pVar));
     	        }
     	        $msg_pVar .= '</td></tr>';
            }
            $msg_pVar .= '</table>';
            unset($array_copy_pVar);
	    }

        if(strlen($title_pVar)) $msg_pVar = $title_pVar . $msg_pVar;

        $out_pVar = self::$traceArrayRecursion_pVar ? false:true;
        $ret_pVar = message_gClass::debug_gFunc($msg_pVar, $out_pVar);

        return($ret_pVar);
	}
	*/

/*
	public static function getSystemStatus_gFunc() {
        $totalTime_pVar = main_gClass::getScriptTime_gFunc();

        $data_pVar='';
        $data_pVar .= str__debug_script_time_sVar . ': ' . $totalTime_pVar . ' s';
        message_gClass::debug_gFunc($data_pVar, true);

        $data_pVar=str__debug_scriptfilename_sVar . ': ' . main_gClass::getServerVar_gFunc('SCRIPT_FILENAME');
        message_gClass::debug_gFunc($data_pVar, true);
        //echo $data_pVar;

        self::traceArray_gFunc(modules_gClass::getModules_gFunc(), str__debug_loadedmodules_sVar . ': ');
        self::traceArray_gFunc(self::getIncludedFiles_gFunc(), str__debug_includedfiles_sVar . ': ');
        self::traceArray_gFunc($GLOBALS,'GLOBALS: ');
	}
*/
/*
	public static function getIncludedFiles_gFunc()
	{
	    return(get_included_files());
	}
	*/

}

return(true);