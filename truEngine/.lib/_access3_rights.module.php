<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__access3_rights_pVar'])) return(true);
$GLOBALS['file__access3_rights_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('items'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('tables'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('access2_session'))
{
    return(false);
}


return(true);
