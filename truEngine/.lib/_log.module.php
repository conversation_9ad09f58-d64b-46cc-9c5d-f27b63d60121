<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__log_pVar'])) return(true);
$GLOBALS['file__log_pVar']=true;

/**
 * Struktura tabuliek:
 * 
 * log__data
 * 		id -
 * 		request_id -
 * 		timestamp_sec -
 * 		timestamp_usec - (min. 6 miest)
 * 		tag_name -
 * 		value -
 * 		param1 -
 * 
 * log__stats_access
 * 		id - year - month - day - hour - weekday -
 * 		n - pocet
 * 		total_bytes -
 * 		time_total -
 * 		method - (enum - GET/POST)
 * 		doc - nazov dokumentu (to by som mohol spravit ako relaciu do dalsej tabulky)
 */

class logIface_gClass
{
	function parseLog_gFunc()
	{
		
	}
	
	/**
	 * Zmaze zbytocne zaznamy z logu. Predtym musia byt tieto zaznamy zahrnute do statistik.
	 * Zbytocnymi zaznamami z logu su zaznamy starsie ako X dni, ktore uz boli zahrnute do statistik.
	 *
	 */
	function cleanLog_gFunc()
	{
		
	}
}

return(true);