<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__export_csv_pVar'])) return(true);
$GLOBALS['file__export_csv_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('export'))
{
    return(false);
}

class export_csv_gClass extends export_gClass
{
	private $prefixes_pVar;
	
	function __construct($settings_pVar)
	{
		parent::__construct($settings_pVar);
		$this->right_pVar = s_system_export_data_csv;
		$this->prefixes_pVar = array(''=>'');
	}
	
	protected function export_open_gFunc()
	{
		$this->result_pVar->echoCsv_gFunc(array('charset:', $this->settings_pVar['encoding']));
		$this->result_pVar->echoCsv_gFunc(array('data:', $this->settings_pVar['data_type']));
		$this->result_pVar->echoCsv_gFunc(array('timestamp:', $this->settings_pVar['timestamp']));
		$this->result_pVar->echoCsv_gFunc(array('comment:', $this->comment_pVar));
		$fields_pVar = explode(',', $this->settings_pVar['fields']);
		$usePrefixes_pVar = array('-', '*', '$', '#');
		foreach($fields_pVar as $v_pVar) {
			if(strpos($v_pVar, '.') !== false) {
				$key_pVar = substr($v_pVar, 0 , strpos($v_pVar, '.'));
				if(!isset($this->prefixes_pVar[$key_pVar])) {
					$this->prefixes_pVar[$key_pVar] = array_shift($usePrefixes_pVar);
				}
			}
		}
		
		foreach($this->prefixes_pVar as $k_pVar=>$v_pVar) {
			if(empty($k_pVar)) {
				continue;
			}
			$this->result_pVar->echoCsv_gFunc(array('item:', $k_pVar, $v_pVar));
		}
		
		$this->result_pVar->echoCsv_gFunc(array('', ''));

		return(true);
	}
	
	protected function export_close_gFunc()
	{
		return(true);
	}
	
	protected function export_data_gFunc()
	{
		$data_pVar = $this->getData_gFunc();
		$this->writeCsv_gFunc($data_pVar);
		return(true);
	}
	

	/**
	 * Funkcia zapisuje CSV priamo do BUFFRU.
	 * @param $data_pVar
	 * @return unknown_type
	 */
	private function writeCsv_gFunc(&$data_pVar)
	{
		if(!isset($data_pVar['_fields'])) {
			return(true);
		}

		$fieldDef_pVar = array();
		foreach($this->prefixes_pVar as $k_pVar=>$v_pVar) {
			$fields_pVar = array();
			$fields_pVar[] = $v_pVar;
			foreach($data_pVar['_fields'] as $kField_pVar=>$vField_pVar) {
				$useField_pVar = $kField_pVar;
				$p_pVar = strpos($kField_pVar, '.');
				if($p_pVar) {
					$prefix_pVar = substr($kField_pVar, 0, $p_pVar);
					$useField_pVar = substr($kField_pVar, $p_pVar + 1);
				}
				else {
					$prefix_pVar = '';
				}
				if($prefix_pVar !== $k_pVar) {
					continue;
				}
				$fields_pVar[] = $useField_pVar;
			}
			$fieldDef_pVar[$k_pVar] = $fields_pVar;
			$this->result_pVar->echoCsv_gFunc($fields_pVar);
		}
		
		$this->result_pVar->echoCsv_gFunc(array('', ''));
		
		// data
		
		foreach($data_pVar as $kRow_pVar=>$row_pVar) {
			if($kRow_pVar[0] == '_') {
				continue;
			}
			$this->writeOneRow_gFunc($row_pVar, $fieldDef_pVar);
		}
	}
	
	private function writeOneRow_gFunc($rowData_pVar, &$fieldDefs_pVar, $rowPrefix_pVar = '')
	{
		$res_pVar = array();
		
		foreach($fieldDefs_pVar[$rowPrefix_pVar] as $k_pVar=>$field_pVar) {
			if(!$k_pVar) {
				$res_pVar[] = $field_pVar;
				continue;
			}
			if(isset($rowData_pVar[$field_pVar])) {
				if(is_array($rowData_pVar[$field_pVar]) 
					&& isset($rowData_pVar[$field_pVar]['_TE:file_content'])){
						$val_pVar = basename($rowData_pVar[$field_pVar]['_TE:file_content']);
						unset($rowData_pVar[$field_pVar]['_TE:file_content']);
				}
				else {
					$val_pVar = $rowData_pVar[$field_pVar];
				}
				$res_pVar[] = $val_pVar;
				unset($val_pVar);
			}
			else {
				$res_pVar[] = '';
			}
		}
		$this->result_pVar->echoCsv_gFunc($res_pVar);
		
		foreach($rowData_pVar as $k_pVar=>$v_pVar) {
			if(is_array($v_pVar) && isset($fieldDefs_pVar[$k_pVar])) {
				foreach($v_pVar as $vv_pVar) {
					$this->writeOneRow_gFunc($vv_pVar, $fieldDefs_pVar, $k_pVar);
				}
			}
		}
	}

}


return(true);
