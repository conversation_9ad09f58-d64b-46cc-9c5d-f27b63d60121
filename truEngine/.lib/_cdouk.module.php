<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__cdouk_pVar'])) return(true);
$GLOBALS['file__cdouk_pVar']=true;


if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}


class cdouk_gClass {
	private $cachedData_pVar = array();
	private $data_pVar = array();
	private $curl_pVar = false;

	const ISIC_CHECK_FAIL_pVar = 1;
	const ISIC_OK_pVar = 2;
	const ISIC_ONLINE_pVar = 4;
	const ISIC_DATA_CHANGED_pVar = 8;

	function __construct()
	{
		$this->cachedData_pVar = array();
		$this->data_pVar = array();
		$this->curl_pVar = false;
	}

	function getData_gFunc($isic_snr_pVar, $work_offline_pVar = false, $force_online_pVar = false, $updateProfile_pVar = true, $debug_pVar = false)
	{
		$isic_snr_pVar = self::formatIsicSnr_gFunc($isic_snr_pVar);

		$this->data_pVar = array('status'=>0);

		if(!$this->getDataFromCache_gFunc($isic_snr_pVar, 1)) {
			$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_CHECK_FAIL_pVar;
			return($this->data_pVar);
		}

		if(!$work_offline_pVar && ($force_online_pVar || $this->needUpdate_gFunc(isset($this->cachedData_pVar[0])?$this->cachedData_pVar[0]:null))) {
			// idem updatovat z CDOUK
			$data_pVar = $this->downloadData_gFunc($isic_snr_pVar, $debug_pVar);
			$now_pVar = date('Y-m-d H:i:s');
			if($data_pVar === false) {
				// nepodarilo sa pripojit, alebo boli vratene chybne data (chybne xml)
				$sql_pVar = 'UPDATE `%tcdouk__status` SET `checked` = %s WHERE `isic` = %s';
				if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($now_pVar, $isic_snr_pVar), true)) {
					$sql_pVar = 'REPLACE INTO `%tcdouk__status` SET `isic` = %s, `checked` = %s';
					db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($isic_snr_pVar, $now_pVar));
				}
				$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_CHECK_FAIL_pVar;
				return($this->data_pVar);
			}
			$data_pVar .= ' ';
			if(!isset($this->cachedData_pVar[0]) || $this->cachedData_pVar[0]['data'] != $data_pVar) {
				// data este neboli cachovane pre tento isic, alebo boli zmenene.
				$sql_pVar = 'INSERT INTO `%tcdouk__changes` (`isic`, `first_checked`, `last_checked`, `data`) VALUES (%s, %s, %s, %s)';
				if(!db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($isic_snr_pVar, $now_pVar, $now_pVar, $data_pVar))) {
					$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_CHECK_FAIL_pVar;
					return($this->data_pVar);
				}
				$sql_pVar = 'REPLACE INTO `%tcdouk__status` (`isic`, `checked_ok`, `checked`) VALUES(%s, %s, %s)';
				if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($isic_snr_pVar, $now_pVar, $now_pVar))) {
					$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_CHECK_FAIL_pVar;
					return($this->data_pVar);
				}
				// obnovim data $this->cachedData_pVar
				if(!$this->getDataFromCache_gFunc($isic_snr_pVar, 1)) {
					$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_CHECK_FAIL_pVar;
					return($this->data_pVar);
				}
				$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_DATA_CHANGED_pVar;

				$tmp_data_pVar = $this->parseXmlData_gFunc($data_pVar);
				if(!isset($tmp_data_pVar['chyba'])) {
					// ulozim subor
					if(isset($tmp_data_pVar['foto']) && !empty($tmp_data_pVar['foto'])) {
						$target_dir_pVar = main_gClass::getConfigVar_gFunc('isic', 'files');
				    	if(empty($target_dir_pVar)) {
				    		$target_dir_pVar = 'isic';
		    			}
				    	$target_dir_pVar = string_gClass::formatAsDirectory_gFunc($target_dir_pVar);
				    	$target_name_pVar = $isic_snr_pVar . '.jpg';
				    	$n_pVar = 0;
				    	while(file_exists($target_dir_pVar . $target_name_pVar) || db_public_gClass::files_file_exists_gFunc('isic', $target_name_pVar)) {
				    		$n_pVar++;
				    		$target_name_pVar = $isic_snr_pVar . '(' . $n_pVar . ').jpg';
				    	}
				    	file_put_contents($target_dir_pVar . $target_name_pVar, base64_decode($tmp_data_pVar['foto']));

						// aktualizujem fotku
						$sql_pVar = 'SELECT `item_id` FROM `%titems_users__data` WHERE `isic` LIKE %s';
						$tmp_users_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array('%' . $isic_snr_pVar));
						$users_pVar = array();
						foreach($tmp_users_pVar as $k_pVar=>$v_pVar) {
							$users_pVar[] = $v_pVar['item_id'];
						}
						$files_pVar = db_public_gClass::files_get_records_gFunc('items_users_foto_rec', $users_pVar);
						foreach($files_pVar as $k_pVar=>$v_pVar) { // aktualizujem fotky
							// zmazem povodnu foto
							$deleteFileName_pVar = db_public_gClass::files_deleteFileRecord_gFunc($v_pVar['file_id'], true, 'items_users_foto_rec');
							if($deleteFileName_pVar !== false && $deleteFileName_pVar !== true) {
								unlink($deleteFileName_pVar);
								// @TODO: dorobit zmazanie tumbnailov
							}
							//unset($users_pVar[$v_pVar['ref_value']]);
						}
						foreach($users_pVar as $user_id_pVar) { // pridam nove fotky
							// a teraz zapisem do DB
							$custom_name_pVar = $target_name_pVar;
							$nn_pVar = 0;
				    		while(db_public_gClass::files_file_exists_gFunc('isic', $custom_name_pVar)) {
				    			$nn_pVar++;
					    		$custom_name_pVar = $isic_snr_pVar . '(' . $n_pVar . ')' . '(' . $nn_pVar . ')' . '.jpg';
				    		}
							main_gClass::addFileToDb_gFunc($target_dir_pVar . $target_name_pVar, 'isic', 'items_users_foto_rec', $user_id_pVar, 'image', $custom_name_pVar, 'CDO UK');
						}
					}
				}
				else {
					// invalidujem priznak overenia isicu
					if(modules_gClass::isModuleRegistred_gFunc('items')) {
						$sql_pVar = 'SELECT * FROM `%titems_users__data` WHERE `isic` = %s AND `status` <> \'deleted\'';
						$users_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $isic_snr_pVar);
						foreach($users_pVar as $user_pVar) {
							$data_pVar = array();
							if($user_pVar['isic_force_ok'] != 'yes') {
								$data_pVar['isic_ok'] = 'no';
							}

							if(count($data_pVar)) {
								// najskor skontrolujem ci nastala zmena
								$data_changed_pVar = false;
								foreach($data_pVar as $k_pVar=>$v_pVar) {
									if($user_pVar[$k_pVar] != $v_pVar) {
										$data_changed_pVar = true;
										break;
									}
								}
								if(!$data_changed_pVar) {
									continue;
								}

								$data_pVar['item_id'] = $user_pVar['item_id'];
								items_gClass::saveOrUpdateItem_gFunc('users', $data_pVar);
							}
						}
					}
					$updateProfile_pVar = false;
				}
			}
			else {
				// data neboli zmenene, aktualizujem iba datum
				$sql_pVar = 'UPDATE `%tcdouk__status` SET `checked_ok` = %s, `checked` = %s WHERE `isic` = %s';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($now_pVar, $now_pVar, $isic_snr_pVar));
				$sql_pVar = 'UPDATE `%tcdouk__changes` SET `last_checked` = %s WHERE `isic` = %s AND `first_checked` = %s';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($now_pVar, $isic_snr_pVar, $this->cachedData_pVar[0]['first_checked']));
			}
			$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_ONLINE_pVar;
		}
		$status_pVar = $this->data_pVar['status'];
		$this->data_pVar = $this->cachedData_pVar[0];
		$this->data_pVar['status'] = $status_pVar;

		$this->data_pVar['data'] = $this->parseXmlData_gFunc($this->data_pVar['data']);

		if(!isset($this->data_pVar['data']['chyba'])) {
			if(
				(isset($this->data_pVar['data']['Student']) && $this->data_pVar['data']['Student'])
				|| (isset($this->data_pVar['data']['Zamestnanec']) && $this->data_pVar['data']['Zamestnanec'])
			) {
				$this->data_pVar['status'] = $this->data_pVar['status'] | self::ISIC_OK_pVar;
			}
		}

		if($updateProfile_pVar) {
			$this->updateUserData_gFunc($isic_snr_pVar, $this->data_pVar);
		}

		return($this->data_pVar);
	}

	function updateUserData_gFunc($isic_snr_pVar, $isic_data_pVar)
	{
		if(!modules_gClass::isModuleRegistred_gFunc('items')) {
			return(false);
		}

		if($isic_data_pVar['status'] & self::ISIC_CHECK_FAIL_pVar) {
			return;
		}
		if(!isset($isic_data_pVar['data'])) {
			return;
		}

		$sql_pVar = 'SELECT * FROM `%titems_users__data` WHERE `isic` = %s AND `status` <> \'deleted\'';
		$users_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $isic_snr_pVar);
		foreach($users_pVar as $user_pVar) {
			$data_pVar = array();
			if($isic_data_pVar['status'] & self::ISIC_OK_pVar) {
				$data_pVar['isic_ok'] = 'yes';
				$data_pVar['isic_force_ok'] = 'no';
			}
			else {
				if($user_pVar['isic_force_ok'] != 'yes') {
					$data_pVar['isic_ok'] = 'no';
				}
			}
			if(isset($isic_data_pVar['data']['Priezvisko']) && !empty($isic_data_pVar['data']['Priezvisko'])) {
				$data_pVar['last_name'] = $isic_data_pVar['data']['Priezvisko'];
			}
			if(isset($isic_data_pVar['data']['Meno']) && !empty($isic_data_pVar['data']['Meno'])) {
				$data_pVar['first_name'] = $isic_data_pVar['data']['Meno'];
			}
			if(isset($isic_data_pVar['data']['TitulPred'])) {
				$data_pVar['titul_pred'] = $isic_data_pVar['data']['TitulPred'];
			}
			if(isset($isic_data_pVar['data']['TitulZa'])) {
				$data_pVar['titul_za'] = $isic_data_pVar['data']['TitulZa'];
			}
			if(!isset($isic_data_pVar['data']['StudProgram'])
				&& isset($isic_data_pVar['data']['Zamestnanec'])
				&& $isic_data_pVar['data']['Zamestnanec'] == 1) {
				$isic_data_pVar['data']['StudProgram'] = 'ZAMESTNANEC';
			}
			if(isset($isic_data_pVar['data']['StudProgram'])) {
				$program_pVar = explode('.', $isic_data_pVar['data']['StudProgram']);
				if(is_numeric($program_pVar[0])) {
					// ulozim rocnik
					$program_pVar[0] = trim($program_pVar[0]);
					$rocniky_pVar = items_gClass::getValues_gFunc('users', 'rocnik');
					$found_pVar = false;
					foreach($rocniky_pVar as $k_pVar=>$v_pVar) {
						if($v_pVar['enum_field_value'] == 'rocnik_' . $program_pVar[0]) {
							$found_pVar = true;
							$data_pVar['rocnik'] = $v_pVar['enum_field_value'];
							break;
						}
					}
					if(!$found_pVar) {
						$itemValue_pVar = array();
						$itemValue_pVar['enum_field_tag'] = 'rocnik';
						$itemValue_pVar['enum_field_value'] = 'rocnik_' . $program_pVar[0];
						$itemValue_pVar['sk_enum_field_name_item'] = $program_pVar[0] . '. ročník';
						session_gClass::giveMeFullAccess_gFunc();
							db_items_gClass::saveOrUpdateItemValue_gFunc('users', $itemValue_pVar);
							db_items_gClass::reorderItemValues_gFunc('users', 'rocnik');
							db_items_gClass::applyFieldsToDataTable_gFunc('users');
						session_gClass::revokeMeFullAccess_gFunc();
						$data_pVar['rocnik'] = 'rocnik_' . $program_pVar[0];
					}

					if(isset($program_pVar[1])) {
						$program_pVar[0] = $program_pVar[1];
					}
					else {
						unset($program_pVar[0]);
					}
				}
				else {
					$data_pVar['rocnik'] = '';
				}
				if(isset($program_pVar[0])) {
					// ulozim smer
					$program_pVar[0] = trim($program_pVar[0]);
					$smery_pVar = items_gClass::getValues_gFunc('users', 'smer');
					$found_pVar = false;
					foreach($smery_pVar as $k_pVar=>$v_pVar) {
						if($v_pVar['enum_field_value'] == $program_pVar[0]) {
							$found_pVar = true;
							$data_pVar['smer'] = $v_pVar['enum_field_value'];
							break;
						}
					}
					if(!$found_pVar) {
						$itemValue_pVar = array();
						$itemValue_pVar['enum_field_tag'] = 'smer';
						$itemValue_pVar['enum_field_value'] = $program_pVar[0];
						$itemValue_pVar['sk_enum_field_name_item'] = $program_pVar[0];
						session_gClass::giveMeFullAccess_gFunc();
							db_items_gClass::saveOrUpdateItemValue_gFunc('users', $itemValue_pVar);
							db_items_gClass::reorderItemValues_gFunc('users', 'smer');
							db_items_gClass::applyFieldsToDataTable_gFunc('users');
						session_gClass::revokeMeFullAccess_gFunc();
						$data_pVar['smer'] = $program_pVar[0];
					}
				}
				else {
					$data_pVar['smer'] = '';
				}
			}
			if(count($data_pVar)) {
				// najskor skontrolujem ci nastala zmena
				$data_changed_pVar = false;
				foreach($data_pVar as $k_pVar=>$v_pVar) {
					if($user_pVar[$k_pVar] != $v_pVar) {
						$data_changed_pVar = true;
						break;
					}
				}
				if(!$data_changed_pVar) {
					continue;
				}

				$data_pVar['item_id'] = $user_pVar['item_id'];
				session_gClass::giveMeFullAccess_gFunc();
					items_gClass::saveOrUpdateItem_gFunc('users', $data_pVar);
				session_gClass::revokeMeFullAccess_gFunc();
			}
		}
	}

	function needUpdate_gFunc($data_pVar)
	{
		if($data_pVar === null) {
			return(true);
		}
		$checked_ok_pVar = strtotime($data_pVar['checked_ok']);
		$checked_pVar = strtotime($data_pVar['checked']);
		if(!$checked_pVar || $checked_pVar < time() - 3600 * 24) {
			// updatujem max. raz za 24 hod.
			return(true);
		}
		return(false);
	}

	function checkIsicSnr_gFunc($isic_snr_pVar, $inicialy_pVar, $work_offline_pVar = false, $force_online_pVar = false)
	{
		$isic_snr_pVar = self::formatIsicSnr_gFunc($isic_snr_pVar);
		// TODO: dorobit...
	}

	private function downloadData_gFunc($isic_snr_pVar, $debug_pVar = false)
	{
		// predpokladam, ze $isic_snr_pVar je osetrene metodou formatIsicSnr_gFunc

		// odstranim uvodne nuly
		$isic_snr_pVar = ltrim($isic_snr_pVar, '0');

		$server_pVar = main_gClass::getConfigVar_gFunc('data_server', 'cdouk');
		$path_pVar = main_gClass::getConfigVar_gFunc('data_path', 'cdouk');
		$login_pVar = main_gClass::getConfigVar_gFunc('data_login', 'cdouk');
		$password_pVar = main_gClass::getConfigVar_gFunc('data_password', 'cdouk');

		if(substr($path_pVar, -1) != '?' && substr($path_pVar, -1) != '&') {
			if(strpos($path_pVar, '?')) {
				$path_pVar .= '&';
			}
			else {
				$path_pVar .= '?';
			}
		}

		$downloadFoto_pVar = true;
		$path_pVar .= 'snr=' . $isic_snr_pVar . '&foto=' . ($downloadFoto_pVar ? 'true' : 'false');


		if($this->curl_pVar === false) {
			$this->curl_pVar = curl_init();
		}

		$post_data_pVar = array();

		// autentifikacia
		curl_setopt($this->curl_pVar, CURLOPT_USERPWD, $login_pVar . ":" . $password_pVar);

		curl_setopt($this->curl_pVar, CURLOPT_URL, "https://" . $server_pVar . $path_pVar);
		curl_setopt($this->curl_pVar, CURLOPT_SSL_VERIFYPEER, false);

		curl_setopt($this->curl_pVar, CURLOPT_RETURNTRANSFER, 1);

		if(count($post_data_pVar)) {
			$poststr_pVar='';
			foreach($post_data_pVar as $k_pVar=>$v_pVar) {
				if(!empty($poststr_pVar)) {
					$poststr_pVar .= '&';
				}
				$poststr_pVar .= urlencode($k_pVar) . '=' . urlencode($v_pVar);
			}

			curl_setopt($this->curl_pVar, CURLOPT_POST, 1);
			curl_setopt($this->curl_pVar, CURLOPT_POSTFIELDS, $poststr_pVar);
		}
		else {
			curl_setopt($this->curl_pVar, CURLOPT_POST, 0);
		}

		$response_pVar = curl_exec($this->curl_pVar);
		$info_pVar = curl_getinfo($this->curl_pVar);

		if($debug_pVar) {
			echo '<hr />HTTP RESULT CODE: ' . $info_pVar['http_code'] . '<br />';
			echo 'RESPONSE: ' . htmlspecialchars($response_pVar) . '<hr />';
		}

		if($response_pVar === false || $info_pVar['http_code'] != 200) {
			return(false);
		}
		return($response_pVar);
	}

	private function parseXmlData_gFunc($xmlStr_pVar)
	{
		$xmlHeaderOk_pVar = false;

		// skonvertujem na utf-8, pretoze parser robi problemy
        $p0_pVar = strpos($xmlStr_pVar, '<'.'?');
        if($p0_pVar !== false) {
            $p1_pVar = strpos($xmlStr_pVar, '?'.'>', $p0_pVar);
            if($p1_pVar !== false) {
                $p1_pVar += 2;
                $x_pVar = substr($xmlStr_pVar, $p0_pVar, $p1_pVar - $p0_pVar);
                $x_pVar = strtolower($x_pVar);
                if(substr($x_pVar, 0, 5) == '<'.'?xml') {
                    $subpatterns_pVar = array();
                    if(preg_match('/(.*\s*encoding=[\"\']{1}){1}(.*)([\"\']{1}\s*.*){1}/i', $x_pVar, $subpatterns_pVar)) {
                        $xmlHeaderOk_pVar = true;
                        $x_pVar = $subpatterns_pVar[1] . 'UTF-8' . $subpatterns_pVar[3];
                        if(strtolower($subpatterns_pVar[2]) !== 'utf-8') {
                        	$xmlStr_pVar = iconv($subpatterns_pVar[2], 'UTF-8/'.'/TRANSLIT', $xmlStr_pVar);
                        	$x_pVar = iconv($subpatterns_pVar[2], 'UTF-8/'.'/TRANSLIT', $x_pVar);
                        }
                        $xmlStr_pVar = substr($xmlStr_pVar, 0, $p0_pVar) . $x_pVar . substr($xmlStr_pVar, $p1_pVar);
                    }
                }
            }
        }

	    if(!$xmlHeaderOk_pVar) {
            error_gClass::error_gFunc(__FILE__,__LINE__, string_gClass::get('str__xml_encoding_sVar'));
            return(false);
        }

        $domDoc_pVar = new DOMDocument();
        if(!$domDoc_pVar->loadXML($xmlStr_pVar)) {
            return(false);
        }

        $data_pVar = array();

        $xpath_pVar = new DOMXPath($domDoc_pVar);
        $query_pVar = '//*';
        $nodes_pVar = $xpath_pVar->query($query_pVar);

	    foreach ($nodes_pVar as $node_pVar) {
	    	if($node_pVar->nodeName == 'Chyba') {
	    		$data_pVar['chyba'] = $node_pVar->nodeValue;
	    		continue;
	    	}
	    }

        $query_pVar = '//Osoba/*';
        $nodes_pVar = $xpath_pVar->query($query_pVar);
       	foreach ($nodes_pVar as $node_pVar) {
	    	if($node_pVar->nodeName == 'Zamestnanec' || $node_pVar->nodeName == 'Student') {
	    		$nodeData_pVar = array();
				if($node_pVar->firstChild) {
                	$cnode_pVar =& $node_pVar->firstChild;
                	while(1) {
	                    if($cnode_pVar instanceof DOMCdataSection) {
	                        $nodeType_pVar = XML_CDATA_SECTION_NODE;
	                    }
	                    else {
	                        $nodeType_pVar = $cnode_pVar->nodeType;
	                    }

	                    switch ($nodeType_pVar)
	                    {
	                    	case XML_ELEMENT_NODE:
	                    		$nodeData_pVar[$cnode_pVar->nodeName] = $cnode_pVar->nodeValue;
	                    		break;
	                    	default:
	                    		break;
	                    }

                    	if(!$cnode_pVar->nextSibling) break;
						$cnode_pVar =& $cnode_pVar->nextSibling;
                	}
				}
				if(!isset($nodeData_pVar['Ukonceny']) || $nodeData_pVar['Ukonceny'] == 'false') {
					if($node_pVar->nodeName == 'Zamestnanec') {
						$data_pVar['Zamestnanec'] = true;
					}
					if($node_pVar->nodeName == 'Student') {
						$data_pVar['Student'] = true;
					}
					foreach ($nodeData_pVar as $k_pVar=>$v_pVar) {
						if($k_pVar == 'Ukonceny') {
							continue;
						}
						$data_pVar[$k_pVar] = $v_pVar;
					}
				}
	    	}
	    	if($node_pVar->nodeName == 'Foto') {
	    		$data_pVar['foto'] = $node_pVar->nodeValue;
	    		$data_pVar['foto'] = str_replace("\n", '', $data_pVar['foto']);
	    		$data_pVar['foto'] = str_replace("\r", '', $data_pVar['foto']);
	    	}
        }
        return($data_pVar);
	}


	public static function formatIsicSnr_gFunc($isic_snr_pVar)
	{
		$isic_snr_pVar = trim($isic_snr_pVar);
		$isic_snr_pVar = str_replace(' ', '', $isic_snr_pVar);
		$strlen_isic_pVar = strlen($isic_snr_pVar);
		for($i_pVar = 0; $i_pVar < $strlen_isic_pVar; $i_pVar++) {
			if(!ctype_digit($isic_snr_pVar[$i_pVar])) {
				return('0000000000');
			}
		}

		$isic_snr_pVar = str_pad($isic_snr_pVar, 10, '0', STR_PAD_LEFT);
		return($isic_snr_pVar);
	}

	private function getDataFromCache_gFunc($isic_snr_pVar, $count_pVar = 1)
	{
		$sql_pVar = 'SELECT * FROM `%tcdouk__status` as `s`
						LEFT JOIN `%tcdouk__changes` as `ch` ON `s`.`isic` = `ch`.`isic`
						WHERE `s`.`isic` = %s
						ORDER BY `ch`.`first_checked` DESC';
		if($count_pVar) {
			$sql_pVar .= ' LIMIT 0, %d';
		}

		$this->cachedData_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($isic_snr_pVar, $count_pVar));
		if(!is_array($this->cachedData_pVar) && $cachedData_pVar === true) {
			$this->cachedData_pVar = array();
		}
		if(is_array($this->cachedData_pVar)) {
			return(true);
		}
		return(false);
	}
}

class cdouk_auto_checker_gClass extends cron_exec_gClass
{
	function __construct()
	{
		parent::__construct();

		$this->name_pVar = 'cron_cdouk_checker';
	}

	protected function run_gFunc()
	{
		while(1) {
			if($this->timeExceeded_gFunc()) {
				return(0);
			}
			$sql_pVar = 'SELECT `u`.`item_id` , `u`.`isic` FROM `%titems_users__data` as `u`
							LEFT JOIN `%tcdouk__status` as `s` ON `u`.`isic` = `s`.`isic`
							WHERE
								`u`.`status` <> \'deleted\'
								AND
									(
										`s`.`checked` IS NULL
										OR
										DATE_ADD(`s`.`checked`, INTERVAL 30 DAY) < now()
									)
						 	GROUP BY `u`.`isic`
						 	ORDER BY `s`.`checked`
						 	LIMIT 0, 50';

			$result_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
			if(!count($result_pVar)) {
				return(1);
			}
			foreach($result_pVar as $entry_pVar)
			{
				if($this->timeExceeded_gFunc()) {
					return(0);
				}

				$isic_pVar = cdouk_gClass::formatIsicSnr_gFunc($entry_pVar['isic']);
				if($isic_pVar != $entry_pVar['isic'] || strlen($isic_pVar) != strlen($entry_pVar['isic'])) {
					$sql_pVar = 'UPDATE `%titems_users__data` SET `isic` = %s WHERE `item_id` = %d';
					db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($isic_pVar, $entry_pVar['item_id']));
				}
				$cdouk_pVar = new cdouk_gClass();
				$cdouk_pVar->getData_gFunc($isic_pVar, false, true);
				echo $isic_pVar . '<br />';
			}
		}
	}
}


class cdouk_get_user_data_gClass extends source_gClass
{
	protected function getData()
	{
		$user_pVar = db_session_gClass::getUserDataByID_gFunc($this->params['item_id']);
		if(is_array($user_pVar)) {
			$cdo_pVar = new cdouk_gClass();
			$data_pVar = $cdo_pVar->getData_gFunc($user_pVar['isic'], false, true, true, isset($this->params['debug']) && $this->params['debug'] == 'true');
			return($data_pVar);
		}
		else {
			return(array());
		}
	}
}

class cdouk_get_user_data extends cdouk_get_user_data_gClass {}

return(true);
