<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__xml_pVar'])) return(true);
$GLOBALS['file__xml_pVar'] = true;

abstract class xmlElement_gClass  extends truEngineBaseClass_gClass implements Iterator
{
    const XML_ELEMENT_TYPE_TAG_pVar = 1;
    const XML_ELEMENT_TYPE_DATA_pVar = 2;
    const XML_ELEMENT_TYPE_XDATA_pVar = 3;
    const XML_ELEMENT_TYPE_COMMENT_pVar = 4;
    const XML_ELEMENT_TYPE_PI_pVar = 5;
    const XML_ELEMENT_TYPE_ROOT_pVar = 6;

    protected $elementType_pVar;
    private $data_pVar;
    private $attributes_pVar;
    private $lastChildIndex_pVar;
    private $currentChildIndex_pVar;
    protected $childs_pVar;

    /**
     * metody Iteratora
     * rewind, current, key, next, valid, getChildRef_gFunc
     *
     * nevracaju objekt, ale iba index (okrem getChildRef_gFunc)
     * getChildRef_gFunc vracia referenciu na objekt
     *
     * foreach($xmlElement as $key=>$value) {
     *      $element = $xmlElement->getChildRef_gFunc($key);
     *      ......
     *      unset($element);
     * }
     *
     *
     */
    public function rewind()
    {
        reset($this->childs_pVar);
        $this->currentChildIndex_pVar = key($this->childs_pVar);
    }

    public function current()
    {
        if(!isset($this->childs_pVar[$this->currentChildIndex_pVar])) {
            return(false);
        }
        return($this->currentChildIndex_pVar);
    }

    public function key() {
        if(!isset($this->childs_pVar[$this->currentChildIndex_pVar])) {
            return(false);
        }
        return($this->currentChildIndex_pVar);
    }

    public function next() {
        $this->currentChildIndex_pVar++;
        while($this->currentChildIndex_pVar <= $this->lastChildIndex_pVar
              && !isset($this->childs_pVar[$this->currentChildIndex_pVar])) {
            $this->currentChildIndex_pVar++;
        }
        if(!isset($this->childs_pVar[$this->currentChildIndex_pVar])) {
            return(false);
        }
        return($this->currentChildIndex_pVar);
    }

    public function valid() {
        if(!isset($this->childs_pVar[$this->currentChildIndex_pVar])) {
            return(false);
        }
        else {
            return(true);
        }
    }

    public function &getChildRef_gFunc($childIndex_pVar)
    {
        if(isset($this->childs_pVar[$childIndex_pVar])) {
            $ret_pVar = &$this->childs_pVar[$childIndex_pVar];
            return($ret_pVar);
        }
    }


    function __construct()
    {
        $this->data_pVar = '';
        $this->childs_pVar = array();
        $this->attributes_pVar = array();
        $this->lastChildIndex_pVar = -1;
    }

    public function baseClearChilds_gFunc()
    {
        if($this->elementType_pVar != self::XML_ELEMENT_TYPE_ROOT_pVar) return;
        if($this->lastChildIndex_pVar >=0) {
            unset($this->childs_pVar);
            $this->data_pVar = '';
            $this->childs_pVar = array();
            $this->lastChildIndex_pVar = -1;
        }
    }

    public function setData_gFunc($data_pVar)
    {
        $this->data_pVar = $data_pVar;
    }

    public function getData_gFunc()
    {
        return($this->data_pVar);
    }

    public function setAttribute_gFunc($attrName_pVar, $attrValue_pVar)
    {
        $attrName_pVar=strtolower($attrName_pVar);
        $this->attributes_pVar[$attrName_pVar] = $attrValue_pVar;
    }

    public function isAttribute_gFunc($attrName_pVar)
    {
        $attrName_pVar=strtolower($attrName_pVar);
        if(isset($this->attributes_pVar[$attrName_pVar])) {
            return(true);
        }
        else {
            return(false);
        }
    }

    public function getAttributes_gFunc()
    {
        return($this->attributes_pVar);
    }

    public function getAttribute_gFunc($attrName_pVar)
    {
        $attrName_pVar=strtolower($attrName_pVar);
        if(isset($this->attributes_pVar[$attrName_pVar])) {
            return($this->attributes_pVar[$attrName_pVar]);
        }
        else {
            return(false);
        }
    }

    public function getLastChildID_gFunc()
    {
        return($this->lastChildIndex_pVar);
    }

    protected function & getChildReferenceByID_gFunc($child_id_pVar=0)
    {
        if(!isset($this->childs_pVar[$child_id_pVar])) {
            $ret_pVar = $this;
            return($ret_pVar);
        }
        else {
            $ret_pVar = & $this->childs_pVar[$child_id_pVar];
            return($ret_pVar);
        }
    }

    protected function addChildElement_gFunc($elementType_pVar, $elementData_pVar, $classPrefix_pVar = '')
    {
        $this->lastChildIndex_pVar = $this->lastChildIndex_pVar + 1;
        $this->childs_pVar[$this->lastChildIndex_pVar] = xmlFactory_gClass::newElementByType_gFunc($classPrefix_pVar . $elementType_pVar);
        if($elementType_pVar == 'xmlTag_gClass') {
            $this->childs_pVar[$this->lastChildIndex_pVar]->setTagName_gFunc($elementData_pVar);
        }
        else {
            $this->childs_pVar[$this->lastChildIndex_pVar]->setData_gFunc($elementData_pVar);
        }
    }

    /**
     * Vrati pocet detskych elementov (rekurzivne, alebo nerekurzivne).
     * Typ polozky sa rozumie $this->elementType_pVar (vnutorna konstanta)
     * TAG, DATA, COMMENT, atd.
     *
     * @param unknown_type $elementType_pVar
     * @param unknown_type $recursive_pVar
     */
    public function countElements_gFunc($elementType_pVar=0, $recursive_pVar=false)
    {
        $count_pVar = 0;

        for($i_pVar = 0; $i_pVar <= $this->lastChildIndex_pVar; $i_pVar++)
        {
            if($elementType_pVar == 0) {
                // pocita vsetky polozky
                $count_pVar++;
            }
            else {
                // pocita iba jeden typ poloziek
                if($this->childs_pVar[$i_pVar]->elementType_pVar == $elementType_pVar) $count_pVar++;
            }

            // rekurzia
            if($recursive_pVar) {
                // rekurzivny moze byt asi iba tag.. ale spravim to univerzalne na vsetky
                // ved nikto nenastavi child pre data (ani to triedy neumoznuju)
                if($this->childs_pVar[$i_pVar]->lastChildIndex_pVar != -1) {
                    $count_pVar += $this->childs_pVar[$i_pVar]->countElements_gFunc($elementType_pVar, true);
                }
            }
        }
        return($count_pVar);
    }

    /*
    public function getResult_gFunc()
    {
        // volam funkciu definovanu v potomkoch.
        return($this->getXMLCode_gFunc());
    }*/

    public function getElementTypeId_gFunc()
    {
        return($this->elementType_pVar);
    }

    public function getTagName_gFunc()
    {
        return('');
    }

}

class xmlBase_gClass extends xmlElement_gClass
{
    private $classPrefix_pVar;
    private $sourceEncoding_pVar;
    private $outputEncoding_pVar;
    private $addChildElementUseIconvUtf8_pVar;

    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_ROOT_pVar;
        $this->sourceEncoding_pVar = 'UTF-8'; // je jedno co tu bude, prepise sa to nastaveniami xml suboru
        $this->outputEncoding_pVar = 'UTF-8';
        $this->addChildElementUseIconvUtf8_pVar = false;
    }

    public function setClassPrefix_gFunc($classPrefix_pVar)
    {
        $this->classPrefix_pVar = $classPrefix_pVar;
    }

    /**
     * pretazena metoda z xmlElement_gClass
     *
     * @param unknown_type $elementType_pVar
     * @param unknown_type $elementData_pVar
     */
    protected function addChildElement_gFunc($elementType_pVar, $elementData_pVar, $classPrefix_pVar = '')
    {
        $classes_pVar = class_parents($elementType_pVar);
        if(isset($classes_pVar['xmlTag_gClass'])) {
            // tag moze byt iba jeden
            $countTags_pVar = $this->countElements_gFunc(self::XML_ELEMENT_TYPE_TAG_pVar,false);
            if($countTags_pVar) {
                // uz jeden tag existuje, nemozem pridat dalsi
                error_gClass::error_gFunc(__FILE__,__LINE__,string_gClass::get('str__xml_base_sVar'));
                return; // nepridam ho
            }
        }
        parent::addChildElement_gFunc($elementType_pVar, $elementData_pVar, $classPrefix_pVar);
    }

    public function parseXMLString_gFunc($xmlStr_pVar)
    {
        $this->baseClearChilds_gFunc();

        $xmlHeaderOk_pVar = false;
        // skonvertujem na utf-8, pretoze parser robi problemy
        $p0_pVar = strpos($xmlStr_pVar, '<'.'?');
        if($p0_pVar !== false) {
            $p1_pVar = strpos($xmlStr_pVar, '?'.'>', $p0_pVar);
            if($p1_pVar !== false) {
                $p1_pVar += 2;
                $x_pVar = substr($xmlStr_pVar, $p0_pVar, $p1_pVar - $p0_pVar);
                $x_pVar = strtolower($x_pVar);
                if(substr($x_pVar, 0, 5) == '<'.'?xml') {
                    $subpatterns_pVar = array();
                    if(preg_match('/(.*\s*encoding=[\"\']{1}){1}(.*)([\"\']{1}\s*.*){1}/i', $x_pVar, $subpatterns_pVar)) {
                        $xmlHeaderOk_pVar = true;
                        $this->sourceEncoding_pVar = $subpatterns_pVar[2];
                        $x_pVar = $subpatterns_pVar[1] . 'UTF-8' . $subpatterns_pVar[3];
                        if(strtolower($this->sourceEncoding_pVar) !== 'utf-8') {
                        	$xmlStr_pVar = iconv($this->sourceEncoding_pVar, 'UTF-8/'.'/TRANSLIT', $xmlStr_pVar);
                        	$x_pVar = iconv($this->sourceEncoding_pVar, 'UTF-8/'.'/TRANSLIT', $x_pVar);
                        }
                        $xmlStr_pVar = substr($xmlStr_pVar, 0, $p0_pVar) . $x_pVar . substr($xmlStr_pVar, $p1_pVar);
                    }
                }
            }
        }

        if(!$xmlHeaderOk_pVar) {
            error_gClass::error_gFunc(__FILE__,__LINE__, string_gClass::get('str__xml_encoding_sVar'));
            return(false);
        }

        $doc = new DOMDocument();
        if(!$doc->loadXML($xmlStr_pVar)) {
            return(false);
        }

        $xx_pVar = $this->addChildElementUseIconvUtf8_pVar;
        if(strtoupper($this->outputEncoding_pVar) !== 'UTF-8') {
            $this->addChildElementUseIconvUtf8_pVar = true;
        }
        $this->parseDOM_gFunc($doc, $this);

        $this->addChildElementUseIconvUtf8_pVar = $xx_pVar;

        return(true);
    }

    private function parseDOM_gFunc(&$dom_pVar, &$myObject_pVar)
    {
            if($dom_pVar->firstChild) {
                $child_pVar =& $dom_pVar->firstChild;

                while(1) {
                    if($child_pVar instanceof DOMCdataSection) {
                        $nodeType_pVar = XML_CDATA_SECTION_NODE;
                    }
                    else {
                        $nodeType_pVar = $child_pVar->nodeType;
                    }

                    if(isset($child_pVar->nodeName)) {
                        $_NodeName_pVar = $child_pVar->nodeName;
                    }
                    if(isset($child_pVar->nodeValue)) {
                        $_NodeValue_pVar = $child_pVar->nodeValue;
                    }
                    if($this->addChildElementUseIconvUtf8_pVar) {
                        $_NodeName_pVar = iconv('UTF-8', $this->outputEncoding_pVar . '/'.'/TRANSLIT', $_NodeName_pVar);
                        $_NodeValue_pVar = iconv('UTF-8', $this->outputEncoding_pVar . '/'.'/TRANSLIT', $_NodeValue_pVar);
                    }

                    switch($nodeType_pVar) {
                        case XML_ELEMENT_NODE:          // 1
                            $myObject_pVar->addChildElement_gFunc('xmlTag_gClass', $_NodeName_pVar, $this->classPrefix_pVar);
                            $newElement_pVar = &$myObject_pVar->getChildReferenceByID_gFunc($myObject_pVar->getLastChildID_gFunc());

                            if($child_pVar->hasAttributes()) {
                                $attrList_pVar = $child_pVar->attributes;
                                foreach($attrList_pVar as $attrName_pVar=>$attrValue_pVar) {
                                    $attrFullName_pVar = $attrValue_pVar->nodeName;
                                    $attrFullValue_pVar = $attrValue_pVar->nodeValue;

                                    if($this->addChildElementUseIconvUtf8_pVar) {
                                        $attrFullName_pVar = iconv('UTF-8', $this->sourceEncoding_pVar, $attrFullName_pVar);
                                        $attrFullValue_pVar = iconv('UTF-8', $this->sourceEncoding_pVar, $attrFullValue_pVar);
                                    }
                                    //$attrValue_pVar->prefix
                                    //$attrValue_pVar->namespaceURI
                                    //$attrValue_pVar->nodeValue
                                    $newElement_pVar->setAttribute_gFunc($attrFullName_pVar, $attrFullValue_pVar);
                                }
                            }
                            $this->parseDOM_gFunc($child_pVar, $newElement_pVar);
                            unset($newElement_pVar);
                            break;
                        case XML_TEXT_NODE:             //3
                            $myObject_pVar->addChildElement_gFunc('xmlData_gClass', $_NodeValue_pVar, $this->classPrefix_pVar);
                            break;
                        case XML_CDATA_SECTION_NODE:    //4
                            $myObject_pVar->addChildElement_gFunc('xmlCData_gClass', $_NodeValue_pVar, $this->classPrefix_pVar);
                            break;
                        case XML_COMMENT_NODE:          // 8
                            $myObject_pVar->addChildElement_gFunc('xmlComment_gClass', $_NodeValue_pVar, $this->classPrefix_pVar);
                            break;
                        case XML_PI_NODE:               // 7
                            $myObject_pVar->addChildElement_gFunc('xmlPi_gClass', $_NodeValue_pVar, $this->classPrefix_pVar);
                            break;
                        default:
                            error_gClass::fatal_gFunc(__FILE__,__LINE__,string_gClass::get('str__xml_parser_error_sVar',''.$nodeType_pVar));
                            break;
                    }

                    if(!$child_pVar->nextSibling) break;
                    $child_pVar =& $child_pVar->nextSibling;
                }
            }
    }

    public function getXMLCode_gFunc()
    {
        $data_pVar = '';
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getXMLCode_gFunc();
            }
        }
        return($data_pVar);
    }

    public function getDocData_gFunc()
    {
        $data_pVar = '';
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getDocData_gFunc();
            }
        }
        return($data_pVar);
    }
}

class xmlData_gClass extends xmlElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_DATA_pVar;
    }

    public function getXMLCode_gFunc()
    {
        return($this->getData_gFunc());
    }

    public function getDocData_gFunc()
    {
        return($this->getData_gFunc());
    }
}

class xmlCData_gClass extends xmlData_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_XDATA_pVar;
    }

    public function getXMLCode_gFunc()
    {
        return('<![CDATA[' . $this->getData_gFunc() . ']]>');
    }

}

class xmlComment_gClass extends xmlElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_COMMENT_pVar;
    }

    public function setComment_gFunc($comment_pVar)
    {
        $this->data_pVar = $comment_pVar;
    }

    public function getComment_gFunc()
    {
        return($this->data_pVar);
    }

    public function getXMLCode_gFunc()
    {
        return('<!--' .$this->getData_gFunc() . '-->');
    }

    public function getDocData_gFunc()
    {
        return('');
    }
}

class xmlTag_gClass extends xmlElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_TAG_pVar;
    }

    public function setTagName_gFunc($tagName_pVar)
    {
        $this->data_pVar = strtoupper($tagName_pVar);
    }

    public function getTagName_gFunc()
    {
        return($this->data_pVar);
    }

    public function getXMLCode_gFunc()
    {
        $data_pVar = '';
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getDocData_gFunc();
            }
        }
        $out_pVar = '<' .$this->getTagName_gFunc();

        // attributes
        $attr_pVar = $this->getAttributes_gFunc();
        foreach ($attr_pVar as $k_pVar=>$v_pVar)
        {
            $out_pVar .= ' ' . $k_pVar .'=\'' . $v_pVar . '\'';
        }

        $out_pVar .= '>';
        $out_pVar .= $data_pVar;
        $out_pVar .= '</' . $this->getTagName_gFunc() . '>';

        return($out_pVar);
    }

    public function getDocData_gFunc()
    {
        $data_pVar = '';
        if($this->getTagName_gFunc() == 'BR')
        {
            return LF;
        }
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getDocData_gFunc();
            }
        }
        return($data_pVar);
    }
}

class xmlPi_gClass extends xmlElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_PI_pVar;
    }

    public function getXMLCode_gFunc()
    {
        return('<' . '?' . $this->getData_gFunc() . '?' . '>');
    }

    public function getDocData_gFunc()
    {
        return('');
    }
}

class xmlFactory_gClass extends truEngineBaseClass_gClass
{
    /**
     * vytvori objekt triedy $elementClass_pVar.
     * Tato trieda musi byt potomkom triedy xmlElement_gClass
     *
     * @param unknown_type $elementClass_pVar
     * @return unknown
     */
    public static function newElementByType_gFunc($elementClass_pVar)
    {
        $parents_pVar = class_parents($elementClass_pVar);
        if(!isset($parents_pVar['xmlElement_gClass']) || !class_exists($elementClass_pVar, false)) {
            error_gClass::fatal_gFunc(__FILE__,__LINE__, string_gClass::get('str__xml_type_sVar', $elementClass_pVar));
            return null;
        }

        return(new $elementClass_pVar);
    }
}


return(true);
