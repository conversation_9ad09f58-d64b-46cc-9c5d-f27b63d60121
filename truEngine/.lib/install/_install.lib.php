<?php

class install_gClass {

	public static function getAccessNameCategory_gFunc($categoryPrefix_pVar)
	{
		$sql_pVar = 'SELECT * FROM `%taccess__names_categories` WHERE `prefix` = %s';
		$data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($categoryPrefix_pVar));
		if(is_array($data_pVar)) {
			return($data_pVar);
		}
		return(false);
	}

	public static function addAccessNameCategory_gFunc($categoryPrefix_pVar, $categoryName_pVar, $categoryModule_pVar = '_loader')
	{
		$category_pVar = self::getAccessNameCategory_gFunc($categoryPrefix_pVar);
		if(is_array($category_pVar)) {
			return($category_pVar['access_category_id']);
		}

		$sql_pVar = 'INSERT INTO `%taccess__names_categories` (`prefix`, `sk_category_name`, `category_module`)
					 	VALUES(%s, %s, %s)';
		$category_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($categoryPrefix_pVar, $categoryName_pVar, $categoryModule_pVar), true);
		return($category_id_pVar);
	}

	public static function addAccessName_gFunc($categoryPrefix_pVar, $alias_pVar, $name_pVar, $module_pVar = null, $archive_pVar = true)
	{
		$category_data_pVar = self::getAccessNameCategory_gFunc($categoryPrefix_pVar);
		if(!isset($category_data_pVar['access_category_id']) || !$category_data_pVar['access_category_id']) {
			return(false);
		}

		$category_id_pVar = $category_data_pVar['access_category_id'];

		$archive_pVar = $archive_pVar ? 'yes' : 'no';

		$data_pVar = array('access_category_id'=>$category_id_pVar, 'alias'=>$alias_pVar, 'sk_name'=>$name_pVar, 'archive'=>$archive_pVar, 'module'=>$module_pVar);
		return(db_public_gClass::insertData_gFunc('%taccess__names', '%d, %s, %s, %s, %xs', $data_pVar, __FILE__, __LINE__, true));
	}
}


// @TODO: nedaju sa robit sekvencie.. napr. deleteEnum, addEnum... Pretoze je indexovane podla enum.. Takto sa vykona iba addEnum (a to sa v skutocnosti nevykona, lebo uz existuje)
class install_item_gClass {
	private $create_pVar;
	private $system_name_pVar;
	private $systemProprties_pVar;
	private $fieldsets_pVar;
	private $fields_pVar;
	private $values_pVar;
	private $forms_pVar;
	private $formRules_pVar;
	private $formRights_pVar;
	private $indexes_pVar;

	function __construct($system_name_pVar, $create_pVar = true)
	{
		$this->system_name_pVar = $system_name_pVar;
		$this->create_pVar = $create_pVar;
		$this->systemProprties_pVar = array();
		$this->fields_pVar = array();
		$this->fieldsets_pVar = array();
		$this->values_pVar = array();
		$this->formRights_pVar = array();
		$this->forms_pVar = array();
		$this->indexes_pVar = array();
	}

	public function setSystemProperty_gFunc($property_name_pVar, $property_value_pVar)
	{
		$this->systemProprties_pVar[$property_name_pVar] = $property_value_pVar;
	}

	private function _initField_gFunc($field_tag_pVar)
	{

	}

	function addIndex_gFunc($index_name_pVar, $index_fields_pVar, $index_type_pVar = 'INDEX')
	{
		$this->indexes_pVar[] = array('index_name'=>$index_name_pVar, 'index_fields'=>$index_fields_pVar, 'index_type'=>$index_type_pVar);
	}

	function addFieldset_gFunc($fieldset_name_pVar, $fieldset_legend_sk_pVar)
	{
		$this->fieldsets_pVar[$fieldset_name_pVar] = array('fieldset_name'=>$fieldset_name_pVar, 'sk_fieldset_legend' => $fieldset_legend_sk_pVar);
	}

	/**
	 * Prida novy field.
	 * Ak field existuje, a ma nastavene ine parametre, generuje warning, ale neupdatne ho.
	 * Ak field existuje, ale ma rovnake parametre, tak ho ponecha bez zmeny, ani negeneruje warning.
	 * Ak chcem updatnut field, alebo vymazat, musim pouzit metodu updateField_gFunc, alebo deleteField_gFunc
	 *
	 * @param $fieldset_name_pVar
	 * @param $field_tag_pVar
	 * @param $field_type_pVar
	 * @param $field_sk_name_pVar
	 * @param $field_properties_pVar
	 * @return unknown_type
	 */
	function addField_gFunc($fieldset_name_pVar, $field_tag_pVar, $field_type_pVar, $field_sk_name_pVar, $field_properties_pVar = array())
	{
		$this->fields_pVar[$field_tag_pVar] = array('_'=>'add', 'tag'=>$field_tag_pVar, 'fieldset' => $fieldset_name_pVar, 'type' => $field_type_pVar, 'sk_name' => $field_sk_name_pVar);
		foreach($field_properties_pVar as $k_pVar=>$v_pVar) {
			$this->fields_pVar[$field_tag_pVar][$k_pVar] = $v_pVar;
		}
	}

	/**
	 * Aktualizuje existujuci field v databaze
	 * Pozri tiež addField_gFunc.
	 *
	 * @return unknown_type
	 */
	function updateField_gFunc($fieldset_name_pVar, $field_tag_pVar, $field_type_pVar, $field_sk_name_pVar, $field_properties_pVar = array())
	{
		$this->fields_pVar[$field_tag_pVar] = array('_'=>'update', 'tag'=>$field_tag_pVar, 'fieldset' => $fieldset_name_pVar, 'type' => $field_type_pVar, 'sk_name' => $field_sk_name_pVar);
		foreach($field_properties_pVar as $k_pVar=>$v_pVar) {
			$this->fields_pVar[$field_tag_pVar][$k_pVar] = $v_pVar;
		}
	}

	/**
	 * Vymaze existujuci field z databazy
	 * @return unknown_type
	 */
	function deleteField_Func()
	{

	}

	function setFieldProperty_gFunc($field_tag_pVar, $property_name_pVar, $property_value_pVar)
	{
		$this->fields_pVar[$field_tag_pVar][$property_name_pVar] = $property_value_pVar;
	}

	function addEnum_gFunc($field_tag_pVar, $enum_field_value_pVar, $sk_enum_field_name_item_pVar, $properties_pVar = array())
	{
		if(!isset($this->fields_pVar[$field_tag_pVar])) {
			$this->fields_pVar[$field_tag_pVar] = array('_'=>'skip', 'tag'=>$field_tag_pVar);
		}
		if(!isset($this->values_pVar[$field_tag_pVar])) {
			$this->values_pVar[$field_tag_pVar] = array();
		}
		$this->values_pVar[$field_tag_pVar][$enum_field_value_pVar] = array('_'=>'add', 'enum_field_value'=>$enum_field_value_pVar, 'sk_enum_field_name_item'=>$sk_enum_field_name_item_pVar);
		foreach($properties_pVar as $k_pVar=>$v_pVar) {
			$this->values_pVar[$field_tag_pVar][$enum_field_value_pVar][$k_pVar] = $v_pVar;
		}
	}

	function updateEnum_gFunc($field_tag_pVar, $enum_field_old_value_pVar, $enum_field_value_pVar, $sk_enum_field_name_item_pVar, $properties_pVar = array())
	{
		if(!isset($this->fields_pVar[$field_tag_pVar])) {
			$this->fields_pVar[$field_tag_pVar] = array('_'=>'skip', 'tag'=>$field_tag_pVar);
		}
		if(!isset($this->values_pVar[$field_tag_pVar])) {
			$this->values_pVar[$field_tag_pVar] = array();
		}
		$this->values_pVar[$field_tag_pVar][$enum_field_value_pVar] = array('_'=>'update', 'enum_field_value'=>$enum_field_value_pVar, 'sk_enum_field_name_item'=>$sk_enum_field_name_item_pVar, 'enum_field_old_value'=>$enum_field_old_value_pVar);
		foreach($properties_pVar as $k_pVar=>$v_pVar) {
			$this->values_pVar[$field_tag_pVar][$enum_field_value_pVar][$k_pVar] = $v_pVar;
		}
	}

	function deleteEnum_gFunc($field_tag_pVar, $enum_field_value_pVar)
	{
		if(!isset($this->fields_pVar[$field_tag_pVar])) {
			$this->fields_pVar[$field_tag_pVar] = array('_'=>'skip');
		}
		if(!isset($this->values_pVar[$field_tag_pVar])) {
			$this->values_pVar[$field_tag_pVar] = array();
		}
		$this->values_pVar[$field_tag_pVar][$enum_field_value_pVar] = array('_'=>'delete', 'enum_field_value'=>$enum_field_value_pVar);
	}

	/**
	 * Pridanie formulára do forms/forms_rules
	 * Ak nastavím $copyFrom_pVar na názov nejakého existujúceho formuláru,
	 * po pridaní formuláru sa spraví kópia pravidiel
	 * podľa tohto existujúceho formuláru. Až potom sa aplikujú zmeny nastavené
	 * napr. pomocou setFormRule_gFunc.
	 * !! Akcia sa vykoná až zavolaním apply_gFunc().
	 * !! Kopírujú sa len pravidlá už existujúce v DB. nekopírujú sa pravidlá vytvorené vrámci toho istého apply_gFunc. (Pretože najskôr sa vytvárajú formuláre, až potom pravidlá.)
	 *
	 * @param $formName_pVar
	 * @param $formTitle_pVar
	 * @param $copyFrom_pVar
	 */
	function addForm_gFunc($formName_pVar, $formTitle_pVar, $copyFrom_pVar = false)
	{
		$tmp_pVar = array('form_name'=>$formName_pVar,
						  'sk_form_legend'=>$formTitle_pVar);

		if($copyFrom_pVar !== false) {
			$tmp_pVar['copy_from'] = $copyFrom_pVar;
		}
		$this->forms_pVar[] = $tmp_pVar;
	}

	/**
	 * Pridanie pravidla formulára do form_rules.
	 * !! Akcia sa vykoná až zavolaním apply_gFunc().
	 *
	 * @param $formName_pVar
	 * @param $fieldName_pVar
	 * @param $fieldAccess_pVar
	 * @param $fieldInit_pVar
	 * @param $fieldValue_pVar
	 */
	function setFormRule_gFunc($formName_pVar, $fieldName_pVar, $fieldAccess_pVar = 'edit', $fieldInit_pVar = 'default', $fieldValue_pVar = null)
	{
		$this->formRules_pVar[] = array('form_name'=>$formName_pVar,
										'field_name'=>$fieldName_pVar,
										'field_access'=>$fieldAccess_pVar,
										'field_init'=>$fieldInit_pVar,
										'field_value'=>$fieldValue_pVar);
	}

	function add_right_gFunc($access_type, $access_right, $access_filter = null, $access_field = null)
	{
		$this->formRights_pVar[] = array('access_type'=>$access_type, 'access_rights'=>$access_right, 'access_filter'=>$access_filter, 'access_field'=>$access_field);
	}

	function apply_gFunc()
	{
		if($this->create_pVar) {
			$sql_pVar = "insert  into `%titems___data`(`name`,`languages`,`tree`,`tree_defs`,`forms`) values (%s, 'sk','no','no','add_item,update_item');";
			if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($this->system_name_pVar))) {
				return(false);
			}
			if(count($this->systemProprties_pVar)) { // update na pozadovane hodnoty
				$sql_pVar = 'UPDATE `%titems___data` SET ';
				$i_pVar = 0;
				foreach($this->systemProprties_pVar as $k_pVar=>$v_pVar) {
					if($i_pVar) {
						$sql_pVar .= ', ';
					}
					$sql_pVar .= '`' . $k_pVar . '` = %s';
					$i_pVar++;
				}
				$sql_pVar .= ' WHERE `name` = %s';
				$params_pVar = $this->systemProprties_pVar;
				$params_pVar[] = $this->system_name_pVar;
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $params_pVar);
			}

			db_items_gClass::createBaseStruct_gFunc($this->system_name_pVar);
			db_items_gClass::applyLanguagesToTables_gFunc($this->system_name_pVar);
		}

		// fieldsets
		$sql_pVar = 'SELECT `fieldset_id`, `fieldset_name` FROM `%titems_'.$this->system_name_pVar.'__fieldsets`';
		$fieldsets_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array(), 'fieldset_name');
		$sql_pVar = 'SELECT max(`fieldset_order`) FROM `%titems_'.$this->system_name_pVar.'__fieldsets`';
		$fieldset_next_order_pVar = db_public_gClass::getfield_gFunc($sql_pVar, __FILE__, __LINE__);
		$fieldset_next_order_pVar++;

		foreach($this->fieldsets_pVar as $fieldset_pVar) {
			if(isset($fieldsets_pVar[$fieldset_pVar['fieldset_name']])) {
				// fieldset uz existuje...
				// @TODO: fieldset uz existuje..., co  tym?

			}
			else {
				$sql_pVar = 'INSERT INTO `%titems_'.$this->system_name_pVar.'__fieldsets` ';
				$sql_fields_pVar = array();
				$sql_str_pVar = array();
				$sql_data_pVar = array();
				$fieldset_pVar['fieldset_order'] = $fieldset_next_order_pVar;
				$fieldset_next_order_pVar++;
				foreach($fieldset_pVar as $k_pVar=>$v_pVar) {
					if(substr($k_pVar, 0, 1) == '_') {
						continue;
					}
					$sql_fields_pVar[] = $k_pVar;
					$sql_str_pVar[] = '%s';
					$sql_data_pVar[] = $v_pVar;
				}
				$sql_pVar .= ' (`' . implode('`, `', $sql_fields_pVar) . '`) ';
				$sql_pVar .= ' VALUES (' . implode(',', $sql_str_pVar) . ')';
				$fieldset_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar, true);
				if($fieldset_id_pVar) {
					$fieldsets_pVar[$fieldset_pVar['fieldset_name']] = array('fieldset_id'=>$fieldset_id_pVar, 'fieldset_name'=>$fieldset_pVar['fieldset_name']);
				}
			}
		}

		// fields
		$sql_pVar = 'SELECT `field_id`, `tag` FROM `%titems_'.$this->system_name_pVar.'__fields`';
		$fields_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array(), 'tag');

		foreach($this->fields_pVar as $field_pVar) {
			if($field_pVar['_'] == 'skip') {
				continue;
			}
			if(isset($fieldsets_pVar[$field_pVar['fieldset']])) {
				$field_pVar['fieldset'] = $fieldsets_pVar[$field_pVar['fieldset']]['fieldset_id'];
			}
			else {
				$field_pVar['fieldset'] = 0;
			}

			$sql_pVar = 'SELECT max(`field_order`) FROM `%titems_'.$this->system_name_pVar.'__fields` WHERE `fieldset` = %d';
			$field_next_order_pVar = db_public_gClass::getfield_gFunc($sql_pVar, __FILE__, __LINE__, $field_pVar['fieldset']);
			$field_next_order_pVar++;

			if(isset($fields_pVar[$field_pVar['tag']])) {
				// fieldset uz existuje...
				// @TODO: field uz existuje..., co s tym?
				if($field_pVar['_'] !== 'update') {
					continue;
				}
				$sql_pVar = 'UPDATE `%titems_'.$this->system_name_pVar.'__fields` SET ';
				$sql_str_pVar = array();
				$sql_data_pVar = array();
				foreach($field_pVar as $k_pVar=>$v_pVar) {
					if(substr($k_pVar, 0, 1) == '_') {
						continue;
					}
					$sql_str_pVar[] = '`' . $k_pVar . '` = %s';
					$sql_data_pVar[] = $v_pVar;
				}
				$sql_pVar .= implode(', ', $sql_str_pVar);
				$sql_pVar .= ' WHERE `field_id` = %d';
				$sql_data_pVar[] = $fields_pVar[$field_pVar['tag']]['field_id'];
				$field_updated_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar, true);
				if($field_updated_pVar && $field_updated_pVar !== true) {
					$fields_pVar[$field_pVar['tag']] = $field_pVar;
				}
			}
			else {
				if($field_pVar['_'] !== 'add') {
					continue;
				}
				$sql_pVar = 'INSERT INTO `%titems_'.$this->system_name_pVar.'__fields` ';
				$sql_fields_pVar = array();
				$sql_str_pVar = array();
				$sql_data_pVar = array();
				$field_pVar['field_order'] = $field_next_order_pVar;
				$field_next_order_pVar++;
				foreach($field_pVar as $k_pVar=>$v_pVar) {
					if(substr($k_pVar, 0, 1) == '_') {
						continue;
					}
					$sql_fields_pVar[] = $k_pVar;
					$sql_str_pVar[] = '%s';
					$sql_data_pVar[] = $v_pVar;
				}
				$sql_pVar .= ' (`' . implode('`, `', $sql_fields_pVar) . '`) ';
				$sql_pVar .= ' VALUES (' . implode(',', $sql_str_pVar) . ')';
				$field_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar, true);
				if($field_id_pVar) {
					$fields_pVar[$field_pVar['tag']] = array('field_id'=>$field_id_pVar, 'tag'=>$field_pVar['tag']);
				}
			}
		}

		// enums
		foreach($this->fields_pVar as $field_pVar) {
			if(!isset($fields_pVar[$field_pVar['tag']]) && !isset($fields_pVar[$field_pVar['tag']]['field_id'])) {
				continue;
			}
			else {
				$field_pVar['field_id'] = $fields_pVar[$field_pVar['tag']]['field_id'];
			}
			if(isset($field_pVar['type']) && $field_pVar['type'] !== 'enum' && $field_pVar['type'] !== 'set') {
				continue;
			}

			if(!isset($this->values_pVar[$field_pVar['tag']]) || !count($this->values_pVar[$field_pVar['tag']])) {
				continue;
			}

			$sql_pVar = 'SELECT `enum_id`, `enum_field_value` FROM `%titems_'.$this->system_name_pVar.'__values` WHERE `enum_field_id` = %d';
			$values_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($field_pVar['field_id']), 'enum_field_value');

			$sql_pVar = 'SELECT max(`enum_value_order`) FROM `%titems_'.$this->system_name_pVar.'__values` WHERE `enum_field_id` = %d';
			$value_next_order_pVar = db_public_gClass::getfield_gFunc($sql_pVar, __FILE__, __LINE__, array($field_pVar['field_id']));
			$value_next_order_pVar++;

			foreach($this->values_pVar[$field_pVar['tag']] as $value_pVar) {
				if(isset($values_pVar[$value_pVar['enum_field_value']])) {
					// value uz existuje
					if($value_pVar['_'] === 'update') {
						$sql_pVar = 'UPDATE `%titems_'.$this->system_name_pVar.'__values` ';
						$sql_fields_pVar = array();
						$sql_str_pVar = array();
						$sql_data_pVar = array();
						//$value_pVar['enum_value_order'] = $value_next_order_pVar;
						$value_pVar['enum_field_id'] = $field_pVar['field_id'];
						//$value_next_order_pVar++;
						foreach($value_pVar as $k_pVar=>$v_pVar) {
							if(substr($k_pVar, 0, 1) == '_' || $k_pVar == 'enum_field_old_value') {
								continue;
							}
							$sql_str_pVar[] = '`' . $k_pVar . '` = %s';
							$sql_data_pVar[] = $v_pVar;
						}
						$sql_pVar .= ' SET ' . implode(',', $sql_str_pVar);
						$sql_pVar .= ' WHERE `enum_id` = %d';
						$sql_data_pVar[] = $values_pVar[$value_pVar['enum_field_value']]['enum_id'];

						$updated_pVar = db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar, true);
						if($updated_pVar && $updated_pVar !== true) {
							$values_pVar[$value_pVar['enum_field_value']] = array('value_id'=>$values_pVar[$value_pVar['enum_field_value']]['enum_id'], 'enum_field_value'=>$value_pVar['enum_field_value']);
						}
					}
					elseif($value_pVar['_'] === 'delete') {
						$sql_pVar = 'DELETE FROM `%titems_'.$this->system_name_pVar.'__values`
										WHERE `enum_field_value` = %s';
						$deleted_pVar = db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($values_pVar[$value_pVar['enum_field_value']]['enum_field_value']), true);
						if($deleted_pVar && $deleted_pVar !== true) {
							unset($values_pVar[$value_pVar['enum_field_value']]);
						}
					}
					else {
						// @TODO: value uz existuje.. nemozem spravit insert... co s tym?
					}
				}
				else {
					if($value_pVar['_'] === 'add') {
						$sql_pVar = 'INSERT INTO `%titems_'.$this->system_name_pVar.'__values` ';
						$sql_fields_pVar = array();
						$sql_str_pVar = array();
						$sql_data_pVar = array();
						$value_pVar['enum_value_order'] = $value_next_order_pVar;
						$value_pVar['enum_field_id'] = $field_pVar['field_id'];
						$value_next_order_pVar++;
						foreach($value_pVar as $k_pVar=>$v_pVar) {
							if(substr($k_pVar, 0, 1) == '_') {
								continue;
							}
							$sql_fields_pVar[] = $k_pVar;
							$sql_str_pVar[] = '%s';
							$sql_data_pVar[] = $v_pVar;
						}
						$sql_pVar .= ' (`' . implode('`, `', $sql_fields_pVar) . '`) ';
						$sql_pVar .= ' VALUES (' . implode(',', $sql_str_pVar) . ')';
						$value_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, $sql_data_pVar, true);
						if($value_id_pVar) {
							$values_pVar[$value_pVar['enum_field_value']] = array('value_id'=>$value_id_pVar, 'enum_field_value'=>$value_pVar['enum_field_value']);
						}
					}
					elseif($value_pVar['_'] === 'update') {
						// update sa neda spravit, pretoze polozka neexistuje............
					}
					elseif($value_pVar['_'] === 'delete') {
						// delete sa neda spravit, pretoze polozka neexistuje............
					}
					else {
						continue;
					}
				}
			}
		}

		// forms
		if(count($this->forms_pVar)) {
			foreach($this->forms_pVar as $v_pVar) {
				$tmp_pVar = $v_pVar;
				unset($tmp_pVar['copy_from']);
				$newFormId_pVar = db_public_gClass::insertData_gFunc('%titems_' . $this->system_name_pVar . '__forms', '%s, %s', $tmp_pVar, __FILE__, __LINE__, true);
				if(isset($v_pVar['copy_from'])) { // urobim kopiu form_rules
					$oldFormId_pVar = db_public_gClass::getField_gFunc('SELECT `form_id` FROM `%titems_' . $this->system_name_pVar . '__forms` WHERE `form_name` = %s', __FILE__, __LINE__, array($v_pVar['copy_from']));
					db_public_gClass::execute_gFunc('INSERT INTO `%titems_' . $this->system_name_pVar . '__form_rules` (`form_id`, `field_name`, `field_access`, `field_init`, `field_value`) SELECT %d, `field_name`, `field_access`, `field_init`, `field_value` FROM `%titems_' . $this->system_name_pVar . '__form_rules` WHERE `form_id`=%d', __FILE__, __LINE__, array($newFormId_pVar, $oldFormId_pVar));
				}
			}
		}

		// form rules
		if(count($this->formRules_pVar)) {
			$sql_pVar = 'SELECT `form_id`, `form_name` FROM `%titems_' . $this->system_name_pVar . '__forms`';
			$tmp_forms_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array(), 'form_id');
			$forms_pVar = array();
			foreach ($tmp_forms_pVar as $v_pVar) {
				$forms_pVar[$v_pVar['form_name']] = $v_pVar['form_id'];
			}

			$sql_pVar = 'SELECT `form_rule_id`, `form_id`, `field_name` FROM `%titems_' . $this->system_name_pVar . '__form_rules`';
			$rules_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

			foreach($this->formRules_pVar as $rule_pVar) {
				$form_id_pVar = $forms_pVar[$rule_pVar['form_name']];
				if(!$form_id_pVar) {
					continue;
				}

				$found_pVar = false;
				foreach($rules_pVar as $k_pVar=>$v_pVar) {
					if($v_pVar['form_id'] == $form_id_pVar && $v_pVar['field_name'] == $rule_pVar['field_name']) {
						$found_pVar = $k_pVar;
						break;
					}
				}

				if($found_pVar === false) {
					$sql_pVar = 'INSERT INTO `%titems_' . $this->system_name_pVar . '__form_rules` '
					. '(`form_id`, `field_name`, `field_access`, `field_init`, `field_value`) '
					. 'VALUES(%d, %s, %s, %s, %xs)';
					$rule_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($form_id_pVar, $rule_pVar['field_name'], $rule_pVar['field_access'], $rule_pVar['field_init'], $rule_pVar['field_value']), true);
					$rules_pVar[] = array('form_rule_id'=>$rule_id_pVar, 'form_id'=>$form_id_pVar, 'field_name'=>$rule_pVar['form_name']);
				}
				else {
					$sql_pVar = 'UPDATE `%titems_' . $this->system_name_pVar . '__form_rules` SET '
					. '`field_access` = %s, `field_init` = %s, `field_value` = %xs'
					. ' WHERE `form_id`=%d AND `field_name`=%s';
					db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($rule_pVar['field_access'], $rule_pVar['field_init'], $rule_pVar['field_value'], $form_id_pVar, $rule_pVar['field_name']));
				}
			}
		}

		// form rights
		foreach($this->formRights_pVar as $rights_pVar) {
			$sql_pVar = 'INSERT INTO `%titems_' . $this->system_name_pVar . '__access` (`access_type`, `access_filter`, `access_field`, `access_rights`)'
				. ' VALUES (%s, %xs, %xs, %s)';
			db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($rights_pVar['access_type'], $rights_pVar['access_filter'], $rights_pVar['access_field'], $rights_pVar['access_rights']));
		}

		// indexes
		foreach($this->indexes_pVar as $index_pVar) {
			$sql_pVar = 'REPLACE INTO `%titems_' . $this->system_name_pVar . '__indexes` (`index_name`,`index_type`,`index_fields`) VALUES(%s, %s, %s)';
			db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($index_pVar['index_name'], $index_pVar['index_type'], $index_pVar['index_fields']));
		}

		db_items_gClass::applyLanguagesToTables_gFunc($this->system_name_pVar);
	}
}
