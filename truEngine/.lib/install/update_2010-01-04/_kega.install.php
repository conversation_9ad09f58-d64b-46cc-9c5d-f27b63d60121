<?php

if(!isStamp_gFunc('kega', 'kega update-2010-01-04')) {
	$items_pVar = new install_item_gClass('test_templates', false);
	$items_pVar->addField_gFunc('other', 'halloffame', 'enum', 'Hall of fame', array('not_null'=>'yes', 'sk_default_value'=>'no'));
	$items_pVar->setFormRule_gFunc('add_item', 'halloffame', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'halloffame', 'edit');
	
	$items_pVar->addEnum_gFunc('halloffame', 'yes', 'áno');
	$items_pVar->addEnum_gFunc('halloffame', 'no', 'nie');
		
	$items_pVar->apply_gFunc();

	$sql_pVar = "
		CREATE TABLE `kega_test_hall_of_fame` (
	  `id` int(10) unsigned NOT NULL auto_increment,
	  `test_id` int(10) unsigned NOT NULL,
	  `type` enum('test','template') default NULL,
	  `status` enum('archived','active') NOT NULL default 'active',
	  `user_id` int(10) unsigned NOT NULL,
	  `join_date` datetime default NULL,
	  `score` int(11) NOT NULL default '0',
	  `archive_date` datetime default NULL,
	  PRIMARY KEY  (`id`))";
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	stamp_gFunc('kega', 'kega update-2010-01-04');
}

if(!isStamp_gFunc('kega', 'kega update-2010-01-14')) {
	$items_pVar = new install_item_gClass('test_questions', false);
	$items_pVar->addField_gFunc('main', 'literatura_md5', 'xvarchar', 'Literatura md5', array('not_null'=>'yes', 'sk_default_value'=>''));
			
	$items_pVar->apply_gFunc();
	
	
	stamp_gFunc('kega', 'kega update-2010-01-14');
}

if(!isStamp_gFunc('items,kega', 'vytvorenie tabuliek items_literatura__')) {

	$items_pVar = new install_item_gClass('literatura', true);
	$items_pVar->setSystemProperty_gFunc('languages', 'sk');
	$items_pVar->setSystemProperty_gFunc('tree_defs', 'no');
	$items_pVar->setSystemProperty_gFunc('garant', 'no');
	
	$items_pVar->addFieldset_gFunc('main', 'Základné informácie');
		
	$items_pVar->addField_gFunc('main', 'name', 'xtext', 'Názov', array('not_null'=>'yes', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'autor', 'xvarchar', 'Autor', array('not_null'=>'yes', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'vydavatelstvo', 'xvarchar', 'Vydavateľstvo', array('not_null'=>'yes', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'year', 'int',  'Rok vydania', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'image', 'ximagelist',  'Foto');
	$items_pVar->addField_gFunc('main', 'qmd5', 'xvarchar',  'Qmd5');
	$items_pVar->addField_gFunc('main', 'search_name', 'xtext',  'Search name');
	$items_pVar->addField_gFunc('main', 'info', 'xvarchar', 'Info (ISBN, strany, a iné.)', array('not_null'=>'yes', 'order_by'=>'yes'));

	$items_pVar->addField_gFunc('main', 'link', 'xvarchar',  'Linka na online verziu', array('order_by'=>'no'));
		
	$items_pVar->setFormRule_gFunc('add_item', 'name', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'autor', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'vydavatelstvo', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'image', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'year', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'link', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'info', 'edit');

	$items_pVar->setFormRule_gFunc('edit_item', 'name', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'autor', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'vydavatelstvo', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'image', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'year', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'link', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'info', 'edit');
	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('items,kega', 'vytvorenie tabuliek items_literatura__');
}

if(!isStamp_gFunc('items,kega', 'alter table files by literatura')) {
	$sql_pVar = 'show full fields from `%tfiles`';
	$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Field');
	$enumDef_pVar = $data_pVar['ref_tag']['Type'];
	if(strpos($enumDef_pVar, '\'items_literatura_image\'') === false) {
		$enumDef_pVar = str_replace(')', ',\'items_literatura_image\')', $enumDef_pVar);
	}
	
	$default_pVar = !empty($data_pVar['ref_tag']['Default'])?('\''.$data_pVar['ref_tag']['Default'].'\''):'NULL';
	$sql_pVar = 'alter table `%tfiles` change `ref_tag` `ref_tag` ' . $enumDef_pVar . ' DEFAULT '.$default_pVar.' NOT NULL ';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('items,kega', 'alter table files by literatura');	
}

if(!isStamp_gFunc('access3_rights,kega', 'inicializacia prav literatura')) {
	
	$sql_pVar = "
		insert  into `%titems_literatura__access`(`access_type`,`access_filter`,`access_field`,`access_rights`)
		values ('get',			'status=active',						NULL,	's_test_literatura')
			  ,('insert',		'status=active',						NULL,	's_test_literatura_add')
			  ,('update',		'status=active',						NULL,	's_test_literatura_edit')
			  ,('delete',		'status=active',						NULL,	's_test_literatura_delete')
			  ,('update_from',	'status=active',						NULL,	's_test_literatura_edit')
			  ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access3_rights,kega', 'inicializacia prav literatura');
}

if(!isStamp_gFunc('access3_rights,kega', 'literatura update')) {
	$sql_pVar = 'INSERT INTO %titems_literatura__data (name, search_name, qmd5)
				SELECT DISTINCT literatura, literatura, MD5(literatura) FROM kega_items_test_questions__data';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = 'INSERT INTO %titems_literatura__data (name, search_name, qmd5)
				SELECT DISTINCT kniha, kniha, MD5(kniha) FROM %ttest_work';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = 'UPDATE %titems_test_questions__data SET literatura_md5 = md5(literatura)';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	// odstranim duplicity
	$sql_pVar = 'CREATE TABLE %titems_literatura__data_tmp as SELECT * FROM %titems_literatura__data GROUP BY qmd5';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) { return(false); }
	$sql_pVar = 'TRUNCATE TABLE %titems_literatura__data';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) { return(false); }
	$sql_pVar = 'INSERT INTO %titems_literatura__data SELECT * FROM %titems_literatura__data_tmp';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) { return(false); }
	$sql_pVar = 'DROP TABLE %titems_literatura__data_tmp';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) { return(false); }
	
	stamp_gFunc('access3_rights,kega', 'literatura update');
}

return(true);