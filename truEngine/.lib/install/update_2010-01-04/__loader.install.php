<?php

if(!isStamp_gFunc('_loader', 'kegahalloffame')) {
	
	install_gClass::addAccessName_gFunc('test', 'hall_of_fame', 'Zobraziť hall of fame', 'kega', false);
	install_gClass::addAccessName_gFunc('test', 'hall_of_fame_archive', 'Zmazať hall of fame', 'kega', true);
	
	main_gClass::initRights_gFunc(true);
	stamp_gFunc('_loader', 'kegahalloffame');
}

if(!isStamp_gFunc('_loader', 'literatura_rights')) {
	install_gClass::addAccessName_gFunc('test', 'literatura', 'Zobraziť zoznam literatúry', 'kega', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'literatura_add', 'Pridať novú literatúru', 'kega', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'literatura_edit', 'Editovať literatúru', 'kega', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'literatura_delete', 'Zmazať literatúru', 'kega', 'kega', true);
	install_gClass::addAccessName_gFunc('test', 'literatura_organize', 'Zlúčiť literatúru', 'kega', 'kega', true);
	
	stamp_gFunc('_loader', 'literatura_rights');
}

if(!isStamp_gFunc('_loader', 'keywords_rights')) {
	install_gClass::addAccessName_gFunc('test', 'keywords_organize', 'Organizovať kľúčové slová', 'kega', 'kega', true);
	stamp_gFunc('_loader', 'keywords_rights');
}

return(true);