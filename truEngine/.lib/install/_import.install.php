<?php 

if(!isStamp_gFunc('import', 'import')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%timport_log` (
			`import_id` int(10) unsigned NOT NULL auto_increment,
			`user_id` int(10) unsigned NOT NULL default '0',
			`import_datetime` datetime NOT NULL default '0000-00-00 00:00:00',
			
			`export_settings` text,
			`export_comment` text,
			`export_source` varchar(255),
			
			`tmp_file`	varchar(255),
			`finished` bool  DEFAULT '0' NULL,
			PRIMARY KEY  (`import_id`)
			) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('import', 'import');
}


return(true);