<?php 

if(!isStamp_gFunc('items,access2_session', 'vytvorenie tabuliek items_users__')) {
	
	$items_pVar = new install_item_gClass('users', true);
	$items_pVar->updateEnum_gFunc('status', 'deleted', 'deleted', 'Zmazať', array('sk_enum_confirm_set'=>'Naozaj chcete používateľa zmazať?', 'sk_enum_confirm_unset'=>'Používateľ nebude zmazaný.'));
	$items_pVar->addEnum_gFunc('status', 'request', 'Žiadosť o registráciu');
	
	$items_pVar->addFieldset_gFunc('status', 'Stavové informácie');
	$items_pVar->addFieldset_gFunc('personal', 'Osobné informácie');
	$items_pVar->addFieldset_gFunc('login', 'Prihlasovacie informácie');
	$items_pVar->addFieldset_gFunc('rights', 'Práva');
	$items_pVar->addFieldset_gFunc('languages', 'Nastavenie jazyka');
	
	$items_pVar->addField_gFunc('status', 'time_update', 'timestamp', 'Posledná aktualizácia', array('not_null'=>'yes', 'sk_default_value'=>'!CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP', 'comment'=>'Čas poslednej aktualizacie riadku', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('status', 'time_last_access', 'timestamp', 'Čas poslednej aktivity', array('comment'=>'Cas poslednej aktivity.', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('status', 'last_remote_ip', 'xvarchar', 'Posledný prístup z IP', array('len'=>'16', 'comment'=>'Posledna aktivita z IP.'));
	$items_pVar->addField_gFunc('status', 'last_remote_key', 'xvarchar', 'Posledný kľúč', array('len'=>'16', 'comment'=>'Posledna aktivita s klucom...'));
	$items_pVar->addField_gFunc('status', 'time_last_login', 'timestamp', 'Posledné prihlásenie', array('comment'=>'Cas posledneho prihlasenia.', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('status', 'last_login_ip', 'xvarchar', 'Posledné prihlásenie z IP', array('len'=>'16', 'comment'=>'Naposledy prihlásený z IP.'));
	$items_pVar->addField_gFunc('login', 'enabled_remote_ips', 'xvarchar', 'Povolené IP adresy', array('len'=>'254', 'comment'=>'Povolene IP adresy. IP/maska oddelene ciarkou, bez bodiek a / ***************/32 = 25525525525532, max 17 IP (za poslednou nie je ciarka). Ak je NULL, da sa prihladit z hociakej IP. Inak sa da prihlasit len z uvedenych.'));
	$items_pVar->addField_gFunc('login', 'enabled_remote_keys', 'xvarchar', 'Povolené kľúče', array('len'=>'230', 'comment'=>'Povolene kluce. 32 bajtove kluce oddelene ciarkou. max 7 klucov (za poslednym nie je ciarka); Ak je NULL, kluc nie je pozadovany. Inak musi byt kluc nastaveny v secure cookies.'));
	$items_pVar->addField_gFunc('login', 'timeout', 'int', 'Timeout', array('sk_default_value'=>'30', 'pattern'=>'/[0-9]+/', 'min_value'=>'0', 'comment'=>'Timeout v minutach'));
	$items_pVar->addField_gFunc('login', 'login_enabled', 'enum', 'Prihlásenie povolené', array('not_null'=>'yes', 'sk_default_value'=>'enabled', 'comment'=>'Povolenie/zakázanie prihlásenia'));
	$items_pVar->addField_gFunc('login', 'disabled_login_level', 'enum', 'Dočasne zakázané prihlásenie', array('not_null'=>'yes', 'sk_default_value'=>'enabled', 'comment'=>'Uroven docasne zakazaneho prihlasenia, ak zle zadal heslo.'));
	$items_pVar->addField_gFunc('login', 'disabled_login', 'timestamp', 'Dočasne zakázané prihlásenie do', array('comment'=>'Cas dokedy je docasne zakazane prihlasenie. Ak je level disabled, cas sa neuplatnuje a je blokovany nadobro. Ak nie je blokovany, musi byt null alebo mensi ako aktualny cas.'));
	$items_pVar->addField_gFunc('login', 'login', 'xvarchar', 'Prihlasovacie meno', array('len'=>'255', 'not_null'=>'yes', 'pattern'=>'/^[a-z0-9\.]+(@[a-z0-9\.]+)?$/i', 'min_value'=>'0', 'order_by'=>'yes', 'sk_field_info'=>'::Meno, ktorým sa budete prihlasovať do systému.<br />Zvoľte si ľahko zapamätateľný názov.<br />Dobrou voľbou môže byť Vaša emailová adresa.<br />Ostatným používateľom sa nebude Vaše prihlasovacie<br />meno zobrazovať, budú vidieť Váš nick.'));
	$items_pVar->addField_gFunc('login', 'password', 'password', 'Prihlasovacie heslo|Kontrola hesla', array('len'=>'40', 'min_value'=>'5'));
	$items_pVar->addField_gFunc('personal', 'last_name', 'xvarchar', 'Priezvisko', array('len'=>'255', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('personal', 'first_name', 'xvarchar', 'Krstné meno', array('len'=>'255', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('personal', 'titul_pred', 'xvarchar', 'Tituly pred menom', array('len'=>'100'));
	$items_pVar->addField_gFunc('personal', 'titul_za', 'xvarchar', 'Tituly za menom', array('len'=>'100'));
	$items_pVar->addField_gFunc('personal', 'nick', 'xvarchar', 'Zobrazované meno (nick)', array('len'=>'255', 'not_null'=>'yes', 'order_by'=>'yes', 'sk_field_info'=>'::Meno, pod ktorým Vás budú vidieť ostatní používatelia.<br />Môžete si sem nastaviť \'Meno priezvisko\', alebo inú prezývku.'));
	$items_pVar->addField_gFunc('personal', 'gender', 'enum', 'Pohlavie', array('not_null'=>'yes', 'sk_default_value'=>'notset'));
	$items_pVar->addField_gFunc('personal', 'email', 'email', 'E-mailová adresa', array('len'=>'255', 'not_null'=>'yes', 'order_by'=>'yes', 'sk_field_info'=>'::Zadajte svoju e-mailovú adresu.<br />Adresa sa nebude zobrazovať bežným používateľom.<br />Adresa musí byť zadaná správne, je overená overovacím emailom.'));
	$items_pVar->addField_gFunc('personal', 'email_check_status', 'enum', 'E-mail overený', array('not_null'=>'yes', 'sk_default_value'=>'notset'));
	$items_pVar->addField_gFunc('personal', 'foto', 'ximagelist', 'Fotografia');
	$items_pVar->addField_gFunc('rights', 'user_role', 'enum', 'Rola', array('not_null'=>'yes', 'sk_default_value'=>'none', 'comment'=>'Rola používateľa (superuser, admin, user, none,...)'));
	$items_pVar->addField_gFunc('rights', 'user_groups', 'xvarchar', 'Skupiny', array('len'=>'255','not_null'=>'yes', 'sk_default_value'=>'', 'comment'=>'Skupiny používateľa (idcka oddelene ciarkou, zacina sa a konci sa ciarkou. Sluzi hlavne pre filter. Pre ucely prav sa zistuje z inej tabulky.)'));
	$items_pVar->addField_gFunc('status', 'join_users_onlineusers', 'join', 'join_to_users_onlineusers', array('not_null'=>'yes', 'comment'=>'`online`.*|LEFT JOIN `%tusers_onlineusers` as `online` ON `online`.`user_id`=`D`.`item_id`'));
	$items_pVar->addField_gFunc('languages', 'default_language', 'enum', 'Predvolený jazyk', array('not_null'=>'yes', 'sk_default_value'=>'sk'));
	
	$items_pVar->addEnum_gFunc('user_role', 'superuser', 'Superuser');
	$items_pVar->addEnum_gFunc('user_role', 'admin', 'Administrátor');
	$items_pVar->addEnum_gFunc('user_role', 'user', 'Používateľ');
	$items_pVar->addEnum_gFunc('user_role', 'none', 'bez roly');

	$items_pVar->addEnum_gFunc('login_enabled', 'enabled', 'povolené');
	$items_pVar->addEnum_gFunc('login_enabled', 'disabled', 'zakázané');

	$items_pVar->addEnum_gFunc('disabled_login_level', 'enabled', 'povolené');
	$items_pVar->addEnum_gFunc('disabled_login_level', 'level1', 'level1');
	$items_pVar->addEnum_gFunc('disabled_login_level', 'level2', 'level2');
	$items_pVar->addEnum_gFunc('disabled_login_level', 'level3', 'level3');
	$items_pVar->addEnum_gFunc('disabled_login_level', 'disabled', 'zakázané');
	
	$items_pVar->addEnum_gFunc('gender', 'male', 'muž');
	$items_pVar->addEnum_gFunc('gender', 'female', 'žena');
	$items_pVar->addEnum_gFunc('gender', 'notset', 'nenastavené');
	
	$items_pVar->addEnum_gFunc('email_check_status', 'notset', 'neoverený');
	$items_pVar->addEnum_gFunc('email_check_status', 'changed', 'zmenený');
	$items_pVar->addEnum_gFunc('email_check_status', 'checked', 'overený');
	$items_pVar->addEnum_gFunc('email_check_status', 'protected', 'neoveruje sa');
	
	$items_pVar->addEnum_gFunc('default_language', 'sk', 'slovensky');
	
	//function add_right_gFunc($access_type, $access_right, $access_filter = null, $access_field = null)
	$items_pVar->add_right_gFunc('get', 's_users_show_user');
	$items_pVar->add_right_gFunc('get', 's_users_show_user_owned','owner_id=@{session:user_id}');
	
	$items_pVar->add_right_gFunc('insert', 's_users_add_user','user_role=user');
	$items_pVar->add_right_gFunc('update', 's_users_edit_user','user_role=user');
	$items_pVar->add_right_gFunc('update_from', 's_users_edit_user','user_role=user');
	$items_pVar->add_right_gFunc('insert', 's_users_add_user','user_role=none');
	$items_pVar->add_right_gFunc('update', 's_users_edit_user','user_role=none');
	$items_pVar->add_right_gFunc('update_from', 's_users_edit_user','user_role=none');
	$items_pVar->add_right_gFunc('insert', 's_users_add_superadmin','user_role=superuser');
	$items_pVar->add_right_gFunc('update', 's_users_edit_superadmin','user_role=superuser');
	$items_pVar->add_right_gFunc('update_from', 's_users_edit_superadmin','user_role=superuser');
	$items_pVar->add_right_gFunc('insert', 's_users_add_admin','user_role=admin');
	$items_pVar->add_right_gFunc('update', 's_users_edit_admin','user_role=admin');
	$items_pVar->add_right_gFunc('update_from', 's_users_edit_admin','user_role=admin');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself','item_id=@{session:user_id}&user_role<>admin&user_role<>superuser'); // musi to byt takto osetrene, lebo inak by sa mohol admin sam promotnut na superadmina
	$items_pVar->add_right_gFunc('update_from', 's_users_edit_myself','item_id=@{session:user_id}');
	$items_pVar->add_right_gFunc('delete', 's_users_delete_superadmin','user_role=superuser');
	$items_pVar->add_right_gFunc('delete', 's_users_delete_admin','user_role=admin');
	$items_pVar->add_right_gFunc('delete', 's_users_delete_user','user_role<>admin&user_role<>superuser');
	$items_pVar->add_right_gFunc('delete', 's_users_delete_admin_owned','user_role=admin&owner_id=@{session:user_id}');
	$items_pVar->add_right_gFunc('delete', 's_users_delete_user_owned','owner_id=@{session:user_id}&user_role<>admin&user_role<>superuser');
	
	$items_pVar->apply_gFunc();
	
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->updateField_gFunc('', 'status', 'enum', 'Status', array('pattern'=>'/active|deleted|request/'));
	$items_pVar->apply_gFunc();
	

/*



	$sql_pVar = "update `%titems_users__fields` set `fieldset`=1 WHERE `tag` = 'status'";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	db_items_gClass::applyFieldsToDataTable_gFunc('users');
	*/

	$sql_pVar = "insert  into `%titems_users__data`(`login`,`nick`,`user_role`) values ('admin','ADMIN','superuser')";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('items,access2_session', 'vytvorenie tabuliek items_users__');
}

if(!isStamp_gFunc('access2_session', 'vytvorenie tabulky users_onlineusers')) {
	$sql_pVar = "
		CREATE TABLE `%tusers_onlineusers` (
			`online_user_id` int(10) unsigned NOT NULL auto_increment,
			`session_str_id` varchar(50) NOT NULL default '',
			`session_status` enum('online','timeouted','timeout','restoring') NOT NULL default 'online',
			`time_start` timestamp NULL default '0000-00-00 00:00:00',
			`time_last_access` timestamp NULL default NULL,
			`time_next_timeout` timestamp NULL default NULL,
			`timeout` smallint(5) unsigned NOT NULL default '30',
			`user_id` int(10) unsigned NOT NULL default '0',

			`pc_str_id` varchar(50) NOT NULL default '',
			`remote_ip` varchar(14) NOT NULL default '',
			`user_agent` varchar(255) NOT NULL default '',

			`rights` blob,
			`current_key` varchar(32) default NULL,
			PRIMARY KEY  (`online_user_id`),
			UNIQUE KEY `session_str_id` (`session_str_id`)
		) ENGINE=InnoDB
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access2_session', 'vytvorenie tabulky users_onlineusers');
}

if(!isStamp_gFunc('access2_session', 'vytvorenie tabulky users_accesslog')) {
	$sql_pVar = "
		CREATE TABLE `%tusers_accesslog` (
			`access_id` int(10) unsigned NOT NULL auto_increment,
			`user_id` int(10) unsigned NOT NULL default '0',
			`access_time` timestamp NOT NULL default '0000-00-00 00:00:00',
			`access_type` enum('login','logout','timeout','test') default NULL,
			PRIMARY KEY  (`access_id`)
		) ENGINE=InnoDB
    ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access2_session', 'vytvorenie tabulky users_accesslog');
}

if(!isStamp_gFunc('access2_session', 'vytvorenie tabulky users_requestlog')) {
	$sql_pVar = "
		CREATE TABLE `%tusers_requestlog` (
			`request_id` int(10) unsigned NOT NULL auto_increment,
			`user_id` int(10) unsigned NOT NULL default '0',
			`request_time` timestamp NOT NULL default '0000-00-00 00:00:00',
			`request_type` enum('request','autoaccept','accept','delete') default NULL,
			`admin_user_id` int unsigned NULL,
			PRIMARY KEY  (`request_id`)
		) ENGINE=InnoDB
    ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access2_session', 'vytvorenie tabulky users_requestlog');
}


if(!isStamp_gFunc('items,access2_session', 'prava k fieldom users')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->add_right_gFunc('get', 's_users_show_field_email', 'item_id<>@{session:user_id}', 'email');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_last_remote_ip', 'item_id<>@{session:user_id}', 'last_remote_ip');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_last_login_ip', 'item_id<>@{session:user_id}', 'last_login_ip');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_last_remote_key', 'item_id<>@{session:user_id}', 'last_remote_key');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_enabled_remote_ips', 'item_id<>@{session:user_id}', 'enabled_remote_ips');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_enabled_remote_keys', 'item_id<>@{session:user_id}', 'enabled_remote_keys');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_email_check_status', 'item_id<>@{session:user_id}', 'email_check_status');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_date_of_birth', 'item_id<>@{session:user_id}', 'date_of_birth');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_mobil', 'item_id<>@{session:user_id}', 'mobil');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_password', 'item_id<>@{session:user_id}', 'password');
	
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_status', 'item_id=@{session:user_id}', 'status');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_last_name', 'item_id=@{session:user_id}', 'last_name');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_first_name', 'item_id=@{session:user_id}', 'last_first_name');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_timeout', 'item_id=@{session:user_id}', 'timeout');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_login_enabled', 'item_id=@{session:user_id}', 'login_enabled');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_disabled_login_level', 'item_id=@{session:user_id}', 'disabled_login_level');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_login', 'item_id=@{session:user_id}', 'login');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_password', 'item_id=@{session:user_id}', 'password');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_titul_pred', 'item_id=@{session:user_id}', 'titul_pred');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_titul_za', 'item_id=@{session:user_id}', 'titul_za');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_nick', 'item_id=@{session:user_id}', 'nick');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_gender', 'item_id=@{session:user_id}', 'gender');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_email', 'item_id=@{session:user_id}', 'email');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_foto', 'item_id=@{session:user_id}', 'foto');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_user_role', 'item_id=@{session:user_id}', 'user_role');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_default_language', 'item_id=@{session:user_id}', 'default_language');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_date_of_birth', 'item_id=@{session:user_id}', 'date_of_birth');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_address', 'item_id=@{session:user_id}', 'street');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_address', 'item_id=@{session:user_id}', 'city');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_address', 'item_id=@{session:user_id}', 'country');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_mobil', 'item_id=@{session:user_id}', 'mobil');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_icq', 'item_id=@{session:user_id}', 'icq');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_msn', 'item_id=@{session:user_id}', 'msn');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_skype', 'item_id=@{session:user_id}', 'skype');

	$items_pVar->apply_gFunc();
							
	stamp_gFunc('items,access2_session', 'prava k fieldom users');
}


return(true);