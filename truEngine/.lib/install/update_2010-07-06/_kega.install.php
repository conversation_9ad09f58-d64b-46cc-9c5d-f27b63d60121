<?php

if(!isStamp_gFunc('kega', 'kega update-2010-07-06')) {
	$sql_pVar = 'show full fields from `%tfiles`';
	$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Field');
	$enumDef_pVar = $data_pVar['ref_tag']['Type'];
	if(strpos($enumDef_pVar, '\'items_test_answers_cz_odpoved_media\'') === false) {
		$enumDef_pVar = str_replace(')', ',\'items_test_answers_cz_odpoved_media\')', $enumDef_pVar);
	}
	if(strpos($enumDef_pVar, '\'items_test_questions_cz_otazka_media\'') === false) {
		$enumDef_pVar = str_replace(')', ',\'items_test_questions_cz_otazka_media\')', $enumDef_pVar);
	}
	
	$default_pVar = !empty($data_pVar['ref_tag']['Default'])?('\''.$data_pVar['ref_tag']['Default'].'\''):'NULL';
	$sql_pVar = 'alter table `%tfiles` change `ref_tag` `ref_tag` ' . $enumDef_pVar . ' DEFAULT '.$default_pVar.' NOT NULL ';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
		
	stamp_gFunc('kega', 'kega update-2010-07-06');
}

if(!isStamp_gFunc('kega', 'kega update-2010-07-09')) {
	$items_pVar = new install_item_gClass('test_questions', false);
	$items_pVar->addField_gFunc('main', 'literatura_strana', 'xvarchar', 'Literatúra strana', array('len'=>255, 'field_order'=>5, 'sk_field_info'=>'::Zadajte stranu literatúry, alebo bližšie určenie jej časti.'));	
	$items_pVar->setFormRule_gFunc('edit_item', 'literatura_strana', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'literatura_strana', 'edit');
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega update-2010-07-09');
}

if(!isStamp_gFunc('kega', 'kega update-2010-07-09 x')) {
	$items_pVar = new install_item_gClass('test_questions', false);
	// este update, koli order=5
	$items_pVar->updateField_gFunc('main', 'literatura_strana', 'xvarchar', 'Literatúra strana', array('len'=>255, 'field_order'=>5, 'sk_field_info'=>'::Zadajte stranu literatúry, alebo bližšie určenie jej časti.'));	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega update-2010-07-09 x');
}

if(!isStamp_gFunc('kega', 'kega update-2010-07-16')) {
	$items_pVar = new install_item_gClass('test_questions', false);
	$items_pVar->updateField_gFunc('main', 'otazka', 'text', 'Znenie otázky (text)', array('not_null'=>'no'));	
	$items_pVar->apply_gFunc();
	
	$items_pVar = new install_item_gClass('test_answers', false);
	$items_pVar->updateField_gFunc('main', 'odpoved', 'text', 'Znenie odpovede (text)', array('not_null'=>'no'));	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega update-2010-07-16');
}

if(!isStamp_gFunc('kega', 'kega update-2010-07-16 2')) {
	$sql_pVar = 'ALTER TABLE `%ttests_running` ADD COLUMN `ip` VARCHAR(255) NULL AFTER `settings`';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	stamp_gFunc('kega', 'kega update-2010-07-16 2');
}

return(true);