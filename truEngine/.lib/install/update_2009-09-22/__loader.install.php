<?php

if(!isStamp_gFunc('_loader', 'facebook')) {
	
	install_gClass::addAccessName_gFunc('users', 'edit_myself_field_facebook', 'Editovať vlastný profil (pole \'facebook\')', 'access2_session', false);
	
	install_gClass::addAccessName_gFunc('users', 'show_field_msn', 'Čítať používateľský profil (pole \'msn\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_skype', '<PERSON><PERSON><PERSON><PERSON> používateľský profil (pole \'skype\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_icq', 'Čítať používateľský profil (pole \'icq\')', 'access2_session', false);
	install_gClass::addAccessName_gFunc('users', 'show_field_facebook', '<PERSON><PERSON>ta<PERSON> používateľský profil (pole \'facebook\')', 'access2_session', false);
	main_gClass::initRights_gFunc(true);
	
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addField_gFunc('kontakt', 'facebook', 'xvarchar', 'Facebook', array('len'=>'255'));
	$items_pVar->setFormRule_gFunc('add_item', 'facebook', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'facebook', 'edit');
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('_loader', 'facebook');
}

return(true);