<?php 

if(!isStamp_gFunc('maintainance', 'log table')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tlog` (
			`request_id` varchar(255),
			`time_sec` int,
			`time_usec` int,
			`tag` varchar(255),
			`value` text,
			PRIMARY KEY  (`request_id`, `time_sec`, `time_usec`)
			) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('maintainance', 'log table');
}

if(!isStamp_gFunc('maintainance', 'maintainance tables')) {
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__documents` (              
                                `document_id` int unsigned NOT NULL AUTO_INCREMENT,  
                                `document_name` text,                                    
                                `system_call` tinyint(1) DEFAULT '0',                    
                                PRIMARY KEY (`document_id`),
                                KEY `docname` (`document_name`(4))                               
                              ) ENGINE=InnoDB;
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__access` (
								`year` tinyint,
								`day`  smallint,
								`pcid` varchar(255),
								`user_id` int unsigned NOT NULL,
                                `document_id` int unsigned NOT NULL,                    
                                PRIMARY KEY (`year`, `day`, `document_id`, `pcid`, `user_id`)                              
                              ) ENGINE=InnoDB;
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tmaintainance__queries_slow` (
			`id` bigint(20) NOT NULL auto_increment,
		    `time_executed` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
		    `duration`	float,
		    `source` text,
		    `query` text,
		    `document_id` int unsigned,
		    PRIMARY KEY  (`id`),
			KEY  `inserted` (`time_executed`)
			) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tmaintainance__queries_base` (  
                                   `md5` varchar(32) NOT NULL,                     
                                   `last_time_executed` datetime NOT NULL,
                                   `parametric` tinyint(4) NOT NULL default '0',     
                                   `optimised` tinyint(4) NOT NULL default '0',  
                                   `count` int(11) NOT NULL,                       
                                   `duration` float NOT NULL,                      
                                   `query` text NOT NULL,                          
                                   `source` text NOT NULL,                         
                                   `params_example` text NOT NULL,                 
                                   PRIMARY KEY (`md5`)                             
                                 ) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tmaintainance__errors` (
			`id` bigint(20) NOT NULL auto_increment,
		    `time_executed` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
		    `error_type` varchar(255),
		    `error` text,
		    `document_id` int unsigned,
		    PRIMARY KEY  (`id`),
			KEY  `inserted` (`time_executed`)
			) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_documents_per_day` (                             
                                               `year` tinyint unsigned NOT NULL COMMENT 'rok vo formate RR',
                                               `day` smallint unsigned NOT NULL COMMENT 'den v roku',                           
                                               `document_id` int unsigned NOT NULL,
                                               `document_redirected` int unsigned DEFAULT 0 COMMENT 'pocet vykonani, ktore skoncili redirectom. (Tieto vkonania nie su zapocitane v document_executed ani v document_executed_unique',                                               
                                               `document_executed` int unsigned DEFAULT 0 COMMENT 'pocet vykonani',
                                               `document_executed_unique` int unsigned DEFAULT 0 COMMENT 'pocet unikatnych vykonani vrmci dna',  
                                               PRIMARY KEY (`year`, `day`,`document_id`)                                              
                                             ) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_documents_per_week` (                             
                                               `year` tinyint unsigned NOT NULL COMMENT 'rok vo formate RR',                       
                                               `week` tinyint unsigned NOT NULL COMMENT 'tyzden v roku',                           
                                               `document_id` int unsigned NOT NULL,
                                               `document_redirected` int unsigned DEFAULT 0 COMMENT 'pocet vykonani, ktore skoncili redirectom. (Tieto vkonania nie su zapocitane v document_executed ani v document_executed_unique',
                                               `document_executed` int unsigned DEFAULT 0 COMMENT 'pocet vykonani',
                                               `document_executed_unique` int unsigned DEFAULT 0 COMMENT 'pocet unikatnych vykonani vrmci tyzdna',
                                               `document_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania casov',     
                                               `document_memory_sum` bigint unsigned DEFAULT 0 COMMENT 'sucet pouzitej pamete',
                                                  
                                               `query_executed_sum_level_0_001` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.001',        
                                               `query_executed_sum_level_0_01` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.01',        
                                               `query_executed_sum_level_0_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.1',        
                                               `query_executed_sum_level_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<1',        
                                               `query_executed_sum_level_3` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<3',
                                               `query_executed_sum` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov',
                                               `query_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania sql dotazov',
                                               `query_bytes_sum` int unsigned DEFAULT 0 COMMENT 'sucet prenesenych bytes sql dotazov',
                                               
                                               `notice_sum` int unsigned DEFAULT 0 COMMENT 'sucet notice',
                                               `warning_sum` int unsigned DEFAULT 0 COMMENT 'sucet warningov',
                                               `error_sum` int unsigned DEFAULT 0 COMMENT 'sucet errorov',
                                               `fatal_sum` int unsigned DEFAULT 0 COMMENT 'sucet fatalov',
                                               `uncompleted_sum` int unsigned DEFAULT 0 COMMENT 'sucet nedokoncenych vykonani',
                                               `doc_error` int unsigned DEFAULT 0 COMMENT 'sucet dokumentov s vyskytom chyby',
                                               PRIMARY KEY (`year`,`week`,`document_id`)                                              
                                             ) ENGINE=InnoDB
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_documents_per_month` (                             
                                               `year` tinyint unsigned NOT NULL COMMENT 'rok vo formate RR',                       
                                               `month` tinyint unsigned NOT NULL COMMENT 'mesiac',                           
                                               `document_id` int unsigned NOT NULL,
                                               `document_redirected` int unsigned DEFAULT 0 COMMENT 'pocet vykonani, ktore skoncili redirectom. (Tieto vkonania nie su zapocitane v document_executed ani v document_executed_unique',
                                               `document_executed` int unsigned DEFAULT 0 COMMENT 'pocet vykonani',
                                               `document_executed_unique` int unsigned DEFAULT 0 COMMENT 'pocet unikatnych vykonani vrmci mesiaca',
                                               `send_mail` int unsigned DEFAULT 0 COMMENT 'pocet SEND_MAIL',  
                                               PRIMARY KEY (`year`,`month`,`document_id`)                                              
                                             ) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_per_hour` (                             
                                               `year` tinyint unsigned NOT NULL COMMENT 'rok vo formate RR',                       
                                               `day` smallint unsigned NOT NULL COMMENT 'den v roku',
                                               `hour` tinyint unsigned NOT NULL COMMENT 'hodina',
                                               `system`	tinyint unsigned NOT NULL COMMENT 'priznak, ci je to systemovy zaznam',
                                               `document_redirected` int unsigned DEFAULT 0 COMMENT 'pocet vykonani, ktore skoncili redirectom. (Tieto vkonania nie su zapocitane v document_executed ani v document_executed_unique',
                                               `document_executed` int unsigned DEFAULT 0 COMMENT 'pocet vykonani',
                                               `document_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania casov',     
                                               `document_memory_sum` bigint unsigned DEFAULT 0 COMMENT 'sucet pouzitej pamete',
                                               `document_total_bytes` bigint unsigned DEFAULT 0 COMMENT 'prenesene data',
                                                  
                                               `query_executed_sum_level_0_001` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.001',        
                                               `query_executed_sum_level_0_01` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.01',        
                                               `query_executed_sum_level_0_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.1',        
                                               `query_executed_sum_level_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<1',        
                                               `query_executed_sum_level_3` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<3',
                                               `query_executed_sum` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov',
                                               `query_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania sql dotazov',
                                               `query_bytes_sum` int unsigned DEFAULT 0 COMMENT 'sucet prenesenych bytes sql dotazov',
                                               PRIMARY KEY (`year`,`day`,`hour`,`system`)                                              
                                             ) ENGINE=InnoDB
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_per_day` (                             
                                               `year` tinyint unsigned NOT NULL COMMENT 'rok vo formate RR',                       
                                               `day` smallint unsigned NOT NULL COMMENT 'den v roku',
                                               `system`	tinyint unsigned NOT NULL COMMENT 'priznak, ci je to systemovy zaznam',
                                               `document_redirected` int unsigned DEFAULT 0 COMMENT 'pocet vykonani, ktore skoncili redirectom. (Tieto vkonania nie su zapocitane v document_executed ani v document_executed_unique',
                                               `document_executed` int unsigned DEFAULT 0 COMMENT 'pocet vykonani',
                                               `document_executed_unique` int unsigned DEFAULT 0 COMMENT 'pocet unikatnych vykonani vramci dna',
                                               `document_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania casov',     
                                               `document_memory_sum` bigint unsigned DEFAULT 0 COMMENT 'sucet pouzitej pamete',
                                               `document_total_bytes` bigint unsigned DEFAULT 0 COMMENT 'prenesene data',
                                                  
                                               `query_executed_sum_level_0_001` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.001',        
                                               `query_executed_sum_level_0_01` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.01',        
                                               `query_executed_sum_level_0_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.1',        
                                               `query_executed_sum_level_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<1',        
                                               `query_executed_sum_level_3` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<3',
                                               `query_executed_sum` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov',
                                               `query_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania sql dotazov',
                                               `query_bytes_sum` int unsigned DEFAULT 0 COMMENT 'sucet prenesenych bytes sql dotazov',
                                               
                                               `notice_sum` int unsigned DEFAULT 0 COMMENT 'sucet notice',
                                               `warning_sum` int unsigned DEFAULT 0 COMMENT 'sucet warningov',
                                               `error_sum` int unsigned DEFAULT 0 COMMENT 'sucet errorov',
                                               `fatal_sum` int unsigned DEFAULT 0 COMMENT 'sucet fatalov',
                                               `uncompleted_sum` int unsigned DEFAULT 0 COMMENT 'sucet nedokoncenych vykonani',
                                               `doc_error` int unsigned DEFAULT 0 COMMENT 'sucet dokumentov s vyskytom chyby',
                                               PRIMARY KEY (`year`,`day`, `system`)                                              
                                             ) ENGINE=InnoDB
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_per_month` (                             
                                               `year` tinyint unsigned NOT NULL COMMENT 'rok vo formate RR',                       
                                               `month` tinyint unsigned NOT NULL COMMENT 'mesiac v roku',
                                               `system`	tinyint unsigned NOT NULL COMMENT 'priznak, ci je to systemovy zaznam',
                                               `document_redirected` int unsigned DEFAULT 0 COMMENT 'pocet vykonani, ktore skoncili redirectom. (Tieto vkonania nie su zapocitane v document_executed ani v document_executed_unique',
                                               `document_executed` int unsigned DEFAULT 0 COMMENT 'pocet vykonani',
                                               `document_executed_unique` int unsigned DEFAULT 0 COMMENT 'pocet unikatnych vykonani vramci mesiaca',
                                               `document_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania casov',     
                                               `document_memory_sum` bigint unsigned DEFAULT 0 COMMENT 'sucet pouzitej pamete',
                                               `document_total_bytes` bigint unsigned DEFAULT 0 COMMENT 'prenesene data',
                                                  
                                               `query_executed_sum_level_0_001` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.001',        
                                               `query_executed_sum_level_0_01` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.01',        
                                               `query_executed_sum_level_0_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<0.1',        
                                               `query_executed_sum_level_1` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<1',        
                                               `query_executed_sum_level_3` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov t<3',
                                               `query_executed_sum` int unsigned DEFAULT 0 COMMENT 'sucet sql dotazov',
                                               `query_duration_sum` float unsigned DEFAULT 0 COMMENT 'sucet trvania sql dotazov',
                                               `query_bytes_sum` int unsigned DEFAULT 0 COMMENT 'sucet prenesenych bytes sql dotazov',
                                               
                                               `notice_sum` int unsigned DEFAULT 0 COMMENT 'sucet notice',
                                               `warning_sum` int unsigned DEFAULT 0 COMMENT 'sucet warningov',
                                               `error_sum` int unsigned DEFAULT 0 COMMENT 'sucet errorov',
                                               `fatal_sum` int unsigned DEFAULT 0 COMMENT 'sucet fatalov',
                                               `uncompleted_sum` int unsigned DEFAULT 0 COMMENT 'sucet nedokoncenych vykonani',
                                               `doc_error` int unsigned DEFAULT 0 COMMENT 'sucet dokumentov s vyskytom chyby',
                                               
                                               `login_disaling_timeout_sum` int unsigned DEFAULT 0 COMMENT 'pocet LOGIN_DISABLING_TIMEOUT',
                                               `send_mail` int unsigned DEFAULT 0 COMMENT 'pocet SEND_MAIL',
                                               `session_error_ip_sum` int unsigned DEFAULT 0 COMMENT 'pocet SESSION_ERROR_IP',
                                               `session_error_agent` int unsigned DEFAULT 0 COMMENT 'pocet SESSION_ERROR_AGENT',
                                               
                                               PRIMARY KEY (`year`,`month`, `system`)                                              
                                             ) ENGINE=InnoDB
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE `%tmaintainance__stats_ext_per_month` (                      
                                          `id` bigint(20) unsigned NOT NULL auto_increment,                          
                                          `year` tinyint(4) NOT NULL default '0',                                    
                                          `month` tinyint(4) NOT NULL default '0',                                   
                                          `type` enum('HTTP_REFERER','USER_AGENT') NOT NULL default 'HTTP_REFERER',  
                                          `tag` text,                                                       
                                          `value` int(10) unsigned NOT NULL,                                         
                                          PRIMARY KEY  (`id`),                                                       
                                          KEY `NewIndex1` (`year`,`month`,`type`)                                    
                                        ) ENGINE=InnoDB
	";
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('maintainance', 'maintainance tables');
}

return(true);