<?php 

if(!isStamp_gFunc('items,access2_session', 'users settings 2')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addField_gFunc('others', 'session_settings', 'xtext', 'Nastavenia session', array('not_null'=>'yes', 'sk_default_value'=>''));
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('items,access2_session', 'users settings 2');
}

if(!isStamp_gFunc('items,access2_session', 'login enabled')) {
	$items_pVar = new install_item_gClass('users', false);
		
	$items_pVar->setFormRule_gFunc('edit_item', 'login_enabled', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'disabled_login_level', 'edit');
	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('items,access2_session', 'login enabled');
}

return(true);