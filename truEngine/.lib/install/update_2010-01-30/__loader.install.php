<?php

if(!isStamp_gFunc('_loader', 'humanlog')) {

	$sql_pVar = '
			CREATE TABLE `%thumanlog` (
			  `id` int(10) unsigned NOT NULL auto_increment,
			  `event` varchar(255) default NULL,
			  `event_time` datetime default NULL,
			  `user_id` int(11) default NULL,
			  `param1` varchar(255) default NULL,
		    PRIMARY KEY  (`id`))
	';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	

	stamp_gFunc('_loader', 'humanlog');
}

if(!isStamp_gFunc('_loader', 'strings')) {
	
	$sql_pVar = '
			CREATE TABLE `%tstrings` (
			  `id` int(11) unsigned NOT NULL auto_increment,
			  `sk_name` text,
			  `en_name` text,
			  `cz_name` text,
			  `sk_text` text,
			  `en_text` text,
			  `cz_text` text,
			  PRIMARY KEY  (`id`)
			)
	';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('_loader', 'strings');
}


return(true);