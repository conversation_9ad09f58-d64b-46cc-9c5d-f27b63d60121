<?php 

if(!isStamp_gFunc('restore', 'restore')) {
	/**
	 * rovnaka tabulka je aj v module backup !!
	 */
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%tbackup_log` (
			`id` int(10) unsigned NOT NULL auto_increment,
			`user_id` int(10) unsigned NOT NULL default '0',
			`datetime` datetime NOT NULL default '0000-00-00 00:00:00',
			`type` enum ('backup','restore'),
			
			`settings` text,
			`comment` text,
			`target` enum('download_only','download_and_save','save_only'),
			`file`	varchar(255),
			PRIMARY KEY  (`id`)
			) ENGINE=InnoDB
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('restore', 'restore');
}


return(true);