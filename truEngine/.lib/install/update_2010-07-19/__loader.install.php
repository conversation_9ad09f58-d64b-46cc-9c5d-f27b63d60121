<?php

if(!isStamp_gFunc('_loader', 'bookmarks')) {
	
	$sql_pVar = 'CREATE TABLE `kega_bookmarks` (
  `user_id` int(11) NOT NULL,
  `bookmark_id` int(11) NOT NULL,
  `last_access` datetime default NULL,
  `score` int(11) default NULL,
  PRIMARY KEY  (`user_id`,`bookmark_id`)
)';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	$sql_pVar = 'CREATE TABLE `kega_bookmarks_index` (
  `bookmark_id` int(10) unsigned NOT NULL auto_increment,
  `document` varchar(255) default NULL,
  `string_id` int(11) default NULL,
  PRIMARY KEY  (`bookmark_id`)
)';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	stamp_gFunc('_loader', 'bookmarks');
}

return(true);