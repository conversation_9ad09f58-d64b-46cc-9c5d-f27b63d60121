<?php

if(!isStamp_gFunc('access3_rights', 'vytvorenie tabuliek access__users_rights, access__roles_rights')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__users_rights` (
		  `rule_id` int(10) unsigned NOT NULL auto_increment,
		  `user_id` int(10) unsigned NOT NULL,
		  `user_right_id` int(10) unsigned NOT NULL,
		  `user_right_state` enum('allow', 'deny'),
		  `user_right_object` int(10) unsigned default NULL,
		  PRIMARY KEY  (`rule_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__roles_rights` (
		  `rule_id` int(10) unsigned NOT NULL auto_increment,
		  `role_id` varchar(255) NOT NULL,
		  `role_right_id` int(10) unsigned NOT NULL,
		  `role_right_state` enum('allow', 'deny'),
		  `role_right_object` int(10) unsigned default NULL,
		  PRIMARY KEY  (`rule_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('access3_rights', 'vytvorenie tabuliek access__users_rights, access__roles_rights');
}

if(!isStamp_gFunc('access3_rights,_loader', 'access_settings _loader')) {
	insertRights_gFunc('role', 'user', array('s_users_edit_myself' => true));
	insertRights_gFunc('role', 'user', array('s_users_show_user' => true));
	insertRights_gFunc('role', 'user', array('s_document_read' => true));
	stamp_gFunc('access3_rights,_loader', 'access_settings _loader');
}


if(!isStamp_gFunc('access3_rights', 'prvotna inicializacia items_users__form_rules')) {
	$sql_pVar = "
		INSERT INTO `%titems_users__form_rules` (`form_id`,	`field_name`,	`field_access`,	`field_init`,	`field_value`)
										 VALUES (1,			'user_role',	'edit',			'default',		NULL)
										 	   ,(1,			'timeout',		'edit',			'default',		NULL)
										 	   ,(1,			'login',		'edit',			'default',		NULL)
										 	   ,(1,			'password',		'edit',			'default',		NULL)
										 	   ,(1,			'nick',			'edit',			'default',		NULL)
										 	   ,(1,			'first_name',	'edit',			'default',		NULL)
										 	   ,(1,			'last_name',	'edit',			'default',		NULL)
										 	   ,(1,			'titul_pred',	'edit',			'default',		NULL)
										 	   ,(1,			'titul_za',		'edit',			'default',		NULL)
										 	   ,(1,			'gender',		'edit',			'default',		NULL)
										 	   ,(1,			'email',		'edit',			'default',		NULL)
										 	   ,(1,			'foto',			'edit',			'default',		NULL)
											   ,(2,			'user_role',	'edit',			'default',		NULL)
										 	   ,(2,			'timeout',		'edit',			'default',		NULL)
										 	   ,(2,			'login',		'edit',			'default',		NULL)
										 	   ,(2,			'password',		'edit',			'default',		NULL)
										 	   ,(2,			'nick',			'edit',			'default',		NULL)
										 	   ,(2,			'first_name',	'edit',			'default',		NULL)
										 	   ,(2,			'last_name',	'edit',			'default',		NULL)
										 	   ,(2,			'titul_pred',	'edit',			'default',		NULL)
										 	   ,(2,			'titul_za',		'edit',			'default',		NULL)
										 	   ,(2,			'gender',		'edit',			'default',		NULL)
										 	   ,(2,			'email',		'edit',			'default',		NULL)
										 	   ,(2,			'foto',			'edit',			'default',		NULL)
										 	   ,(2,			'status',		'edit',			'default',		NULL)
	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access3_rights', 'prvotna inicializacia items_users__form_rules');
}

if(!isStamp_gFunc('access3_rights', 'vytvorenie tabulky access__change_password')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__change_password` (
		  `request_id` int(10) unsigned NOT NULL auto_increment,
		  `email` varchar(255) NOT NULL,
		  `hash` varchar(255) NOT NULL,
		  `request_time` datetime NOT NULL,
		  `destroy_time` datetime NOT NULL,
		  `no_change_pass` tinyint unsigned default NULL,
		  `user_id_changed` INT unsigned NULL DEFAULT NULL,
		  PRIMARY KEY  (`request_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access3_rights', 'vytvorenie tabulky access__change_password');
}

if(!isStamp_gFunc('access3_rights', 'vytvorenie tabulky access__check_mail')) {
	$sql_pVar = "
		CREATE TABLE IF NOT EXISTS `%taccess__check_mail` (
		  `request_id` int(10) unsigned NOT NULL auto_increment,
		  `email` varchar(255) NOT NULL,
		  `hash` varchar(255) NOT NULL,
		  `user_id` int unsigned NOT NULL,
		  `request_time` datetime NOT NULL,
		  `destroy_time` datetime NOT NULL,
		  `user_id_checked` INT unsigned NULL DEFAULT NULL,
		  PRIMARY KEY  (`request_id`)
		) ENGINE=InnoDB;
	";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	stamp_gFunc('access3_rights', 'vytvorenie tabulky access__check_mail');
}

if(!isStamp_gFunc('access3_rights', 'registracny formular')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addForm_gFunc('register_user', 'Registrovať používateľa');
	$items_pVar->setFormRule_gFunc('register_user', 'login', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'password', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'nick', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'email', 'edit');
	
	$items_pVar->setFormRule_gFunc('register_user', 'first_name', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'last_name', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'titul_pred', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'titul_za', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'gender', 'edit');
	$items_pVar->setFormRule_gFunc('register_user', 'user_role', 'edit');
	
	$items_pVar->addForm_gFunc('request_edit', 'Editovať registráciu');
	$items_pVar->setFormRule_gFunc('request_edit', 'login', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'nick', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'email', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'email_check_status', 'static');
	$items_pVar->setFormRule_gFunc('request_edit', 'first_name', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'last_name', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'titul_pred', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'titul_za', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'gender', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'user_role', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'isic', 'edit');
	$items_pVar->setFormRule_gFunc('request_edit', 'isic_ok', 'static');
	$items_pVar->setFormRule_gFunc('request_edit', 'isic_force_ok', 'edit');

	
	$items_pVar->apply_gFunc();
	stamp_gFunc('access3_rights', 'registracny formular');
}

if(!isStamp_gFunc('access3_rights,kega', 'registracny formular kega')) {
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->setFormRule_gFunc('register_user', 'isic', 'edit');
	
	$items_pVar->apply_gFunc();
	stamp_gFunc('access3_rights,kega', 'registracny formular kega');
}

return(true);