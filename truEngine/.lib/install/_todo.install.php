<?php 

if(!isStamp_gFunc('todo', 'todo')) {
	$items_pVar = new install_item_gClass('users', false);
	
	$items_pVar->addEnum_gFunc('user_role', 'client', 'Klient');
	$items_pVar->addEnum_gFunc('user_role', 'client_assistant',  'Klient - asistent');
	$items_pVar->addEnum_gFunc('user_role', 'worker', 'Pracovník');
	$items_pVar->addEnum_gFunc('user_role', 'manager', 'Projektový vedúci');
	$items_pVar->addEnum_gFunc('user_role', 'accountant', 'Účtovník');
	$items_pVar->addEnum_gFunc('user_role', 'ceo', 'Riaditeľ');
	
	$items_pVar->deleteEnum_gFunc('user_role', 'user');

	$items_pVar->apply_gFunc();
	
	stamp_gFunc('todo', 'todo');
}

return(true);
