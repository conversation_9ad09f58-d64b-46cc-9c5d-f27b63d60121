<?php 


if(!isStamp_gFunc('cdouk', 'cdouk')) {	
	
	$sql_pVar = 'CREATE TABLE `%tcdouk__status` (
                      `isic` char(10) NOT NULL COMMENT \'isic snr cislo doplnene na uvodne nuly\',
                      `checked_ok` datetime DEFAULT NULL COMMENT \'datum posledneho uspesneho nacitania. Za uspesne nacitanie sa poklada aj stav, ked cdo vrati odpoved ze user neexistuje. (Nesmie vzniknut chyba v komunikacii)\',
                      `checked` datetime DEFAULT NULL COMMENT \'datum posledneho pokusu o nacitanie\',
                      PRIMARY KEY (`isic`)                                                                                                                                                                                          
                    )';
	
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	$sql_pVar = 'CREATE TABLE `%tcdouk__changes` (                
                       `isic` char(10) NOT NULL COMMENT \'isic snr cislo doplnene na uvodne nuly\',
                       `first_checked` datetime NOT NULL COMMENT \'cas prveho checkovania\',
                       `last_checked` datetime NOT NULL COMMENT \'cas posledneho checkovania\',
                       `data` mediumblob NOT NULL,
                       PRIMARY KEY (`isic`,`first_checked`)
                     )';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	stamp_gFunc('cdouk', 'cdouk');
}

return(true);
