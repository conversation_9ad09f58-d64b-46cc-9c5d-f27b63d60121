<?php

if(!isStamp_gFunc('access3_rights,kega', 'access_settings kega')) {
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_show_user' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_add_user' => true));
	insertRights_gFunc('role', 'student', array('s_users_show_user' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_user_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_userlist' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_userlist_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_delete_user_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_onlinelist' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_show_userlist' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_user' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_accesslog' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_show_onlinelist' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_user_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_user' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_my_instances' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_test' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_list_my_instances' => true));
	insertRights_gFunc('role', 'student', array('s_test_list_my_instances' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_mobil' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_default_language' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_address' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_date_of_birth' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_password_uk' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_msn' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_email' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_password' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_date_of_birth' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_default_language' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_rocnik' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_password' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_first_name' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_password_uk' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_email' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_default_language' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_skype' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_password_uk' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_foto' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_rocnik' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_first_name' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_field_email_check_status' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_email' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_smer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_skype' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_rocnik' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_foto' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_field_isic' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_first_name' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_gender' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_timeout' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_smer' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_skype' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_gender' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_field_last_login_ip' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_foto' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_icq' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_timeout' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_smer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_titul_pred' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_icq' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_isic' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_gender' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_titul_pred' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_timeout' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_titul_za' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_last_name' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_isic' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_icq' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_titul_za' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_mobil' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_last_name' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_titul_pred' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_address' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_isic' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_msn' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_mobil' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_titul_za' => true));
	insertRights_gFunc('role', 'student', array('s_users_edit_myself_field_last_name' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_date_of_birth' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_address' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_myself_field_password' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_users_edit_myself_field_msn' => true));
	insertRights_gFunc('role', 'pedagog', array('s_system_show_myself_rights' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_system_show_myself_rights' => true));
	insertRights_gFunc('role', 'student', array('s_system_show_myself_rights' => true));
	insertRights_gFunc('role', 'pedagog', array('s_system_show_user_rights' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_requestlog' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_show_requests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_system_show_backup_log' => true));
	insertRights_gFunc('role', 'pedagog', array('s_cdouk_checkisic' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_edit_tester' => true));
	insertRights_gFunc('role', 'pedagog', array('s_users_add_tester' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_edit_xanswer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_answer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_template_run' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_xquestion' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_test_run' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_properties' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_tests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_question' => true));
	insertRights_gFunc('role', 'student', array('s_test_report_error' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_xquestion' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_xanswer_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_edit_xanswer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_template_new_test' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_delete_xanswer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_answer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_question' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_generate_print' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_xquestion_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_template_run' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_instances' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_template_new_official_test' => true));
	insertRights_gFunc('role', 'student', array('s_test_test_run' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_delete_xanswer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_accept_xquestion' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_xanswer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_answer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_question_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_xquestion_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_official_tests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_reject_xquestion' => true));
	insertRights_gFunc('role', 'student', array('s_test_template_run' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_template' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_convert_xquestion_to_question' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_template' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_templates' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_question_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_xquestion_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_question' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_template' => true));
	insertRights_gFunc('role', 'student', array('s_test_show_template' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_comments' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_add_xquestion' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_convert_question_to_xquestion' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_questions_export_csv' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_question_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_xanswer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_question_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_xquestion' => true));
	insertRights_gFunc('role', 'student', array('s_test_show_comments' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_users_count' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_questions_export_pdf' => true));
	insertRights_gFunc('role', 'student', array('s_test_show_question' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_unactive_tests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_answer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_xanswer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_answer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_list_tests' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_edit_xquestion_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_display_user_test_prepared' => true));
	insertRights_gFunc('role', 'student', array('s_test_show_users_count' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_questions_export_txt' => true));
	insertRights_gFunc('role', 'student', array('s_test_show_answer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_answer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_list_official_unactive_tests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_answer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_xanswer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_list_templates' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_delete_xquestion_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_template' => true));
	insertRights_gFunc('role', 'student', array('s_test_list_tests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_questions_export_xml' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_answer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_xanswer' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_xquestion_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_display_user_test' => true));
	insertRights_gFunc('role', 'student', array('s_test_list_templates' => true));
	insertRights_gFunc('role', 'tester', array('s_test_test_run' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_template' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_answer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_xanswer_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_show_xanswer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_comments' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_report_error' => true));
	insertRights_gFunc('role', 'tester', array('s_test_list_official_tests' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_question' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_answer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_test' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_xquestion' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_xanswer_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_add_xanswer_owned' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_show_users_count' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_test_run' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_report_error' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_question' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_xquestion' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_xanswer_owned' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_add_xanswer' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_add_official_test' => true));
	insertRights_gFunc('role', 'student', array('s_document_read' => true));
	insertRights_gFunc('role', 'pedagog', array('s_document_read' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_document_read' => true));
	insertRights_gFunc('role', 'tester', array('s_document_read' => true));
	insertRights_gFunc('role', 'student', array('s_test_rozpis_prace' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_rozpis_prace' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_rozpis_prace' => true));
	insertRights_gFunc('role', 'student', array('s_test_rozpis_prace_zapis' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_rozpis_prace_zapis' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_rozpis_prace_zapis' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_rozpis_prace_edit' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_rozpis_prace_delete' => true));
	
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_comment' => true));
	insertRights_gFunc('role', 'pedagog', array('s_test_delete_owner_comment' => true));
	insertRights_gFunc('role', 'student_navrhovatel', array('s_test_delete_owner_comment' => true));
	insertRights_gFunc('role', 'student', array('s_test_delete_owner_comment' => true));
	
	insertRights_gFunc('role', 'pedagog', array('s_test_edit_from_test' => true));
	stamp_gFunc('access3_rights,kega', 'access_settings kega');
}

if(!isStamp_gFunc('access3_rights,kega', 'inicializacia prav users-kega')) {
/*
	$sql_pVar = "insert  into `%titems___data`(`name`,`languages`,`tree`,`forms`)
					values ('users','sk','no','add_item,update_item');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	db_items_gClass::createBaseStruct_gFunc('users');
*/
	
	$sql_pVar = "
		insert  into `%titems_users__access`(`access_type`,`access_filter`,`access_field`,`access_rights`)
		values ('insert',		'user_role=student',					NULL,	's_users_add_user')
			  ,('update',		'user_role=student',					NULL,	's_users_edit_user')
			  ,('update_from',	'user_role=student',					NULL,	's_users_edit_user')
			  ,('delete',		'user_role=student',					NULL,	's_users_delete_user')
			  
			  ,('insert',		'user_role=student_navrhovatel',		NULL,	's_users_add_user')
			  ,('update',		'user_role=student_navrhovatel',		NULL,	's_users_edit_user')
			  ,('update_from',	'user_role=student_navrhovatel',		NULL,	's_users_edit_user')
			  ,('delete',		'user_role=student_navrhovatel',		NULL,	's_users_delete_user')
			  
			  ,('insert',		'user_role=pedagog',		NULL,	's_users_add_admin')
			  ,('update',		'user_role=pedagog',		NULL,	's_users_edit_admin')
			  ,('update_from',	'user_role=pedagog',		NULL,	's_users_edit_admin')
			  ,('delete',		'user_role=pedagog',		NULL,	's_users_delete_user')
			  
			  ,('insert',		'user_role=tester',		NULL,	's_users_add_tester')
			  ,('update',		'user_role=tester',		NULL,	's_users_edit_tester')
			  ,('update_from',	'user_role=tester',		NULL,	's_users_edit_tester')
			  ,('delete',		'user_role=tester',		NULL,	's_users_delete_user')
			  ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('access3_rights,kega', 'inicializacia prav users-kega');
}


if(!isStamp_gFunc('kega', 'kega user vlastnosti')) {
	
	$items_pVar = new install_item_gClass('users', false);
	$items_pVar->addFieldset_gFunc('kontakt', 'Kontaktné informácie');
	$items_pVar->addFieldset_gFunc('skola', 'Škola');
	$items_pVar->addFieldset_gFunc('statistics', 'Štatistické informácie');
	$items_pVar->addFieldset_gFunc('others', 'Iné');
	
	$items_pVar->addField_gFunc('personal', 'date_of_birth', 'date', 'Dátum narodenia', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('kontakt', 'street', 'xvarchar', 'Ulica, č.d.', array('len'=>'255'));
	$items_pVar->addField_gFunc('kontakt', 'city', 'xvarchar', 'PSČ, Mesto', array('len'=>'255', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('kontakt', 'country', 'xvarchar', 'Štát', array('len'=>'255', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('kontakt', 'mobil', 'xvarchar', 'Mobil', array('len'=>'255'));
	$items_pVar->addField_gFunc('kontakt', 'icq', 'xvarchar', 'ICQ', array('len'=>'20'));
	$items_pVar->addField_gFunc('kontakt', 'msn', 'xvarchar', 'MSN', array('len'=>'255'));
	$items_pVar->addField_gFunc('kontakt', 'skype', 'xvarchar', 'Skype', array('len'=>'30'));
	
	$items_pVar->addField_gFunc('skola', 'isic', 'xvarchar', 'ISIC snr', array('len'=>'20', 'pattern'=>'/([0-9] |[0-9]){10}/', 'order_by'=>'yes', 'not_null'=>'yes', 'sk_field_info'=>'Zadajte sériové číslo ISIC karty (10 miestne číslo)::Ak nemáte pridelený ISIC/ITIC preukaz, použite náhradnú hodnotu 0000000000 (10 núl).<br />Administrátori overia Vaše dôvody zadania tejto hodnoty, a ak sú opodstatnené, schvália Vám prístup do systému.'));
	$items_pVar->addField_gFunc('skola', 'isic_ok', 'enum', 'ISIC platný', array('sk_default_value'=>'no'));
	$items_pVar->addField_gFunc('skola', 'isic_force_ok', 'enum', 'ISIC manuálne schválený', array('sk_default_value'=>'no'));
	$items_pVar->addField_gFunc('skola', 'rocnik', 'enum', 'Ročník', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('skola', 'smer', 'enum', 'Smer', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('skola', 'foto_rec', 'ximagelist', 'Oficiálne foto');
	$items_pVar->addField_gFunc('skola', 'password_uk', 'xvarchar', 'Heslo UK', array('len'=>'30', 'comment'=>'Heslo UK vyplna pouzivatel. Este neviem ako sa pouzije.'));
	
	$items_pVar->addField_gFunc('statistics', 'stat_score', 'int', 'Dosiahnuty počet bodov', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('statistics', 'stat_time', 'int', 'Testovací čas', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('statistics', 'stat_total', 'int', 'Celkový počet odpovedí', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('statistics', 'stat_ok', 'int', 'Správne odpovede', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('statistics', 'stat_failed', 'int', 'Nesprávne odpovede', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('statistics', 'stat_questions_autor', 'int', 'Počet navrhnutých otázok', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'comment'=>'Počet otázok v systéme, ktoré navrhol používateľ', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('statistics', 'stat_questions_garant', 'int', 'Počet schválených otázok', array('not_null'=>'yes', 'min_value'=>'0', 'sk_default_value'=>'0', 'pattern'=>'/[0-9]+/', 'comment'=>'Počet otázok v systéme, ktoré schválil používateľ', 'order_by'=>'yes'));
	
	
	$items_pVar->addField_gFunc('others', 'motto', 'xtext', 'Motto');
	$items_pVar->addField_gFunc('others', 'motto_last_changed', 'timestamp', 'Posledná zmena motta', array('order_by'=>'yes'));
	
	$items_pVar->addEnum_gFunc('isic_ok', 'yes', 'áno');
	$items_pVar->addEnum_gFunc('isic_ok', 'no', 'nie');
	$items_pVar->addEnum_gFunc('isic_force_ok', 'yes', 'áno');
	$items_pVar->addEnum_gFunc('isic_force_ok', 'no', 'nie');
	
	$items_pVar->addEnum_gFunc('rocnik', 'rocnik_1', '1. ročník');
	$items_pVar->addEnum_gFunc('rocnik', 'rocnik_2', '2. ročník');
	$items_pVar->addEnum_gFunc('rocnik', 'rocnik_3', '3. ročník');
	$items_pVar->addEnum_gFunc('rocnik', 'rocnik_4', '4. ročník');
	$items_pVar->addEnum_gFunc('rocnik', 'rocnik_5', '5. ročník');
	$items_pVar->addEnum_gFunc('rocnik', 'rocnik_6', '6. ročník');
	
	$items_pVar->addEnum_gFunc('smer', 'ZL', 'Zubné lekárstvo');
	
	$items_pVar->setFormRule_gFunc('add_item', 'date_of_birth', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'mobil', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'street', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'city', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'country', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'rocnik', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'smer', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'isic', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'icq', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'msn', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'skype', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'password_uk', 'edit');
	
	$items_pVar->setFormRule_gFunc('edit_item', 'date_of_birth', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'mobil', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'street', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'city', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'country', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'rocnik', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'smer', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'isic', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'isic_ok', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'isic_force_ok', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'icq', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'msn', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'skype', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'password_uk', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'stat_questions_autor', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'stat_questions_garant', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'stat_time', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'stat_total', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'stat_ok', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'stat_failed', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'motto', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'motto_last_changed', 'static');
		
	$items_pVar->addEnum_gFunc('user_role', 'pedagog', 'Pedagóg');
	$items_pVar->addEnum_gFunc('user_role', 'student_navrhovatel', 'Študent navrhovateľ');
	$items_pVar->addEnum_gFunc('user_role', 'student', 'Študent');
	$items_pVar->addEnum_gFunc('user_role', 'tester', 'Oficiálny tester');
	
	$items_pVar->deleteEnum_gFunc('user_role', 'user');
	
	$items_pVar->add_right_gFunc('update', 's_users_set_isic_force_ok', null, 'isic_force_ok');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_isic', 'item_id<>@{session:user_id}', 'isic_force_ok');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_isic', 'item_id<>@{session:user_id}', 'isic_ok');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_isic', 'item_id<>@{session:user_id}', 'isic');
	$items_pVar->add_right_gFunc('get', 's_users_show_field_password_uk', 'item_id<>@{session:user_id}', 'password_uk');
	
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_isic', 'item_id=@{session:user_id}', 'isic');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_rocnik', 'item_id=@{session:user_id}', 'rocnik');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_smer', 'item_id=@{session:user_id}', 'smer');
	$items_pVar->add_right_gFunc('update', 's_users_edit_myself_field_password_uk', 'item_id=@{session:user_id}', 'password_uk');
	
	$items_pVar->apply_gFunc();
	
	stamp_gFunc('kega', 'kega user vlastnosti');
}


if(!isStamp_gFunc('items,kega', 'vytvorenie tabuliek items_test_questions__')) {

	$items_pVar = new install_item_gClass('test_questions', true);
	$items_pVar->setSystemProperty_gFunc('languages', 'sk,en');
	$items_pVar->setSystemProperty_gFunc('tree_defs', 'yes');
	$items_pVar->setSystemProperty_gFunc('garant', 'yes');
	
	$items_pVar->addFieldset_gFunc('main', 'Základné informácie');
	$items_pVar->addFieldset_gFunc('categories', 'Zaradenie');
	$items_pVar->addFieldset_gFunc('stats', 'Štatistické informácie');
		
	$items_pVar->addField_gFunc('main', 'otazka', 'text', 'Znenie otázky (text)', array('not_null'=>'yes', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'otazka_media', 'imagelist',  'Znenie otázky (media)');
	$items_pVar->addField_gFunc('main', 'moznosti', 'join',  'Možnosti', array('pattern'=>'test_answers|answers','min_value'=>4, 'max_value'=>25, 'comment'=>'`answers`.*|LEFT JOIN `%titems_test_answers__data` as `answers` ON `answers`.`test_question`=`D`.`item_id` AND `answers`.`status`<>\'deleted\''));
	$items_pVar->addField_gFunc('categories', 'modul', 'enum',  'Modul', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('categories', 'program', 'enum',  'Cieľ. št. program', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('categories', 'predmet', 'enum',  'Predmet', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('categories', 'kategoria', 'enum',  'Problematika (kategória)', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('categories', 'podkategoria', 'enum',  'Podkategória', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'vysvetlenie', 'text',  'Vysvetlenie');
	$items_pVar->addField_gFunc('main', 'literatura', 'xvarchar',  'Odkaz na literatúru', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'navrhovatel', 'xvarchar',  'Navrhovateľ', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'autorske_prava', 'xvarchar',  'Autorské práva', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'keywords', 'varchar',  'Kľúčové slová (odelené čiarkou)');
	$items_pVar->addField_gFunc('stats', 'obtiaznost', 'float',  'Index obtiažnosti', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('stats', 'nekorektnost', 'int',  'Index nekorektnosti', array('order_by'=>'yes', 'not_null'=>'yes'));
	$items_pVar->addField_gFunc('stats', 'aktualnost', 'float',  'Index aktuálnosti', array('order_by'=>'yes'));
	//$items_pVar->addField_gFunc('stats', 'kbytes', 'int',  'Objem KB', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('stats', 'spravne', 'int',  'Počet správnych zodpovedaní', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('stats', 'nespravne', 'int',  'Počet nesprávnych zodpovedaní', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('stats', 'spravne_test', 'int',  'Počet správnych zodpovedaní pri teste', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('stats', 'nespravne_test', 'int',  'Počet nesprávnych zodpovedaní pri teste', array('order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'typ', 'enum', 'Typ', array('not_null'=>'yes', 'sk_default_value'=>'pregradual', 'order_by'=>'yes'));
	$items_pVar->addField_gFunc('main', 'dolezitost', 'enum',  'Dôležitosť', array('not_null'=>'yes', 'sk_default_value'=>'_3', 'order_by'=>'yes'));
	
	$items_pVar->setFormRule_gFunc('add_item', 'otazka', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'otazka_media', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'odpovede', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'modul', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'program', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'predmet', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'kategoria', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'podkategoria', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'vysvetlenie', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'navrhovatel', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'navrhovatel', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'keywords', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'literatura', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'status', 'hidden');
	$items_pVar->setFormRule_gFunc('add_item', 'typ', 'edit');
	$items_pVar->setFormRule_gFunc('add_item', 'dolezitost', 'edit');
	
	$items_pVar->setFormRule_gFunc('edit_item', 'otazka', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'otazka_media', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'odpovede', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'modul', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'program', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'predmet', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'kategoria', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'podkategoria', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'vysvetlenie', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'navrhovatel', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'autorske_prava', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'keywords', 'edit');
	
	$items_pVar->setFormRule_gFunc('edit_item', 'obtiaznost', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'nekorektnost', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'aktualnost', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'kbytes', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'spravne', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'nespravne', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'spravne_test', 'static');
	$items_pVar->setFormRule_gFunc('edit_item', 'nespravne_test', 'static');
	
	$items_pVar->setFormRule_gFunc('edit_item', 'literatura', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'status', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'typ', 'edit');
	$items_pVar->setFormRule_gFunc('edit_item', 'dolezitost', 'edit');

	
	$items_pVar->addEnum_gFunc('modul', 'modul1', 'modul1');
	$items_pVar->addEnum_gFunc('program', 'stomatologia', 'stomatologia');
	$items_pVar->addEnum_gFunc('predmet', 'vrtanie', 'vrtanie');
	$items_pVar->addEnum_gFunc('kategoria', 'kategoria', 'kategoria');
	$items_pVar->addEnum_gFunc('podkategoria', 'podkategoria', 'podkategoria');
	
	$items_pVar->addEnum_gFunc('typ', 'pregradual', 'pregradual');
	$items_pVar->addEnum_gFunc('typ', 'postgradual', 'postgradual');
	
	$items_pVar->addEnum_gFunc('dolezitost', '_1', '1');
	$items_pVar->addEnum_gFunc('dolezitost', '_2', '2');
	$items_pVar->addEnum_gFunc('dolezitost', '_3', '3');
	$items_pVar->addEnum_gFunc('dolezitost', '_4', '4');
	$items_pVar->addEnum_gFunc('dolezitost', '_5', '5');
	
	$items_pVar->addEnum_gFunc('status', 'waiting', 'Predotázka');
	$items_pVar->addEnum_gFunc('status', 'rejected', 'Zamietnutá');
	$items_pVar->updateEnum_gFunc('status', 'deleted', 'deleted', 'Zmazať', array('sk_enum_confirm_set'=>'Naozaj chcete otázku zmazať?', 'sk_enum_confirm_unset'=>'Otázka nebude zmazaná.'));
	$items_pVar->updateEnum_gFunc('status', 'active', 'active', 'Otázka');
	$items_pVar->updateField_gFunc('main', 'status', 'enum', 'Status', array('pattern'=>'/active|deleted|waiting|rejected/'));

	$items_pVar->apply_gFunc();
	
	stamp_gFunc('items,kega', 'vytvorenie tabuliek items_test_questions__');
}

if(!isStamp_gFunc('items,kega', 'vytvorenie tabuliek items_test_answers__')) {

	$sql_pVar = "insert  into `%titems___data`(`name`,`languages`,`tree`,`forms`) values ('test_answers','sk,en','no','add_item,edit_item');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	db_items_gClass::createBaseStruct_gFunc('test_answers');
	db_items_gClass::applyLanguagesToTables_gFunc('test_answers');

	$sql_pVar = "insert  into `%titems_test_answers__fields`(
							`field_id`,	`sk_name`,									`tag`,					`type`,						`len`,	`not_null`,	`sk_default_value`,		`pattern`,		`min_value`,	`max_value`,	`fieldset`,	`field_order`, 	`comment`)
				values (	8,			'Otázka',									'test_question',		'hidden_int',					NULL,	'no',		NULL,					NULL,			NULL,			NULL,			1,				10,			'')
					  ,(	9,			'Znenie odpovede (text)',					'odpoved',				'text',						NULL,	'yes',		NULL,					NULL,			NULL,			NULL,			1,				20,			'')
					  ,(	10,			'Znenie odpovede (media)',					'odpoved_media',		'imagelist',					NULL,	'no',		NULL,					NULL,			NULL,			NULL,			1,				30,			'')
					  ,(	5,			'Správnosť',								'spravnost',			'enum',						NULL,	'yes',		NULL,					NULL,			NULL,			NULL,			1,				40,			'')
					  ,(	7,			'Vysvetlenie',								'odpoved_vysvetlenie',	'text',						NULL,	'no',		NULL,					NULL,			NULL,			NULL,			1,				50,			'')
					  ,(	6,			'Order',									'order',				'int',						NULL,	'no',		NULL,					NULL,			NULL,			NULL,			1,				60,			'')
					  ,(	11,			'Počet správnych odpovedí',					'spravne',				'int',						NULL,	'no',		NULL,					NULL,			NULL,			NULL,			2,				60,			'')
					  ,(	12,			'Počet nesprávnych odpovedí',				'nespravne',			'int',						NULL,	'no',		NULL,					NULL,			NULL,			NULL,			2,				60,			'')
					  ,(	13,			'Počet správnych odpovedí pri teste',		'spravne_test',			'int',						NULL,	'no',		NULL,					NULL,			NULL,			NULL,			2,				60,			'')
					  ,(	14,			'Počet nesprávnych odpovedí pri teste',		'nespravne_test',		'int',						NULL,	'no',		NULL,					NULL,			NULL,			NULL,			2,				60,			'')
					  ,(	15,			'Index nekorektnosti', 						'nekorektnost',			'int',						NULL,   'yes',		NULL, 					NULL, 			NULL, 			NULL,			2,				60,			'')
				";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "
		INSERT INTO `%titems_test_answers__form_rules` (`form_id`,	`field_name`,	`field_access`,	`field_init`,	`field_value`)
										 VALUES (1,			'odpoved',		'edit',			'default',		NULL)
										 	   ,(1,			'odpoved_media','edit',			'default',		NULL)
										 	   ,(1,			'spravnost',	'edit',			'default',		NULL)
										 	   ,(1,			'odpoved_vysvetlenie','edit',			'default',		NULL)
										 	   ,(2,			'odpoved',		'edit',			'default',		NULL)
										 	   ,(2,			'odpoved_media','edit',			'default',		NULL)
										 	   ,(2,			'spravnost',	'edit',			'default',		NULL)
										 	   ,(2,			'odpoved_vysvetlenie','edit',			'default',		NULL)
										 	   ,(2,			'status', 'edit',			'default',		NULL)
										 	   ,(2,			'spravne', 	'static',			'default',		NULL)
										 	   ,(2,			'nespravne', 	'static',			'default',		NULL)
										 	   ,(2,			'spravne_test', 	'static',			'default',		NULL)
										 	   ,(2,			'nespravne_test', 	'static',			'default',		NULL)
										 	   ,(2,			'nekorektnost', 	'static',			'default',		NULL)
										 	   	";

	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "insert  into `%titems_test_answers__values`(`enum_field_id`,	`enum_value_order`,	`enum_field_value`,	`sk_enum_field_name_item`,	`sk_enum_field_name_group`,	`sk_url_name`)
													  values (	5,				10,					'spravne',			'správne',					'správne',					'správne')
													  		,(	5,				20,					'nespravne',		'nesprávne',				'nesprávne',				'nesprávne')
													";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	$sql_pVar = "insert  into `%titems_test_answers__fieldsets`(`fieldset_id`,`fieldset_name`,`fieldset_order`,`sk_fieldset_legend`)
													values (1,'main',1,'Základné informácie')
														  ,(2,'stats',2,'Štatistické informácie')
													";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	db_items_gClass::applyLanguagesToTables_gFunc('test_answers');
	
	$items_pVar = new install_item_gClass('test_answers', false);
	$items_pVar->updateField_gFunc('main', 'status', 'enum', 'Status', array()); // pole status presuniem fo fieldsetu main.
	$items_pVar->apply_gFunc();

	stamp_gFunc('items,kega', 'vytvorenie tabuliek items_test_answers__');
}

if(!isStamp_gFunc('items,kega', 'alter table files by test_questions and test_answers')) {
	$sql_pVar = 'show full fields from `%tfiles`';
	$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false, 'Field');
	$enumDef_pVar = $data_pVar['ref_tag']['Type'];
	if(strpos($enumDef_pVar, '\'items_test_questions_otazka_media\'') === false) {
		$enumDef_pVar = str_replace(')', ',\'items_test_questions_sk_otazka_media\',\'items_test_questions_en_otazka_media\')', $enumDef_pVar);
	}
	if(strpos($enumDef_pVar, '\'items_test_answers_odpoved_media\'') === false) {
		$enumDef_pVar = str_replace(')', ',\'items_test_answers_sk_odpoved_media\',\'items_test_answers_en_odpoved_media\')', $enumDef_pVar);
	}
	
	$default_pVar = !empty($data_pVar['ref_tag']['Default'])?('\''.$data_pVar['ref_tag']['Default'].'\''):'NULL';
	$sql_pVar = 'alter table `%tfiles` change `ref_tag` `ref_tag` ' . $enumDef_pVar . ' DEFAULT '.$default_pVar.' NOT NULL ';
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	stamp_gFunc('items,kega', 'alter table files by test_questions and test_answers');	
}

if(!isStamp_gFunc('access3_rights,kega', 'inicializacia prav questionsanswers-kega')) {
	
	$sql_pVar = "
		insert  into `%titems_test_questions__access`(`access_type`,`access_filter`,`access_field`,`access_rights`)
		values ('get',			'status=waiting',						NULL,	's_test_show_xquestion')
			  ,('get',			'status=active',						NULL,	's_test_show_question')
			  ,('get',			'status=rejected',						NULL,	's_test_show_xquestion_owned')
			  ,('insert',		'status=waiting',						NULL,	's_test_add_xquestion')
			  ,('insert',		'status=active',						NULL,	's_test_add_question')
			  ,('update',		'status=waiting',						NULL,	's_test_edit_xquestion')
			  ,('update',		'status=rejected',						NULL,	's_test_edit_xquestion')
			  ,('update',		'status=active',						NULL,	's_test_edit_question')
			  ,('delete',		'status=waiting',						NULL,	's_test_delete_xquestion')
			  ,('delete',		'status=rejected',						NULL,	's_test_delete_xquestion')
			  ,('delete',		'status=active',						NULL,	's_test_delete_question')
			  ,('update_from',	'status=waiting',						NULL,	's_test_edit_xquestion')
			  ,('update_from',	'status=rejected',						NULL,	's_test_edit_xquestion')
			  ,('update_from',	'status=active',						NULL,	's_test_edit_question')
			  ,('update_from',	'status=waiting&owner_id=@{session:user_id}',	NULL,	's_test_edit_xquestion_owned')
			  ,('update',		'status=waiting&owner_id=@{session:user_id}',	NULL,	's_test_edit_xquestion_owned')
			  ,('update_from',	'status=rejected&owner_id=@{session:user_id}',	NULL,	's_test_edit_xquestion_owned')
			  ,('update',		'status=rejected&owner_id=@{session:user_id}',	NULL,	's_test_edit_xquestion_owned')
			  ,('update_from',	'status=active&owner_id=@{session:user_id}',	NULL,	's_test_edit_question_owned')
			  ,('update',		'status=active&owner_id=@{session:user_id}',	NULL,	's_test_edit_question_owned')
			  ,('properties',	NULL,									NULL,	's_test_edit_properties')
			  ,('delete',		'status=waiting&owner_id=@{session:user_id}',	NULL, 's_test_delete_xquestion_owned')
			  ,('delete',		'status=rejected&owner_id=@{session:user_id}',	NULL, 's_test_delete_xquestion_owned')
			  ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	// @TODO: TIETO PRAVA NIE SU DOBRE NASTAVENE, JE TO LEN PROVIZORNE RIESENIE!! nefunguje status=waiting
	$sql_pVar = "
		insert  into `%titems_test_answers__access`(`access_type`,`access_filter`,`access_field`,`access_rights`)
		values ('get',			'status=waiting',						NULL,	's_test_show_xanswer')
			  ,('get',			'status=active',						NULL,	's_test_show_answer')
			  ,('insert',		'status=waiting',						NULL,	's_test_add_xanswer')
			  ,('insert',		'status=active',						NULL,	's_test_add_answer')
			  ,('update',		'status=waiting',						NULL,	's_test_edit_xanswer')
			  ,('update',		'status=active',						NULL,	's_test_edit_answer')
			  ,('delete',		'status=waiting',						NULL,	's_test_delete_xanswer')
			  ,('delete',		'status=active',						NULL,	's_test_delete_xanswer')
			  ,('update_from',	'status=waiting',						NULL,	's_test_edit_xanswer')
			  ,('update_from',	'status=active',						NULL,	's_test_edit_answer')
			  ,('update_from',	'status=waiting&owner_id=@{session:user_id}',	NULL,	's_test_edit_xanswer_owned')
			  ,('update',		'status=waiting&owner_id=@{session:user_id}',	NULL,	's_test_edit_xanswer_owned')
			  ,('update_from',	'status=active&owner_id=@{session:user_id}',	NULL,	's_test_edit_xanswer_owned')
			  ,('update',		'status=active&owner_id=@{session:user_id}',	NULL,	's_test_edit_xanswer_owned')
			  ,('properties',	NULL,									NULL,	's_test_edit_properties')
			  ,('insert',		NULL,									NULL,   's_test_add_xanswer')
			  ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('access3_rights,kega', 'inicializacia prav questionsanswers-kega');
}

if(!isStamp_gFunc('kega', 'inicializacia stromu')) {
	
	$sql_pVar = "
		insert  into `%titems_test_questions__tree__defs`(`tree_id`, `tree_name`,`tree_def`)
			values (1, 'zaradenie','modul,program,predmet,kategoria,podkategoria')
			  ";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	stamp_gFunc('kega', 'inicializacia stromu');
}



if(!isStamp_gFunc('kega', 'kega sablony testov')) {
	$sql_pVar = "insert  into `%titems___data`(`name`,`languages`,`tree`,`tree_defs`,`forms`) values ('test_templates','sk,en','no','yes','add_item,edit_item');";
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}

	// not_null, sk_field_info
	$i = new install_item_gClass('test_templates', true);
	
	$i->addFieldset_gFunc('base', 'Všeobecné');
	
	$i->addField_gFunc('base', 'name', 'varchar', 'Názov šablony', array('not_null'=>'yes', 'sk_field_info'=>'::Výstižný názov testu. Maximálne 256 znakov.', 'order_by'=>'yes'));
	
	$i->addField_gFunc('base', 'description', 'text', 'Upresnenie', array('sk_field_info'=>'::Sem možno napísať spresnenie názvu, napr. pre študentov pre udelenie zápočtu v zimnom semestri.', 'order_by'=>'yes'));
	
	$i->addField_gFunc('base', 'smer', 'enum', 'Smer', array('order_by'=>'yes'));
		$i->addEnum_gFunc('smer', 'zl', 'Zubné lekárstvo');
		$i->addEnum_gFunc('smer', 'vl', 'Všeobecné lekárstvo');
		
	$i->addField_gFunc('base', 'rocnik', 'enum', 'Ročník', array('order_by'=>'yes'));
		$i->addEnum_gFunc('rocnik', 'rocnik_1', '1. ročník');
		$i->addEnum_gFunc('rocnik', 'rocnik_2', '2. ročník');
		$i->addEnum_gFunc('rocnik', 'rocnik_3', '3. ročník');
		$i->addEnum_gFunc('rocnik', 'rocnik_4', '4. ročník');
		$i->addEnum_gFunc('rocnik', 'rocnik_5', '5. ročník');
		$i->addEnum_gFunc('rocnik', 'rocnik_6', '6. ročník');

	$i->addFieldset_gFunc('test', 'Test');
	$i->addFieldset_gFunc('vyhodnotenie', 'Vyhodnotenie');
	$i->addFieldset_gFunc('other', 'Ostatné');
	
	$i->addField_gFunc('test', 'sposob_testovania', 'enum', 'Spôsob testovania', array('sk_default_value'=>'vsetko', 'not_null'=>'yes', 'sk_field_info'=>'::Otázky sa budú zobrazovať po jednej, alebo sa zobrazí celý test.'));
		$i->addEnum_gFunc('sposob_testovania', 'postupne', 'Po jednej otázke');
		$i->addEnum_gFunc('sposob_testovania', 'vsetko', 'Všetky otázky');
		
	$i->addField_gFunc('vyhodnotenie', 'ukazat_odpovede', 'enum', 'Ukázať odpovede', array('sk_default_value'=>'na_konci', 'not_null'=>'yes', 'sk_field_info'=>'::Či sa majú zobraziť správne odpovede po vyplnení testu.'));
		$i->addEnum_gFunc('ukazat_odpovede', 'postupne', 'Áno, po každej otázke');
		$i->addEnum_gFunc('ukazat_odpovede', 'na_konci', 'Áno, po ukončení testu');
		$i->addEnum_gFunc('ukazat_odpovede', 'nie', 'Nie');
		
	$i->addField_gFunc('test', 'pocet_otazok', 'int', 'Počet otázok', array('not_null'=>'yes', 'sk_field_info'=>'::Počet otázok v teste', 'order_by'=>'yes'));
	$i->addField_gFunc('test', 'moznosti_min', 'int', 'Minimálny počet možností', array('sk_field_info'=>'::Minimálny počet možností, ktoré musí otázka obsahovať. Ak otázka obsahuje menej možností, nebude zaradená do testu. Ak hodnotu nenastavíte, nebude žiadne obmedzenie.', 'order_by'=>'yes'));
	$i->addField_gFunc('test', 'moznosti_max', 'int', 'Maximálny počet možností', array('sk_field_info'=>'::Minimálny počet možností, ktoré budú v teste zobrazné. Ak otázka obsahuje viacej možností, nebudú zobrazené všetky. Ak hodnotu nenastavíte, nebude žiadne obmedzenie.', 'order_by'=>'yes'));
	$i->addField_gFunc('test', 'cas', 'int', 'Čas na testovanie (max)', array('sk_field_info'=>'::Po uplynutí času bude test ukončený a vyhodnotený. Čas je zadaný v minútach.', 'order_by'=>'yes'));
	$i->addField_gFunc('test', 'miesanie_otazok', 'enum', 'Náhodné miešanie otázok', array('sk_default_value'=>'yes', 'sk_field_info'=>'::Pri každom spustení testu bude poradie otázok iné. Pri generovaní zo šablóny je poradie vždy náhodné bez ohľadu na toto nastavenie.'));
	$i->addEnum_gFunc('miesanie_otazok', 'yes', 'Áno');
	$i->addEnum_gFunc('miesanie_otazok', 'no', 'Nie');
	$i->addField_gFunc('test', 'miesanie_odpovedi', 'enum', 'Náhodné miešanie odpovedí', array('sk_default_value'=>'yes', 'sk_field_info'=>'::Pri každom spustení testu bude poradie odpovedí iné. Pri generovaní zo šablóny je poradie vždy náhodné bez ohľadu na toto nastavenie.'));
	$i->addEnum_gFunc('miesanie_odpovedi', 'yes', 'Áno');
	$i->addEnum_gFunc('miesanie_odpovedi', 'no', 'Nie');
	
	$i->addField_gFunc('vyhodnotenie', 'ohodnotit_test', 'enum', 'Ohodnotiť test', array('sk_default_value'=>'yes', 'not_null'=>'yes', 'sk_field_info'=>'::Či sa má test vyhodnotiť a oznámkovať.'));
		$i->addEnum_gFunc('ohodnotit_test', 'yes', 'áno');
		$i->addEnum_gFunc('ohodnotit_test', 'no', 'nie');
	//$i->addField_gFunc('other', 'autor', 'xvarchar', 'Autor', array('sk_field_info'=>'::Meno autora šablóny.'));
	$i->addField_gFunc('test', 'preferred_keywords', 'varchar', 'Preferované kľúčové slová', array('sk_field_info'=>'::Do testu budú uprednostnené otázky s výskytom uvedených kľúčových slov. Zadajte kľúčové slová oddelené čiarkami.'));
	$i->addField_gFunc('test', 'preferred_authors', 'xvarchar', 'Preferovaní autori', array('sk_field_info'=>'::Do testu budú uprednostnené otázky od uvedených autorov. Zadajte loginy autorov, alebo mená autorov s uveením user_id v zátvorke.<br />Jenotlivých autorov oddeľujte čiarkami.'));
	$i->addField_gFunc('test', 'language', 'enum', 'Jazyk', array('sk_default_value'=>'sk', 'sk_field_info'=>'::Test bude zobrazený v uvedenom jazyku.', 'order_by'=>'yes'));
		$i->addEnum_gFunc('language', 'sk', 'slovenský');
		$i->addEnum_gFunc('language', 'en', 'anglický');
	$i->addField_gFunc('test', 'media', 'enum', 'Povoliť video a flash', array('sk_default_value'=>'yes', 'sk_field_info'=>'::Do testu môžu byť zaradené aj otázky obsahzjúce video. Ak chcete test tlačiť na papier, zrušte túto možnosť, pretože video nie je možné vytlačiť.', 'order_by'=>'yes'));
		$i->addEnum_gFunc('media', 'yes', 'áno');
		$i->addEnum_gFunc('media', 'no', 'nie');		
	$i->addField_gFunc('test', 'index_obtiaznosti', 'float', 'Index obtiažnosti', array('min_value'=>'0', 'max_value'=>'1', 'sk_field_info'=>'::Zadajte index obtiažnosti testu z intervalu &lt;0,1&gt;', 'order_by'=>'yes'));
		
	$i->addFieldset_gFunc('categories', 'Výber kategórií');
	$i->addField_gFunc('categories', 'categories',  'xtext', '');
		
	$i->setFormRule_gFunc('add_item', 'name', 'edit');
	$i->setFormRule_gFunc('add_item', 'description', 'edit');
	$i->setFormRule_gFunc('add_item', 'smer', 'edit');
	$i->setFormRule_gFunc('add_item', 'rocnik', 'edit');
	$i->setFormRule_gFunc('add_item', 'pocet_otazok', 'edit');
	$i->setFormRule_gFunc('add_item', 'moznosti_min', 'edit');
	$i->setFormRule_gFunc('add_item', 'moznosti_max', 'edit');
	$i->setFormRule_gFunc('add_item', 'cas', 'edit');
	$i->setFormRule_gFunc('add_item', 'ohodnotit_test', 'edit');
	$i->setFormRule_gFunc('add_item', 'autor', 'edit');
	$i->setFormRule_gFunc('add_item', 'preferred_keywords', 'edit');
	$i->setFormRule_gFunc('add_item', 'preferred_authors', 'edit');
	$i->setFormRule_gFunc('add_item', 'language', 'edit');
	$i->setFormRule_gFunc('add_item', 'sposob_testovania', 'edit');
	$i->setFormRule_gFunc('add_item', 'ukazat_odpovede', 'edit');
	$i->setFormRule_gFunc('add_item', 'miesanie_otazok', 'edit');
	$i->setFormRule_gFunc('add_item', 'miesanie_odpovedi', 'edit');
	$i->setFormRule_gFunc('add_item', 'media', 'edit');
	$i->setFormRule_gFunc('add_item', 'categories', 'edit');
	$i->setFormRule_gFunc('add_item', 'index_obtiaznosti', 'edit');
	
	$i->setFormRule_gFunc('edit_item', 'name', 'edit');
	$i->setFormRule_gFunc('edit_item', 'description', 'edit');
	$i->setFormRule_gFunc('edit_item', 'smer', 'edit');
	$i->setFormRule_gFunc('edit_item', 'rocnik', 'edit');
	$i->setFormRule_gFunc('edit_item', 'pocet_otazok', 'edit');
	$i->setFormRule_gFunc('edit_item', 'moznosti_min', 'edit');
	$i->setFormRule_gFunc('edit_item', 'moznosti_max', 'edit');
	$i->setFormRule_gFunc('edit_item', 'cas', 'edit');
	$i->setFormRule_gFunc('edit_item', 'ohodnotit_test', 'edit');
	$i->setFormRule_gFunc('edit_item', 'autor', 'static');
	$i->setFormRule_gFunc('edit_item', 'preferred_keywords', 'edit');
	$i->setFormRule_gFunc('edit_item', 'preferred_authors', 'edit');
	$i->setFormRule_gFunc('edit_item', 'language', 'edit');
	$i->setFormRule_gFunc('edit_item', 'sposob_testovania', 'edit');
	$i->setFormRule_gFunc('edit_item', 'ukazat_odpovede', 'edit');
	$i->setFormRule_gFunc('edit_item', 'miesanie_otazok', 'edit');
	$i->setFormRule_gFunc('edit_item', 'miesanie_odpovedi', 'edit');
	$i->setFormRule_gFunc('edit_item', 'media', 'edit');
	$i->setFormRule_gFunc('edit_item', 'categories', 'edit');
	$i->setFormRule_gFunc('edit_item', 'status', 'edit');
	$i->setFormRule_gFunc('edit_item', 'index_obtiaznosti', 'edit');
	
	$i->add_right_gFunc('get', 's_test_show_template');
	$i->add_right_gFunc('update', 's_test_edit_template');
	$i->add_right_gFunc('update_from', 's_test_edit_template');	
	$i->add_right_gFunc('delete', 's_test_delete_template');
	$i->add_right_gFunc('insert', 's_test_add_template');
		
	$i->apply_gFunc();	
	
	stamp_gFunc('kega', 'kega sablony testov');
}

if(!isStamp_gFunc('kega', 'kega testy')) {

	$sql_pVar = '
			CREATE TABLE IF NOT EXISTS `%ttests` (
              `id` int(10) unsigned NOT NULL AUTO_INCREMENT,
              `creator_id` int(10) unsigned NOT NULL,
              `status` enum(\'active\',\'deleted\') NOT NULL DEFAULT \'active\',
              `time_create` datetime NOT NULL,
              `time_start` datetime DEFAULT NULL,
              `time_stop` datetime DEFAULT NULL,
              `source_template_id` int(11) DEFAULT NULL,
              `sk_name` varchar(255),
              `en_name` varchar(255),
              `sk_description` text,
              `en_description` text,
              `smer` varchar(255),
              `rocnik` varchar(255),
              `official` enum(\'no\',\'yes\') NOT NULL DEFAULT \'no\',
              `access_key` varchar(255) NOT NULL DEFAULT \'\',
              `settings` text,
              PRIMARY KEY (`id`)                              
            ) ENGINE=InnoDB';
    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
    
	$sql_pVar = '
			CREATE TABLE IF NOT EXISTS `%ttests__questions` (
                                 `test_id` int(10) unsigned NOT NULL,      
                                 `db_question_id` int(10) unsigned NOT NULL,       
                                 `question_order` int(11) NOT NULL,                
                                 `db_answer_ids` varchar(255) NOT NULL,
                                 PRIMARY KEY (`test_id`,`db_question_id`)  
                               ) ENGINE=InnoDB';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	$sql_pVar = '
			CREATE TABLE IF NOT EXISTS `%ttests_running` (
              `id` int(10) unsigned NOT NULL AUTO_INCREMENT,  
              `creator_id` int(10) unsigned NOT NULL,         
              `user_id` int(11) NOT NULL,   
              `official` enum(\'no\',\'yes\') NOT NULL DEFAULT \'no\',
              `time_create` datetime NOT NULL,                
              `time_start` datetime DEFAULT NULL,             
              `time_end` datetime DEFAULT NULL,
              `time_first` datetime DEFAULT NULL,    
              `time_total` int(11) NOT NULL DEFAULT \'0\',         
			  `status` enum(\'waiting\',\'running\',\'closed\') NOT NULL DEFAULT \'waiting\',
			  `score` int(11) NOT NULL DEFAULT \'0\',
			  `n_answers_ok` int(11) NOT NULL DEFAULT \'0\',
			  `n_answers_fail` int(11) NOT NULL DEFAULT \'0\',
              `source_test_id` int(11) DEFAULT NULL,          
              `source_template_id` int(11) DEFAULT NULL,      
              `settings` text,                                
              PRIMARY KEY (`id`)                              
            ) ENGINE=InnoDB';
    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);        
	
	$sql_pVar = '
			CREATE TABLE IF NOT EXISTS `%ttests_running__questions` (      
                                 `running_test_id` int(10) unsigned NOT NULL,      
                                 `db_question_id` int(10) unsigned NOT NULL,       
                                 `question_order` int(11) NOT NULL,                
                                 `db_answer_ids` varchar(255) NOT NULL,            
                                 `answer_results` varchar(255) DEFAULT NULL,       
                                 PRIMARY KEY (`running_test_id`,`db_question_id`)  
                               ) ENGINE=InnoDB';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	
	stamp_gFunc('kega', 'kega testy');
}


if(!isStamp_gFunc('kega', 'kega indexy')) {
	$i_pVar = new install_item_gClass('test_answers', false);
	$i_pVar->addIndex_gFunc('otazka', '`test_question`', 'INDEX');
	$i_pVar->apply_gFunc();
	stamp_gFunc('kega', 'kega indexy');
}

if(!isStamp_gFunc('kega', 'kega test_work')) {
	$sql_pVar = '
		CREATE TABLE `%ttest_work` (                                   
		                  `item_id` int(10) unsigned NOT NULL AUTO_INCREMENT,             
		                  `status` enum(\'active\',\'archived\',\'deleted\') DEFAULT \'active\',  
		                  `user_id` int(10) unsigned NOT NULL,                            
		                  `join_date` datetime NOT NULL,                                  
		                  `prepare_date` datetime DEFAULT NULL,                           
		                  `koordinator_id` int(10) unsigned DEFAULT NULL,                 
		                  `rocnik` int(10) unsigned DEFAULT NULL,                         
		                  `predmet` varchar(255) DEFAULT NULL,                            
		                  `kniha` text,                                                   
		                  `kniha_cast` varchar(255) DEFAULT NULL,                         
		                  `poznamka` text,                                                
		                  PRIMARY KEY (`item_id`)                                         
		                )';
	db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__);
	stamp_gFunc('kega', 'kega test_work');
}


return(true);
