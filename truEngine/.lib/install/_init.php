<?php
set_time_limit(180);

if(!modules_gClass::isModuleRegistred_gFunc('db')) {
	return(false);
}

require_once('_install.lib.php');

function stamp_gFunc($module_name_pVar, $stamp_pVar)
{
	global $startTime_pVar;
	$sql_pVar = 'INSERT INTO `%tinstall` (`install_time`, `install_module`, `install_tag`) VALUES(now(), %s, %s)';
	if(!db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($module_name_pVar, $stamp_pVar))) {
		return(false);
	}
	echo $module_name_pVar . ': ' . $stamp_pVar . '<br />' . LF;
	if(time()-$startTime_pVar > 30) {
		echo 'UKONCUJEM... '.(time()-$startTime_pVar). LF;
		exit;
	}
	return(true);
}

function isStamp_gFunc($module_name_pVar, $stamp_pVar)
{
	global $startTime_pVar;
	
	if(!$startTime_pVar) {
		$startTime_pVar = time();
	}
	
	if(!modules_gClass::isModuleRegistred_gFunc($module_name_pVar)) {
		return(true);
	}
	$sql_pVar = 'SELECT count(`id`) FROM `%tinstall` WHERE `install_module`=%s AND `install_tag`=%s';
	$res_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($module_name_pVar, $stamp_pVar));
	if($res_pVar === false) {
		return(null);
	}
	if($res_pVar > 0) {
		return(true);
	}
	else {
		return(false);
	}
}

/**
 * $type_pVar moze byt 'user', 'group', alebo 'role'
 *
 * @param unknown_type $type_pVar
 * @return unknown
 */
function insertRights_gFunc($type_pVar, $typeId_pVar, $rights_pVar = array())
{	
	$sql_pVar = "INSERT INTO `%taccess__" . $type_pVar . "s_rights` (`" . $type_pVar . "_id`,`" . $type_pVar . "_right_id`,	`" . $type_pVar . "_right_state`) VALUES ";
	$n_pVar = 0;
	foreach ($rights_pVar as $k_pVar=>$v_pVar) {
		if(!is_numeric($k_pVar)) {
			$k_pVar = constant($k_pVar);
		}
		if($n_pVar) {
			$sql_pVar .= ',';
		}
		$sql_pVar .= "('" . $typeId_pVar . "', " . $k_pVar . ", '" . ($v_pVar?'allow':'deny') . "')";
		$n_pVar++;
	}
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	return(true);
}

echo '<style type="text/css">.msg_error { color:red; border:1px solid red; padding:10px; background-color:#ffcccc; margin:10px; }</style>';

$sql_pVar = 'ALTER DATABASE `' . main_gClass::getConfigVar_gFunc('database', 'db') . '` character set ' . main_gClass::getConfigVar_gFunc('charset', 'db') . ' collate '. main_gClass::getConfigVar_gFunc('collation', 'db');
if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
	return(false);
}

$sql_pVar = "
CREATE TABLE IF NOT EXISTS `%tinstall` (
`id` int(10) unsigned NOT NULL auto_increment,
`install_time` datetime NOT NULL,
`install_module` varchar(255) NOT NULL default '' COMMENT 'modul',
`install_tag` varchar(255) NOT NULL default '' COMMENT 'razitko updatu',
 PRIMARY KEY  (`id`)
) ENGINE=InnoDB;
";
if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
	return(false);
}

$order_pVar = array('_loader', 'items', 'access1_auth', 'access2_session', 'access3_rights',
					'access4_groups', 'access5_objects', 'kega', 'search');

$includeDir_pVar = main_gClass::getConfigVar_gFunc('path_system_include_gVar', 'runtime_pVar');
$tmpModules_pVar = main_gClass::getConfigSectionVar_gFunc('modules');

/**
 * vytvorim si zoznam moulov, ktore budem instalovat
 * Najskor pouzijem moduly podla poradia, potom pridam zvysne moduly z konfiguracie. 
 */
$modules_pVar = array();
foreach ($order_pVar as $v_pVar) {
	$modules_pVar[$v_pVar] = false;
}
foreach ($tmpModules_pVar as $k_pVar=>$v_pVar) {
	$modules_pVar[$k_pVar] = $v_pVar;
}

/**
 * INSTALL
 */
foreach ($modules_pVar as $module_pVar=>$enabled_pVar)
{
	if(!$enabled_pVar) {
		continue;
	}
	$fileName_pVar = $includeDir_pVar . 'install/_' . strtolower($module_pVar) . '.install.php';
	if(file_exists($fileName_pVar)) {
		echo $fileName_pVar . '<br />';
	    $ret_pVar = include($fileName_pVar);
	    if($ret_pVar !== true) return(false);
	}
}

/**
 * UPDATE
 */
$updateDirs_pVar = array();
$path_pVar = $includeDir_pVar . 'install/';
$dir_pVar = dir($path_pVar);
while (($file_pVar = $dir_pVar->read()) !== false) {
	if($file_pVar == '..' || $file_pVar == '.' || $file_pVar == '.svn') {
		continue;
	}
	if(!is_dir($path_pVar . $file_pVar)) {
		continue;
	}
	$updateDirs_pVar[] = $path_pVar . $file_pVar;
}
$dir_pVar->close();
sort($updateDirs_pVar);

foreach($updateDirs_pVar as $dir_pVar) {
	foreach ($modules_pVar as $module_pVar=>$enabled_pVar)
	{
		if(!$enabled_pVar) {
			continue;
		}
		$fileName_pVar = $dir_pVar . '/_' . strtolower($module_pVar) . '.install.php';
		if(file_exists($fileName_pVar)) {
			echo $fileName_pVar . '<br />';
		    $ret_pVar = include($fileName_pVar);
		    if($ret_pVar !== true) return(false);
		}
	}
}


return(true);