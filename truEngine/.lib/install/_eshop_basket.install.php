<?php

if(!isStamp_gFunc('eshop_basket', 'eshop basket')) {

	$sql_pVar = '
		CREATE TABLE `%teshop_payment_types` (                                                            
                           `payment_type_id` int(10) unsigned NOT NULL AUTO_INCREMENT,                                       
                           `payment_type_status` enum(\'active\',\'deleted\') NOT NULL DEFAULT \'active\',  
                           `transport_type_id` int(11) NOT NULL DEFAULT \'0\',                                                 
                           `sk_payment_type_name` varchar(255) NOT NULL DEFAULT \'\',                   
                           `en_payment_type_name` varchar(255) NOT NULL DEFAULT \'\',                   
                           PRIMARY KEY (`payment_type_id`)                                                                   
                         )
	';
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	
	$sql_pVar = '
		CREATE TABLE `%teshop_transport_types` (                                                            
		                             `transport_type_id` int(10) unsigned NOT NULL AUTO_INCREMENT,                                       
		                             `sk_transport_type_name` varchar(255) NOT NULL DEFAULT \'\',                   
		                             `transport_type_price` float NOT NULL DEFAULT \'0\',                                                  
		                             `transport_type_currency` varchar(3) NOT NULL DEFAULT \'\',                    
		                             `transport_type_status` enum(\'active\',\'deleted\') COLLATE utf8_slovak_ci NOT NULL DEFAULT \'active\',  
		                             `en_transport_type_name` varchar(255) COLLATE utf8_slovak_ci NOT NULL DEFAULT \'\',                   
		                             PRIMARY KEY (`transport_type_id`)                                                                   
		                           )
	';
	
	if(!db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
		return(false);
	}
	
	
	stamp_gFunc('eshop_basket', 'eshop basket');
}

return(true);
