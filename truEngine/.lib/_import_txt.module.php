<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__import_txt_pVar'])) return(true);
$GLOBALS['file__import_txt_pVar']=true;


class import_txt_gClass extends import_gClass
{
	protected function __construct($fileName_pVar)
	{
		parent::__construct($fileName_pVar);
	}
	
	protected function loadInfo_gFunc($useThisFileHandler_pVar = false)
	{
		$this->info_pVar = array();
		
		if($useThisFileHandler_pVar === false) {
			$f = fopen($this->fileName_pVar, 'rt');
		}
		else {
			$f = $useThisFileHandler_pVar;
		}
		
		if(!$f) {
			return(false);
		}

		$this->info_pVar = array();
		while(1) {
			$str_pVar = fgets($f);
			if($str_pVar === false) {
				break;
			}
			$str_pVar = trim($str_pVar);
			if(empty($str_pVar)) {
				break;
			}
			
			$p_pVar = strpos($str_pVar, ':');
			if($p_pVar === false) {
				break;
			}
			
			$field_pVar = trim(substr($str_pVar, 0, $p_pVar));
			$value_pVar = trim(substr($str_pVar, $p_pVar + 1));

			if($field_pVar === 'data') {
				$field_pVar = 'data_type';
			}
			$this->info_pVar[$field_pVar] = $value_pVar;
		}
		
		if($useThisFileHandler_pVar === false) {
			fclose($f);
		}
		
        return($this->info_pVar);
	}
	
	public function _import_gFunc()
	{		
		$f = fopen($this->fileName_pVar, 'rt');
		if(!$f) {
			return(false);
		}
		$this->loadInfo_gFunc($f);

		$data_pVar = array('data'=>array());

		$root_loaded_pVar = false;
		while(!feof($f)) {
			$row_pVar = fgets($f);
			if($row_pVar === false) {
				break;
			}
			$row_pVar = trim($row_pVar);
			
			if($root_loaded_pVar === false) {
				if(empty($row_pVar)) {
					continue;
				}
				$tmp_pVar = array();
				if(preg_match('/^(\d+\.\s*)(.*)$/', $row_pVar, $tmp_pVar)) {
					$row_pVar = $tmp_pVar[2];
				}
				else {
					continue;
				}
				$row_pVar = trim($row_pVar);
				if($row_pVar == '-') {
					$row_pVar = '';
				}
				
				$fields_pVar = explode(',', $this->info_pVar['fields']);
				$item_data_pVar = array();
				$subitem_index_pVar = array();
				$field_pVar = array_shift($fields_pVar);
				if(strpos($field_pVar, '.') !== false) {
					$fields_pVar[] = $field_pVar;
					$p_pVar = strpos($field_pVar, '.');
					$prefix_pVar = substr($field_pVar, 0, $p_pVar);
					$field_pVar = substr($field_pVar, $p_pVar + 1);
					if(!isset($item_data_pVar[$prefix_pVar])) {
						$item_data_pVar[$prefix_pVar] = array();
						$subitem_index_pVar[$prefix_pVar] = 0;
					}
					if(isset($item_data_pVar[$prefix_pVar][$subitem_index_pVar[$prefix_pVar]][$field_pVar])) {
						$subitem_index_pVar[$prefix_pVar]++;
					}
					$item_data_pVar[$prefix_pVar][$subitem_index_pVar[$prefix_pVar]][$field_pVar] = $row_pVar;
				}
				else {
					$item_data_pVar[$field_pVar] = $row_pVar;
				}
				$root_loaded_pVar = true;
				continue;
			}
			
			if(empty($row_pVar)) {
				$data_pVar['data'][] = $item_data_pVar;
				$item_data_pVar = array();
				$root_loaded_pVar = false;
				continue;
			}
			
			$field_pVar = array_shift($fields_pVar);
			if(strpos($field_pVar, '.') !== false) {
					$fields_pVar[] = $field_pVar;
					$p_pVar = strpos($field_pVar, '.');
					$prefix_pVar = substr($field_pVar, 0, $p_pVar);
					$field_pVar = substr($field_pVar, $p_pVar + 1);
					if(!isset($item_data_pVar[$prefix_pVar])) {
						$item_data_pVar[$prefix_pVar] = array();
						$subitem_index_pVar[$prefix_pVar] = 0;
					}
					if(isset($item_data_pVar[$prefix_pVar][$subitem_index_pVar[$prefix_pVar]][$field_pVar])) {
						$subitem_index_pVar[$prefix_pVar]++;
					}
					
					$tmp_pVar = array();
					if(preg_match('/^(\s*[A-Z]+\)\s*)(.*)$/', $row_pVar, $tmp_pVar)) {
						$row_pVar = $tmp_pVar[2];
					}
					
					$row_pVar = trim($row_pVar);
					if($row_pVar == '-') {
						$row_pVar = '';
					}
					
					$item_data_pVar[$prefix_pVar][$subitem_index_pVar[$prefix_pVar]][$field_pVar] = $row_pVar;
			}
			else {
				$row_pVar = trim($row_pVar);
				if($row_pVar == '-') {
					$row_pVar = '';
				}
				$item_data_pVar[$field_pVar] = $row_pVar;
			}
			
			
			
		}
		if(isset($item_data_pVar) && count($item_data_pVar)) {
			$data_pVar['data'][] = $item_data_pVar;
		}

        $data_pVar['data_type'] = $this->info_pVar['data_type'];
        
//        echo '<pre>'; print_r($data_pVar); echo '</pre>';
		fclose($f);
        $this->_import_data_gFunc($data_pVar);
        return(true);        
	}

}


return(true);
