<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__templates_pVar'])) return(true);
$GLOBALS['file__templates_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('xml'))
{
    return(false);
}

class documentTemplate_gClass extends document_gClass
{
    private $rules_pVar;

    public static function getTemplateObject_gFunc($docName_pVar, $docType_pVar = document_gClass::DOCTYPE_DOCUMENT, $systemCall_pVar = false)
    {
        $tmpDocName_pVar = document_gClass::safeDocName_gFunc($docName_pVar, $docType_pVar, $systemCall_pVar);

        if($tmpDocName_pVar === false) {
            return new document404_gClass($docName_pVar, $docType_pVar);
        }
        else {
            $docName_pVar = $tmpDocName_pVar;
        }

        if(strtolower(substr($docName_pVar, -8)) === '.tpl.xml') {
            $docName_pVar = substr($docName_pVar, 0, -8);
        }

        return new documentTemplate_gClass($docName_pVar, $docType_pVar);
    }

    function __construct($docName_pVar, $docType_pVar = document_gClass::DOCTYPE_DOCUMENT)
    {
        parent::__construct($docName_pVar, $docType_pVar);
        $this->rules_pVar = array();
    }

    public function execute_gFunc()
    {
        $pathChecked_pVar = self::getPathForChecked_gFunc($this->getDocName_gFunc());

        $fileName_pVar = document_gClass::getTemplateFileName_gFunc($this->getDocName_gFunc());

            if(!fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
                return($this->doc404_gFunc());
            }

            $xmlString_pVar = file_get_contents($fileName_pVar);
            if(!strlen(trim($xmlString_pVar))) {
                return($this->doc404_gFunc(true));
            }

            if(!modules_gClass::isModuleRegistred_gFunc('xml'))
            {
                return($this->doc404_gFunc(true));
            }

            $rules_pVar = new templateRuleParser_gClass('template_rules.xml');
            if(!$rules_pVar->isError_gFunc())
            {
                $this->rules_pVar = $rules_pVar->getParsedRules_gFunc();
            }

            if($rules_pVar->isError_gFunc()) {
                error_gClass::fatal_gFunc(__FILE__,__LINE__);
                return(''); // ruleserror
            }

/*
            ob_start();
            echo '<pre>';
            print_r($this->rules_pVar);
            echo '</pre>';
            $ret=ob_get_contents();
            ob_end_clean();
            return($ret);
*/

            $xdoc_pVar = new tpl__xmlBase_gClass();
            $xdoc_pVar->setClassPrefix_gFunc('tpl__');
            if(!$xdoc_pVar->parseXMLString_gFunc($xmlString_pVar))
            {
                error_gClass::error_gFunc(__FILE__,__LINE__);
                if(fileSafe_gClass::file_exists_gFunc($pathChecked_pVar)) {
                    $xmlString_pVar = file_get_contents($pathChecked_pVar);
                    if(!strlen(trim($xmlString_pVar))) {
                        return($this->doc404_gFunc(true));
                    }
                    if(!$xdoc_pVar->parseXMLString_gFunc($xmlString_pVar))
                    {
                        error_gClass::error_gFunc(__FILE__,__LINE__);
                        return($this->doc502_gFunc(true));
                    }
                }
                else {
                    return($this->doc502_gFunc(false));
                }
            }

            //// checked update
            $fileName1_pVar=fileSafe_gClass::safePath_gFunc($pathChecked_pVar);
            $fileName2_pVar=fileSafe_gClass::safePath_gFunc($fileName_pVar);
            if(fileSafe_gClass::file_exists_gFunc($fileName1_pVar)) {
            	$fileTime1_pVar=filemtime(fileSafe_gClass::unsafePath_gFunc($fileName1_pVar)); // filemtime nepodporuje wrapper
            	$fileTime2_pVar=filemtime(fileSafe_gClass::unsafePath_gFunc($fileName2_pVar)); // filemtime nepodporuje wrapper
            }
            if(!fileSafe_gClass::file_exists_gFunc($fileName1_pVar) || ($fileTime1_pVar<$fileTime2_pVar)) {
                $needUpdate_pVar=true;
            }
            else {
                $needUpdate_pVar=false;
            }

        	if($needUpdate_pVar) {
        	    if(!file_put_contents($fileName1_pVar, $xmlString_pVar)) {
        	        error_gClass::warning_gFunc(__FILE__,__LINE__,$fileName1_pVar);
        	    }
        	}

        	// spracovanie dokumentu
        	docStringsStack_gClass::createStack_gFunc();
        	templateStack_gClass::createStack_gFunc();
            $docContent_pVar = $xdoc_pVar->getResult_gFunc($this->rules_pVar, '/', templateRuleParser_gClass::XML_SYNTAX_XHTML);
            templateStack_gClass::destroyStack_gFunc();
            self::cacheStrings_gFunc($this->getDocName_gFunc());
	        docStringsStack_gClass::destroyStack_gFunc();

            $docContent_pVar = self::clean_gFunc($docContent_pVar);

        	/// cache update
        	if(modules_gClass::isModuleRegistred_gFunc('cache')) {
        	   $cacheDocContent_pVar = $docContent_pVar . PHP_OPEN . ' return(true); ' . PHP_CLOSE;
        	   documentCache_gClass::updateDoc_gFunc($this->getDocName_gFunc(), $cacheDocContent_pVar);
        	}

            // var_dump($this->getDocName_gFunc());

            // eval & return
            ob_start();
            eval(PHP_CLOSE . $docContent_pVar . PHP_OPEN);
            $docContent_pVar = ob_get_contents();
            ob_end_clean();
            return($docContent_pVar);
    }

    private function cacheStrings_gFunc($docName_gFunc)
    {
       if(modules_gClass::isModuleRegistred_gFunc('cms_edit')) {
       		cms_edit_gClass::cacheStrings_gFunc($docName_gFunc, docStringsStack_gClass::getStackData_gFunc());
       }
    }

    private function clean_gFunc($str_pVar)
    {
    	$str_pVar = str_replace('\'\'_TE:VAR- . ', '', $str_pVar);
    	$str_pVar = str_replace('""_TE:VAR- . ', '', $str_pVar);
    	$str_pVar = str_replace(' . -TE:VAR_\'\'', '', $str_pVar);
    	$str_pVar = str_replace(' . -TE:VAR_""', '', $str_pVar);

    	$str_pVar = str_replace('<truengine-document>', '', $str_pVar);
    	$str_pVar = str_replace('</truengine-document>', '', $str_pVar);

    	$str_pVar = str_replace('-TE:VAR_', '', $str_pVar);
    	$str_pVar = str_replace('_TE:VAR-', '', $str_pVar);

    	$str_pVar = str_replace('?'.'><'.'?php', '', $str_pVar);

    	return($str_pVar);
    }

    private static function getPathForChecked_gFunc($documentName_pVar = '')
    {
        $pathChecked_pVar = \Illuminate\Support\Facades\Storage::disk('truengine-legacy-data')->path('.checked/');

        $documentName_pVar = str_replace('^', '^^', $documentName_pVar);
        $documentName_pVar = str_replace('/', '^', $documentName_pVar);
        $documentName_pVar = str_replace('\\', '^', $documentName_pVar);
        $documentName_pVar = str_replace('%', '^', $documentName_pVar);
        if(strlen($documentName_pVar)) {
            $documentName_pVar = 'doc_' . $documentName_pVar . '.chck.xml';
        }

        $pathChecked_pVar .= $documentName_pVar;
        fileSafe_gClass::safePath_gFunc($pathChecked_pVar);
        return($pathChecked_pVar);
    }


}

class templateRuleParser_gClass extends truEngineBaseClass_gClass
{
    private $inError_pVar;
    private $rules_pVar;
    private $fileName_pVar;
    private $xmlData_pVar;

    const XML_SYNTAX_ALL    = 'all';
    const XML_SYNTAX_XML    = 'xml';
    const XML_SYNTAX_XHTML  = 'xhtml';
    const XML_SYNTAX_HTML   = 'html';

    const XML_DISPLAY_NORMAL = 'normal';
    const XML_DISPLAY_OPEN   = 'open';
    const XML_DISPLAY_SHORT  = 'short';
    const XML_DISPLAY_NONE   = 'none';
    const XML_DISPLAY_INSIDE = 'inside';

    function __construct($ruleFile_pVar)
    {
        $this->inError_pVar = false;
        $this->xmlData_pVar = false;

        $pathWeb_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        $fileName_pVar = $pathWeb_pVar . '.rules/' . $ruleFile_pVar;
        $fileName_pVar = fileSafe_gClass::safePath_gFunc($fileName_pVar);
        $this->fileName_pVar = $fileName_pVar;

        $this->rules_pVar = array();

        $xmlString_pVar = file_get_contents($fileName_pVar);
        if(!strlen(trim($xmlString_pVar))) {
            return;
        }

        if(!modules_gClass::isModuleRegistred_gFunc('xml'))
        {
            $this->inError_pVar = true;
            return;
        }

        $this->xmlData_pVar = new xmlBase_gClass();
        if(!$this->xmlData_pVar->parseXMLString_gFunc($xmlString_pVar)) {
            $this->inError_pVar = true;
            return;
        }

        $this->parseXmlRules_gFunc($this->xmlData_pVar);
    }

    function isError_gFunc()
    {
        if($this->inError_pVar) {
            return(true);
        }
        else {
            return(false);
        }
    }

    function parseXmlRules_gFunc(&$x, $props_pVar= array('path_pVar'=>'/(.*/)*',
                                                        'syntax_pVar'=>self::XML_SYNTAX_ALL,
                                                        'type_pVar'=>'all',
                                                        'display_pVar'=>self::XML_DISPLAY_SHORT))
    {
        if($x->getElementTypeId_gFunc() != xmlElement_gClass::XML_ELEMENT_TYPE_TAG_pVar
            && $x->getElementTypeId_gFunc() != xmlElement_gClass::XML_ELEMENT_TYPE_ROOT_pVar)
        {
            return;
        }

        if($x->isAttribute_gFunc('type')) {
            switch ($x->getAttribute_gFunc('type'))
            {
                case 'element':
                case 'data':
                case 'cdata':
                case 'comment':
                case 'all':
                    $props_pVar['type_pVar'] = $x->getAttribute_gFunc('type');
                    break;
                default:
                    error_gClass::error_gFunc(__FILE__, __LINE__, sprintf(string_gClass::get('str__templates_rules_type_sVar'), $x->getAttribute_gFunc('type')));
                    // return;
            }
            unset($props_pVar['tagName_pVar']);
            unset($props_pVar['attrName_pVar']);
        }


        if($x->isAttribute_gFunc('tag-name')) {
            $props_pVar['tagName_pVar'] = $x->getAttribute_gFunc('tag-name');
            $props_pVar['type_pVar'] = 'element';
        }
        if($x->isAttribute_gFunc('attr-name')) {
            $props_pVar['attrName_pVar'] = $x->getAttribute_gFunc('attr-name');
            $props_pVar['type_pVar'] = 'element';
        }

        if($x->isAttribute_gFunc('path')) {
		  $xpath_pVar = $x->getAttribute_gFunc('path');
		  if($xpath_pVar[0] == '/') {
		      $props_pVar['path_pVar'] = $xpath_pVar;
		  }
		  else {
		      $props_pVar['path_pVar'] .= $xpath_pVar;
		  }
        }

        if($x->isAttribute_gFunc('syntax')) {
            $xsyntax_pVar = $x->getAttribute_gFunc('syntax');
            switch($x->getAttribute_gFunc('syntax')) {
                case 'xml':
                    $props_pVar['syntax_pVar'] = self::XML_SYNTAX_XML;
                    break;
                case 'html':
                    $props_pVar['syntax_pVar'] = self::XML_SYNTAX_HTML;
                    break;
                case 'xhtml':
                    $props_pVar['syntax_pVar'] = self::XML_SYNTAX_XHTML;
                    break;
                case 'all':
                    $props_pVar['syntax_pVar'] = self::XML_SYNTAX_ALL;
                    break;
                default:
                    error_gClass::error_gFunc(__FILE__, __LINE__, sprintf(string_gClass::get('str__templates_rules_syntax_sVar'), $x->getAttribute_gFunc('syntax')));
                    // return;
            }
        }

        if($x->isAttribute_gFunc('display')) {
            switch ($x->getAttribute_gFunc('display')) {
                case 'normal':
                    $props_pVar['display_pVar'] = self::XML_DISPLAY_NORMAL;
                    break;
                case 'open':
                    $props_pVar['display_pVar'] = self::XML_DISPLAY_OPEN;
                    break;
                case 'short':
                    $props_pVar['display_pVar'] = self::XML_DISPLAY_SHORT;
                    break;
                case 'none':
                    $props_pVar['display_pVar'] = self::XML_DISPLAY_NONE;
                    break;
                case 'inside':
                    $props_pVar['display_pVar'] = self::XML_DISPLAY_INSIDE;
                    break;
                default:
                    error_gClass::error_gFunc(__FILE__, __LINE__, sprintf(string_gClass::get('str__templates_rules_display_sVar'), $x->getAttribute_gFunc('display')));
                    // return;
            }
        }

        if($x->isAttribute_gFunc('rename')) {
            $props_pVar['rename_pVar'] = $x->getAttribute_gFunc('rename');
        }

        if($x->isAttribute_gFunc('class')) {
            $className_pVar = $x->getAttribute_gFunc('class');
            if(strlen($className_pVar)) {
                $prefix_pVar = substr($className_pVar, 0, 13);
                if(strtolower($prefix_pVar) === 'templaterule_') {
                    // najskor odstranim prefix, potom ho pridam.. koli case sensive.
                    $className_pVar = substr($className_pVar, 13);
                }
                $className_pVar = 'templateRule_' . $className_pVar;

                $props_pVar['class_pVar'] = $className_pVar;
                $moduleName_pVar = modules_gClass::findClass_gFunc($className_pVar);
                if(strlen($moduleName_pVar)) {
                    $props_pVar['module_pVar'] = $moduleName_pVar;
                }
            }
        }

        if($x->getTagName_gFunc() === 'RULE') {
            $this->rules_pVar[] = $props_pVar;
        }

        foreach($x as $k_pVar=>$v_pVar) {
            $tag_pVar = $x->getChildRef_gFunc($k_pVar);
            $this->parseXmlRules_gFunc($tag_pVar, $props_pVar);
            unset($tag_pVar);
        }
    }

    public function getParsedRules_gFunc()
    {
        if($this->inError_pVar) {
            return(array());
        }
        else {
            return($this->rules_pVar);
        }

    }
}


class docStringsStack_gClass extends genericStack_gClass
{
    public static function createStack_gFunc()
    {
    	parent::genCreateStack_gFunc('docStringsStack_gClass');
    }

	public static function destroyStack_gFunc()
	{
		parent::genDestroyStack_gFunc('docStringsStack_gClass');
	}

	public static function getStackData_gFunc()
	{
		return(parent::genGetStackData_gFunc('docStringsStack_gClass'));
	}

    public static function push_gFunc($data_pVar)
    {
    	parent::genPush_gFunc('docStringsStack_gClass', $data_pVar);
    }

    public static function pop_gFunc($instanceName_pVar)
    {
    	return(parent::genPop_gFunc('docStringsStack_gClass'));
    }
}

/**
 * stack na objekty pri parsovani dokumentu
 */
class templateStack_gClass
{
    static private $stacks_pVar = array();
    static private $lastStackId_pVar = -1;
    static private $stack_lastItemId_pVar = array();

    static function createStack_gFunc()
    {
        self::$lastStackId_pVar++;
        self::$stacks_pVar[self::$lastStackId_pVar] = array();
        self::$stack_lastItemId_pVar[self::$lastStackId_pVar] = -1;
    }

    static function destroyStack_gFunc()
    {
        unset(self::$stacks_pVar[self::$lastStackId_pVar]);
        unset(self::$stack_lastItemId_pVar[self::$lastStackId_pVar]);
        self::$lastStackId_pVar--;
    }

    static function push_gFunc($object_pVar)
    {
        self::$stack_lastItemId_pVar[self::$lastStackId_pVar]++;

        self::$stacks_pVar[self::$lastStackId_pVar][self::$stack_lastItemId_pVar[self::$lastStackId_pVar]] = $object_pVar;
    }

    static function pop_gFunc()
    {
        $object_pVar = self::$stacks_pVar[self::$lastStackId_pVar][self::$stack_lastItemId_pVar[self::$lastStackId_pVar]];
        self::deleteLastItem_gFunc();
        return($object_pVar);
    }

    static function deleteLastItem_gFunc()
    {
        unset(self::$stacks_pVar[self::$lastStackId_pVar][self::$stack_lastItemId_pVar[self::$lastStackId_pVar]]);
        self::$stack_lastItemId_pVar[self::$lastStackId_pVar]--;
    }

    static function deleteItemsToId_gFunc($toItemId_pVar)
    {
        while(self::$stack_lastItemId_pVar[self::$lastStackId_pVar] > $toItemId_pVar) {
            self::deleteLastItem_gFunc();
        }
    }

    static function getLastItemId_gFunc()
    {
        return(self::$stack_lastItemId_pVar[self::$lastStackId_pVar]);
    }

    static function createAndPush_gFunc($className_pVar, $path_pVar)
    {
        $obj_pVar = new $className_pVar;
        $obj_pVar->path_pVar = $path_pVar;

        self::push_gFunc($obj_pVar);
        return(true);
    }

    static function &getLastItemAsRef_gFunc()
    {
        $ref_pVar = &self::$stacks_pVar[self::$lastStackId_pVar][self::$stack_lastItemId_pVar[self::$lastStackId_pVar]];
        return($ref_pVar);
    }

    static function findParentObjectId($objectClassName, $elementName = false, $elementAttr = false)
    {
        self::findParentObjectId($objectClassName, $elementName, $elementAttr);
    }

    static function findParentObjectId_gFunc($objectClassName_pVar, $elementName_pVar = false, $elementAttr_pVar = false)
    {
        $currentId_pVar = self::$stack_lastItemId_pVar[self::$lastStackId_pVar];

        if($elementName_pVar !== false) {
            $elementName_pVar = strtoupper($elementName_pVar);
        }

        while(1)
        {
            $currentId_pVar--;
            if($currentId_pVar < 0) {
                return(-1);
            }
            if(self::$stacks_pVar[self::$lastStackId_pVar][$currentId_pVar]->getClassName_gFunc() != $objectClassName_pVar) {
                continue;
            }
            if($elementName_pVar !== false) {
                if(self::$stacks_pVar[self::$lastStackId_pVar][$currentId_pVar]->elementName_pVar != $elementName_pVar) {
                    continue;
                }
            }
            if($elementAttr_pVar !== false) {
                if(!self::$stacks_pVar[self::$lastStackId_pVar][$currentId_pVar]->attributeExists_gFunc($elementAttr_pVar)) {
                    continue;
                }
            }

            // ok
            return($currentId_pVar);
        }
    }

    static function getElementByRelPathAsId_gFunc($path_pVar)
    {
        $currentId_pVar = self::$stack_lastItemId_pVar[self::$lastStackId_pVar];

        if($path_pVar[0] == '/') {
            $currentId_pVar = 0;
        }

        $path_pVar = explode('/', $path_pVar);

        foreach ($path_pVar as $p_pVar) {
            if($p_pVar === '.') {
                // nerobim nic
            }
            else if($p_pVar === '..') {
                $currentId_pVar--;
            }
            else if(self::$stacks_pVar[self::$lastStackId_pVar][$currentId_pVar+1] != $p_pVar) {
                return(-1);
            }
            else {
                $currentId_pVar++;
            }
        }

        if(!isset(self::$stacks_pVar[self::$lastStackId_pVar][$currentId_pVar])) {
            return(-1);
        }
        return($currentId_pVar);
    }

    static function &getItemByIdAsRef_gFunc($id_pVar)
    {
        $ref_pVar = &self::$stacks_pVar[self::$lastStackId_pVar][$id_pVar];
        return($ref_pVar);
    }

}


class autoAccessStack_gClass {
    private static $stacks_pVar = array();
    private static $lastStackId_pVar = -1;
    static public $access_pVar = array();

    private function __construct()
    {

    }

    static function createStack_gFunc()
    {
        self::$lastStackId_pVar++;
        self::$stacks_pVar[self::$lastStackId_pVar] = self::$access_pVar;
        self::$access_pVar = array();
    }

    static function destroyStack_gFunc()
    {
        if(self::$lastStackId_pVar < 0) {
            self::$access_pVar = array();
            return;
        }
        self::$access_pVar = array_merge(self::$access_pVar, self::$stacks_pVar[self::$lastStackId_pVar]);
        unset(self::$stacks_pVar[self::$lastStackId_pVar]);
        self::$lastStackId_pVar--;
    }

    static function pushAccess_gFunc($accessString_pVar)
    {
    	array_push(self::$access_pVar, $accessString_pVar);
    }

    static function getAccess_gFunc()
    {
    	if(!count(self::$access_pVar)) {
    		return(false);
    	}
    	return(' (' . implode(') || (', array_unique(self::$access_pVar)) . ') ');
    }
}

class tpl__xmlBase_gClass extends xmlBase_gClass
{
    public function getResult_gFunc(&$rules_pVar, $path_pVar = '/', $syntax_pVar = templateRuleParser_gClass::XML_SYNTAX_HTML)
    {
        $data_pVar = '';
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getResult_gFunc($rules_pVar, $path_pVar, $syntax_pVar);
            }
        }
        $data_pVar = $this->replaceVars_gFunc($data_pVar);
        return($data_pVar);
    }

    /**
     * Replace vars musim spravit zvlast pre php bloky a zvlast pre html bloky.
     * V php blokoch musim este rozlisovat v retazcoch ", v retazcoch ' a mimo retazcov
     * Preto si to rozparsujem na bloky, a replacnem metodami
     * replaceVarsInPhp_gFunc, replaceVarsInOutput_gFunc
     *
     */
    private function replaceVars_gFunc($data_pVar)
    {
        ///// rozdelim na HTML a PHP bloky
            $array_pVar = preg_split('/(<\?)|(\?>)/', $data_pVar, null, PREG_SPLIT_OFFSET_CAPTURE);

            $dataBlocks_pVar = array();
            $dataLen_pVar = strlen($data_pVar);
            foreach($array_pVar as $k_pVar=>$v_pVar) {
                $suffix_pVar = '';
                if($v_pVar[1]) {
                    $prefix_pVar = substr($data_pVar, $v_pVar[1]-2, 2);
                    if($prefix_pVar === '<' . '?') {
                        $suffix_pVar = '?' . '>';
                    }
                    elseif ($prefix_pVar === '?' . '>') {
                        $prefix_pVar = '';
                    }
                }
                else {
                    $prefix_pVar = '';
                }

                if(!empty($prefix_pVar)) {
                    // php blok
                    $v_pVar[0] = $this->replaceVarsInPhp_gFunc($v_pVar[0]);
                }
                else {
                    // html blok
                    $v_pVar[0] = $this->replaceVarsInOutput_gFunc($v_pVar[0]);
                }
                $dataBlocks_pVar[] = $prefix_pVar . $v_pVar[0] . $suffix_pVar;
            }
            // v $dataBlocks_pVar mam php bloky a html bloky

            $data_pVar = '';
            foreach ($dataBlocks_pVar as $v_pVar) {
                $data_pVar .= $v_pVar;
            }

        return($data_pVar);
    }

    private function replaceVarsInPhp_gFunc($data_pVar)
    {
        // rozdelim podla znakov ' a "
        $array_pVar = preg_split('/(\')|(\")/', $data_pVar, null, PREG_SPLIT_OFFSET_CAPTURE);
        $dataBlocks_pVar = array();
        $dataLen_pVar = strlen($data_pVar);

        $quote_pVar = false;
        $lastId_pVar = -1;
        foreach($array_pVar as $k_pVar=>$v_pVar) {

            if($quote_pVar === false) {
                // nie je v retazci
                if(isset($data_pVar[$v_pVar[1]-1])) {
                    if($data_pVar[$v_pVar[1]-1] === '\'') {
                        $quote_pVar = '\'';
                        $array_pVar[$k_pVar][0] = $quote_pVar . $v_pVar[0];
                    }
                    if($data_pVar[$v_pVar[1]-1] === '"') {
                        $quote_pVar = '"';
                        $array_pVar[$k_pVar][0] = $quote_pVar . $v_pVar[0];
                    }
                }
                $lastId_pVar++;
                $dataBlocks_pVar[$lastId_pVar] = $array_pVar[$k_pVar];
                continue;
            }

            // je v retazci

            if($data_pVar[$v_pVar[1]-1] !== $quote_pVar) {
                // pridam k predoslemu... nie  je to zaciatok ani koniec retazca
                $dataBlocks_pVar[$lastId_pVar][0] .= $data_pVar[$v_pVar[1]-1] . $v_pVar[0];
                continue;
            }

            if(isset($data_pVar[$v_pVar[1]-2]) && $data_pVar[$v_pVar[1]-2] === '\\') {
                // pridam k predoslemu... nie  je to zaciatok ani koniec retazca
                // escapovane uvodzovky alebo apostrof
                $dataBlocks_pVar[$lastId_pVar][0] .= $data_pVar[$v_pVar[1]-1] . $v_pVar[0];
                continue;
            }

            // koniec retazca
            $dataBlocks_pVar[$lastId_pVar][0] .= $quote_pVar;
            $quote_pVar = false;
            $lastId_pVar++;
            $dataBlocks_pVar[$lastId_pVar] = $array_pVar[$k_pVar];
        }

        $data_pVar = '';
        foreach ($dataBlocks_pVar as $v_pVar) {
        	if(strlen($v_pVar[0])) {
	            if($v_pVar[0][0] === '\'') {
	                $tmp_pVar = preg_replace_callback('/@{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceVarsInPhpStringSingleQuotedCallback_gFunc'), $v_pVar[0]);
	                $tmp_pVar = preg_replace_callback('/@L{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceLangsCallback_gFunc'), $tmp_pVar);
	                $data_pVar .= $tmp_pVar;
	            }
	            else if($v_pVar[0][0] === '"') {
	                $tmp_pVar = preg_replace_callback('/@{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceVarsInPhpStringDoubleQuotedCallback_gFunc'), $v_pVar[0]);
	                $tmp_pVar = preg_replace_callback('/@L{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceLangsCallback_gFunc'), $tmp_pVar);
	                $data_pVar .= $tmp_pVar;
	            }
	            else {
	                $tmp_pVar = preg_replace_callback('/@{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceVarsInPhpCodeCallback_gFunc'), $v_pVar[0]);
	                $tmp_pVar = preg_replace_callback('/@L{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceLangsCallback_gFunc'), $tmp_pVar);
	                $data_pVar .= $tmp_pVar;
	            }
        	}
        }
        return($data_pVar);
    }
    private function replaceVarsInOutput_gFunc($data_pVar)
    {
        $data_pVar = preg_replace_callback('/@{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceVarsInOutputCallback_gFunc'), $data_pVar);
        $data_pVar = preg_replace_callback('/@L{([^{}]+)}/', array('tpl__xmlBase_gClass', 'replaceLangsCallback_gFunc'), $data_pVar);
        return($data_pVar);
    }

    static public function replaceLangsCallback_gFunc($matches_pVar)
    {
       	$string_pVar = string_gClass::getString_gFunc($matches_pVar[1], main_gClass::getLanguage_gFunc());
    	docStringsStack_gClass::push_gFunc(array('lng_pVar'=>main_gClass::getLanguage_gFunc(), 'str_pVar'=>$matches_pVar[1]));
    	return($string_pVar);
    }


    static public function replaceVarsInPhpCodeCallback_gFunc($matches_pVar)
    {
    	return(varStack_gClass::getVarExpression_gFunc($matches_pVar[1]));
    }
    static public function replaceVarsInPhpStringSingleQuotedCallback_gFunc($matches_pVar)
    {
        return('\'_TE:VAR- . ' . varStack_gClass::getVarExpression_gFunc($matches_pVar[1]) . ' . -TE:VAR_\'');
    }
    static public function replaceVarsInPhpStringDoubleQuotedCallback_gFunc($matches_pVar)
    {
        $varsString_pVar = str_replace(':', '\'][\'' , $matches_pVar[1]);
        return('"_TE:VAR- . ' . varStack_gClass::getVarExpression_gFunc($matches_pVar[1]) . ' . -TE:VAR_"');
    }

    static public function replaceVarsInOutputCallback_gFunc($matches_pVar)
    {
        $varsString_pVar = str_replace(':', '\'][\'' , $matches_pVar[1]);
        return(PHP_OPEN . 'echo  ' . varStack_gClass::getVarExpression_gFunc($matches_pVar[1]) . '; ' . PHP_CLOSE);
    }
}


class xmlTemplateElement_gClass extends xmlElement_gClass
{
    private $vars_pVar;

    public $defaultElementName_pVar = false;
    public $defaultElementAttributes_pVar = array();
    public $elementResultAttributes_pVar = array();

    protected $elementAttributes_pVar = array();
    protected $elementAttributesSet_pVar = array();
    protected $elementAttributesUnset_pVar = array();

    protected $mergedObjects_pVar = array();
    protected $mergedObjectLastId_pVar = -1;

    private $varMap_pVar = array(
            'elementPrefix' => 'elementPrefix_pVar',
            'elementName' => 'elementName_pVar',
            'elementAttributes' => 'elementAttributes_pVar',
            'elementAttributesSet' => 'elementAttributesSet_pVar',
            'elementAttributesUnset' => 'elementAttributesUnset_pVar',
            'elementDataPrefix' => 'elementDataPrefix_pVar',
            'elementData' => 'elementData_pVar',
            'elementDataSuffix' => 'elementDataSuffix_pVar',
            'elementSuffix' => 'elementSuffix_pVar',
            'elementDisplayType' => 'elementDisplayType_pVar',
            'elementRename' => 'elementRename_pVar'
         );

    function __get($varName_pVar)
    {
        if(isset($this->varMap_pVar[$varName_pVar])) {
            $varName_pVar = $this->varMap_pVar[$varName_pVar];
        }
        if(!isset($this->vars_pVar[$varName_pVar])) {
            return(false);
        }
        else {
            return($this->vars_pVar[$varName_pVar]);
        }
    }

    function __set($varName_pVar, $varValue_pVar)
    {
        if(isset($this->varMap_pVar[$varName_pVar])) {
            $varName_pVar = $this->varMap_pVar[$varName_pVar];
        }
        $this->vars_pVar[$varName_pVar] = $varValue_pVar;
    }

    function __isset($varName_pVar)
    {
        if(isset($this->varMap_pVar[$varName_pVar])) {
            $varName_pVar = $this->varMap_pVar[$varName_pVar];
        }
        if(isset($this->vars_pVar[$varName_pVar])) {
            return(true);
        }
        else {
            return(false);
        }

    }

    function initAttributes_gFunc($attributes_pVar = array())
    {
        // indexy na strtolower
        $this->elementAttributes_pVar = array();
        foreach($attributes_pVar as $attrName_pVar=>$attrValue_pVar) {
            $attrName_pVar = strtolower($attrName_pVar);
            $this->elementAttributes_pVar[$attrName_pVar] = $attrValue_pVar;
        }
        $this->defaultElementAttributes_pVar = $this->elementAttributes_pVar;
    }

    function attributeExists_gFunc($attrName_pVar)
    {
        $attrName_pVar = strtolower($attrName_pVar);
        if(isset($this->elementAttributes_pVar[$attrName_pVar])) {
            return(true);
        }
        else {
            return(false);
        }
    }

    function getAttributeValue_gFunc($attrName_pVar)
    {
        $attrName_pVar = strtolower($attrName_pVar);
        if(isset($this->elementAttributes_pVar[$attrName_pVar])) {
            return($this->elementAttributes_pVar[$attrName_pVar]);
        }
        else {
            return(false);
        }
    }

    function attributeSet_gFunc($attrName_pVar, $attrValue_pVar)
    {
        $attrName_pVar = strtolower($attrName_pVar);
        $this->elementAttributesSet_pVar[$attrName_pVar] = $attrValue_pVar;
    }

    function attributeUnset_gFunc($attrName_pVar)
    {
        $attrName_pVar = strtolower($attrName_pVar);
        $this->elementAttributesUnset_pVar[$attrName_pVar] = true;
    }

    protected function addObjectForMerge_gFunc(&$object_pVar)
    {
        $this->mergedObjectLastId_pVar++;
        $this->mergedObjects_pVar[$this->mergedObjectLastId_pVar] = &$object_pVar;
    }


    public function getResult_gFunc(&$rules_pVar, $path_pVar, $syntax_pVar)
    {
        return($this->getXMLCode_gFunc());
    }

    protected function getRulesForMe_gFunc(&$rules_pVar, $path_pVar = '/', $syntax_pVar = templateRuleParser_gClass::XML_SYNTAX_HTML)
    {
        $retRule_pVar = array();

        foreach ($rules_pVar as $rule_pVar) {
            // type
            if($rule_pVar['type_pVar'] !== 'all') {

                switch ($rule_pVar['type_pVar']) {
                    case 'element':
                        if($this->elementType_pVar != self::XML_ELEMENT_TYPE_TAG_pVar) {
                            continue 2;
                        }
                        if(isset($rule_pVar['tagName_pVar']) && $this->getTagName_gFunc() !== strtoupper($rule_pVar['tagName_pVar'])) {
                            continue 2;
                        }
                        if(isset($rule_pVar['attrName_pVar']) && !$this->isAttribute_gFunc($rule_pVar['attrName_pVar'])) {
                            continue 2;
                        }
                        break;
                    case 'comment':
                        if($this->elementType_pVar != self::XML_ELEMENT_TYPE_COMMENT_pVar) {
                            continue 2;
                        }
                        break;
                    case 'data':
                        if($this->elementType_pVar != self::XML_ELEMENT_TYPE_DATA_pVar) {
                            continue 2;
                        }
                        break;
                    case 'cdata':
                        if($this->elementType_pVar != self::XML_ELEMENT_TYPE_CDATA_pVar) {
                            continue 2;
                        }
                        break;
                    default:
                        continue 2; // rule nie je pre tento typ
                }
            }


            // syntax
            if($rule_pVar['syntax_pVar'] != templateRuleParser_gClass::XML_SYNTAX_ALL) {
                if($rule_pVar['syntax_pVar'] != $syntax_pVar) continue;
            }

            // path
            $cPath_pVar = $rule_pVar['path_pVar'];
    		$cPath_pVar = str_replace('/', '\\/', $cPath_pVar); // aby nebola chyba - Unknown modifier '('
    		$result_pVar = preg_match('/^' . $cPath_pVar . '$/i', $path_pVar);
    		if(!$result_pVar) {
    		    continue;
    		}

    		$retRule_pVar[] = $rule_pVar;
        }
        return($retRule_pVar);
    }
}

/**
 * teraz ide seria tried s prefixom tpl__, ktora je podobna triedam v xml module,
 * ale ma potomkov xmlElement_gClass,xmlTemplateElement_gClass
 *
 */

class tpl__xmlData_gClass extends xmlTemplateElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_DATA_pVar;
    }

    public function getXMLCode_gFunc()
    {
    	$data_pVar = $this->getData_gFunc();
    	$trans_pVar = array();
    	$trans_pVar['<?'] = PHP_OPEN . ' echo \'<\'.\'?\';' .PHP_CLOSE;
    	$trans_pVar['?>'] = PHP_OPEN . ' echo \'?\'.\'>\';' .PHP_CLOSE;
    	$data_pVar = strtr($data_pVar, $trans_pVar);
        return($data_pVar);
    }

    public function getDocData_gFunc()
    {
        return($this->getData_gFunc());
    }
}

class tpl__xmlCData_gClass extends tpl__xmlData_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_XDATA_pVar;
    }

    public function getXMLCode_gFunc()
    {
        return('<![CDATA[' . $this->getData_gFunc() . ']]>');
    }

}

class tpl__xmlComment_gClass extends xmlTemplateElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_COMMENT_pVar;
    }

    public function setComment_gFunc($comment_pVar)
    {
        $this->data_pVar = $comment_pVar;
    }

    public function getComment_gFunc()
    {
        return($this->data_pVar);
    }

    public function getXMLCode_gFunc()
    {
    	$data_pVar = $this->getData_gFunc();
    	if(isset($data_pVar[0]) && $data_pVar[0] === 'x') {
    		return('');
    	}
        return('<!--' . $data_pVar . '-->');
    }

    public function getDocData_gFunc()
    {
        return('');
    }
}

class tpl__xmlTag_gClass extends xmlTemplateElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_TAG_pVar;
    }

    public function setTagName_gFunc($tagName_pVar)
    {
        $this->data_pVar = strtoupper($tagName_pVar);
    }

    public function getTagName_gFunc()
    {
        return($this->data_pVar);
    }

    public function getXMLCode_gFunc()
    {
        $data_pVar = '';
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getXMLCode_gFunc();
            }
        }
        $out_pVar = '<' .$this->getTagName_gFunc();

        // attributes
        $attr_pVar = $this->getAttributes_gFunc();
        foreach ($attr_pVar as $k_pVar=>$v_pVar)
        {
            $out_pVar .= ' ' . $k_pVar .'="' . $v_pVar . '"';
        }

        $out_pVar .= '>';
        $out_pVar .= $data_pVar;
        $out_pVar .= '</' . $this->getTagName_gFunc() . '>';

        return($out_pVar);
    }

    private function initCustomClass_gFunc(&$customClass_pVar)
    {
        if($customClass_pVar->elementName === false) {
            $customClass_pVar->elementName = $this->getTagName_gFunc();
            $customClass_pVar->defaultElementName_pVar = $customClass_pVar->elementName;
        }
        $customClass_pVar->initAttributes_gFunc($this->getAttributes_gFunc());
    }

    public function getResult_gFunc(&$rules_pVar, $path_pVar, $syntax_pVar)
    {
        $rule_pVar = $this->getRulesForMe_gFunc($rules_pVar, $path_pVar, $syntax_pVar);

        log_gClass::write_gFunc('TEMPLATE_RULE',  $this->getTagName_gFunc(). ' ' . string_gClass::arrayToString_gFunc($this->getAttributes_gFunc(), true) . ' :: ' . $path_pVar . '(' . $syntax_pVar . ')' . string_gClass::arrayToString_gFunc($rule_pVar, true, array('path_pVar'=>'path','syntax_pVar'=>'syntax','type_pVar'=>'type','display_pVar'=>'display','class_pVar'=>'class','module_pVar'=>'module','tagName_pVar'=>'tag')));

        $templateCustomClass_pVar = new templateRule_elementBaseClass();

        $stackItemId_pVar = templateStack_gClass::getLastItemId_gFunc();
        if(count($rule_pVar)) {
            foreach ($rule_pVar as $r_pVar) {
                $displayByRule_pVar = false;
                $renameByRule_pVar = false;

                switch ($r_pVar['display_pVar']) {
                    case templateRuleParser_gClass::XML_DISPLAY_NONE:
                        $display_pVar = 'none';
                        $displayByRule_pVar = $display_pVar;
                        break;
                    case templateRuleParser_gClass::XML_DISPLAY_NORMAL:
                        $display_pVar = 'normal';
                        $displayByRule_pVar = $display_pVar;
                        break;
                    case templateRuleParser_gClass::XML_DISPLAY_SHORT:
                        $display_pVar = 'short';
                        $displayByRule_pVar = $display_pVar;
                        break;
                    case templateRuleParser_gClass::XML_DISPLAY_OPEN:
                        $display_pVar = 'open';
                        $displayByRule_pVar = $display_pVar;
                        break;
                    case templateRuleParser_gClass::XML_DISPLAY_INSIDE:
                        $display_pVar = 'inside';
                        $displayByRule_pVar = $display_pVar;
                        break;
                    default:
                        $display_pVar ='normal';
                        break;
                }

                if(isset($r_pVar['rename_pVar']) && strlen($r_pVar['rename_pVar'])) {
                    $renameByRule_pVar = $r_pVar['rename_pVar'];
                }


                if(isset($r_pVar['class_pVar'])) {
                    $className_pVar = $r_pVar['class_pVar'];

                    $moduleName_pVar = modules_gClass::findClass_gFunc($className_pVar);
                    if(strlen($moduleName_pVar) && modules_gClass::isModuleRegistred_gFunc($moduleName_pVar)) {
                        if(templateStack_gClass::createAndPush_gFunc($className_pVar, $path_pVar)) {
                            $templateCustomClassTmp_pVar = templateStack_gClass::getLastItemAsRef_gFunc();
                        }
                    }
                    else {
                        error_gClass::error_gFunc(__FILE__, __LINE__, sprintf(string_gClass::get('str__templates_rules_class_sVar'), $className_pVar));
                        continue;
                    }

                    $this->initCustomClass_gFunc($templateCustomClassTmp_pVar);

                    //if($templateCustomClassTmp_pVar->elementName_pVar == 'H1') {
                       // print_r($templateCustomClassTmp_pVar);
                      //  exit;
                    //}
                    $rx_pVar = $r_pVar;
                    if(!isset($rx_pVar['tagName_pVar'])) {
                        $rx_pVar['tagName_pVar'] = false;
                    }
                    if(!isset($rx_pVar['attrName_pVar'])) {
                        $rx_pVar['attrName_pVar'] = false;
                    }
                    $templateCustomClassTmp_pVar->parseElement( array('syntax'=>$rx_pVar['syntax_pVar'],
                                                                      'type'=>$rx_pVar['type_pVar'],
                                                                      'display'=>$rx_pVar['display_pVar'],
                                                                      'tagName'=>strtoupper($rx_pVar['tagName_pVar']),
                                                                      'attrName'=>strtolower($rx_pVar['attrName_pVar'])));

                    if($templateCustomClassTmp_pVar->elementDisplayType === false) {
                        $templateCustomClassTmp_pVar->elementDisplayType = $displayByRule_pVar;
                    }
                    if($templateCustomClassTmp_pVar->elementRename === false) {
                        $templateCustomClassTmp_pVar->elementRename = $renameByRule_pVar;
                    }
                    $templateCustomClass_pVar->addObjectForMerge_gFunc($templateCustomClassTmp_pVar);
                    unset($templateCustomClassTmp_pVar);
                }
                else {
                    if($displayByRule_pVar !== false || $renameByRule_pVar !== false) {
                        if(templateStack_gClass::createAndPush_gFunc('templateRule_elementBaseClass', $path_pVar)) {
                                $templateCustomClassTmp_pVar = templateStack_gClass::getLastItemAsRef_gFunc();
                        }
                        else {
                            error_gClass::error_gFunc(__FILE__, __LINE__, sprintf(string_gClass::get('str__templates_rules_class_sVar'), $className_pVar));
                            continue;
                        }

                        $this->initCustomClass_gFunc($templateCustomClassTmp_pVar);

                        if($displayByRule_pVar !== false) {
                            $templateCustomClassTmp_pVar->elementDisplayType = $displayByRule_pVar;
                        }
                        if($renameByRule_pVar !== false) {
                            $templateCustomClassTmp_pVar->elementRename = $renameByRule_pVar;
                        }

                        $templateCustomClass_pVar->addObjectForMerge_gFunc($templateCustomClassTmp_pVar);
                        unset($templateCustomClassTmp_pVar);
                    }
                }
            }
        }

        $this->initCustomClass_gFunc($templateCustomClass_pVar);
        $templateCustomClass_pVar->mergeData_gFunc();

        if(!empty($templateCustomClass_pVar->elementRename)) {
            $templateCustomClass_pVar->elementName = strtoupper($templateCustomClass_pVar->elementRename);
        }

        $data_pVar = '';


/*
if($templateCustomClass_pVar->elementName_pVar == 'H1') {
        ob_start();
        echo '<pre>';
        print_r($this->getAttributes_gFunc());
        print_r($templateCustomClass_pVar);
        echo '</pre>';
        $data_pVar .= ob_get_contents();
        ob_end_clean();
}*/

        if($templateCustomClass_pVar->elementDisplayType === false || $templateCustomClass_pVar->elementDisplayType !== 'none') {
            if($templateCustomClass_pVar->elementData !== false) {
                $data_pVar = $templateCustomClass_pVar->elementData;
            }
            else {
                $newPath_pVar = $path_pVar .= $this->getTagName_gFunc() . '/';
                for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
                {
                    if(isset($this->childs_pVar[$i])) {
                        if($templateCustomClass_pVar->ignoreInside_data_pVar && $this->childs_pVar[$i]->getElementTypeId_gFunc() === xmlElement_gClass::XML_ELEMENT_TYPE_DATA_pVar) {

                        }
                        else {
                            $data_pVar .= $this->childs_pVar[$i]->getResult_gFunc($rules_pVar, $newPath_pVar, $syntax_pVar);
                        }
                    }
                }
                if(!empty($templateCustomClass_pVar->elementCallbackBeforeClose_pVar)
                	&& is_array($templateCustomClass_pVar->elementCallbackBeforeClose_pVar)
                	&& count($templateCustomClass_pVar->elementCallbackBeforeClose_pVar)) {
                       call_user_func($templateCustomClass_pVar->elementCallbackBeforeClose_pVar, $templateCustomClass_pVar);
                }
            }
        }

        if($templateCustomClass_pVar->elementDataPrefix !== false) {
            $data_pVar = $templateCustomClass_pVar->elementDataPrefix . $data_pVar;
        }
        if($templateCustomClass_pVar->elementDataSuffix !== false) {
            $data_pVar = $data_pVar . $templateCustomClass_pVar->elementDataSuffix;
        }

        if($templateCustomClass_pVar->elementDisplayType === false) {
            if(!strlen($data_pVar) && (
                $syntax_pVar == templateRuleParser_gClass::XML_SYNTAX_XHTML
                || $syntax_pVar == templateRuleParser_gClass::XML_SYNTAX_XML))
            {
                $templateCustomClass_pVar->elementDisplayType = 'short';
            }
            else {
                $templateCustomClass_pVar->elementDisplayType = 'normal';
            }
        }

        if($templateCustomClass_pVar->elementDisplayType === 'short' && strlen($data_pVar)) {
            $templateCustomClass_pVar->elementDisplayType = 'normal';
        }

        $out_pVar = '';

        if($templateCustomClass_pVar->elementPrefix) {
            $out_pVar .= $templateCustomClass_pVar->elementPrefix;
        }

        if($templateCustomClass_pVar->elementDisplayType !== 'none') {
            if($templateCustomClass_pVar->elementDisplayType !== 'inside') {
                $out_pVar .= '<' . strtolower($templateCustomClass_pVar->elementName);

                // attributes
                	// najskor zistim, ci obsahuje dynamicke atributy
                	$dAttr_pVar = false;
                	$attr_pVar = $templateCustomClass_pVar->elementResultAttributes_pVar;
                	foreach ($attr_pVar as $k_pVar=>$v_pVar)
                	{
                		if(substr($k_pVar, 0, 9) == 'te:attrs_') {
                			if(strlen($v_pVar)) {
	                			$dAttr_pVar = true;
	                			break;
                			}
                			else {
                				unset($attr_pVar[$k_pVar]);
                			}
                		}
                	}
                	if(!$dAttr_pVar) {
		                foreach ($attr_pVar as $k_pVar=>$v_pVar)
		                {
		                    $out_pVar .= ' ' . strtolower($k_pVar) .'="' . $v_pVar . '"';
		                }
                	}
                	else {
                		// dynamicke atributy
                		$out_pVar .= PHP_OPEN;
                		$out_pVar .= '$attrs_pVar=array();';
                		foreach ($attr_pVar as $k_pVar=>$v_pVar)
                		{
                			if(substr($k_pVar, 0, 9) == 'te:attrs_') {
                				$out_pVar .= 'if(is_array(' . $v_pVar . ')) {';
                				$out_pVar .= ' foreach(' . $v_pVar . ' as $k_pVar=>$v_pVar) {';
                				$out_pVar .= '  $attrs_pVar[$k_pVar] = $v_pVar;';
                				$out_pVar .= ' }';
                				$out_pVar .= '}';

                				$out_pVar .= 'foreach($attrs_pVar as $k_pVar=>$v_pVar) {';
                				$out_pVar .= ' echo \' \' . $k_pVar . \'="\' . $v_pVar . \'"\';';
                				$out_pVar .= '}';
                			}
                			else {
                				$out_pVar .= 'ob_start();' .PHP_CLOSE . $v_pVar . PHP_OPEN . '$aattr_pVar=ob_get_contents();ob_end_clean();';
                				$out_pVar .= '$attrs_pVar[\'' . $k_pVar . '\'] = $aattr_pVar;';
                			}
                		}
                		$out_pVar .= PHP_CLOSE;
                	}

                if($templateCustomClass_pVar->elementDisplayType === 'short') {
                    $out_pVar .= ' />';
                }
                else {
                    $out_pVar .= '>';
                    $out_pVar .= $data_pVar;
                    if($templateCustomClass_pVar->elementDisplayType !== 'open') {
                        $out_pVar .= '</' . strtolower($templateCustomClass_pVar->elementName) . '>';
                    }
                }
            }
            else {
                $out_pVar .= $data_pVar;
            }
        }

        if($templateCustomClass_pVar->elementSuffix !== false) {
            $out_pVar .= $templateCustomClass_pVar->elementSuffix;
        }

        templateStack_gClass::deleteItemsToId_gFunc($stackItemId_pVar);
        unset($templateCustomClass_pVar);

        return($out_pVar);
    }

    public function getDocData_gFunc()
    {
        $data_pVar = '';
        if($this->getTagName_gFunc() == 'BR')
        {
            return LF;
        }
        for($i=0; $i<=$this->getLastChildID_gFunc(); $i++)
        {
            if(isset($this->childs_pVar[$i])) {
                $data_pVar .= $this->childs_pVar[$i]->getDocData_gFunc();
            }
        }
        return($data_pVar);
    }
}

class tpl__xmlPi_gClass extends xmlTemplateElement_gClass
{
    function __construct()
    {
        parent::__construct();
        $this->elementType_pVar = self::XML_ELEMENT_TYPE_PI_pVar;
    }

    public function getXMLCode_gFunc()
    {
        return('<' . '?php ' . $this->getData_gFunc() . '?' . '>');
    }

    public function getDocData_gFunc()
    {
        return('');
    }
}


class templateRule_elementBaseClass extends xmlTemplateElement_gClass // bez pripony _gClass !!
{

    function mergeData_gFunc()
    {
        // najskor vynulujem objekt
        $this->elementPrefix_pVar = false;
        $this->elementName_pVar = $this->defaultElementName_pVar;
        $this->initAttributes_gFunc($this->defaultElementAttributes_pVar);
        $this->elementAttributesSet_pVar = array();
        $this->elementAttributesUnset_pVar = array();
        $this->elementDataPrefix_pVar = false;
        $this->elementData_pVar = false;
        $this->elementDataSuffix_pVar = false;
        $this->elementSuffix_pVar = false;
        $this->elementDisplayType_pVar = false;
        $this->elementRename_pVar = false;
        $this->ignoreInside_data_pVar = false;
        $this->elementCallbackBeforeClose_pVar = false;

        // teraz spravim merge
        for($i=0; $i<=$this->mergedObjectLastId_pVar; $i++) {

            $obj_pVar = &$this->mergedObjects_pVar[$i];

            if($obj_pVar->elementData !== false) {
                $this->elementData = $obj_pVar->elementData;
            }
            if($obj_pVar->elementDataPrefix !== false) {
                $this->elementDataPrefix = $obj_pVar->elementDataPrefix . $this->elementDataPrefix;
            }
            if($obj_pVar->elementData !== false) {
                $this->elementData = $obj_pVar->elementData;
            }
            if($obj_pVar->elementDataSuffix !== false) {
                $this->elementDataSuffix = $this->elementDataSuffix . $obj_pVar->elementDataSuffix;
            }
            if($obj_pVar->elementPrefix !== false) {
                $this->elementPrefix = $obj_pVar->elementPrefix . $this->elementPrefix;
            }
            if($obj_pVar->elementSuffix !== false) {
                $this->elementSuffix = $this->elementSuffix . $obj_pVar->elementSuffix;
            }
            if($obj_pVar->elementRename !== false) {
                $this->elementRename = $obj_pVar->elementRename;
            }
            if($obj_pVar->ignoreInside_data_pVar !== false) {
                $this->ignoreInside_data_pVar = $obj_pVar->ignoreInside_data_pVar;
            }
            if($obj_pVar->elementCallbackBeforeClose_pVar !== false) {
                $this->elementCallbackBeforeClose_pVar = $obj_pVar->elementCallbackBeforeClose_pVar;
            }

            if(count($obj_pVar->elementAttributesUnset_pVar)) {
                foreach($obj_pVar->elementAttributesUnset_pVar as $attrName_pVar=>$attrValue_pVar) {
                    if(isset($this->elementAttributesSet_pVar[$attrName_pVar])) {
                        unset($this->elementAttributesSet_pVar[$attrName_pVar]);
                    }
                    $this->elementAttributesUnset_pVar[$attrName_pVar] = $attrValue_pVar;
                }
            }
            if(count($obj_pVar->elementAttributesSet_pVar)) {
                foreach($obj_pVar->elementAttributesSet_pVar as $attrName_pVar=>$attrValue_pVar) {
                    if(isset($this->elementAttributesUnset_pVar[$attrName_pVar])) {
                        unset($this->elementAttributesUnset_pVar[$attrName_pVar]);
                    }
                    $this->elementAttributesSet_pVar[$attrName_pVar] = $attrValue_pVar;
                }
            }
            if($obj_pVar->elementDisplayType !== false) {
                $this->elementDisplayType = $obj_pVar->elementDisplayType;
            }

            unset($obj_pVar);
        }

        // vyhodnotim atributesSet, attributesUnset do elementResultAttributes_pVar
        $this->elementResultAttributes_pVar = array();
        foreach ($this->elementAttributes_pVar as $attrName_pVar=>$attrValue_pVar) {
            if(isset($this->elementAttributesUnset_pVar[$attrName_pVar])) {
                continue;
            }
            $this->elementResultAttributes_pVar[$attrName_pVar] = $attrValue_pVar;
        }
        foreach ($this->elementAttributesSet_pVar as $attrName_pVar=>$attrValue_pVar) {
            $this->elementResultAttributes_pVar[$attrName_pVar] = $attrValue_pVar;
        }
    }

    function parseElement($rule_pVar = array()) // bez pripony _gFunc, aby ju mohol pretazit
    {
        return;
    }

}


class templatesTE_gClass extends templateRule_elementBaseClass
{

    function parseElement($rule_pVar = array()) // bez pripony _gFunc, aby ju mohol pretazit
    {

        /**
         * Rule:
         *  array('syntax'=>$r_pVar['syntax_pVar'],
         *  'type'=>$r_pVar['type_pVar'],
         *  'display'=>$r_pVar['display_pVar'],
         *  'tagName'=>$r_pVar['tagName_pVar'],
         *  'attrName'=>$r_pVar['attrName_pVar']
         *
         */

        if($rule_pVar['type'] === 'element') {
            if(!empty($rule_pVar['tagName']) && !empty($rule_pVar['attrName'])) {
                switch ($rule_pVar['tagName']) {
                    case 'FORM':
                        if($rule_pVar['attrName'] === 'action') {
                            $this->te_action_gFunc(true);
                        }
                        break;
                    case 'TE:MENU-ITEM':
                        if($rule_pVar['attrName'] === 'te:source') {
                        	$this->te_menuitem_from_source_gFunc(false);
                        }
                        break;
                }
            }
            elseif(!empty($rule_pVar['tagName'])) {
                switch ($rule_pVar['tagName']) {
                    case 'TE:LEGACY-FORM':
                        $this->te_legacy_form_gFunc(false);
                        break;
                    case 'TE:COMPONENT':
                        $this->te_component_gFunc(false);
                        break;
                    case 'TE:LIVEWIRE':
                        $this->te_livewire_gFunc(false);
                        break;
                    case 'TE:IF':
                        $this->te_if_gFunc(false);
                        break;
                    case 'TE:AJAX':
                        $this->te_ajax_gFunc(false);
                        break;
                    case 'TE:AJAX-REQUEST':
                        $this->te_ajax_request_gFunc(false);
                        break;
                    case 'TE:ELSE':
                        $this->te_else_gFunc(false);
                        break;
                    case 'TE:CHOOSE':
                        $this->te_choose_gFunc(false);
                        break;
                    case 'TE:WHEN':
                        $this->te_choose_when_gFunc(false);
                        break;
                    case 'TE:OTHERWISE':
                        $this->te_choose_otherwise_gFunc(false);
                        break;
                    case 'TE:WHILE':
                        $this->te_while_gFunc(false);
                        break;
                    case 'TE:FOR':
                        $this->te_for_gFunc(false);
                        break;
                    case 'TE:SET':
                        $this->te_set_gFunc(false);
                        break;
                    case 'TE:CONTENT':
                        $this->te_content_gFunc(false);
                        break;
                    case 'TE:CONTENT-GET':
                        $this->te_content_get_gFunc(false);
                        break;
                    case 'TE:CONTENT-SET':
                        $this->te_content_set_gFunc(false);
                        break;
                    case 'TE:TITLE':
                        $this->te_title_gFunc(false);
                        break;
                    case 'TE:INCLUDE':
                        $this->te_include_gFunc(false);
                        break;
                    case 'TE:INCLUDE-PARAM':
                        $this->te_include_param_gFunc(false);
                        break;
                    case 'TE:LAYOUT-INSERT':
                        $this->te_layout_insert_gFunc(false);
                        break;
                    case 'TE:LAYOUT-REPLACE':
                        $this->te_layout_replace_gFunc(false);
                        break;
                    case 'TE:SOURCE':
                        $this->te_source_gFunc(false);
                        break;
                    case 'TE:SEARCH':
                        $this->te_search_gFunc(false);
                        break;
                    case 'TE:SOURCE-PARAM':
                        $this->te_source_param_gFunc(false);
                        break;
                    case 'TE:ACCESS':
                        $this->te_access_gFunc(false);
                        break;
                    case 'TE:XACCESS':
                        $this->te_xaccess_gFunc(false);
                        break;
                    case 'TE:MENU-GROUP':
                        $this->te_menugroup_gFunc(false);
                        break;
                    case 'TE:MENU-ITEM':
                        $this->te_menuitem_gFunc(false);
                        break;
                    case 'TE:MENU-PATH':
                        $this->te_menupath_gFunc(false);
                        break;
                    case 'TE:RESULT':
                    	$this->te_result_gFunc(false);
                    	break;
                    case 'TE:HEADER':
                    	$this->te_header_gFunc(false);
                    	break;
                    case 'TE:PRICE':
                    	$this->te_price_gFunc(false);
                    	break;
                    case 'TE:REDIRECT':
                    	$this->te_redirect_gFunc(false);
                    	break;
                    case 'TE:EDIT':
                        $this->te_edit_gFunc(false);
                        break;
                    case 'TE:STRING':
                        $this->te_string_gFunc(false);
                        break;
                    case 'TE:BOOKMARK':
                        $this->te_bookmark_gFunc(false);
                        break;
                }
            }
            elseif(!empty($rule_pVar['attrName'])) {
                switch ($rule_pVar['attrName']) {
                    case 'te:xmlns':
                        $this->te_xmlns_gFunc(true);
                        break;
                    case 'te:if':
                        $this->te_if_gFunc(true);
                        break;
                    case 'te:ajax':
                        $this->te_ajax_gFunc(true);
                        break;
                    case 'te:ajax-request':
                        $this->te_ajax_request_gFunc(true);
                        break;
                    case 'te:else':
                        $this->te_else_gFunc(true);
                        break;
                    case 'te:choose':
                        $this->te_choose_gFunc(true);
                        break;
                    case 'te:when':
                        $this->te_choose_when_gFunc(true);
                        break;
                    case 'te:otherwise':
                        $this->te_choose_otherwise_gFunc(true);
                        break;
                    case 'te:while':
                        $this->te_while_gFunc(true);
                        break;
                    case 'te:for':
                        $this->te_for_gFunc(true);
                        break;
                    case 'te:attrs':
                        $this->te_attrs_gFunc(true);
                        break;
                    case 'te:content':
                        $this->te_content_gFunc(true);
                        break;
                    case 'te:content-get':
                        $this->te_content_get_gFunc(true);
                        break;
                    case 'href':
                        $this->te_href_gFunc(true);
                        break;
                    case 'src':
                        $this->te_src_gFunc(true);
                        break;
                    case 'te:access':
                        $this->te_access_gFunc(true);
                        break;
                    case 'te:xaccess':
                        $this->te_xaccess_gFunc(true);
                        break;
                    case 'te:system-document':
                        $this->te_system_document_gFunc(true);
                        break;
                    case 'te:print':
                    	$this->te_print_gFunc(true);
                    	break;
                    case 'te:edit':
                    	$this->te_edit_gFunc(true);
                    	break;
                    case 'te:string':
                    	$this->te_string_gFunc(true);
                    	break;
                }
            }
        }
        return;
    }

    private function te_xmlns_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $xmlns_pVar = $this->getAttributeValue_gFunc('te:xmlns');
            $this->attributeUnset_gFunc('te:xmlns');
            $this->attributeSet_gFunc('xmlns', $xmlns_pVar);
        }
    }


    private function te_component_gFunc($asAttribute_pVar = false)
    {
        if(!$asAttribute_pVar) {
            $this->elementDisplayType_pVar = 'inside';
            $componentClass = $this->getAttributeValue_gFunc('component');
            $attrs = $this->elementAttributes_pVar;
            unset($attrs['component']);

            $this->elementData = PHP_OPEN;
            $this->elementData .= ' echo \\App\\Legacy\\LegacyApp::renderLaravelComponent('. $componentClass.'::class, ' . var_export($attrs, true) . ');';
            $this->elementData .= PHP_CLOSE;

        }
    }

    private function te_livewire_gFunc($asAttribute_pVar = false)
    {
        if(!$asAttribute_pVar) {
            $this->elementDisplayType_pVar = 'inside';
            $componentName = $this->getAttributeValue_gFunc('component');
            $attrs = $this->elementAttributes_pVar;
            unset($attrs['component']);

            $this->elementData = PHP_OPEN;
            $this->elementData .= ' echo \\App\\Legacy\\LegacyApp::renderLivewireComponent(\''. $componentName.'\', ' . var_export($attrs, true) . ');';
            $this->elementData .= PHP_CLOSE;

        }
    }

    private function te_legacy_form_gFunc($asAttribute_pVar = false)
    {
        if(!$asAttribute_pVar) {
            $this->elementDisplayType_pVar = 'inside';
            $formClass = $this->getAttributeValue_gFunc('class');
            $itemId = $this->getAttributeValue_gFunc('item_id');
            $legacySource = $this->getAttributeValue_gFunc('legacy_source');

            //$this->elementPrefix_pVar = PHP_OPEN . 'if(' . $condition_pVar . ') {' . PHP_CLOSE;
            //$this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;

            $this->elementData = PHP_OPEN;
            $this->elementData .= ' echo \\App\\Legacy\\LegacyApp::renderLivewireComponent(\'form\', [\'formClass\' => ' . $formClass . '::class, \'config\' => [\'item_id\' => \'' . $itemId . '\', \'legacy_source\' => \'' . $legacySource . '\']]);';
            $this->elementData .= PHP_CLOSE;
        }
    }

    private function te_if_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $condition_pVar = $this->getAttributeValue_gFunc('te:if');
            if(!strlen($condition_pVar)) {
                $condition_pVar = '0';
            }
            $this->attributeUnset_gFunc('te:if');
        }
        else {
            if($this->attributeExists_gFunc('test')) {
                $condition_pVar = $this->getAttributeValue_gFunc('test');
            }
            else {
                $condition_pVar = '0';
            }
            $this->elementDisplayType_pVar = 'inside';
        }

        $this->elementPrefix_pVar = PHP_OPEN . 'if(' . $condition_pVar . ') {' . PHP_CLOSE;
        $this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;
    }

    private function te_ajax_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $condition_pVar = $this->getAttributeValue_gFunc('te:ajax');
            if(!strlen($condition_pVar)) {
                $condition_pVar = '0';
            }
            $this->attributeUnset_gFunc('te:ajax');
        }
        else {
			$condition_pVar = false;
            $this->elementDisplayType_pVar = 'inside';
        }

        $this->elementPrefix_pVar = PHP_OPEN . 'if(session_gClass::ajaxEnabled_gFunc()';
		if($condition_pVar !== false) {
			$this->elementPrefix_pVar .= ' && (' . $condition_pVar . ')';
		}
		$this->elementPrefix_pVar .= ') {' . PHP_CLOSE;
        $this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;
    }

    private function te_ajax_request_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $condition_pVar = $this->getAttributeValue_gFunc('te:ajax-request');
            if(!strlen($condition_pVar)) {
                $condition_pVar = '0';
            }
            $this->attributeUnset_gFunc('te:ajax-request');
        }
        else {
			$condition_pVar = false;
            $this->elementDisplayType_pVar = 'inside';
        }

        $this->elementPrefix_pVar = PHP_OPEN . 'if(session_gClass::ajaxRequest_gFunc()';
		if($condition_pVar !== false) {
			$this->elementPrefix_pVar .= ' && (' . $condition_pVar . ')';
		}
		$this->elementPrefix_pVar .= ') {' . PHP_CLOSE;
        $this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;
    }

    private function te_print_gFunc($asAttribute_pVar = false)
    {
    	if($asAttribute_pVar) {
            $status_pVar = $this->getAttributeValue_gFunc('te:print');
            if(strlen($status_pVar) && $status_pVar == 'yes') {
                $status_pVar = true;
            }
            else {
            	$status_pVar = false;
            }

            $this->attributeUnset_gFunc('te:print');

        	$this->elementPrefix_pVar = PHP_OPEN . 'if('.($status_pVar?'':'!').'callStack_gClass::isPrinting_gFunc()) {' . PHP_CLOSE;
	        $this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;
    	}
    	else {

    	}
    }

    private function te_system_document_gFunc($asAttribute_pVar = false)
    {
    	if($asAttribute_pVar) {
            $status_pVar = $this->getAttributeValue_gFunc('te:system-document');
            if(strlen($status_pVar) && $status_pVar == 'yes') {
                $status_pVar = true;
            }
            else {
            	$status_pVar = false;
            }

            $this->attributeUnset_gFunc('te:system-document');

            if($status_pVar) {
            	$this->elementPrefix_pVar = PHP_OPEN . 'log_gClass::write_gFunc(\'SYSTEM_DOCUMENT\',\'1\');' . PHP_CLOSE;
            }
    	}
    	else {

    	}
    }

    private function te_else_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
        	$this->elementPrefix_pVar = PHP_OPEN . '} else {' . PHP_CLOSE;
        	$this->attributeUnset_gFunc('te:else');
        }
        else {
        	$this->elementPrefix_pVar = PHP_OPEN . '} else {' . PHP_CLOSE;
        	$this->elementDisplayType_pVar = 'none';
        }
    }

    private function te_choose_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            if(strlen($this->getAttributeValue_gFunc('te:choose'))) {
                // switch
                $this->chooseTestValue_pVar = $this->getAttributeValue_gFunc('te:choose');
                $this->elementDataPrefix_pVar = PHP_OPEN . 'switch("' . $this->chooseTestValue_pVar . '") {';
                $this->elementDataSuffix_pVar = '}' . PHP_CLOSE;
            }
            else {
                // if/else...
                $this->elementDataPrefix_pVar = PHP_OPEN;
                $this->elementDataSuffix_pVar = PHP_CLOSE;
            }
            $this->attributeUnset_gFunc('te:choose');
        }
        else {
            if($this->attributeExists_gFunc('test') && strlen($this->getAttributeValue_gFunc('test'))) {
                // switch
                $this->chooseTestValue_pVar = $this->getAttributeValue_gFunc('test');
                $this->elementDataPrefix_pVar = PHP_OPEN . 'switch("' . $this->chooseTestValue_pVar . '") {';
                $this->elementDataSuffix_pVar = '}' . PHP_CLOSE;
            }
            else {
                // if/else...
                $this->elementDataPrefix_pVar = PHP_OPEN;
                $this->elementDataSuffix_pVar = PHP_CLOSE;
            }
            $this->elementDisplayType_pVar = 'inside';
        }
        $this->ignoreInside_data_pVar = true;
    }

    private function te_choose_when_gFunc($asAttribute_pVar = false)
    {
        $chooseObjectId1_pVar = templateStack_gClass::findParentObjectId_gFunc('templateRule_te',false, 'te:choose');
        $chooseObjectId2_pVar = templateStack_gClass::findParentObjectId_gFunc('templateRule_te', 'te:choose', false);
        $chooseObjectId_pVar = max($chooseObjectId1_pVar, $chooseObjectId2_pVar);
        if($chooseObjectId_pVar < 0) {
            return ;
        }

        if($asAttribute_pVar) {
            $chooseObject_pVar = templateStack_gClass::getItemByIdAsRef_gFunc($chooseObjectId_pVar);

            if(isset($chooseObject_pVar->chooseTestValue_pVar)) {
                // switch/case
                $this->elementPrefix_pVar =  'case \'' . $this->getAttributeValue_gFunc('te:when') . '\':' . PHP_CLOSE;
                $this->elementSuffix_pVar = PHP_OPEN . 'break;';
            }
            else {
                // if/else
                if(!isset($chooseObject_pVar->ifNum_pVar)) {
                    $this->elementPrefix_pVar = 'if(' . $this->getAttributeValue_gFunc('te:when') . ') {' . PHP_CLOSE;
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar = 1;
                }
                else {
                    $this->elementPrefix_pVar = 'elseif (' . $this->getAttributeValue_gFunc('te:when') . ') {' . PHP_CLOSE;
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar++;
                }
            }

            $this->attributeUnset_gFunc('te:when');
            unset($chooseObject_pVar);
        }
        else {
            $chooseObject_pVar = templateStack_gClass::getItemByIdAsRef_gFunc($chooseObjectId_pVar);

            if(isset($chooseObject_pVar->chooseTestValue_pVar)) {
                // switch/case
                $this->elementPrefix_pVar = 'case \'' . $this->getAttributeValue_gFunc('case') . '\':' . PHP_CLOSE;
                $this->elementSuffix_pVar = PHP_OPEN . 'break;';
            }
            else {
                // if/else
                if(!isset($chooseObject_pVar->ifNum_pVar)) {
                    $this->elementPrefix_pVar = 'if(' . $this->getAttributeValue_gFunc('case') . ') {' . PHP_CLOSE;
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar = 1;
                }
                else {
                    $this->elementPrefix_pVar = 'elseif (' . $this->getAttributeValue_gFunc('case') . ') {' . PHP_CLOSE;
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar++;
                }
            }

            $this->elementDisplayType_pVar = 'inside';
            unset($chooseObject_pVar);
        }
    }

    private function te_choose_otherwise_gFunc($asAttribute_pVar = false)
    {
        $chooseObjectId1_pVar = templateStack_gClass::findParentObjectId_gFunc('templateRule_te',false, 'te:choose');
        $chooseObjectId2_pVar = templateStack_gClass::findParentObjectId_gFunc('templateRule_te', 'te:choose', false);
        $chooseObjectId_pVar = max($chooseObjectId1_pVar, $chooseObjectId2_pVar);
        if($chooseObjectId_pVar < 0) {
            return ;
        }

        if($asAttribute_pVar) {
            $chooseObject_pVar = templateStack_gClass::getItemByIdAsRef_gFunc($chooseObjectId_pVar);

            if(isset($chooseObject_pVar->chooseTestValue_pVar)) {
                // switch/case
                $this->elementPrefix_pVar = 'default:' . PHP_CLOSE;
                $this->elementSuffix_pVar = PHP_OPEN . 'break;';
            }
            else {
                if(!isset($chooseObject_pVar->ifNum_pVar)) {
                    $this->elementPrefix_pVar = '{' . PHP_CLOSE; // ak nebolo este if, nemozem dat else.. Ale netreba nic. Ale preistotu dam aspon {}
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar = 1;
                }
                else {
                    $this->elementPrefix_pVar = 'else {' . PHP_CLOSE;
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar++;
                }
            }
            $this->attributeUnset_gFunc('te:otherwise');
            unset($chooseObject_pVar);
        }
        else {
            $chooseObject_pVar = templateStack_gClass::getItemByIdAsRef_gFunc($chooseObjectId_pVar);

            if(isset($chooseObject_pVar->chooseTestValue_pVar)) {
                // switch/case
                $this->elementPrefix_pVar = 'default:' . PHP_CLOSE;
                $this->elementSuffix_pVar = PHP_OPEN . 'break;';
            }
            else {
                if(!isset($chooseObject_pVar->ifNum_pVar)) {
                    $this->elementPrefix_pVar = '{' . PHP_CLOSE; // ak nebolo este if, nemozem dat else.. Ale netreba nic. Ale preistotu dam aspon {}
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar = 1;
                }
                else {
                    $this->elementPrefix_pVar = 'else {' . PHP_CLOSE;
                    $this->elementSuffix_pVar = PHP_OPEN . '}';
                    $chooseObject_pVar->ifNum_pVar++;
                }
            }

            $this->elementDisplayType_pVar = 'inside';
            unset($chooseObject_pVar);
        }
    }

    private function te_while_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $condition_pVar = $this->getAttributeValue_gFunc('te:while');
            if(!strlen($condition_pVar)) {
                $condition_pVar = '0';
            }
            $this->attributeUnset_gFunc('te:while');
        }
        else {
            if($this->attributeExists_gFunc('test')) {
                $condition_pVar = $this->getAttributeValue_gFunc('test');
            }
            else {
                $condition_pVar = '0';
            }
            $this->elementDisplayType_pVar = 'inside';
        }

        $this->elementPrefix_pVar = PHP_OPEN . 'while(' . $condition_pVar . ') {' . PHP_CLOSE;
        $this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;
    }

    private function te_for_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $condition_pVar = $this->getAttributeValue_gFunc('te:for');
            $this->attributeUnset_gFunc('te:for');
        }
        else {
            if($this->attributeExists_gFunc('each')) {
                $condition_pVar = $this->getAttributeValue_gFunc('each');
            }
            else {
                $condition_pVar = '';
            }
            $this->elementDisplayType_pVar = 'inside';
        }

        if(!preg_match('/\s*([^\s]*)\s+as\s+((([^\s]+)\s*=>\s*)?([^\s]+))\s*/', $condition_pVar, $patterns_pVar)) {
            $this->elementDisplayType_pVar = 'none';
            return;
        }
        $array_pVar = $patterns_pVar[1];
        $key_pVar = $patterns_pVar[4];
        $value_pVar = $patterns_pVar[5];

        $this->elementPrefix_pVar = PHP_OPEN . 'foreach(' . $array_pVar . ' as ';
        if(strlen($key_pVar)) {
            $this->elementPrefix_pVar .= $key_pVar . '=>';
        }
        $this->elementPrefix_pVar .= $value_pVar . ') {';
        $this->elementPrefix_pVar .= PHP_CLOSE;
        $this->elementSuffix_pVar = PHP_OPEN . '}' . PHP_CLOSE;
    }

    private function te_content_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $this->elementData = $this->getAttributeValue_gFunc('te:content');
            $this->attributeUnset_gFunc('te:content');
        }
        else {
            $this->elementData = $this->getAttributeValue_gFunc('data');
            $this->elementDisplayType = 'inside';
        }
    }

    private function te_content_get_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $contentName_pVar = $this->getAttributeValue_gFunc('te:content-get');
            $this->attributeUnset_gFunc('te:content-get');
        }
        else {
            $contentName_pVar = $this->getAttributeValue_gFunc('name');
            $varName_pVar = $this->getAttributeValue_gFunc('varname');
            $this->elementDisplayType = 'inside';
        }

        if(!isset($varName_pVar) || empty($varName_pVar)) {
	        $this->elementData = PHP_OPEN . 'echo content_gClass::getContent_gFunc(\'' . $contentName_pVar . '\', true);' . PHP_CLOSE;
        }
        else {
        	$this->elementData = PHP_OPEN . 'vars::$vars[\''.$varName_pVar.'\'] = content_gClass::getContent_gFunc(\'' . $contentName_pVar . '\', true);' . PHP_CLOSE;
        }
    }

    private function te_content_set_gFunc($asAttribute_pVar = false)
    {
        $this->elementPrefix_pVar = '';
        if($asAttribute_pVar) {
        }
        else {
            $contentName_pVar = $this->getAttributeValue_gFunc('name');
        }

        if($this->attributeExists_gFunc('value')) {
            $contentValue_pVar = $this->getAttributeValue_gFunc('value');
            $this->elementPrefix_pVar .= PHP_OPEN . 'content_gClass::setContent_gFunc(\'' . $contentName_pVar . '\', \'' . $contentValue_pVar . '\');' . PHP_CLOSE;
            $this->elementDisplayType = 'none';
        }
        else {
            $contentValue_pVar = $this->getData_gFunc();
            $varName_pVar = uniqid('var_', true);
            $varName_pVar = str_replace('.', '_', $varName_pVar);
            $this->elementPrefix_pVar .= PHP_OPEN . 'ob_start();' . PHP_CLOSE;
            $this->elementDisplayType_pVar = 'inside';
            $this->elementSuffix_pVar = PHP_OPEN . '$' . $varName_pVar .' = ob_get_contents(); ob_end_clean(); content_gClass::setContent_gFunc(\'' . $contentName_pVar . '\', $' . $varName_pVar . '); ' . PHP_CLOSE;
        }
    }

    private function te_title_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
        }
        else {
            $this->elementPrefix_pVar = PHP_OPEN;
            $this->elementSuffix_pVar = '';
            if($this->attributeExists_gFunc('prefix')) {
                $titlePrefix_pVar = $this->getAttributeValue_gFunc('prefix');
                $this->elementPrefix_pVar .= 'content_gClass::setContent_gFunc(\'head-title-prefix\', \'' . $titlePrefix_pVar . '\');';
            }
            if($this->attributeExists_gFunc('suffix')) {
                $titleSuffix_pVar = $this->getAttributeValue_gFunc('suffix');
                $this->elementPrefix_pVar .= 'content_gClass::setContent_gFunc(\'head-title-suffix\', \'' . $titleSuffix_pVar . '\');';
            }
            if($this->attributeExists_gFunc('title')) {
                $title_pVar = $this->getAttributeValue_gFunc('title');
                $this->elementPrefix_pVar .= 'content_gClass::setContent_gFunc(\'title\', \'' . $title_pVar . '\');';
                $this->elementPrefix_pVar .= 'content_gClass::setContent_gFunc(\'head-title\', \'' . $title_pVar . '\');';
                $this->elementDisplayType_pVar = 'none';
            }
            else {
                $varName_pVar = uniqid('var_', true);
                $varName_pVar = str_replace('.', '_', $varName_pVar);
                $this->elementPrefix_pVar .= PHP_OPEN . 'ob_start();' . PHP_CLOSE;
                $this->elementDisplayType_pVar = 'inside';
                $this->elementSuffix_pVar = PHP_OPEN . '$' . $varName_pVar .' = ob_get_contents(); ob_end_clean(); content_gClass::setContent_gFunc(\'title\', $' . $varName_pVar . '); content_gClass::setContent_gFunc(\'head-title\', $' . $varName_pVar . '); ' . PHP_CLOSE;
            }
            $this->elementSuffix_pVar .= PHP_CLOSE;
        }
    }

    private function te_include_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
            $docName_pVar = $this->getAttributeValue_gFunc('name');
            if($docName_pVar[0] !== '/') {
                // vytvorim absolutnu cestu
                $path_pVar = callStack_gClass::getDocPath_gFunc();
                $path_pVar .= $docName_pVar;
                $docName_pVar = $path_pVar;
            }
            $this->elementPrefix_pVar = PHP_OPEN . 'callStack_gClass::includePrepare_gFunc(); $includeVars_pVar = array(\'doc\'=>vars::$vars[\'doc\'], \'REQUEST_URI\'=>vars::$vars[\'REQUEST_URI\']);';
            $attrs_pVar = $this->elementAttributes_pVar;
            foreach ($attrs_pVar as $k_pVar=>$v_pVar) {
            	if($k_pVar == 'name') {
            		continue;
            	}
            	$this->elementPrefix_pVar .= '$includeVars_pVar[\'' . $k_pVar . '\'] = ';
            	if(substr($v_pVar, 0, 3) == '@@{') {
            		$this->elementPrefix_pVar .= '&'. substr($v_pVar, 1).';';
            	}
            	elseif(substr($v_pVar, 0, 2) == '@{'){
            		$this->elementPrefix_pVar .= $v_pVar.';';
            	}
            	else {
            		$this->elementPrefix_pVar .= '\''. addslashes($v_pVar) .'\';';
            	}
            	$this->elementPrefix_pVar .= '  ';
            }
            $this->elementPrefix_pVar .= 'ob_start();' . PHP_CLOSE;
            $this->elementSuffix_pVar = PHP_OPEN . 'ob_end_clean(); callStack_gClass::includeEnd_gFunc(\'' . $docName_pVar . '\', $includeVars_pVar);' . PHP_CLOSE;
            $this->elementDisplayType_pVar = 'inside';
            $this->ignoreInside_data_pVar = true;
        }
    }

    private function te_include_param_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
            $paramName_pVar = strtolower($this->getAttributeValue_gFunc('name'));
            $this->elementPrefix_pVar = PHP_OPEN;
            $this->elementPrefix_pVar .= 'ob_start();' . PHP_CLOSE;

            $this->elementSuffix_pVar .= PHP_OPEN . '$paramValue_pVar = ob_get_contents(); ob_end_clean();';
           	$this->elementSuffix_pVar .= '$includeVars_pVar[\'' . $paramName_pVar . '\'] = $paramValue_pVar;';
            $this->elementSuffix_pVar .= PHP_CLOSE;
            $this->elementDisplayType_pVar = 'inside';
        }
    }

    /**
     * @TODO: layout-insert nepodporuje zapinanie print flagu ako layout-replace...navyse oba pripady by mali kontrolovat, ci ide o korenovy layout, aby bolo spravanie rovnaake ako pri prirodzenom nstaveni layoutu.
     *
     * @param $asAttribute_pVar
     * @return unknown_type
     */

    private function te_layout_insert_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
            $layoutName_pVar = $this->getAttributeValue_gFunc('name');
            if(!empty($layoutName_pVar) && $layoutName_pVar[0] === '/') {
                $layoutName_pVar = substr($layoutName_pVar, 1);
            }
            $layoutName_pVar = '.layouts/' . $layoutName_pVar;
            $this->elementDisplayType_pVar = 'inside';
            $this->elementPrefix_pVar = PHP_OPEN . 'callStack_gClass::pushLayout_gFunc(\''. $layoutName_pVar . '\');' . PHP_CLOSE;
        }
    }

    private function te_layout_replace_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
            $layoutName_pVar = $this->getAttributeValue_gFunc('name');
            if(!empty($layoutName_pVar) && $layoutName_pVar[0] === '/') {
                $layoutName_pVar = substr($layoutName_pVar, 1);
            }
            $layoutName_pVar = '.layouts/' . $layoutName_pVar;
            $this->elementDisplayType_pVar = 'inside';
            $this->elementPrefix_pVar = PHP_OPEN . 'callStack_gClass::popLayout_gFunc();';
            $this->elementPrefix_pVar .= 'if(preg_match(\'/^\.layouts\/[a-z]{2}_pdf$/i\', \''. $layoutName_pVar . '\')) { callStack_gClass::printStart_gFunc();  \\App\\Legacy\\LegacyApp::setPdfExport(); }';
            $this->elementPrefix_pVar .= ' callStack_gClass::pushLayout_gFunc(\''. $layoutName_pVar . '\');' . PHP_CLOSE;
        }
    }

    private function te_href_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $url_pVar = $this->getAttributeValue_gFunc('href');
            $url_pVar = $this->makeUrl_gFunc($url_pVar);
            $this->elementAttributesSet_pVar['href'] = $url_pVar;
        }
        else {

        }
    }

    private function te_src_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $url_pVar = $this->getAttributeValue_gFunc('src');
            $url_pVar = $this->makeUrl_gFunc($url_pVar);
            $this->elementAttributesSet_pVar['src'] = $url_pVar;
        }
        else {

        }
    }

    private function te_action_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
            $url_pVar = $this->getAttributeValue_gFunc('action');
            $method_pVar = $this->getAttributeValue_gFunc('method');

            if(strtolower($method_pVar) === 'get') {
            	$phpCode_pVar = PHP_OPEN . '@{post_url} = main_gClass::makePostUrl_gFunc(\'' . $url_pVar . '\')' .PHP_CLOSE;
            	$this->elementAttributesSet_pVar['action'] = '@{post_url:action}';
                $this->elementPrefix_pVar = $phpCode_pVar;
                $this->elementDataPrefix_pVar = '@{post_url:vars}';
            }
            else {
            	$url_pVar = $this->makeUrl_gFunc($url_pVar);
                $this->elementAttributesSet_pVar['action'] = $url_pVar;
            }
        }
        else {

        }
    }

    private function te_source_gFunc($asAttribute_pVar = false, $envName_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
        	if($envName_pVar === false) {
            	$envName_pVar = strtolower($this->getAttributeValue_gFunc('name'));
        	}
            $resultName_pVar = strtolower($this->getAttributeValue_gFunc('varname'));
            $parameters_pVar = $this->elementAttributes_pVar;
            unset($parameters_pVar['name']);
            unset($parameters_pVar['varname']);
            if(empty($resultName_pVar)) {
            	$resultName_pVar = 'source';
            }

            $this->elementPrefix_pVar = PHP_OPEN;
            $this->elementSuffix_pVar = PHP_OPEN;

            switch ($envName_pVar) {
                case 'session':
                	if(empty($resultName_pVar)) {
                		$resultName_pVar = 'session';
                	}
                    $this->elementPrefix_pVar .= 'varStack_gClass::$vars[\'' . $resultName_pVar . '\'] = main_gClass::getSessionVars_gFunc();';
                    break;
                default:
                	// zavolam pouzivatelsku triedu
                	$moduleName_pVar = modules_gClass::findClass_gFunc($envName_pVar);

	                if(strlen($moduleName_pVar)) {
	                    $this->elementPrefix_pVar .= 'if(modules_gClass::isModuleRegistred_gFunc(\''.$moduleName_pVar.'\')) {';
	                    if(isset($parameters_pVar['action'])) {
	                    	$action = '\'' . $parameters_pVar['action'] . '\'';
	                    	unset($parameters_pVar['action']);
	                    }
	                    else {
	                    	$action = '';
	                    }
	                    $this->elementPrefix_pVar .= '$source_pVar = new ' . $envName_pVar . '('.$action.');';
	                    if(count($parameters_pVar)) {
	                    	$this->elementPrefix_pVar .= '$source_pVar->setParams_gFunc(' . string_gClass::arrayToPhpCode_gFunc($parameters_pVar) . ');';
	                    }
	                    $this->elementSuffix_pVar .= 'varStack_gClass::$vars[\'' . $resultName_pVar . '\'] = $source_pVar->handleAction_gFunc();';
	                    $this->elementSuffix_pVar .= 'if(isset(varStack_gClass::$vars[\'' . $resultName_pVar . '\'][\'_pager\'])) { varStack_gClass::$vars[\'pager\'] = varStack_gClass::$vars[\'' . $resultName_pVar . '\'][\'_pager\']; unset(varStack_gClass::$vars[\'' . $resultName_pVar . '\'][\'_pager\']); }';
	                    $this->elementSuffix_pVar .= '}';
	                    $this->elementSuffix_pVar .= 'else { varStack_gClass::$vars[\'' . $resultName_pVar . '\'] = array(); }';
	                }
                	break;
            }

            $this->elementPrefix_pVar .= PHP_CLOSE;
            $this->elementSuffix_pVar .= PHP_CLOSE;
            $this->elementDisplayType_pVar = 'inside';
            $this->ignoreInside_data_pVar = true;
        }
    }

    private function te_source_param_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
            $paramName_pVar = strtolower($this->getAttributeValue_gFunc('name'));
            $this->elementPrefix_pVar = PHP_OPEN;
            $this->elementPrefix_pVar .= 'ob_start();' . PHP_CLOSE;

            $this->elementSuffix_pVar .= PHP_OPEN . '$paramValue_pVar = ob_get_contents(); ob_end_clean();';
           	$this->elementSuffix_pVar .= '$source_pVar->setParam_gFunc(\'' . $paramName_pVar . '\', $paramValue_pVar);';
            $this->elementSuffix_pVar .= PHP_CLOSE;
            $this->elementDisplayType_pVar = 'inside';
        }
    }

    private function te_search_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
            $envName_pVar = 'search';
            $this->te_source_gFunc($asAttribute_pVar, $envName_pVar);
        }
    }

    static public function te_access_auto_callback_gFunc($element)
    {
    	$str_pVar = autoAccessStack_gClass::getAccess_gFunc();
    	autoAccessStack_gClass::destroyStack_gFunc();

    	if(!empty($str_pVar)) {
	        $element->elementPrefix_pVar = PHP_OPEN . 'if(' . $str_pVar . ') {' . PHP_CLOSE . $element->elementPrefix_pVar;
	        $element->elementSuffix_pVar = $element->elementSuffix_pVar . PHP_OPEN . '}' . PHP_CLOSE;
    	}
    }

    private function te_access_read_doc_rights_gFunc($href_pVar)
    {
        $doc_root_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        $url_pVar = $this->makeUrl_gFunc($href_pVar);
        $doc_pVar = false;
        if(main_gClass::getConfigVar_gFunc('seo_url', 'main')) {
        	$tmp_pVar = strpos($url_pVar, '?');
        	if($tmp_pVar !== false) {
        		$doc_pVar = substr($url_pVar, 0, $tmp_pVar);
        	}
        	else {
        		$doc_pVar = $url_pVar;
        	}
        }
        else {
        	$tmp_pVar = parse_url($url_pVar);
        	parse_str($tmp_pVar['query'], $tmp_pVar);
        	$doc_pVar = $tmp_pVar['doc'];
        }

        if($doc_pVar !== false) {
        	$doc_pVar = rtrim($doc_pVar, '/');
        	$fileName_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main') . $doc_pVar . '.tpl.xml';
        	if(file_exists($fileName_pVar)) {
        		$f_pVar = fopen($fileName_pVar, 'rt');
        		$tmp_pVar = fread($f_pVar, 1000);
        		fclose($f_pVar);
        		if(preg_match('/.*<truEngine-document\s[^>]*te:access=["\']([^"\']+)["\'].*/i', $tmp_pVar, $matches_pVar)) {
        			return($matches_pVar[1]);
        		}
        	}
        }

        return(false);
    }

    private function te_access_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
        	$action_str_pVar = $this->getAttributeValue_gFunc('te:access');
        	if($action_str_pVar === 'auto') {
        		if($this->defaultElementName_pVar === 'A') {
        			// ak je to linka, tak prava pouzijem z hlavicky dokumentu na ktory linka smeruje.
        			// toto bude tym padom cachovane.. takze ak zmenim prava v dokumente, musim zmazat cache s vyskytom linky na tento dokument.
        			$tmp_pVar = $this->te_access_read_doc_rights_gFunc($this->getAttributeValue_gFunc('href'));
        			if($tmp_pVar !== false) {
						$action_str_pVar = $tmp_pVar;
        			}
        		}
        	}
        	if($action_str_pVar === 'auto') {
        		autoAccessStack_gClass::createStack_gFunc();
        		$this->elementCallbackBeforeClose_pVar = array('templatesTE_gClass', 'te_access_auto_callback_gFunc');
        	}
        	else {
	        	$str_pVar = string_gClass::parseAccessString_gFunc($action_str_pVar);

	        	autoAccessStack_gClass::pushAccess_gFunc($str_pVar);
	        	$this->elementPrefix_pVar = PHP_OPEN . 'if(' . $str_pVar . ') {' . PHP_CLOSE . $this->elementPrefix_pVar;
		        $this->elementSuffix_pVar = $this->elementSuffix_pVar . PHP_OPEN . '}' . PHP_CLOSE;
        	}
	        $this->elementAttributesUnset_pVar['te:access'] = true;
        }
        else {
        	$action_str_pVar = $this->getAttributeValue_gFunc('access');
        	if($action_str_pVar === 'auto') {
        		if($this->defaultElementName_pVar === 'A') {
        			// ak je to linka, tak prava pouzijem z hlavicky dokumentu na ktory linka smeruje.
        			// toto bude tym padom cachovane.. takze ak zmenim prava v dokumente, musim zmazat cache s vyskytom linky na tento dokument.
        			$tmp_pVar = $this->te_access_read_doc_rights_gFunc($this->getAttributeValue_gFunc('href'));
        			if($tmp_pVar !== false) {
						$action_str_pVar = $tmp_pVar;
        			}
        		}
        	}
        	if($action_str_pVar === 'auto') {
        		autoAccessStack_gClass::createStack_gFunc();
        		$this->elementCallbackBeforeClose_pVar = array('templatesTE_gClass', 'te_access_auto_callback_gFunc');
        	}
        	else {
	        	$str_pVar = string_gClass::parseAccessString_gFunc($action_str_pVar);

	        	autoAccessStack_gClass::pushAccess_gFunc($str_pVar);
	        	$this->elementPrefix_pVar = PHP_OPEN . 'if(' . $str_pVar . ') {' . PHP_CLOSE . $this->elementPrefix_pVar;
		        $this->elementSuffix_pVar = $this->elementSuffix_pVar . PHP_OPEN . '}' . PHP_CLOSE;
        	}

        	$this->elementDisplayType_pVar = 'inside';
        }
    }

    private function te_xaccess_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
        	$action_str_pVar = $this->getAttributeValue_gFunc('te:xaccess');
        	$str_pVar = string_gClass::parseAccessString_gFunc($action_str_pVar);

            autoAccessStack_gClass::pushAccess_gFunc('!(' . $str_pVar . ')');
        	$this->elementPrefix_pVar = PHP_OPEN . 'if(!(' . $str_pVar . ')) {' . PHP_CLOSE . $this->elementPrefix_pVar;
	        $this->elementSuffix_pVar = $this->elementSuffix_pVar . PHP_OPEN . '}' . PHP_CLOSE;
	        $this->elementAttributesUnset_pVar['te:access'] = true;
        }
        else {
        	$action_str_pVar = $this->getAttributeValue_gFunc('xaccess');
        	$str_pVar = string_gClass::parseAccessString_gFunc($action_str_pVar);

            autoAccessStack_gClass::pushAccess_gFunc('!(' . $str_pVar . ')');
        	$this->elementPrefix_pVar = PHP_OPEN . 'if(!(' . $str_pVar . ')) {' . PHP_CLOSE . $this->elementPrefix_pVar;
	        $this->elementSuffix_pVar = $this->elementSuffix_pVar . PHP_OPEN . '}' . PHP_CLOSE;

        	$this->elementDisplayType_pVar = 'inside';
        }
    }

    private function te_menugroup_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
        	$varName_pVar = $this->getAttributeValue_gFunc('name');
        	$defaultId_pVar = $this->getAttributeValue_gFunc('default');

            $this->elementDisplayType_pVar = 'inside';
            $this->ignoreInside_data_pVar = true;

            $this->elementPrefix_pVar = PHP_OPEN;
            $this->elementPrefix_pVar .= 'varStack_gClass::$globals[\'menu_' . $varName_pVar . '\'] = array(); varStack_gClass::$vars[\'' . $varName_pVar . '\'] = &varStack_gClass::$globals[\'menu_' . $varName_pVar . '\']; varStack_gClass::$vars[\'' . $varName_pVar . '\'] = array(\'paths_pVar\'=>array(';
            $this->elementPrefix_pVar .= '\'group_pVar\'=>\'' . $varName_pVar . '\'';
            if($defaultId_pVar !== false) {
            	$this->elementPrefix_pVar .= ', \'default_pVar\'=>\'' . $defaultId_pVar . '\'';
            }
            $this->elementPrefix_pVar .= ')); $menuTmpStackId_pVar=0; $menuTmpStack_pVar[$menuTmpStackId_pVar] = &varStack_gClass::$vars[\'' . $varName_pVar . '\'];';
            $this->elementPrefix_pVar .= PHP_CLOSE;



            $this->elementSuffix_pVar = PHP_OPEN. 'document_gClass::menuSelectActiveLink_gFunc($menuTmpStack_pVar[$menuTmpStackId_pVar]); unset($menuTmpStack_pVar[$menuTmpStackId_pVar]);' . PHP_CLOSE;
        }
    }
    private function te_menuitem_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
        	$sourceAttr_pVar = $this->getAttributeValue_gFunc('te:source');
        	if(strlen($sourceAttr_pVar)) {
        		return;
        	}

        	$linkAttr_pVar = $this->getAttributeValue_gFunc('link');
        	$labelAttr_pVar = $this->getAttributeValue_gFunc('label');
        	$titleAttr_pVar = $this->getAttributeValue_gFunc('title');
        	$pathAttr_pVar = $this->getAttributeValue_gFunc('path');
        	$paramsAttr_pVar = $this->getAttributeValue_gFunc('params');
        	$idAttr_pVar = strtolower($this->getAttributeValue_gFunc('id'));

			if(!strlen($idAttr_pVar)) { // ak ni eje id, tak ho vygenerujem
				$idAttr_pVar = uniqid('m_');
			}

        	$this->elementPrefix_pVar = PHP_OPEN . '$menuTmpStackId_pVar++; $menuTmpStack_pVar[$menuTmpStackId_pVar] = array();';
        	if(strlen($linkAttr_pVar)) {
        		if($pathAttr_pVar !== false) {
        			if(strlen($pathAttr_pVar)) {
        				$this->elementPrefix_pVar .= '$menuTmpStack_pVar[0][\'paths_pVar\'][]=array(\'id_pVar\'=>\''.$idAttr_pVar.'\', \'path_pVar\'=>\''.$pathAttr_pVar.'\'';
        				if(strlen($paramsAttr_pVar)) {
        					$this->elementPrefix_pVar .= ',\'params_pVar\'=>\'' . $paramsAttr_pVar . '\'';
        				}
        				$this->elementPrefix_pVar .= ');';
        			}
        			// ak je definovane path, ale je prazdny retazec, nevlozim ani path, ani link.. (Link je ignorovany, a aplikujem pravidla path definovane elementom)
        		}
        		else {
        			$this->elementPrefix_pVar .= '$menuTmpStack_pVar[0][\'paths_pVar\'][]=array(\'id_pVar\'=>\''.$idAttr_pVar.'\', \'link_pVar\'=>\''.$linkAttr_pVar.'\');';
        		}
        		$this->elementPrefix_pVar .= '$menuTmpStack_pVar[$menuTmpStackId_pVar][\'link\']=\''.$linkAttr_pVar.'\';';
        	}
        	if(strlen($labelAttr_pVar)) {
        		$this->elementPrefix_pVar .= '$menuTmpStack_pVar[$menuTmpStackId_pVar][\'label\']=\''.$labelAttr_pVar.'\';';
        	}
        	if(strlen($titleAttr_pVar)) {
        		$this->elementPrefix_pVar .= '$menuTmpStack_pVar[$menuTmpStackId_pVar][\'title\']=\''.$titleAttr_pVar.'\';';
        	}
        	if(strlen($idAttr_pVar)) {
        		$this->elementPrefix_pVar .= '$menuTmpStack_pVar[$menuTmpStackId_pVar][\'id\']=\''.$idAttr_pVar.'\';';
        	}

        	$this->elementPrefix_pVar .= PHP_CLOSE;

        	if(strlen($idAttr_pVar)) {
        		$idAttr_pVar = '\'' . $idAttr_pVar . '\'';
        	}
        	$this->elementSuffix_pVar = PHP_OPEN . '$menuTmpStack_pVar[$menuTmpStackId_pVar - 1][\'tree_childs\'][] = $menuTmpStack_pVar[$menuTmpStackId_pVar]; $menuTmpStackId_pVar--;' . PHP_CLOSE;

        	$accessAttr_pVar = $this->getAttributeValue_gFunc('te:access');
        	if(strlen($accessAttr_pVar)) {
        		$this->te_access_gFunc(true);
        	}


        	$this->elementDisplayType_pVar = 'inside';
        	$this->ignoreInside_data_pVar = true;
        }
    }

    private function te_menupath_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
        	$pathAttr_pVar = $this->getAttributeValue_gFunc('path');
        	$paramsAttr_pVar = $this->getAttributeValue_gFunc('params');
        	$idAttr_pVar = strtolower($this->getAttributeValue_gFunc('id'));

        	$this->elementDisplayType_pVar = 'inside';
        	$this->ignoreInside_data_pVar = true;

    		if($pathAttr_pVar !== false) {
    			if(strlen($pathAttr_pVar)) {
    				$this->elementPrefix_pVar .= PHP_OPEN . '$menuTmpStack_pVar[0][\'paths_pVar\'][]=array(\'id_pVar\'=>\''.$idAttr_pVar.'\', \'path_pVar\'=>\''.$pathAttr_pVar.'\'';
    				if(strlen($paramsAttr_pVar)) {
    					$this->elementPrefix_pVar .= ',\'params_pVar\'=>\'' . $paramsAttr_pVar . '\'';
    				}
    				$this->elementPrefix_pVar .= ');' . PHP_CLOSE;
    			}
    		}
        }
    }

	private function te_menuitem_from_source_gFunc($asAttribute_pVar = false)
	{
        if($asAttribute_pVar) {

        }
        else {
        	$sourceAttr_pVar = $this->getAttributeValue_gFunc('te:source');

        	/**
        	 * vygenerujem pole, a potom ho pridam cez funkciu.
        	 * parametre pre funkciu je toto pole, a referencie na generovanu strukturu menu......
        	 */
        	$this->elementPrefix_pVar = PHP_OPEN . '$sourceData_pVar = main_gClass::source_getData_gFunc(\'' . $sourceAttr_pVar . '\');';
        	$this->elementPrefix_pVar .= ' document_gClass::menuAddItemsFromArray_gFunc($sourceData_pVar, $menuTmpStack_pVar, $menuTmpStackId_pVar);' . PHP_CLOSE;
        	$this->elementDisplayType_pVar = 'none';
        	$this->ignoreInside_data_pVar = true;
        }
	}

    private function te_set_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

        }
        else {
        	$refAttr_pVar = $this->getAttributeValue_gFunc('ref');
        	$valueAttr_pVar = $this->getAttributeValue_gFunc('value');

        	if($this->attributeExists_gFunc('value')) {
	        	$this->elementPrefix_pVar .= PHP_OPEN . $refAttr_pVar . ' = ' . '\'' . $valueAttr_pVar . '\';' . PHP_CLOSE;
        		$this->elementDisplayType_pVar = 'none';
        	}
        	else {
        		$this->elementPrefix_pVar .= PHP_OPEN . 'ob_start();' . PHP_CLOSE;
        		$this->elementSuffix_pVar .= PHP_OPEN . '$x_pVar = ob_get_contents(); ob_end_clean(); '
        			. $refAttr_pVar . ' = ' . '$x_pVar;' . PHP_CLOSE;
        	}
        }
    }

    private function te_attrs_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {
			$attrs_pVar = $this->getAttributeValue_gFunc('te:attrs');
			$this->elementAttributesUnset_pVar['te:attrs'] = true;
			$this->elementAttributesSet_pVar[uniqid('te:attrs_')] = $attrs_pVar;
        }
        else {

        }
    }

    private function te_result_gFunc($asAttribute_pVar = false)
    {
    	if($asAttribute_pVar) {

    	}
    	else {
    		$value_pVar = $this->getAttributeValue_gFunc('value');
    		$value_pVar = intval($value_pVar);

    		$this->elementPrefix_pVar .= PHP_OPEN . '$docObject_pVar = callStack_gClass::getDocObject_gFunc(); $docObject_pVar->result_pVar = ' . $value_pVar . '; unset($docObject_pVar); ' . PHP_CLOSE;
    		$this->elementDisplayType_pVar = 'none';
    	}
    }

    private function te_header_gFunc($asAttribute_pVar = false)
    {
    	if($asAttribute_pVar) {

    	}
    	else {
    		$name_pVar = $this->getAttributeValue_gFunc('name');
    		$value_pVar = $this->getAttributeValue_gFunc('value');

    		$this->elementPrefix_pVar .= PHP_OPEN . 'header(\''.$name_pVar.': '.$value_pVar.'\');' . PHP_CLOSE;
    		$this->elementDisplayType_pVar = 'none';
    	}
    }

    private function te_price_gFunc($asAttribute_pVar = false)
    {
    	if($asAttribute_pVar) {

    	}
    	else {
    		$currency_pVar = $this->getAttributeValue_gFunc('currency');
    		$value_pVar = $this->getAttributeValue_gFunc('value');

    		if(!empty($currency_pVar)) {
    			$currency_pVar = explode('/', $currency_pVar);
    			if(count($currency_pVar) == 1) {
    				$currency_pVar[0] = '\'' . $currency_pVar[0] . '\'';
    				$currency_pVar[1] = 'false';
    			}
    			else {
    				if(strlen($currency_pVar[0])) {
    					$currency_pVar[0] = '\'' . $currency_pVar[0] . '\'';
    				}
    				else {
    					$currency_pVar[0] = 'false';
    				}
    				if(strlen($currency_pVar[1])) {
    					$currency_pVar[1] = '\'' . $currency_pVar[1] . '\'';
    				}
    				else {
    					$currency_pVar[1] = 'false';
    				}
    			}
    		}
    		else {
    			$currency_pVar = array('false', 'false');
    		}

    		$params_pVar = $this->elementAttributes_pVar;
    		unset($params_pVar['currency']);
    		unset($params_pVar['value']);
    		$params_pVar = string_gClass::arrayToPhpCode_gFunc($params_pVar);

    		$this->elementPrefix_pVar .= PHP_OPEN . 'echo string_gClass::priceFormat_gFunc('.$value_pVar.', '. $currency_pVar[0].', '.$currency_pVar[1].', '.$params_pVar.'); ' . PHP_CLOSE;
    		$this->elementDisplayType_pVar = 'none';
    	}
    }

    private function te_redirect_gFunc($asAttribute_pVar = false)
    {
    	if($asAttribute_pVar) {

    	}
    	else {
    		$url_pVar = $this->getAttributeValue_gFunc('url');
    		if(empty($url_pVar)) {
    			$url_pVar = $this->getAttributeValue_gFunc('location');
    		}

    		if(!empty($url_pVar)) {
    			$this->elementPrefix_pVar .= PHP_OPEN
                    . '\\App\\Legacy\\LegacyApp::setHeader(\'Location\', main_gClass::makeUrl_gFunc(\''. string_gClass::addSlashes_magic_gFunc($url_pVar) . '\', false));'
                    . '\\App\\Legacy\\LegacyApp::setHttpStatus(302);'
                    . PHP_CLOSE;
    		}
    		$this->elementDisplayType_pVar = 'none';
    	}
    }

    private function te_edit_gFunc($asAttribute_pVar = false)
    {
		if(modules_gClass::isModuleRegistred_gFunc('cms_edit')) {
			cms_edit_gClass::te_edit_gFunc($this, $asAttribute_pVar);
		}
    }

    private function te_string_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

    	}
    	else {
    		$id_pVar = $this->getAttributeValue_gFunc('id');
    		if(!empty($id_pVar)) {
	    		$string_pVar = string_gClass::getString_gFunc($id_pVar, main_gClass::getLanguage_gFunc());
	    		$this->elementPrefix_pVar .= $string_pVar;
	    		docStringsStack_gClass::push_gFunc(array('lng_pVar'=>main_gClass::getLanguage_gFunc(), 'str_pVar'=>$id_pVar));
	    		$this->elementDisplayType_pVar = 'none';
    		}
    		else {
    			$this->elementPrefix_pVar .= PHP_OPEN . 'ob_start();' . PHP_CLOSE;
    			$this->elementSuffix_pVar .= PHP_OPEN . ' $xstrx = ob_get_contents(); ob_end_clean(); ';
    			$this->elementSuffix_pVar .= 'echo string_gClass::getString_gFunc($xstrx, main_gClass::getLanguage_gFunc());';
    			$this->elementSuffix_pVar .= 'docStringsStack_gClass::push_gFunc(array(\'lng_pVar\'=>main_gClass::getLanguage_gFunc(), \'str_pVar\'=>$xstrx));';
    			$this->elementSuffix_pVar .= PHP_CLOSE;
    			$this->elementDisplayType_pVar = 'inside';
    		}
    	}
    }

    private function te_bookmark_gFunc($asAttribute_pVar = false)
    {
        if($asAttribute_pVar) {

    	}
    	else {
    		$id_pVar = $this->getAttributeValue_gFunc('id');
    		$url_pVar = $this->getAttributeValue_gFunc('url');

    		$string_pVar = string_gClass::getStringId_gFunc($id_pVar, main_gClass::getLanguage_gFunc());
    		$this->elementPrefix_pVar .= $string_pVar['str'];

    		docStringsStack_gClass::push_gFunc(array('lng_pVar'=>main_gClass::getLanguage_gFunc(), 'str_pVar'=>$id_pVar));
    		$this->elementDisplayType_pVar = 'none';

    		if(!empty($url_pVar)) {
    			bookmarks_gClass::addBookmarkIndex_gFunc($string_pVar['id'], $url_pVar);
    		}
    	}
    }

    private function makeUrl_gFunc($url_pVar, $escape_pVar = true)
    {
    	return(main_gClass::makeUrl_gFunc($url_pVar, $escape_pVar));
    }
}


class templateRule_te extends templatesTE_gClass {

}

return(true);
