<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__av_pVar'])) return(true);
$GLOBALS['file__av_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('access3_rights'))
{
    return(false);
}


class addpacient_gClass extends adduser_gClass
{
	private $rc_pVar;
	
	protected function initParams_gFunc()
	{
		$this->setParam_gFunc('formtype-add','add_pacient');
		$this->setParam_gFunc('formtype-edit', 'edit_pacient');
		$this->setParam_gFunc('itemtype', 'users');
		
		$this->forcedData_pVar['user_role'] = 'pacient';

		$this->rc_pVar = false;
		if(!isset($this->params['item_id']) || !$this->params['item_id']) {
			if(isset($this->params['rc']) && !empty($this->params['rc'])) {
				$this->rc_pVar = $this->parseRc_gFunc($this->params['rc']);
				$sql_pVar = 'SELECT `item_id` FROM `%titems_users__data` WHERE birth_no = %s AND status = \'active\'';
				$item_id_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $this->rc_pVar);
				if($item_id_pVar && $item_id_pVar !== true) {
					$this->setParam_gFunc('item_id', $item_id_pVar);
					$this->rc_pVar = false;
				}			
			}
		}
	}
	
	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		parent::initForm_gFunc($multiedit_pVar, $initFormRef_pVar);
		
		$this->setFieldPrefix_gFunc('users_');
		if(!empty($this->rc_pVar)) {
			$this->setDefaultValue_pVar('personal', 'birth_no', $this->rc_pVar);
		}
		
		$this->setFieldPrefix_gFunc('');
	}
	
	private function parseRc_gFunc($rc)
	{
		$rc = str_replace(' ', '', $rc);
		$rc = str_replace('/', '', $rc);
		$rc = str_replace('-', '', $rc);
		return($rc);
	}
}

class addpacient extends addpacient_gClass {}

return(true);