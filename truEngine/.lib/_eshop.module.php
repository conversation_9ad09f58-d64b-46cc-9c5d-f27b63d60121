<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__eshop_pVar'])) return(true);
$GLOBALS['file__eshop_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('items'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('forms')) {
	return(false);
}

class eshop_gClass {

	/**
	 * ziskanie datovej struktury pre menu radene podla vyrobcov
	 *
	 * @return unknown
	 */
	public static function menu_categories_producers_gFunc()
	{
		$ret_pVar = db_eshop_gClass::getCategoriesProducts_gFunc();
		return($ret_pVar);
	}

	/**
	 * ziskanie datovej struktury pre menu produktov
	 * Je vytvorene z vyrobcov, kde vymenim prvy a druhy level
	 */
	public static function menu_categories_products_gFunc()
	{
		$ret_pVar = db_eshop_gClass::getCategoriesProducts_gFunc();
		// zoskupim podla produktov (druhu uroven vymenim s prvou urovnou)

		$level1_pVar = array();
		$level2_pVar = array();

		foreach ($ret_pVar['tree_childs'] as $k_pVar=>$v_pVar)
		{// v $v je level1
			$tmp_pVar = array();
			foreach($v_pVar as $kk_pVar=>$vv_pVar) {
				if($kk_pVar === 'tree_childs') {
					continue;
				}
				$tmp_pVar[$kk_pVar] = $vv_pVar;
			}
			$level1_pVar[$v_pVar[main_gClass::getLanguage_gFunc() . '_tree_path']] = $tmp_pVar;

			// unsetnem paths v prvom leveli, lebo by sa mi bili s mojimi novymi
			foreach ($ret_pVar['paths_pVar'] as $kPathInfo_pVar=>$pathInfo_pVar) {
				if($pathInfo_pVar['id_pVar'] == $v_pVar['id']) {
					unset($ret_pVar['paths_pVar'][$kPathInfo_pVar]);
					continue;
				}
			}

			if(isset($v_pVar['tree_childs'])) {
				foreach ($v_pVar['tree_childs'] as $kk_pVar=>$vv_pVar)
				{
					$p_pVar = strpos($vv_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'], '/', 1);
					if($p_pVar) {
						$subPath_pVar = substr($vv_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'], $p_pVar);
						if(!isset($level2_pVar[$subPath_pVar])) {
							// takato polozka este nebola v 2 urovni
							$level2_pVar[$subPath_pVar] = array();
							foreach ($vv_pVar as $kkk_pVar=>$vvv_pVar) {
								if($kkk_pVar === 'tree_childs') {
									continue;
								}
								$level2_pVar[$subPath_pVar][$kkk_pVar] = $vvv_pVar;
							}
							$level2_pVar[$subPath_pVar][main_gClass::getLanguage_gFunc() . '_tree_path'] = $subPath_pVar;
						}
					}

					// unsetnem paths v druhom leveli, lebo by sa mi bili s mojimi novymi
					foreach ($ret_pVar['paths_pVar'] as $kPathInfo_pVar=>$pathInfo_pVar) {
						if($pathInfo_pVar['id_pVar'] == $vv_pVar['id']) {
							unset($ret_pVar['paths_pVar'][$kPathInfo_pVar]);
							continue;
						}
					}
				}
			}
		}

		$newItems_pVar = array();
		$level1_sets_pVar = array();
		$level2_sets_pVar = array();
		$xid_pVar = 1;
		foreach ($level2_pVar as $k_pVar=>$v_pVar) {
			foreach ($level1_pVar as $kk_pVar=>$vv_pVar) {
				$current_path_pVar = $kk_pVar.$k_pVar;
				$current_path_pVar = str_replace('/'.'/', '/', $current_path_pVar);

				// prehladavam tretiu uroven
				if(isset($ret_pVar['tree_childs']) && is_array($ret_pVar['tree_childs']))
				foreach ($ret_pVar['tree_childs'] as $xk_pVar=>$xv_pVar) {
					if(isset($xv_pVar['tree_childs']) && is_array($xv_pVar['tree_childs']))
					foreach ($xv_pVar['tree_childs'] as $xkk_pVar=>$xvv_pVar) {
						if(isset($xvv_pVar['tree_childs']) && is_array($xvv_pVar['tree_childs']))
						foreach ($xvv_pVar['tree_childs'] as $xkkk_pVar=>$xvvv_pVar) {
							if(substr($xvvv_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'], 0, strlen($current_path_pVar)) == $current_path_pVar) {
								if(!isset($level2_sets_pVar[$k_pVar])) {
									$key_pVar = count($newItems_pVar);
									$level2_sets_pVar[$k_pVar] = $key_pVar;
									$level1_sets_pVar[$k_pVar] = array();
									$newItems_pVar[$key_pVar] = $level2_pVar[$k_pVar];
									$newItems_pVar[$key_pVar]['tree_childs'] = array();
									// uprava path a linkov
									$newItems_pVar[$key_pVar][main_gClass::getLanguage_gFunc() . '_tree_path'] = $k_pVar;
									$newItems_pVar[$key_pVar]['link'] = $k_pVar;
									$newItems_pVar[$key_pVar]['url'] = main_gClass::makeUrl_gFunc($newItems_pVar[$key_pVar]['link']);
									$pathSet_pVar = false;
									foreach($ret_pVar['paths_pVar'] as $kPathInfo_pVar=>$pathInfo_pVar)
									{
										if($pathInfo_pVar['id_pVar'] === $newItems_pVar[$key_pVar]['id']) {
											$ret_pVar['paths_pVar'][$kPathInfo_pVar]['link_pVar'] = $newItems_pVar[$key_pVar]['link'];
											$pathSet_pVar = true;
										}
									}
									if(!$pathSet_pVar) {
										$newPath_pVar = array();
										$newPath_pVar['id_pVar'] = $newItems_pVar[$key_pVar]['id'];
										$newPath_pVar['link_pVar'] = $newItems_pVar[$key_pVar]['link'];
										$ret_pVar['paths_pVar'][] = $newPath_pVar;
									}
								}
								if(!isset($level1_sets_pVar[$k_pVar][$kk_pVar])) {
									$key2_pVar = count($newItems_pVar[$key_pVar]['tree_childs']);
									$level1_sets_pVar[$k_pVar][$kk_pVar] = $key2_pVar;
									$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar] = $level1_pVar[$kk_pVar];
									$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['tree_childs'] = array();
									// uprava path a linkov
									$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar][main_gClass::getLanguage_gFunc() . '_tree_path'] = $current_path_pVar;
									$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['link'] = $current_path_pVar;
									$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['url'] = main_gClass::makeUrl_gFunc($newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['link']);
									$pathSet_pVar = false;
									foreach($ret_pVar['paths_pVar'] as $kPathInfo_pVar=>$pathInfo_pVar)
									{
										if($pathInfo_pVar['id_pVar'] === $newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['id']) {
											$ret_pVar['paths_pVar'][$kPathInfo_pVar]['link_pVar'] = $newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['link'];
											$ret_pVar['paths_pVar'][$kPathInfo_pVar]['id_pVar'] .= '_'.$xid_pVar;
											$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['id'] .= '_'.$xid_pVar;
											$xid_pVar++;
											$pathSet_pVar = true;
											break;
										}
									}
									if(!$pathSet_pVar) {
											$newPath_pVar =array();
											$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['id'] .= '_'.$xid_pVar;
											$newPath_pVar['link_pVar'] = $newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['link'];
											$newPath_pVar['id_pVar'] = $newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['id'];
											$ret_pVar['paths_pVar'][] = $newPath_pVar;
											$xid_pVar++;
									}
								}
								$newItems_pVar[$key_pVar]['tree_childs'][$key2_pVar]['tree_childs'][] = $xvvv_pVar;
							}
						}
					}
				}
			}
		}

		$ret_pVar['tree_childs'] = $newItems_pVar;
		return($ret_pVar);
	}

	public static function getItem_gFunc($itemId_pVar)
	{
		return(items_gClass::getItem_gFunc('eshop', $itemId_pVar));
	}
}



/**
 * Handler pre ziskanie datovej struktury pre menu radene podla vyrobcov
 */
class eshop_menu_categories_producers_gClass extends source_gClass
{
	protected function getData()
	{
		return(eshop_gClass::menu_categories_producers_gFunc());
	}
}

/**
 * Handler pre ziskanie datovej struktury pre menu produktov
 */
class eshop_menu_categories_products_gClass extends source_gClass
{
	protected function getData()
	{
		return(eshop_gClass::menu_categories_products_gFunc());
	}
}

class eshop_additem_gClass extends form_gClass
{
	function __construct($action_pVar = 'get')
	{
		parent::__construct($action_pVar);
	}
/*
	protected function getData()
	{
		$data_pVar = array();
		if(isset($this->params) && count($this->params)) {
			$data_pVar = $this->params;
		}
		$data_pVar['action'] = 'itemadd';
		return(items_gClass::itemAction_gFunc('eshop', $data_pVar));
	}*/

	protected function initForm_gFunc()
	{
		// musim ziskat data
		$data_pVar = array();
		if(isset($this->params) && count($this->params)) {
			$data_pVar = $this->params;
		}
		$this->form_init_pVar = true;

		$dataOptions_pVar = array();
		$dataOptionsTmp_pVar = items_gClass::getItems_gFunc('eshop', array('group'=>'accessories'));
		foreach ($dataOptionsTmp_pVar as $k_pVar=>$v_pVar) {
			if(!is_numeric($k_pVar)) {
				continue;
			}
			$dataOptions_pVar[$v_pVar['item_id']] = '[' . $v_pVar['item_id'] . '] ' . $v_pVar[main_gClass::getLanguage_gFunc() . '_name'] . ' (' . $v_pVar['price'] . ' ' . $v_pVar['currency'] .')';
		}
		$this->prepareOptionsForField_gFunc('eshop_accessories', $dataOptions_pVar);
		items_gClass::initFormRef_gFunc('eshop', $this, $data_pVar);
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		// ulozim item
		items_gClass::editItemByForm_gFunc('eshop', $this);

		$data_pVar = $this->getFormData_gFunc();
		return($data_pVar);
	}
}

class eshop_productlist extends source_gClass
{
	protected function getData()
	{
		$filter_pVar = array();
		if(isset($this->params['url_filter'])) {
			$filter_pVar['url_filter'] = $this->params['url_filter'];
		}
		if(isset($this->params['filter'])) {
			$filter_pVar['filter'] = $this->params['filter'];
		}
		if(!count($filter_pVar)) {
			return(array());
		}

		$products_pVar = db_eshop_gClass::getProducts_gFunc($filter_pVar);

		return($products_pVar);
	}
}

class eshop_order_form_gClass extends form_gClass
{
	function __construct($action_pVar = 'get')
	{
		parent::__construct($action_pVar);
	}

	protected  function handleAction($action_pVar = false)
	{
		if($action_pVar === false) {
			$action_pVar = 'get';
		}

		switch ($action_pVar) {
			case 'get':
				$data_pVar = $this->getData();
				break;
			default:
				$data_pVar = $this->getData();
				break;
		}
		return($data_pVar);
	}

	protected function initForm_gFunc()
	{
		$this->form_init_pVar = true;
		$this->addFieldset_gFunc('invoice', string_gClass::get('str_eshop_orderform_legend_invoice_pVar'));
		$this->addField_gFunc('invoice', 'email',			'email', string_gClass::get('str_eshop_orderform_label_email_pVar'), true);
		$this->addField_gFunc('invoice', 'name',			'text',  string_gClass::get('str_eshop_orderform_label_name_pVar'), true);
		$this->addField_gFunc('invoice', 'street_number', 	'text',  string_gClass::get('str_eshop_orderform_label_street_number_pVar'), true);
		$this->addField_gFunc('invoice', 'zip_city',		'text',  string_gClass::get('str_eshop_orderform_label_zip_city_pVar'), true);
		$this->addField_gFunc('invoice', 'ico',				'text',  string_gClass::get('str_eshop_orderform_label_ico_pVar'), true, '/[0-9 ]+/');
		$this->addField_gFunc('invoice', 'dic',				'text',  string_gClass::get('str_eshop_orderform_label_dic_pVar'), false, '/[0-9 ]*/');
		$this->addField_gFunc('invoice', 'ic_dph',			'text',  string_gClass::get('str_eshop_orderform_label_ic_dph_pVar'), false, '/[0-9 ]*/');
		$this->addField_gFunc('invoice', 'phone',			'text',  string_gClass::get('str_eshop_orderform_label_phone_pVar'), false);
		$this->addFieldset_gFunc('transport_payment', string_gClass::get('str_eshop_orderform_legend_transport_payment_pVar'));
		$this->addField_gFunc('transport_payment', 'transport', 'select',  string_gClass::get('str_eshop_orderform_label_transport_pVar'), true, '/[0-9]+/');
		$this->addField_gFunc('transport_payment', 'payment',	'select',  string_gClass::get('str_eshop_orderform_label_payment_pVar'), true, '/[0-9]+/');
		$this->addField_gFunc('transport_payment', 'message',	'textarea',  string_gClass::get('str_eshop_orderform_label_message_pVar'), false);
		$this->addFieldset_gFunc('delivery', string_gClass::get('str_eshop_orderform_legend_delivery_pVar'));
		$this->addField_gFunc('delivery', 'delivery_name',			'text',  string_gClass::get('str_eshop_orderform_label_name_pVar'), false);
		$this->addField_gFunc('delivery', 'delivery_street_number', 'text',  string_gClass::get('str_eshop_orderform_label_street_number_pVar'), false);
		$this->addField_gFunc('delivery', 'delivery_zip_city',		'text',  string_gClass::get('str_eshop_orderform_label_zip_city_pVar'), false);
		$this->addField_gFunc('delivery', 'delivery_phone',			'text',  string_gClass::get('str_eshop_orderform_label_phone_pVar'), false);

		$transport_types_pVar = db_public_gClass::eshop_getTransportTypes_gFunc();
		$this->setFieldOptions_gFunc('transport_payment', 'transport', $transport_types_pVar);
		$this->setDefaultValue_pVar('transport_payment', 'transport', $this->params['transport']);

		$payment_types_pVar = db_public_gClass::eshop_getPaymentTypes_gFunc();
		$this->setFieldOptions_gFunc('transport_payment', 'payment', $payment_types_pVar);

		// inicializujem defaultne hodnoty
		$user_id_pVar = session_gClass::getUserDetail_gFunc('user_id');
		if($user_id_pVar) {
			$address_pVar = session_gClass::getUserDetail_gFunc('address_id_eshop');
			if(is_array($address_pVar)) {
				$this->setDefaultValue_pVar('invoice', 'name', trim(implode(' ', array($address_pVar['titul_pred'], $address_pVar['name'], $address_pVar['first_name'], $address_pVar['titul_za']))));
				$this->setDefaultValue_pVar('invoice', 'street_number', $address_pVar['street_number']);
				$this->setDefaultValue_pVar('invoice', 'zip_city', trim(implode(' ', array($address_pVar['zip'], $address_pVar['city']))));
				$this->setDefaultValue_pVar('invoice', 'phone', $address_pVar['phone']);
				if(!empty($address_pVar['email'])) {
					$this->setDefaultValue_pVar('invoice', 'email', $address_pVar['email']);
				}
				else {
					$this->setDefaultValue_pVar('invoice', 'email', session_gClass::getUserDetail_gFunc('email'));
				}
			}
			else {
				$this->setDefaultValue_pVar('invoice', 'email', session_gClass::getUserDetail_gFunc('email'));
			}
			$this->setDefaultValue_pVar('invoice', 'ico', session_gClass::getUserDetail_gFunc('ico'));
			$this->setDefaultValue_pVar('invoice', 'dic', session_gClass::getUserDetail_gFunc('dic'));
			$this->setDefaultValue_pVar('invoice', 'ic_dph', session_gClass::getUserDetail_gFunc('ic_dph'));

			$address_pVar = session_gClass::getUserDetail_gFunc('address_id_eshop_delivery');
			if(is_array($address_pVar)) {
				$this->setDefaultValue_pVar('delivery', 'delivery_name', trim(implode(' ', array($address_pVar['titul_pred'], $address_pVar['name'], $address_pVar['first_name'], $address_pVar['titul_za']))));
				$this->setDefaultValue_pVar('delivery', 'delivery_street_number', $address_pVar['street_number']);
				$this->setDefaultValue_pVar('delivery', 'delivery_zip_city', trim(implode(' ', array($address_pVar['zip'], $address_pVar['city']))));
				$this->setDefaultValue_pVar('delivery', 'delivery_phone', $address_pVar['phone']);
			}
		}
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		// mail clientovi
		if(isset($this->params['template1'])) {
			$template1_pVar = $this->params['template1'];
		}
		else {
			$template1_pVar = '/'.main_gClass::getLanguage_gFunc().'/.emails/order_dealer';
		}
		// mail predajcovi
		if(isset($this->params['template2'])) {
			$template2_pVar = $this->params['template2'];
		}
		else {
			$template2_pVar = '/'.main_gClass::getLanguage_gFunc().'/emails/order_client';
		}

		/*
		// pridanie lng prefixu
		list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = main_gClass::addLanguagePrefixToDocName_gFunc($template1_pVar);
		if($prefixEnabled_pVar) {
			$template1_pVar = $lngStr_pVar . '/' . $docx_pVar;
		}
		list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = main_gClass::addLanguagePrefixToDocName_gFunc($template2_pVar);
		if($prefixEnabled_pVar) {
			$template2_pVar = $lngStr_pVar . '/' . $docx_pVar;
		}
		*/

		// pripravim si data
		$vars_pVar = array('order'=>$data_pVar);

		if(modules_gClass::isModuleRegistred_gFunc('eshop_basket')) {
			$basket_pVar = new eshop_basket_gClass();
			$basketData_pVar = $basket_pVar->getBasketData_gFunc();
			$vars_pVar['basket'] = $basketData_pVar;
		}

		$vars_pVar['basket']['transport'] = $vars_pVar['order']['fieldsets']['transport_payment']['fields']['transport']['value'];
		$vars_pVar['basket']['payment'] = $vars_pVar['order']['fieldsets']['transport_payment']['fields']['payment']['value'];
		$vars_pVar['basket']['transport_price'] = $vars_pVar['basket']['transport_types'][$vars_pVar['basket']['transport']]['transport_type_price'];
		$vars_pVar['basket']['total_price'] = $vars_pVar['basket']['items_price'] + $vars_pVar['basket']['transport_price'];

		$vars_pVar['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME');
		$vars_pVar['order_id'] = time();

		if(!empty($vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_name']['value'])
			|| !empty($vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_street_number']['value'])
			|| !empty($vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_zip_city']['value'])
			|| !empty($vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_phone']['value'])) {
			$vars_pVar['delivery_address'] =
				$vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_name']['value'] . '<br />'
				. $vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_street_number']['value'] . '<br />'
				. $vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_zip_city']['value'] . '<br />'
				. $vars_pVar['order']['fieldsets']['delivery']['fields']['delivery_phone']['value'];
		}
		else {
			$vars_pVar['delivery_address'] = '';
		}

		// user data

		$vars_pVar['user_id'] = session_gClass::getUserDetail_gFunc('user_id');
		if($vars_pVar['user_id']) {
			$vars_pVar['user_login'] = session_gClass::getUserDetail_gFunc('login');
		}

		$vars_pVar['dealer'] = main_gClass::getConfigSectionVar_gFunc('eshop');

		$template1Content_pVar = callStack_gClass::getDocContent_gFunc($template1_pVar, $vars_pVar);
		$template2Content_pVar = callStack_gClass::getDocContent_gFunc($template2_pVar, $vars_pVar);

		$to1_pVar = main_gClass::getConfigVar_gFunc('email', 'contacts');
		$from1_pVar = $vars_pVar['order']['fieldsets']['invoice']['fields']['email']['value'];
		$subject1_pVar = string_gClass::get('str_eshop_order_subject_pVar', $vars_pVar['url'].': ', $vars_pVar['order_id']);
		$message1_pVar = $template1Content_pVar;
		email_gClass::mailHtml_gFunc($to1_pVar, $from1_pVar, $subject1_pVar, $message1_pVar);

		$to2_pVar = $vars_pVar['order']['fieldsets']['invoice']['fields']['email']['value'];
		$from2_pVar = main_gClass::getConfigVar_gFunc('email', 'contacts');
		$subject2_pVar = string_gClass::get('str_eshop_order_subject_pVar', $vars_pVar['url'].': ', $vars_pVar['order_id']);
		$message2_pVar = $template2Content_pVar;
		email_gClass::mailHtml_gFunc($to2_pVar, $from2_pVar, $subject2_pVar, $message2_pVar);

		// zmazem kosik
		if(modules_gClass::isModuleRegistred_gFunc('eshop_basket')) {
			$basket_pVar = new eshop_basket_gClass();
			$basket_pVar->getBasketData_gFunc(true);
		}

		return($data_pVar);
	}

}

class eshop_getitem_gClass extends source_gClass
{
	protected function getData()
	{
		$data_pVar = array();
		if(isset($this->params) && count($this->params)) {
			$data_pVar = $this->params;
		}
		$data_pVar['action'] = 'itemget';
		return(items_gClass::itemAction_gFunc('eshop', $data_pVar));
	}
}

class eshop_list_producers_gClass extends source_gClass
{
	protected function getData()
	{
		$producers_pVar = db_eshop_gClass::getProducers_gFunc();

		foreach ($producers_pVar as $k_pVar=>$v_pVar) {
			$producers_pVar[$k_pVar]['enum_field_value'] = '<a href="'.main_gClass::makeUrl_gFunc('/admin/produkty/vyrobca?item_id='.$producers_pVar[$k_pVar]['enum_id']).'">' . str_replace('_', ' ', $producers_pVar[$k_pVar]['enum_field_value']) . '</a>';
			foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
				if(substr($kk_pVar, 3) === 'url_name') {
					$producers_pVar[$k_pVar][$kk_pVar] = str_replace('-', ' - ', $producers_pVar[$k_pVar][$kk_pVar]);
				}

				if(substr($kk_pVar, 2, 1) === '_') { // lng
					if(!isset($producers_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$producers_pVar[$k_pVar][substr($kk_pVar,3)] = '';
					}
					if(!empty($producers_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$producers_pVar[$k_pVar][substr($kk_pVar,3)] .= '<br />';
					}
					$producers_pVar[$k_pVar][substr($kk_pVar,3)] .= '[' . substr($kk_pVar, 0, 2) . ']&nbsp;' . $producers_pVar[$k_pVar][$kk_pVar];
				}
			}
		}


		if(modules_gClass::isModuleRegistred_gFunc('tables')) {
			$table_pVar = new table_gClass();
			$table_pVar->setData_gFunc($producers_pVar);
			if(isset($this->params['columns'])) {
				$table_pVar->setColumnsFromString_gFunc($this->params['columns']);
			}

			return($table_pVar->getData());
		}

		return($producers_pVar);
	}
}

class eshop_list_typ_gClass extends source_gClass
{
	protected function getData()
	{
		$typ_pVar = db_eshop_gClass::getTyp_gFunc();

		foreach ($typ_pVar as $k_pVar=>$v_pVar) {
			$typ_pVar[$k_pVar]['enum_field_value'] = '<a href="'.main_gClass::makeUrl_gFunc('/admin/produkty/typ?item_id='.$typ_pVar[$k_pVar]['enum_id']).'">' . str_replace('_', ' ', $typ_pVar[$k_pVar]['enum_field_value']) . '</a>';
			foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
				if(substr($kk_pVar, 3) === 'url_name') {
					$typ_pVar[$k_pVar][$kk_pVar] = str_replace('-', ' - ', $typ_pVar[$k_pVar][$kk_pVar]);
				}

				if(substr($kk_pVar, 2, 1) === '_') { // lng
					if(!isset($typ_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$typ_pVar[$k_pVar][substr($kk_pVar,3)] = '';
					}
					if(!empty($typ_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$typ_pVar[$k_pVar][substr($kk_pVar,3)] .= '<br />';
					}
					$typ_pVar[$k_pVar][substr($kk_pVar,3)] .= '[' . substr($kk_pVar, 0, 2) . ']&nbsp;' . $typ_pVar[$k_pVar][$kk_pVar];
				}
			}
		}

		if(modules_gClass::isModuleRegistred_gFunc('tables')) {
			$table_pVar = new table_gClass();
			$table_pVar->setData_gFunc($typ_pVar);
			if(isset($this->params['columns'])) {
				$table_pVar->setColumnsFromString_gFunc($this->params['columns']);
			}

			return($table_pVar->getData());
		}

		return($typ_pVar);
	}
}

class eshop_list_druh_gClass extends source_gClass
{
	protected function getData()
	{
		$druh_pVar = db_eshop_gClass::getDruh_gFunc();

		foreach ($druh_pVar as $k_pVar=>$v_pVar) {
			$druh_pVar[$k_pVar]['enum_field_value'] = '<a href="'.main_gClass::makeUrl_gFunc('/admin/produkty/druh?item_id='.$druh_pVar[$k_pVar]['enum_id']).'">' . str_replace('_', ' ', $druh_pVar[$k_pVar]['enum_field_value']) . '</a>';
			foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
				if(substr($kk_pVar, 3) === 'url_name') {
					$druh_pVar[$k_pVar][$kk_pVar] = str_replace('-', ' - ', $druh_pVar[$k_pVar][$kk_pVar]);
				}

				if(substr($kk_pVar, 2, 1) === '_') { // lng
					if(!isset($druh_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$druh_pVar[$k_pVar][substr($kk_pVar,3)] = '';
					}
					if(!empty($druh_pVar[$k_pVar][substr($kk_pVar,3)])) {
						$druh_pVar[$k_pVar][substr($kk_pVar,3)] .= '<br />';
					}
					$druh_pVar[$k_pVar][substr($kk_pVar,3)] .= '[' . substr($kk_pVar, 0, 2) . ']&nbsp;' . $druh_pVar[$k_pVar][$kk_pVar];
				}
			}
		}

		if(modules_gClass::isModuleRegistred_gFunc('tables')) {
			$table_pVar = new table_gClass();
			$table_pVar->setData_gFunc($druh_pVar);
			if(isset($this->params['columns'])) {
				$table_pVar->setColumnsFromString_gFunc($this->params['columns']);
			}

			return($table_pVar->getData());
		}

		return($druh_pVar);
	}
}

class eshop_additemvalue_gClass extends form_gClass
{
	protected $enum_field_id_pVar;

	protected function initForm_gFunc()
	{
		if(isset($this->params['item_id']) && $this->params['item_id']>0) {
			$isItemId_pVar = true;
		}
		else {
			$isItemId_pVar = false;
		}

		$info_pVar = db_items_gClass::getInfo_gFunc('eshop');

		$this->addFieldset_gFunc('main', '');
		$this->addField_gFunc('main', 'enum_field_value', 'varchar', 'Tag:', true);

		foreach ($info_pVar['languages'] as $language) {
			$this->addFieldset_gFunc($language, $language);
			$this->addField_gFunc($language, $language . '_enum_field_name_item', 'varchar', '['.$language.'] Názov:', true);
			$this->addField_gFunc($language, $language . '_enum_field_name_group', 'varchar', '['.$language.'] Názov (množné číslo):', true);
			$this->addField_gFunc($language, $language . '_url_name', 'varchar', '['.$language.'] Url:', true);
		}
		$this->addHiddenField_gFunc('enum_field_id', $this->enum_field_id_pVar, '/[0-9]+/');

		if($isItemId_pVar) {
			$this->addHiddenField_gFunc('item_id', $this->params['item_id'], '/[0-9]+/');
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_update']);
			// selectnem pouzivatela a inicializujem hodnoty
			$data_pVar = db_eshop_gClass::getItemEshopValue_gFunc($this->params['item_id']);
			if(is_array($data_pVar)) {
				$this->setDefaultValue_pVar('main', 'enum_field_value', $data_pVar['enum_field_value']);
				foreach ($info_pVar['languages'] as $language) {
					$this->setDefaultValue_pVar($language, $language . '_enum_field_name_item', $data_pVar[$language . '_enum_field_name_item']);
					$this->setDefaultValue_pVar($language, $language . '_enum_field_name_group', $data_pVar[$language . '_enum_field_name_group']);
					$this->setDefaultValue_pVar($language, $language . '_url_name', $data_pVar[$language . '_url_name']);
				}
			}
		}
		else {
			$this->addHiddenField_gFunc('item_id', 0, '/[0]+/');
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_add']);
		}

		$this->setVar_gFunc('submit_button_title_add', $this->params['submit_button_title_add']);
		$this->setVar_gFunc('submit_button_title_update', $this->params['submit_button_title_update']);
		return;
	}

	protected function getData()
	{
		$data_pVar = $this->getFormData_gFunc();
		if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($data_pVar);
		}

		$info_pVar = db_items_gClass::getInfo_gFunc('eshop');

		// ulozim polozku
		$valueId_pVar = $this->getFieldValue_gFunc('item_id');
		$data_pVar = array();
		$data_pVar['enum_field_value'] = $this->getFieldValue_gFunc('enum_field_value');
		$data_pVar['enum_field_id'] = $this->getFieldValue_gFunc('enum_field_id');
		foreach ($info_pVar['languages'] as $language) {
			$data_pVar[$language . '_enum_field_name_item'] = $this->getFieldValue_gFunc($language . '_enum_field_name_item');
			$data_pVar[$language . '_enum_field_name_group'] = $this->getFieldValue_gFunc($language . '_enum_field_name_group');
			$data_pVar[$language . '_url_name'] = $this->getFieldValue_gFunc($language . '_url_name');

			$data_pVar[$language . '_url_name'] = str_replace(' ', '_', $data_pVar[$language . '_url_name']);
			$data_pVar[$language . '_url_name'] = string_gClass::removeDiacritic_gFunc($data_pVar[$language . '_url_name']);
		}

		$data_pVar['enum_field_value'] = str_replace(' ', '_', $data_pVar['enum_field_value']);
		$data_pVar['enum_field_value'] = string_gClass::removeDiacritic_gFunc($data_pVar['enum_field_value']);



		if($valueId_pVar) {
			// aktualizujem
			db_eshop_gClass::updateItemEshopValue_gFunc($valueId_pVar, $data_pVar);
		}
		else {
			// novy
			db_eshop_gClass::newItemEshopValue_gFunc($data_pVar);
		}

		$data_pVar = $this->getFormData_gFunc();
		return($data_pVar);
	}

}

class eshop_addproducer extends eshop_additemvalue_gClass
{
	protected $enum_field_id_pVar = 8;
}

class eshop_addtyp extends eshop_additemvalue_gClass
{
	protected $enum_field_id_pVar = 10;
}

class eshop_adddruh extends eshop_additemvalue_gClass
{
	protected $enum_field_id_pVar = 9;
}

/*

class eshop_additem_fields_gClass extends source_gClass
{
	protected function getData()
	{

		$filter_pVar = array();
		if(isset($this->params['url_filter'])) {
			$filter_pVar['url_filter'] = $this->params['url_filter'];
		}

		if(isset($this->params['load-request'])) {
			if($this->params['load-request'] == 'ERROR' || $this->params['load-request'] == '1') {
				$filter_pVar['load-request'] = true;
			}
		}

		$fields_pVar = db_eshop_gClass::getProductFields_gFunc($filter_pVar);
		return($fields_pVar);
	}
}
*/

class eshop_menu_categories_producers extends eshop_menu_categories_producers_gClass
{

}

class eshop_menu_categories_products extends eshop_menu_categories_products_gClass
{

}

class eshop_additem extends eshop_additem_gClass
{

}

class eshop_getitem extends eshop_getitem_gClass
{

}

class eshop_order_form extends eshop_order_form_gClass
{

}

class eshop_list_producers extends eshop_list_producers_gClass
{

}

class eshop_list_druh extends eshop_list_druh_gClass
{

}

class eshop_list_typ extends eshop_list_typ_gClass
{

}


/*
class eshop_additem_fields extends eshop_additem_fields_gClass
{

}
*/


class db_eshop_gClass extends db_gClass {

	/**
	 * Select menu produktov
	 * @return unknown
	 */
	static public function getCategoriesProducts_gFunc()
	{
		$treeData_pVar = self::tree_get_gFunc('items_eshop_tree_categories', 1, array('status_pVar'=>'enabled'), array('db_eshop_gClass', 'getCategoriesProductsCallback_gFunc'));
		if(!isset($treeData_pVar['tree_childs'])) {
			$treeData_pVar = array('tree_childs'=>array());
		}
		return($treeData_pVar);
	}

	static public function getCategoriesProductsCallback_gFunc(&$node_pVar, &$parent_pVar)
	{
		$node_pVar['id']= 'cat_'.$node_pVar['tree_id'];
		$node_pVar['label']= $node_pVar[main_gClass::getLanguage_gFunc() . '_name'];
		$node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path']= $parent_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'] . $node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'] . '/';
		if(!isset($node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'][0]) || $node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'][0] != '/') {
			$node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'] = '/' . $node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'];
		}
		$node_pVar['url']= main_gClass::makeUrl_gFunc($node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path']);
		$node_pVar['link']= $node_pVar[main_gClass::getLanguage_gFunc() . '_tree_path'];
	}

	static public function getProducts_gFunc($filter_pVar = array())
	{
		if(modules_gClass::isModuleRegistred_gFunc('items')) {
			$products_pVar = items_gClass::getItems_gFunc('eshop', $filter_pVar);
		}
		else {
			$products_pVar = array();
		}
		return($products_pVar);
	}

	static public function searchProducts_gFunc($params_pVar = array())
	{
		if(modules_gClass::isModuleRegistred_gFunc('items')) {
			$products_pVar = items_gClass::searchItems_gFunc('eshop', $params_pVar);
		}
		else {
			$products_pVar = array();
		}
		return($products_pVar);
	}

	/*
	static public function getProductFields_gFunc($default_values_pVar = array())
	{
		if(modules_gClass::isModuleRegistred_gFunc('items')) {
			$fields_pVar = items_gClass::getItemFields_gFunc('eshop', $default_values_pVar);
		}
		else {
			$fields_pVar = array();
		}
		return($fields_pVar);
	}

	static public function addItemFromRequest_gFunc()
	{
		if(modules_gClass::isModuleRegistred_gFunc('items')) {
			if(items_gClass::addItemFromRequest_gFunc('eshop')) {
				$result_pVar = 'OK';
			}
			else {
				$result_pVar = 'ERROR';
			}
		}
		else {
			$result_pVar = 'ERROR';
		}
		return($result_pVar);
	}*/

	static public function getProducers_gFunc()
	{
		$sql_pVar = 'SELECT * FROM `%titems_eshop_values` WHERE `enum_field_id`=8';
		$data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		return($data_pVar);
	}
	static public function getDruh_gFunc()
	{
		$sql_pVar = 'SELECT * FROM `%titems_eshop_values` WHERE `enum_field_id`=9';
		$data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		return($data_pVar);
	}
	static public function getTyp_gFunc()
	{
		$sql_pVar = 'SELECT * FROM `%titems_eshop_values` WHERE `enum_field_id`=10';
		$data_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
		return($data_pVar);
	}

	static public function getItemEshopValue_gFunc($id_pVar)
	{
		$sql_pVar = 'SELECT * FROM `%titems_eshop_values` WHERE `enum_id`=%d';
		$data_pVar = self::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar);
		return($data_pVar);
	}

	static public function updateItemEshopValue_gFunc($valueId_pVar, $data_pVar)
	{
		unset($data_pVar['enum_field_value']);

    	$sql_pVar = 'UPDATE `%titems_eshop_values` SET ';
    	$sql_params_pVar = array();

    	foreach ($data_pVar as $k_pVar=>$v_pVar) {
    		if(count($sql_params_pVar)) {
    			$sql_pVar .= ', ';
    		}
    		$sql_pVar .= ' `' . $k_pVar . '`=%s';
    		$sql_params_pVar[] = $v_pVar;
    	}

    	$sql_pVar .= ' WHERE `enum_id`=%d';
    	$sql_params_pVar[] = $valueId_pVar;

    	self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

    	db_items_gClass::applyLanguagesToTables_gFunc('eshop');
		db_items_gClass::cacheDataToTree_gFunc('eshop', 'categories');
	}

	static public function newItemEshopValue_gFunc($data_pVar)
	{

    	$sql_pVar = 'INSERT INTO `%titems_eshop_values` SET ';
    	$sql_params_pVar = array();

    	foreach ($data_pVar as $k_pVar=>$v_pVar) {
    		if(count($sql_params_pVar)) {
    			$sql_pVar .= ', ';
    		}
    		$sql_pVar .= ' `' . $k_pVar . '`=%s';
    		$sql_params_pVar[] = $v_pVar;
    	}

    	self::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

    	db_items_gClass::applyLanguagesToTables_gFunc('eshop');
		db_items_gClass::cacheDataToTree_gFunc('eshop', 'categories');
	}
}

return(true);
