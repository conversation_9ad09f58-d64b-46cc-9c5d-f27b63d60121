<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__graphics_pVar'])) return(true);
$GLOBALS['file__graphics_pVar']=true;


class gd_gClass
{
	static private $isGd_pVar = null;
	private $im_pVar;
	private $info_pVar;
	private $fileName_pVar;

	function __construct()
	{
		$this->resetObject_gFunc();
		self::isGD_gFunc();
	}

	static public function isGD_gFunc()
	{
		if(self::$isGd_pVar === null) {
			if(!function_exists('gd_info')) {
				self::$isGd_pVar = false;
				//error_gClass::error_gFunc(__FILE__, __LINE__, 'GD library is not installed.');
			}
			else {
				self::$isGd_pVar = true;
			}
		}
		return(self::$isGd_pVar);
	}

	private function resetObject_gFunc()
	{
		$this->im_pVar = false;
		$this->info_pVar = false;
		$this->fileName_pVar = false;
	}

	function isSupportedFormat_gFunc($format_pVar)
	{
		switch ($format_pVar)
		{
			case IMAGETYPE_JPEG:
			case IMAGETYPE_JPEG2000:
			case IMAGETYPE_GIF:
			case IMAGETYPE_PNG:
				return(true);
			default:
				return(false);
		}
	}

	function loadImage_gFunc($filename_pVar)
	{
		$this->resetObject_gFunc();
		if(!$this->isGD_gFunc()) {
			return(false);
		}
		$filename_pVar = fileSafe_gClass::safePath_gFunc($filename_pVar);
		if(!fileSafe_gClass::file_exists_gFunc($filename_pVar)) {
			return(false);
		}
		$this->info_pVar = getimagesize($filename_pVar);
		if(!is_array($this->info_pVar) || !$this->isSupportedFormat_gFunc($this->info_pVar[2])) {
			return(false);
		}

		switch ($this->info_pVar[2]) {
			case IMAGETYPE_JPEG:
			case IMAGETYPE_JPEG2000:
				$this->im_pVar = imagecreatefromjpeg($filename_pVar);
				break;
			case IMAGETYPE_GIF:
				$this->im_pVar = imagecreatefromgif($filename_pVar);
				break;
			case IMAGETYPE_PNG:
				$this->im_pVar = imagecreatefrompng($filename_pVar);
				break;
			default:
				return(false);
		}

		if($this->im_pVar === false) {
			return(false);
		}
		$this->fileName_pVar = $filename_pVar;
		return(true);
	}

	function saveImage_gFunc()
	{
		if(!$this->isGD_gFunc()) {
			return(false);
		}
		if($this->fileName_pVar === false) {
			return(false);
		}
		$this->fileName_pVar = self::getNameForJpegImage($this->fileName_pVar);
		$this->fileName_pVar = $this->saveImageAs_gFunc($this->fileName_pVar);
		return($this->fileName_pVar);
	}

	function saveImageAs_gFunc($fileName_pVar)
	{
		if(!$this->isGD_gFunc()) {
			return(false);
		}
		if(!$this->im_pVar) {
			return(false);
		}
		$this->fileName_pVar = $fileName_pVar;
		imageinterlace($this->im_pVar, 1);
		imagejpeg($this->im_pVar, fileSafe_gClass::unsafePath_gFunc($this->fileName_pVar));
		return($this->fileName_pVar);
	}

	static public function getNameForJpegImage($fileName_pVar)
	{
		$pathInfo_pVar = pathinfo($fileName_pVar);
		if(isset($pathInfo_pVar['extension']) && $pathInfo_pVar['extension'] !== 'jpg') {
			$fileName_pVar = substr($fileName_pVar, 0, -strlen($pathInfo_pVar['extension']));
			$fileName_pVar .= 'jpg';
		}
		return($fileName_pVar);
	}

	static public function setFileNamePrefix_gFunc($prefix_pVar, $fileName_pVar)
	{
		if($prefix_pVar === 0) {
			return($fileName_pVar);
		}
		$pathinfo_pVar = pathinfo($fileName_pVar);
		if(self::isImageName_gFunc($fileName_pVar, true)) {
			$fileName_pVar = substr($fileName_pVar, 0, -strlen($pathinfo_pVar['basename'])) . $prefix_pVar . $pathinfo_pVar['basename'];
		}
		else {
			if(!empty($prefix_pVar)) {
				if(isset($pathinfo_pVar['extension'])) {
					$fileName_pVar = self::getDefaultThumbnail_gFunc($pathinfo_pVar['extension']);
				}
			}
		}
		return($fileName_pVar);
	}

	function resizeImageToRectangle_gFunc($width_pVar = false, $height_pVar = false, $zoom_in_pVar = false)
	{
		if($width_pVar == 0) {
			$width_pVar = false;
		}
		if($height_pVar == 0) {
			$height_pVar = false;
		}
		if(!$this->isGD_gFunc()) {
			return(false);
		}
		$newWidth_pVar = $this->info_pVar[0];
		$newHeight_pVar = $this->info_pVar[1];

		$resized_pVar = false;
		if($width_pVar !== false && $newWidth_pVar > $width_pVar) {
			// zmensim sirku
			$pomer_pVar = $newWidth_pVar / $width_pVar;
			$newWidth_pVar = $newWidth_pVar / $pomer_pVar;
			$newHeight_pVar = $newHeight_pVar / $pomer_pVar;
			$resized_pVar = true;
		}
		if($height_pVar !== false && $newHeight_pVar > $height_pVar) { // testujem upravenu vysku v predchadzajucom if
			// zmensim sirku. (najskor obnovim povodnu velkost, a potom zmensim. Pomer zmensenia vysky je vaci, takze mozem vychadzat z povodnej velkosti)
			$newWidth_pVar = $this->info_pVar[0];
			$newHeight_pVar = $this->info_pVar[1];

			$pomer_pVar = $newHeight_pVar / $height_pVar;
			$newWidth_pVar = $newWidth_pVar / $pomer_pVar;
			$newHeight_pVar = $newHeight_pVar / $pomer_pVar;
			$resized_pVar = true;
		}

		if(!$resized_pVar && $zoom_in_pVar === true) {
			$newWidth_pVar = $this->info_pVar[0];
			$newHeight_pVar = $this->info_pVar[1];

			// obrazok som nezmensoval, idem ho zvacsit
			if($width_pVar !== false && $newWidth_pVar < $width_pVar) {
				// zvacsim sirku
				$pomer_pVar = $newWidth_pVar / $width_pVar;
				$newWidth_pVar = $newWidth_pVar / $pomer_pVar;
				$newHeight_pVar = $newHeight_pVar / $pomer_pVar;
			}

			if($height_pVar !== false && ($width_pVar === false || $newHeight_pVar > $height_pVar)) {
				// presvihol som to (Alebo som nezvasoval podla sirky), idem zvacsit podla vysky.
				$newWidth_pVar = $this->info_pVar[0];
				$newHeight_pVar = $this->info_pVar[1];

				if($newHeight_pVar < $height_pVar) {
					$pomer_pVar = $newHeight_pVar / $height_pVar;
					$newWidth_pVar = $newWidth_pVar / $pomer_pVar;
					$newHeight_pVar = $newHeight_pVar / $pomer_pVar;
				}
			}
		}

		$newWidth_pVar = round($newWidth_pVar);
		if($width_pVar !== false && $newWidth_pVar > $width_pVar) {
			$newWidth_pVar = $width_pVar;
		}
		$newHeight_pVar = round($newHeight_pVar);
		if($height_pVar !== false && $newHeight_pVar > $height_pVar) {
			$newHeight_pVar = $height_pVar;
		}
		if($newHeight_pVar <= 0 || $newWidth_pVar <= 0) {
			return(false);
		}
		$im_pVar = imagecreatetruecolor($newWidth_pVar, $newHeight_pVar);
		imageinterlace($im_pVar, 1);
		imagecopyresized($im_pVar, $this->im_pVar, 0, 0, 0, 0, $newWidth_pVar, $newHeight_pVar, $this->info_pVar[0], $this->info_pVar[1]);
		$this->im_pVar = $im_pVar;
		$this->info_pVar[0] = $newWidth_pVar;
		$this->info_pVar[1] = $newHeight_pVar;
		return(true);
	}

	static public function isImageName_gFunc($fileName_pVar, $strict_pVar = false)
	{
		$pathinfo_pVar = pathinfo($fileName_pVar);
		if(!isset($pathinfo_pVar['extension'])) {
			return(false);
		}
		$extension_pVar = strtolower($pathinfo_pVar['extension']);

		if($strict_pVar) {
			switch ($extension_pVar) {
				case 'jpg':
				case 'gif':
				case 'png':
					return(true);
				default:
					return(false);
			}
		}
		else {
			switch ($extension_pVar) {
				case 'jpg':
				case 'gif':
				case 'png':
				case 'swf':
				case 'flv':
					return(true);
				default:
					return(false);
			}
		}
	}
	
	static public function getDefaultThumbnail_gFunc($extension_pVar)
	{
			switch ($extension_pVar) {
			case 'jpg':
			case 'gif':
			case 'png':
				return(main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . 'images/icons/image.png');
			case 'swf':
				return(main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . 'images/icons/flash_animation.jpg');
			case 'flv':
				return(main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . 'images/icons/flash_video.jpg');
			default:
				return(false);
		}		
	}

	function initImage_gFunc($imageOptionId_pVar)
	{
		$def_pVar = main_gClass::getFileDefProperties_gFunc($imageOptionId_pVar, 'image');
		if($def_pVar === false || !isset($def_pVar['images'])) {
			return(false);
		}


		$deleteOriginal_pVar = true;
		foreach ($def_pVar['images'] as $k_pVar=>$v_pVar) {
			if($k_pVar === 0) {
				$deleteOriginal_pVar = false;
			}
			$img_pVar = clone $this;
			$img_pVar->resizeImageToRectangle_gFunc($v_pVar['width'], $v_pVar['height']);
			$img_pVar->fileName_pVar = self::setFileNamePrefix_gFunc($k_pVar, $img_pVar->fileName_pVar);
			$img_pVar->saveImage_gFunc();
		}

		if($deleteOriginal_pVar) {
			unlink($this->fileName_pVar);
		}
		return(true);
	}
}

return(true);
