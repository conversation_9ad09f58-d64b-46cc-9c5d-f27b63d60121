<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__import_csv_pVar'])) return(true);
$GLOBALS['file__import_csv_pVar']=true;


class import_csv_gClass extends import_gClass
{
	protected function __construct($fileName_pVar)
	{
		parent::__construct($fileName_pVar);
	}
	
	protected function loadInfo_gFunc($useThisFileHandler_pVar = false)
	{
		$this->info_pVar = array();
		
		if($useThisFileHandler_pVar === false) {
			$f = fopen($this->fileName_pVar, 'rt');
		}
		else {
			$f = $useThisFileHandler_pVar;
		}
		
		if(!$f) {
			return(false);
		}

		$this->info_pVar = array();
		while(1) {
			$row_pVar = fgetcsv($f);
			if(!is_array($row_pVar)) {
				break;
			}
			if(!count($row_pVar) || empty($row_pVar[0])) {
				break;
			}
			$field_pVar = $row_pVar[0];
			$value_pVar = $row_pVar[1];
			if($field_pVar[strlen($field_pVar) - 1] !== ':') {
				break;
			}
			$field_pVar = substr($field_pVar, 0, -1);
			if($field_pVar === 'data') {
				$field_pVar = 'data_type';
			}
			if($field_pVar === 'item') {
				if(!isset($this->info_pVar[$field_pVar])) {
					$this->info_pVar[$field_pVar] = array();
				}
				$this->info_pVar[$field_pVar][$row_pVar[2]] = $value_pVar;  
			}
			else {
				$this->info_pVar[$field_pVar] = $value_pVar;
			}
		}
		
		if($useThisFileHandler_pVar === false) {
			fclose($f);
		}
		
        return($this->info_pVar);
	}
	
	public function _import_gFunc()
	{		
		$f = fopen($this->fileName_pVar, 'rt');
		if(!$f) {
			return(false);
		}
		$this->loadInfo_gFunc($f);

		$data_pVar = array('data'=>array(), 'fields'=>array());
		
		// najskor nacitam hlavicku
		while(!feof($f)) {
			$row_pVar = fgetcsv($f);
			if(!is_array($row_pVar)) {
				break;
			}
			if(count($row_pVar) < 2 || (empty($row_pVar[0]) && empty($row_pVar[1]))) {
				break;
			}
			$data_pVar['fields'][$row_pVar[0]] = $row_pVar;
			array_shift($data_pVar['fields'][$row_pVar[0]]);
		}

		// a teraz data
		$root_loaded_pVar = false;
		$tmp_pVar = array();
		while(!feof($f)) {
			$row_pVar = fgetcsv($f);
			if(!is_array($row_pVar)) {
				break;
			}
			if(count($row_pVar) < 2 || (empty($row_pVar[0]) && empty($row_pVar[1]))) {
				continue;
			}
			
			$row_type_pVar = array_shift($row_pVar);
			if($row_type_pVar == '') {
				if($root_loaded_pVar) {
					if(count($tmp_pVar)) {
						$data_pVar['data'][] = $tmp_pVar;
					}
					$tmp_pVar = array();
				}
				foreach($data_pVar['fields'][$row_type_pVar] as $k_pVar=>$v_pVar) {
					$tmp_pVar[$v_pVar] = $row_pVar[$k_pVar];
				}
				$root_loaded_pVar = true;
			}
			else {
				$row_type_name_pVar = $this->info_pVar['item'][$row_type_pVar];
				$tmp_tmp_pVar = array();
				foreach($data_pVar['fields'][$row_type_pVar] as $k_pVar=>$v_pVar) {
					$tmp_tmp_pVar[$v_pVar] = $row_pVar[$k_pVar];
				}
				if(count($tmp_tmp_pVar)) {
					if(!isset($tmp_pVar[$row_type_name_pVar])) {
						$tmp_pVar[$row_type_name_pVar] = array();
					}
					$tmp_pVar[$row_type_name_pVar][] = $tmp_tmp_pVar;
				}
			}
			
		}
		if(count($tmp_pVar)) {
			$data_pVar['data'][] = $tmp_pVar;
		}
		
        $data_pVar['data_type'] = $this->info_pVar['data_type'];
        
        //echo '<pre>'; print_r($data_pVar); echo '</pre>';
		fclose($f);
        $this->_import_data_gFunc($data_pVar);
        return(true);        
	}

}

return(true);
