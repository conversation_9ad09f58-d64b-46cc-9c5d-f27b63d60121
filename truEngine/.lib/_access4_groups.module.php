<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__access4_groups_pVar'])) return(true);
$GLOBALS['file__access4_groups_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}

if(!modules_gClass::isModuleRegistred_gFunc('access3_rights'))
{
    return(false);
}

class rights_groups_gClass
{

	static function loadRights_gFunc(&$rightsArray_pVar, $userID_pVar, $roleName_pVar, &$userGroups_pVar, &$from_pVar, $addGroups_pVar = false)
	{
		session_gClass::mergeRights_gFunc($rightsArray_pVar, db_access4_groups_gClass::loadRights_gFunc($userGroups_pVar, $userID_pVar, $roleName_pVar, $from_pVar, $addGroups_pVar));
	}
}

class db_access4_groups_gClass extends db_gClass
{
	static public function loadRights_gFunc(&$userGroups_pVar, $userID_pVar, $roleName_pVar, &$from_pVar, $addGroups_pVar = false)
	{
		if(!is_array($addGroups_pVar)) {
			$addGroups_pVar = array();
		}

		$ret_pVar = array();

		if(!empty($roleName_pVar)) {
			$query_string_pVar = 'SELECT * FROM `%taccess__groups_rights` as `a`
									LEFT JOIN `%taccess__groups_roles` as `agr` ON `agr`.`group_id`=`a`.`group_id`
									WHERE `agr`.`role_id`=%s';
			$data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__, $roleName_pVar);
			foreach ($data_pVar as $k_pVar=>$v_pVar) {
				if($v_pVar['group_right_state'] === 'allow') {
					$ret_pVar[(int)$v_pVar['group_right_id']] = true;
					session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['group_right_id'], true, 'role_group', $v_pVar['group_id']);
				}
				else {
					if($roleName_pVar != 'superuser') {
						$ret_pVar[(int)$v_pVar['group_right_id']] = false;
					}
					session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['group_right_id'], false, 'role_group', $v_pVar['group_id']);
				}
			}
		}

		if(is_numeric($userID_pVar) && $userID_pVar) {
			$query_string_pVar = 'SELECT * FROM `%taccess__groups_rights` as `a`
									LEFT JOIN `%taccess__groups_users` as `agu` ON `agu`.`group_id`=`a`.`group_id`
									WHERE `agu`.`user_id`=%d';
			$data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__, $userID_pVar);
			foreach ($data_pVar as $k_pVar=>$v_pVar) {
				if($v_pVar['group_right_state'] === 'allow') {
					if(!isset($ret_pVar[(int)$v_pVar['group_right_id']])) {
						$ret_pVar[(int)$v_pVar['group_right_id']] = true;
					}
					session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['group_right_id'], true, 'group', $v_pVar['group_id']);
				}
				else {
					if($roleName_pVar != 'superuser') {
						$ret_pVar[(int)$v_pVar['group_right_id']] = false;
					}
					session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['group_right_id'], false, 'group', $v_pVar['group_id']);
				}
			}
		}

		if(count($addGroups_pVar)) {
			$query_string_pVar = 'SELECT * FROM `%taccess__groups_rights` as `a`
									WHERE `a`.`group_id` IN (' . implode(',', $addGroups_pVar) . ')';
			$data_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__);
			foreach ($data_pVar as $k_pVar=>$v_pVar) {
				if($v_pVar['group_right_state'] === 'allow') {
					if(!isset($ret_pVar[(int)$v_pVar['group_right_id']])) {
						$ret_pVar[(int)$v_pVar['group_right_id']] = true;
					}
					session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['group_right_id'], true, 'group', $v_pVar['group_id']);
				}
				else {
					if($roleName_pVar != 'superuser') {
						$ret_pVar[(int)$v_pVar['group_right_id']] = false;
					}
					session_session_gClass::setFromRights_gFunc($from_pVar, $v_pVar['group_right_id'], false, 'group', $v_pVar['group_id']);
				}
			}
		}

		return($ret_pVar);
	}

	static function getGroups_gFunc($index_by_pVar = false, $type_id_only_pVar = false)
	{
		$sql_pVar = 'SELECT *, `g`.`'.main_gClass::getLanguage_gFunc().'_group_name` as `group_name`, `t`.`'.main_gClass::getLanguage_gFunc().'_group_type_name` as `group_type_name` FROM `%taccess__groups` as `g`
						LEFT JOIN `%taccess__groups_types` as `t` ON `t`.`group_type_id` = `g`.`group_type_id`';
		if($type_id_only_pVar !== false) {
			$sql_pVar .= ' WHERE `g`.`group_type_id` = %d';
		}
		$sql_pVar .= ' ORDER BY `g`.`group_type_id`, `group_name`';
		$groups_pVar = self::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $type_id_only_pVar, $index_by_pVar);

		return($groups_pVar);
	}

	static function updateUserGroups_gFunc($user_id_pVar)
	{
		// este updatnem usera
		$sql_pVar = 'SELECT * FROM `%taccess__groups_users` where `user_id` = %d';
		$tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($user_id_pVar));
		$newRoles_pVar = array();
		if(is_array($tmp_pVar)) {
			foreach($tmp_pVar as $vv_pVar) {
				$newRoles_pVar[] = $vv_pVar['group_id'];
			}
		}
		$ugroups_pVar = ','.implode(',', $newRoles_pVar).',';
		$userData_pVar = array('item_id'=>$user_id_pVar, 'user_groups'=>$ugroups_pVar);
		items_gClass::saveOrUpdateItem_gFunc('users', $userData_pVar);
	}
}

class list_groups_gClass extends table_gClass
{
	protected function initTable_gFunc()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_groups)) {
			return(array());
		}
		$data_pVar = db_access4_groups_gClass::getGroups_gFunc();
		$this->setData_gFunc($data_pVar);
		if(isset($this->params['columns'])) {
			$this->setColumnsFromString_gFunc($this->params['columns']);
		}
	}
}

class list_groups extends list_groups_gClass {}

class groups_gClass extends source_gClass
{
	protected function getData()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_groups)) {
			return(array());
		}

		$groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');
		return($groups_pVar);
	}
}

class groups extends groups_gClass {}

class list_groups_roles_gClass extends table_gClass
{
	protected function initTable_gFunc()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_group_roles)) {
			return(array());
		}
		$groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');
		if(!isset($this->params['group_id']) || !isset($groups_pVar[$this->params['group_id']])) {
			return(array());
		}
		$group_pVar = $groups_pVar[$this->params['group_id']];

		$roles_pVar = db_access3_rights_gClass::getRoles_gFunc('name');

		if(isset($this->params['role_id_add']) && !empty($this->params['role_id_add']) && intval($groups_pVar[$this->params['group_id']]['group_type_id']) == 1) {
			if(session_gClass::userHasRightsAccess_gFunc(s_system_add_group_role)) {
				// najskor zistim, ci tam uz taka rola nie je
				$sql_pVar = 'SELECT * FROM `%taccess__groups_roles` WHERE `group_id`=%d AND `role_id`=%s';
				if(db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id'], $this->params['role_id_add'])) === true) {
					// nie je, pridam ju
					$sql_pVar = 'INSERT INTO `%taccess__groups_roles` (`group_id`,`role_id`) VALUES(%d,%s)';
					$n_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id'], $this->params['role_id_add']), true);
					if($n_pVar > 0) {
						$data_pVar = array(
							'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
							'record_time'=>'now()',
							'record_type1'=>'role',
							'record_type2'=>'group_add',
							'record_data'=>$this->params['group_id'] . '|' . $this->params['role_id_add']
						);
						db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
					}
				}
			}
		}

		if(isset($this->params['delete']) && !empty($this->params['delete']) && intval($groups_pVar[$this->params['group_id']]['group_type_id']) == 1) {
			if(session_gClass::userHasRightsAccess_gFunc(s_system_delete_group_role)) {
				if(isset($roles_pVar[$this->params['delete']])) {
					$sql_pVar = 'DELETE FROM `%taccess__groups_roles` WHERE `group_id` = %d AND `role_id` = %s';
					$n_pVar = db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id'], $this->params['delete']), true);
					if($n_pVar > 0) {
						$data_pVar = array(
							'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
							'record_time'=>'now()',
							'record_type1'=>'role',
							'record_type2'=>'group_delete',
							'record_data'=>$this->params['group_id'] . '|' . $this->params['delete']
						);
						db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
					}
				}
			}
		}

		/* tento select by to poriesil, ale aj tak mam uz vyselectovane roly, takze to zriesim cez php
		$sql_pVar = 'SELECT `v`.`' . main_gClass::getLanguage_gFunc() . '_enum_field_name_item` as `role_name` FROM `%taccess__groups_roles` AS `a`
						LEFT JOIN `%titems_users__values` as `v` ON `v`.`enum_field_value` = `a`.`role_id`
						LEFT JOIN `%titems_users__fields` as `f` ON `v`.`enum_field_id` = `f`.`field_id`
						WHERE `f`.`tag` = \'user_role\'';
		*/
		$sql_pVar = 'SELECT * FROM `%taccess__groups_roles` WHERE `group_id` = %d';
		$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $this->params['group_id']);
		foreach($data_pVar as $k_pVar=>$v_pVar) {
			if(isset($roles_pVar[$v_pVar['role_id']])) {
				$data_pVar[$k_pVar]['role_name'] = $roles_pVar[$v_pVar['role_id']]['title'];
			}
			else {
				$data_pVar[$k_pVar]['role_name'] = $v_pVar['role_id'];
			}
		}

		$data_pVar['_title'] = $group_pVar['group_name'];
		$data_pVar['_roles'] = $roles_pVar;
		$data_pVar['_this_group'] = $groups_pVar[$this->params['group_id']];

		foreach($data_pVar as $k_pVar=>$v_pVar) {
			if(substr($k_pVar, 0, 1) == '_') {
				continue;
			}
			unset($data_pVar['_roles'][$v_pVar['role_id']]);
		}

		$this->setData_gFunc($data_pVar);
		if(isset($this->params['columns'])) {
			$this->setColumnsFromString_gFunc($this->params['columns']);
		}
	}
}

class list_groups_roles extends list_groups_roles_gClass {}


class list_user_groups_gClass extends table_gClass
{
	protected function initTable_gFunc()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_user_groups)) {
			return(array());
		}

		$groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');

		if(!isset($this->params['user_id']) || !is_numeric($this->params['user_id'])) {
			return(array());
		}
		$sql_pVar = 'SELECT `login` FROM `%titems_users__data` WHERE `item_id`=%d';
		$user_login_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $this->params['user_id']);
		if(empty($user_login_pVar)) {
			return(array());
		}

		if(isset($this->params['group_id_add']) && !empty($this->params['group_id_add']) && intval($groups_pVar[$this->params['group_id_add']]['group_type_id']) == 1) {
			if(session_gClass::userHasRightsAccess_gFunc(s_system_add_user_group)) {
				// najskor zistim, ci tam uz taka gruppa nie je
				$sql_pVar = 'SELECT * FROM `%taccess__groups_users` WHERE `group_id`=%d AND `user_id`=%d';
				if(db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id_add'], $this->params['user_id'])) === true) {
					// nie je, pridam ju
					$sql_pVar = 'INSERT INTO `%taccess__groups_users` (`group_id`,`user_id`) VALUES(%d,%d)';
					$n_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id_add'], $this->params['user_id']), true);
					if($n_pVar > 0) {
						$data_pVar = array(
							'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
							'record_time'=>'now()',
							'record_type1'=>'user',
							'record_type2'=>'group_add',
							'record_data'=>$this->params['group_id_add'] . '|' . $this->params['user_id']
						);
						db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
						db_access4_groups_gClass::updateUserGroups_gFunc($this->params['user_id']);
					}
				}
			}
		}

		if(isset($this->params['delete']) && !empty($this->params['delete']) && intval($groups_pVar[$this->params['delete']]['group_type_id']) == 1) {
			if(session_gClass::userHasRightsAccess_gFunc(s_system_delete_user_group)) {
				if(isset($groups_pVar[$this->params['delete']])) {
					$sql_pVar = 'DELETE FROM `%taccess__groups_users` WHERE `group_id` = %d AND `user_id` = %d';
					$n_pVar = db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['delete'], $this->params['user_id']), true);
					if($n_pVar > 0) {
						$data_pVar = array(
							'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
							'record_time'=>'now()',
							'record_type1'=>'user',
							'record_type2'=>'group_delete',
							'record_data'=>$this->params['delete'] . '|' . $this->params['user_id']
						);
						db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
						db_access4_groups_gClass::updateUserGroups_gFunc($this->params['user_id']);
					}
				}
			}
		}

		$sql_pVar = 'SELECT * FROM `%taccess__groups_users` WHERE `user_id` = %d';
		$data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $this->params['user_id']);
		foreach($data_pVar as $k_pVar=>$v_pVar) {
			if(isset($groups_pVar[$v_pVar['group_id']])) {
				$data_pVar[$k_pVar]['group_name'] = $groups_pVar[$v_pVar['group_id']]['group_name'];
			}
			else {
				$data_pVar[$k_pVar]['group_name'] = $v_pVar['group_id'];
			}
		}

		$data_pVar['_title'] = $user_login_pVar;
		$data_pVar['_groups'] = $groups_pVar;
		foreach($data_pVar['_groups'] as $k_pVar=>$v_pVar) {
			if($v_pVar['group_type_id'] != 1) {
				unset($data_pVar['_groups'][$k_pVar]);
			}
		}

		foreach($data_pVar as $k_pVar=>$v_pVar) {
			if(substr($k_pVar, 0, 1) == '_') {
				continue;
			}
			unset($data_pVar['_groups'][$v_pVar['group_id']]);
		}

		$this->setData_gFunc($data_pVar);
		if(isset($this->params['columns'])) {
			$this->setColumnsFromString_gFunc($this->params['columns']);
		}
	}
}

class list_user_groups extends list_user_groups_gClass {}




class addgroup_gClass extends form_gClass
{
	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		$this->addField_gFunc('', 'sk_group_name', 'varchar', '[sk] Názov skupiny', true);
		if(isset($this->params['submit_button_title'])) {
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title']);
		}
	}

	protected function getData()
	{
		$formData_pVar = $this->getFormData_gFunc();
		if($formData_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($formData_pVar);
		}

		if(session_gClass::userHasRightsAccess_gFunc(s_system_add_group)) {
			$data_pVar = array(
				'group_type_id'=>1,
				'sk_group_name'=>$this->getFieldValue_gFunc('sk_group_name')
			);
			$group_id_pVar = db_public_gClass::insertData_gFunc('%taccess__groups', '%d,%s', $data_pVar, __FILE__, __LINE__, true);

			$data_pVar = array(
				'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
				'record_time'=>'now()',
				'record_type1'=>'group',
				'record_type2'=>'add',
				'record_data'=>$group_id_pVar . '|sk' . $this->getFieldValue_gFunc('sk_group_name')
			);
			db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
		}

		return(parent::getData());
	}
}

class addgroup extends addgroup_gClass {}

class deletegroup_gClass extends form_gClass
{
	protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
	{
		$this->addHiddenField_gFunc('group_id', $this->params['group_id']);
		if(isset($this->params['submit_button_title'])) {
			$this->setVar_gFunc('submit_button_title', $this->params['submit_button_title']);
		}
	}

	protected function getData()
	{
		$formData_pVar = $this->getFormData_gFunc();
		if($formData_pVar['error_code'] !== self::RESULT_OK_pVar) {
			return($formData_pVar);
		}

		$group_id_pVar = $this->getFieldValue_gFunc('group_id');

		$groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');
		if(isset($groups_pVar[$group_id_pVar]) && intval($groups_pVar[$group_id_pVar]['group_type_id']) == 1) {
			if(session_gClass::userHasRightsAccess_gFunc(s_system_delete_group)) {
				$sql_pVar = 'DELETE FROM `%taccess__groups` WHERE `group_id` = %d';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $group_id_pVar);
				$sql_pVar = 'DELETE FROM `%taccess__groups_rights` WHERE `group_id` = %d';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $group_id_pVar);
				$sql_pVar = 'DELETE FROM `%taccess__groups_roles` WHERE `group_id` = %d';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $group_id_pVar);
				$sql_pVar = 'DELETE FROM `%taccess__groups_users` WHERE `group_id` = %d';
				db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $group_id_pVar);

				$data_pVar = array(
					'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
					'record_time'=>'now()',
					'record_type1'=>'group',
					'record_type2'=>'delete',
					'record_data'=>$group_id_pVar
				);
				db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
			}
		}

		return(parent::getData());
	}
}

class deletegroup extends deletegroup_gClass {}

class list_group_rights_gClass extends list_role_rights_gClass
{
	protected function getData()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_group_rights)) {
			return(array());
		}
		if(!isset($this->params['group_id']) || !is_numeric($this->params['group_id'])) {
			return(array('title'=>'??'));
		}

		$ret_pVar = array();

		$groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');

		if(!isset($groups_pVar[$this->params['group_id']])) {
			return(array('title'=>'?'));
		}

		$this->saveRequestData_gFunc('%taccess__groups_rights', 'group_right', s_system_edit_group_rights, array('group_id'=>$this->params['group_id']));

		$ret_pVar['title'] = $groups_pVar[$this->params['group_id']]['group_name'];

		$sql_pVar = 'SELECT `n`.*, `c`.*, `c`.`'.main_gClass::getLanguage_gFunc().'_category_name` as `category_name`, `n`.`'.main_gClass::getLanguage_gFunc().'_name` as `name`  FROM `%taccess__names` as `n`
						LEFT JOIN `%taccess__names_categories` as `c` ON `n`.`access_category_id`=`c`.`access_category_id`
						ORDER BY `c`.`'.main_gClass::getLanguage_gFunc().'_category_name`, `n`.`'.main_gClass::getLanguage_gFunc().'_name`';
		$ret_pVar['actions'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false);
		$ret_pVar['actions_index'] = array();
		foreach($ret_pVar['actions'] as $k_pVar=>$action_pVar) {
			if(!empty($action_pVar['module'])) {
				$modules_pVar = explode(',', $action_pVar['module']);
				$ok_pVar = false;
				foreach ($modules_pVar as $vv_pVar) {
					if(modules_gClass::isModuleRegistred_gFunc($vv_pVar, false)) {
						$ok_pVar = true;
						break;
					}
				}
				if(!$ok_pVar) {
					unset($ret_pVar['actions'][$k_pVar]);
					continue;
				}
			}
			elseif(!empty($action_pVar['category_module'])) {
				$modules_pVar = explode(',', $action_pVar['category_module']);
				$ok_pVar = false;
				foreach ($modules_pVar as $vv_pVar) {
					if(modules_gClass::isModuleRegistred_gFunc($vv_pVar, false)) {
						$ok_pVar = true;
						break;
					}
				}
				if(!$ok_pVar) {
					unset($ret_pVar['actions'][$k_pVar]);
					continue;
				}
			}
			$ret_pVar['actions_index'][$action_pVar['access_id']] = $k_pVar;
		}

		list($rights_pVar, $groups_pVar, $from_pVar) = session_session_gClass::getUserRightsFromDb_gFunc(false, false, array($this->params['group_id']));
		$ret_pVar['rights'] = $rights_pVar;
		$ret_pVar['from'] = $from_pVar;

		return($ret_pVar);
	}
}

class list_group_rights extends list_group_rights_gClass {}

class group_add_users_gClass extends source_gClass
{
	protected function getData()
	{
		if(!session_gClass::userHasRightsAccess_gFunc(s_system_add_group_user)) {
			return(array());
		}

		if(!isset($this->params['users_add']) || empty($this->params['users_add'])) {
			return(array());
		}

		if(!isset($this->params['group_id']) || empty($this->params['group_id']) || !is_numeric($this->params['group_id'])) {
			return(array());
		}

		$groups_pVar = db_access4_groups_gClass::getGroups_gFunc('group_id');
		if(!isset($groups_pVar[$this->params['group_id']]) || $groups_pVar[$this->params['group_id']]['group_type_id'] != 1) {
			return(array());
		}

		$users_pVar = explode(',', $this->params['users_add']);
		foreach($users_pVar as $v_pVar) {
			if(empty($v_pVar) || !is_numeric($v_pVar)) {
				continue;
			}
			$user_id_pVar = $v_pVar;

			// najskor zistim, ci tam uz taky user nie je
			$sql_pVar = 'SELECT * FROM `%taccess__groups_users` WHERE `group_id`=%d AND `user_id`=%d';
			if(db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id'], $user_id_pVar)) === true) {
				// nie je, pridam ho
				$sql_pVar = 'INSERT INTO `%taccess__groups_users` (`group_id`,`user_id`) VALUES(%d,%d)';
				$n_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['group_id'], $user_id_pVar), true);
				if($n_pVar > 0) {
					$data_pVar = array(
						'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
						'record_time'=>'now()',
						'record_type1'=>'user',
						'record_type2'=>'group_add',
						'record_data'=>$this->params['group_id'] . '|' . $user_id_pVar
					);
					db_public_gClass::insertData_gFunc('%taccess__log', '%d,%r,%s,%s,%s', $data_pVar, __FILE__, __LINE__);
				}
				db_access4_groups_gClass::updateUserGroups_gFunc($user_id_pVar);
			}
		}
	}
}

class group_add_users extends group_add_users_gClass {}


return(true);
