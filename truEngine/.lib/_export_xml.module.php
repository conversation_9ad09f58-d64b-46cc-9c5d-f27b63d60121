<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__export_xml_pVar'])) return(true);
$GLOBALS['file__export_xml_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('export'))
{
    return(false);
}

class export_xml_gClass extends export_gClass
{
	private $nOffset_pVar;
	private $attributes_pVar;
	private $files_md5_pVar;
	
	function __construct($settings_pVar)
	{
		parent::__construct($settings_pVar);
		$this->right_pVar = s_system_export_data_xml;
		$this->nOffset_pVar = 0;
		$this->attributes_pVar = array('item_id',
									   'access_time',
									   'access_type',
									   'status',
									   'owner_id',
									   'insert_time',
									   'update_time',
									   'user_id',
								);
		$this->files_md5_pVar = array();
	}
	
	protected function export_open_gFunc()
	{
		$this->result_pVar->echo_gFunc('<' . '?xml version="1.0" encoding="' . $this->settings_pVar['encoding'] . '" ?>' . NL);
		$this->result_pVar->echo_gFunc('<truEngine-export>' . NL);
		$this->result_pVar->echo_gFunc(' <truEngine-export-settings>' . NL);
		foreach ($this->settings_pVar as $k_pVar=>$v_pVar) {
			$this->result_pVar->echo_gFunc('  <'.$k_pVar.'>');
			if($k_pVar === 'item_ids') {
				$this->result_pVar->echo_gFunc(implode(',', array_keys($v_pVar)));
			}
			else {
				$this->result_pVar->echo_gFunc($v_pVar);
			}
			$this->result_pVar->echo_gFunc('</'.$k_pVar.'>' . NL);
		}
		$this->result_pVar->echo_gFunc(' </truEngine-export-settings>' . NL);
		$this->result_pVar->echo_gFunc(' <truEngine-export-data>' . NL);
		$this->nOffset_pVar = 3;

		return(true);
	}
	
	protected function export_close_gFunc()
	{
		$this->result_pVar->echo_gFunc(' </truEngine-export-data>' . NL);
		$this->result_pVar->echo_gFunc('</truEngine-export>' . NL);
		return(true);
	}
	
	protected function export_data_gFunc()
	{
		$data_pVar = $this->getData_gFunc();
		$this->writeXml_gFunc($data_pVar, $this->nOffset_pVar);
		return(true);
	}
	
	/**
	 * Funkcia zapisuje XML priamo do BUFFRU.
	 * @param $data_pVar
	 * @param $nOffset_pVar
	 * @return unknown_type
	 */
	private function writeXml_gFunc(&$data_pVar, $nOffset_pVar)
	{
		$offsetStr_pVar = str_repeat(' ', $nOffset_pVar);
		if(!is_array($data_pVar)) {
			$this->result_pVar->echo_gFunc($data_pVar);
			return;
		}
		foreach ($data_pVar as $k_pVar=>$v_pVar) {
			if(is_numeric($k_pVar)) {
				$k_pVar = 'rec';
			}
			if(is_array($v_pVar)) {
				$tmp_pVar = '';
				// attributy
				foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
					if($vv_pVar === null) {
						unset($v_pVar[$kk_pVar]);
						continue;
					}
					if(!is_array($vv_pVar) && array_search($kk_pVar, $this->attributes_pVar) !== false) {
						$tmp_pVar .= ' ' . $kk_pVar . '="' . $vv_pVar . '"';
						unset($v_pVar[$kk_pVar]);
					}
				}
				if(!count($v_pVar) && !empty($tmp_pVar)) {
					$this->result_pVar->echo_gFunc(NL . $offsetStr_pVar . '<' . $k_pVar . $tmp_pVar . ' />');
				}
				else {
					if(count($v_pVar)) {
						$this->result_pVar->echo_gFunc(NL . $offsetStr_pVar . '<' . $k_pVar . $tmp_pVar . '>');
						$this->writeXml_gFunc($v_pVar, $nOffset_pVar + 1);
						$this->result_pVar->echo_gFunc(NL . $offsetStr_pVar . '</' . $k_pVar . '>');
					}
				}
			}
			else {
				if($v_pVar === null) {
					continue;
				}
				if($k_pVar === '_TE:file_content') {
					if(file_exists($v_pVar)) {
						$md5_pVar = md5_file($v_pVar);
						if(!isset($this->files_md5_pVar[$md5_pVar])) {
							$file_content_pVar = file_get_contents($v_pVar);
						}
						else {
							$file_content_pVar = '';
						}
					}
					else {
						$file_content_pVar = '';
						$md5_pVar = '';
					}
					$this->files_md5_pVar[$md5_pVar] = true;
					$this->result_pVar->echo_gFunc(NL . $offsetStr_pVar . '<content md5="' . $md5_pVar . '">' . base64_encode($file_content_pVar) . '</content>');
					unset($file_content_pVar);
				}
				else {
					$this->result_pVar->echo_gFunc(NL . $offsetStr_pVar . '<' . $k_pVar . '>' . $v_pVar . '</' . $k_pVar . '>');
				}
			}
		}
	}
}


return(true);
