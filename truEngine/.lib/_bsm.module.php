<?php
if(isset($_SERVER['SCRIPT_FILENAME']) && strtolower($_SERVER['SCRIPT_FILENAME']) == strtolower(str_replace("\\",'/',__FILE__))) { header("HTTP/1.0 404 Not Found");  return(false); }
if(isset($GLOBALS['file__bsm_pVar'])) return(true);
$GLOBALS['file__bsm_pVar']=true;

if(!modules_gClass::isModuleRegistred_gFunc('db'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('forms'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('items'))
{
    return(false);
}
if(!modules_gClass::isModuleRegistred_gFunc('access2_session'))
{
    return(false);
}

class adduserbsm_gClass extends additem_gClass
{
	protected function initParams_gFunc()
	{
		$this->setParam_gFunc('formtype-add','add_item');
		$this->setParam_gFunc('formtype-edit', 'edit_item');
		$this->setParam_gFunc('itemtype', 'users');
	}
	
	protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar)
	{
		$result_pVar = parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar);

		if($result_pVar === true) {
			if($fieldName_pVar === 'users_login' || $fieldName_pVar === 'users_nick') {
				// zistim ci uz existuje takyto login alebo nick
				if(!session_session_gClass::checkForUniqueLoginName_gFunc($this->getFieldValue_gFunc($fieldName_pVar), $this->getVar_gFunc('item_id'))) {
					// error
					$this->setError_gFunc(string_gClass::get('str__session_login_exists_sVar'), $fieldName_pVar);
					return(false);
				}
			}
		}
		
		// vypocitam a overim sign
		
		$this->setVar_gFunc('login', main_gClass::getInputString_gFunc('users_login', main_gClass::SRC_POST_pVar));
			
		$sign_pVar = 'giuGmhmdhYioDhoi';
		$sign_pVar .= '&users_item_id='.main_gClass::getInputString_gFunc('users_item_id', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_url_filter='.main_gClass::getInputString_gFunc('users_url_filter', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&adduserbsm_data='.main_gClass::getInputString_gFunc('adduserbsm_data', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_user_role='.main_gClass::getInputString_gFunc('users_user_role', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_login='.main_gClass::getInputString_gFunc('users_login', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_timeout='.main_gClass::getInputString_gFunc('users_timeout', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_nick='.main_gClass::getInputString_gFunc('users_nick', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_first_name='.main_gClass::getInputString_gFunc('users_first_name', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_last_name='.main_gClass::getInputString_gFunc('users_last_name', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_titul_pred='.main_gClass::getInputString_gFunc('users_titul_pred', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_titul_za='.main_gClass::getInputString_gFunc('users_titul_za', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_gender='.main_gClass::getInputString_gFunc('users_gender', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_date_of_birth_d='.main_gClass::getInputString_gFunc('users_date_of_birth_d', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_date_of_birth_m='.main_gClass::getInputString_gFunc('users_date_of_birth_m', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_date_of_birth_y='.main_gClass::getInputString_gFunc('users_date_of_birth_y', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_email='.main_gClass::getInputString_gFunc('users_email', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_street='.main_gClass::getInputString_gFunc('users_street', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_city='.main_gClass::getInputString_gFunc('users_city', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_country='.main_gClass::getInputString_gFunc('users_country', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_mobil='.main_gClass::getInputString_gFunc('users_mobil', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_icq='.main_gClass::getInputString_gFunc('users_icq', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_msn='.main_gClass::getInputString_gFunc('users_msn', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_skype='.main_gClass::getInputString_gFunc('users_skype', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_isic='.main_gClass::getInputString_gFunc('users_isic', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_rocnik='.main_gClass::getInputString_gFunc('users_rocnik', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_smer='.main_gClass::getInputString_gFunc('users_smer', main_gClass::SRC_POST_pVar);
		$sign_pVar .= '&users_password_uk='.main_gClass::getInputString_gFunc('users_password_uk', main_gClass::SRC_POST_pVar);
		
		$sign_pVar = sha1($sign_pVar);
		$this->setVar_gFunc('sign_bsm', main_gClass::getInputString_gFunc('sign', main_gClass::SRC_POST_pVar));
		$this->setVar_gFunc('sign_kega', $sign_pVar);
		
		if($sign_pVar != main_gClass::getInputString_gFunc('sign', main_gClass::SRC_POST_pVar)) {
			$this->setError_gFunc(string_gClass::get('str__session_sign_error_sVar'), $fieldName_pVar);
			return(false);
		}
		
		return($result_pVar);
	}
	
	protected function saveData_gFunc()
	{
		// najskor pridam pouzivatelovi docasne prava na vytvorenie pouzivatela
		session_gClass::setTemporaryRights_gFunc(s_users_add_user);
		
		parent::saveData_gFunc();
	}
}

class adduserbsm extends adduserbsm_gClass {}


return(true);
