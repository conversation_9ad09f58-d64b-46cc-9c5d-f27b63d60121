<?php

use Mixedtype\Tracker\Tracker;

class db_gClass extends truEngineBaseClass_gClass {
    private static $connectionObject_pVar = false;
    private static $cache_pVar = array();
    protected static $tbPrefix_pVar = false;

    const DBTYPE_STRING_pVAr = 1;
    const DBTYPE_INTEGER_pVar = 2;
    const DBTYPE_FLOAT_pVar = 3;
    const DBTYPE_BOOLEAN_pVar = 4;
    const DBTYPE_NULL_pVar = 5;



    static function legacyQueryLog($query_str, $bindings, $start_microtime)
    {
        $time = microtime(true) - $start_microtime;
        Tracker::getInstance()->trackDb([
            'query' => $query_str,
            'bindings' => $bindings,
            'duration' => round($time, 9),
            'connection' => 'legacy',
        ]);
    }


    /**
     * Vracia prefix databazovych tabuliek
     *
     * @return unknown
     */
    protected static function getTbPrefix_gFunc()
    {
        if(self::$tbPrefix_pVar !== false) {
            return(self::$tbPrefix_pVar);
        }
        self::$tbPrefix_pVar = main_gClass::getConfigVar_gFunc('table_prefix', 'db');
        return(self::$tbPrefix_pVar);
    }

    /**
     * Inicializuje self::$connectionObject_pVar podla konfiguracie
     * Objekt je pripraveny na pripojenie k databaze
     *
     * @return unknown
     */
    protected static function init_gFunc()
    {
        if(self::$connectionObject_pVar !== false) {
            if(self::$connectionObject_pVar->getConnectionStatus_gFunc()) {
                return(true);
            }
            else {
                return(false);
            }
        }
        self::$connectionObject_pVar = mysql_connection_gClass::getObject_gFunc();
        $loginName_pVar = main_gClass::getConfigVar_gFunc('username', 'db');
        $password_pVar = main_gClass::getConfigVar_gFunc('password', 'db');
        $hostName_pVar = main_gClass::getConfigVar_gFunc('hostname', 'db');
        $port_pVar = main_gClass::getConfigVar_gFunc('port', 'db');
        $databaseName_pVar = main_gClass::getConfigVar_gFunc('database', 'db');
        $charset_pVar = main_gClass::getConfigVar_gFunc('charset', 'db');
        $tbPrefix_pVar = main_gClass::getConfigVar_gFunc('table_prefix', 'db');


        self::$connectionObject_pVar->setConnectionParams_gFunc($hostName_pVar, $loginName_pVar, $password_pVar,
            $databaseName_pVar, $charset_pVar, self::getTbPrefix_gFunc(), $port_pVar);

        return(true);
    }

    /**
     * zacachuje data. Data su dostupne metodou getCachedResult, pomocou $cacheName_pVar
     *
     * @param unknown_type $cacheName_pVar
     * @param unknown_type $resultVar_pVar
     */
    public static function cacheResult_gFunc($cacheName_pVar, &$resultVar_pVar)
    {
        self::$cache_pVar[$cacheName_pVar] = $resultVar_pVar;
    }

    /**
     * ziskanie zacachovaych dat. Ak data s danym menom neexistuju, vratim false.
     *
     * @param unknown_type $cacheName_pVar
     * @return unknown
     */
    public static function getCachedResult_gFunc($cacheName_pVar)
    {
        if(isset(self::$cache_pVar[$cacheName_pVar])) {
            return(self::$cache_pVar[$cacheName_pVar]);
        }
        else {
            return(false);
        }
    }

    /**
     * Vymazanie zacachovanych dat.
     *
     * @param unknown_type $cacheName_pVar
     */
    public static function clearCache_gFunc($cacheName_pVar = false)
    {
        if($cacheName_pVar === false) {
            self::$cache_pVar = array();
        }
        else {
            unset(self::$cache_pVar[$cacheName_pVar]);
        }
    }

    /**
     * zacatie transakcie.
     * Vracia true/false
     *
     * @param unknown_type $fileName_pVar
     * @param unknown_type $fileLine_pVar
     * @return unknown
     */
    protected static function startTransaction_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if(!self::init_gFunc()) {
            return(false);
        }
        $ret_pVar = self::$connectionObject_pVar->startTransaction_gFunc($fileName_pVar, $fileLine_pVar);
        if($ret_pVar === mysql_connection_gClass::MYSQL_TRANS_ERROR_pVar) {
            return(false);
        }
        else {
            return(true);
        }
    }

    /**
     * Commit transakcie.
     * Do databazy posle commit iba ak ide o korenovu transakciu.
     *
     * @param unknown_type $fileName_pVar
     * @param unknown_type $fileLine_pVar
     * @return unknown
     */
    protected static function commit_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if(!self::init_gFunc()) {
            return(false);
        }
        $ret_pVar = self::$connectionObject_pVar->commit_gFunc($fileName_pVar, $fileLine_pVar);
        if($ret_pVar === mysql_connection_gClass::MYSQL_TRANS_ERROR_pVar) {
            return(false);
        }
        else {
            return(true);
        }
    }

    /**
     * rollback transakcie.
     * Vykona rollback aktualnej transakcie (posle hned rollback na databazu).
     * Ostatne commity budu prevadzane ako ROLLBACK, az po ukoncenie korenovej transakcie.
     *
     * @param unknown_type $fileName_pVar
     * @param unknown_type $fileLine_pVar
     * @return unknown
     */
    protected static function rollback_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if(!self::init_gFunc()) {
            return(false);
        }
        $ret_pVar = self::$connectionObject_pVar->rollback_gFunc($fileName_pVar, $fileLine_pVar);
        if($ret_pVar === mysql_connection_gClass::MYSQL_TRANS_ERROR_pVar) {
            return(false);
        }
        else {
            return(true);
        }
    }

    /**
     * vracia x riadkov databazy ako pole..
     * K stlpcom pristupujem result[x]['column_name']
     *
     * Ak zadam $indexBy_pVar (nazov stlpca), pole je indexovane podla hodnot tohto stlpca. (Ak su duplicitne hodnoty, su prepisane)
     * Ak napr. $indexBy_pVar = 'id', tak potom result[$id]['column_name']
     *
     * indexByType moze byt
     * 		0 -
     * 		1 - inteligentny
     *
     */
    protected static function getResultArray_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $indexBy_pVar = false, $indexByType_pVar = 0)
    {
        $dd=false;
        if(isset($GLOBALS['havala'])) {
            if(substr($query_string_pVar, 0, strlen('SELECT `D`.')) === 'SELECT `D`.') {
                $dd=true;
            }
        }


        if(!self::init_gFunc()) {
            return(false);
        }

        $columns = [];
        $queryStartTime = microtime(true);
        $rs = Illuminate\Support\Facades\DB::getPdo()->query(self::$connectionObject_pVar->processParams($query_string_pVar, $params_pVar));
        self::legacyQueryLog($query_string_pVar, $params_pVar, $queryStartTime);
        for ($i = 0; $i < $rs->columnCount(); $i++) {
            $col = $rs->getColumnMeta($i);
            $columns[] = $col;
        }
        $res = $rs->fetchAll(PDO::FETCH_NUM);
        if(!is_array($res)) {
            return false;
        }


        $nRows_pVar = 0;
        $ret_pVar = array();
        $fields_tables_pVar = array();
        $firstTable_pVar = false;
        $fields_prefixes_pVar = array();
        foreach($res as $row_tmp_pVar) {
            $nRows_pVar++;
            $row_pVar = array();
            foreach ($row_tmp_pVar as $k_pVar=>$v_pVar) {
                if(!is_numeric($k_pVar)) {
                    continue;
                }
                $column_offset_pVar = (int)$k_pVar;
                $k_pVar = $columns[(int)$k_pVar]['name'];
                /*
                if(isset($row_pVar[$k_pVar])) {
                    $n_pVar = 1;
                    while(isset($row_pVar[$k_pVar . '_' . $n_pVar])) {
                        $n_pVar++;
                    }
                    $row_pVar[$k_pVar . '_' . $n_pVar] = $v_pVar;
                    if($nRows_pVar === 1) {
                        $fields_tables_pVar[$k_pVar . '_' . $n_pVar] = mysql_field_table($result_pVar, $column_offset_pVar);
                        if($firstTable_pVar === false) {
                            $firstTable_pVar = $fields_tables_pVar[$k_pVar . '_' . $n_pVar];
                        }
                    }
                }
                else {*/

                if($nRows_pVar === 1) {
                    $ctable_pVar = $columns[$column_offset_pVar]['table'];
                    if($firstTable_pVar === false) {
                        $firstTable_pVar = $ctable_pVar;
                    }

                    if($indexByType_pVar === 1 && $ctable_pVar !== $firstTable_pVar && substr($ctable_pVar, 0, 2) !== 'TX') {
                        $fields_prefixes_pVar[$column_offset_pVar] = $ctable_pVar . '.';
                        $fields_tables_pVar[$fields_prefixes_pVar[$column_offset_pVar] . $k_pVar] = $columns[$column_offset_pVar]['table'];
                    }
                    else {
                        $fields_prefixes_pVar[$column_offset_pVar] = '';
                        $fields_tables_pVar[$fields_prefixes_pVar[$column_offset_pVar] . $k_pVar] = false;
                    }
                }

                $row_pVar[$fields_prefixes_pVar[$column_offset_pVar] . $k_pVar] = $v_pVar;


            }
            if($indexBy_pVar !== false) {

                /*
                                mohol by som rozpoznat tabulky, a typ spojenia medzi tabulkami (podla indexov)
                                ak je spojenie 1:1, tak na fieldoch nerobim polia..
                                ak je spojenie 1:n alebo m:n, tak VZDY robim fieldy ako polia, aj keby mali obsahovat iba jednu polozku.
                                mysql_fetch_field()
                */

                if(!isset($row_pVar[$indexBy_pVar])) {
                    $ret_pVar[] = $row_pVar;
                }
                else {
                    $keyValue_pVar = $row_pVar[$indexBy_pVar];
                    if(!isset($ret_pVar[$keyValue_pVar])) {
                        $ret_pVar[$keyValue_pVar] = $row_pVar;
                        foreach ($ret_pVar[$keyValue_pVar] as $keyColumn_pVar=>$column_pVar) {
                            if($fields_tables_pVar[$keyColumn_pVar] !== false) {
                                $ret_pVar[$keyValue_pVar][$keyColumn_pVar] = array($column_pVar);
                            }
                        }
                    }
                    else {
                        foreach ($row_pVar as $keyColumn_pVar=>$column_pVar) {
                            if($fields_tables_pVar[$keyColumn_pVar] !== false) {
                                $ret_pVar[$keyValue_pVar][$keyColumn_pVar][] = $column_pVar;
                            }
                        }
                    }
                }

                /**
                 * Indexujem inteligetne
                 */
                /*
                            if(isset($row_pVar[$indexBy_pVar])) {
                                //$ret_pVar[$row_pVar[$indexBy_pVar]] = $row_pVar;
                                $keyValue_pVar = $row_pVar[$indexBy_pVar];
                                if(isset($ret_pVar[$keyValue_pVar])) {
                                    foreach ($row_pVar as $keyColumn_pVar=>$column_pVar) {
                                        if(!is_array($ret_pVar[$keyValue_pVar][$keyColumn_pVar])) {
                                            if($ret_pVar[$keyValue_pVar][$keyColumn_pVar] !== $column_pVar) {
                                                $tmp_pVar = array();
                                                for($i_pVar=0; $i_pVar < $ret_pVar[$keyValue_pVar]['_result_n']; $i_pVar++) {
                                                    $tmp_pVar[$i_pVar] = $ret_pVar[$keyValue_pVar][$keyColumn_pVar];
                                                }
                                                $ret_pVar[$keyValue_pVar][$keyColumn_pVar] = $tmp_pVar;
                                                $ret_pVar[$keyValue_pVar][$keyColumn_pVar][] = $column_pVar;
                                            }
                                        }
                                        else {
                                            $ret_pVar[$keyValue_pVar][$keyColumn_pVar][] = $column_pVar;
                                        }
                                    }
                                    $ret_pVar[$keyValue_pVar]['_result_n']++;
                                }
                                else {
                                    $ret_pVar[$keyValue_pVar] = $row_pVar;
                                    $ret_pVar[$keyValue_pVar]['_result_n'] = 1;
                                }
                            }
                            else {
                                $ret_pVar[] = $row_pVar;
                            }
                        */

                /**
                 * indexujem menej inteligentne
                 */
                /*
                if(isset($row_pVar[$indexBy_pVar])) {
                    $keyValue_pVar = $row_pVar[$indexBy_pVar];
                    if(isset($ret_pVar[$keyValue_pVar])) {
                        if(!isset($ret_pVar[$keyValue_pVar]['_result_childs'])) {
                            $ret_pVar[$keyValue_pVar]['_result_all'] = array($ret_pVar[$keyValue_pVar]);
                        }
                        $ret_pVar[$keyValue_pVar]['_result_all'][] = $row_pVar;
                    }
                    else {
                        $ret_pVar[$keyValue_pVar] = $row_pVar;
                    }
                }
                else {
                    $ret_pVar[] = $row_pVar;
                }*/
            }
            else {
                $ret_pVar[] = $row_pVar;
            }
        }




        if($dd) {
            dd($ret_pVar);
        }

        return($ret_pVar);
    }

    /**
     * vracia 1 riadok databazy ako pole..
     * K stlpcom pristupujem result['column_name']
     */
    protected static function getResult_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false)
    {
        if(!self::init_gFunc()) {
            return(false);
        }


        $res = DB::select(self::$connectionObject_pVar->processParams($query_string_pVar, $params_pVar));
        if(!is_array($res)) {
            return false;
        }
        if(isset($res[0])) {
            return (array)$res[0];
        }
        return true;
    }

    /**
     * Vracia selectnutu hodnotu (z prveho riadku, z prveho stlpca)
     * Vhodne ak selectujem jednu hodnotu, napr. select count(id) as pocet from table. Funkcia vrati pocet ako cislo, nie ako pole.
     */
    protected static function getField_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false)
    {
        if(!self::init_gFunc()) {
            return(false);
        }


        $res = DB::select(self::$connectionObject_pVar->processParams($query_string_pVar, $params_pVar));
        if(!is_array($res)) {
            return false;
        }
        if(!isset($res[0])) {
            return false;
        }

        $row = (array)$res[0];

        return $row[array_keys($row)[0]];
    }

    /**
     * Vykona SQL prikaz na databaze.
     * Ak $getAffectedRows_pVar=true, vracia pocet ovplyvnenych riadkov, alebo false pri neuspechu.
     * Ak $getAffectedRows_pVar=false, vracia true pri uspechu, alebo false pri neuspechu.
     *
     * @param unknown_type $query_string_pVar
     * @param unknown_type $fileName_pVar
     * @param unknown_type $lineNum_pVar
     * @param unknown_type $params_pVar
     * @param unknown_type $getAffectedRows_pVar
     * @return unknown
     */
    protected static function execute_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $getAffectedRows_pVar = false)
    {
        if(!self::init_gFunc()) {
            return(false);
        }

        $queryStartTime = microtime(true);
        $affected = DB::getPdo()->exec(self::$connectionObject_pVar->processParams($query_string_pVar, $params_pVar));
        self::legacyQueryLog($query_string_pVar, $params_pVar, $queryStartTime);
        if($affected === false) {
            return false;
        }
        if($getAffectedRows_pVar) {
            return $affected;
        }
        return true;
    }

    /**
     * Vykona insertovaci SQL prikaz na databazu.
     * Ak $getLastId_pVar=true, vracia posledne vlozene ID, alebo false pri neuspechu.
     * Ak $getLastId_pVar=false, vracia true pri uspechu, alebo false pri neuspechu.
     *
     *
     * @param unknown_type $query_string_pVar
     * @param unknown_type $fileName_pVar
     * @param unknown_type $lineNum_pVar
     * @param unknown_type $params_pVar
     * @param unknown_type $getLastId_pVar
     * @return unknown
     */
    protected static function insert_gFunc($query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $getLastId_pVar = false)
    {
        if(!self::init_gFunc()) {
            return(false);
        }

        $queryStartTime = microtime(true);
        $affected = DB::getPdo()->exec(self::$connectionObject_pVar->processParams($query_string_pVar, $params_pVar));
        self::legacyQueryLog($query_string_pVar, $params_pVar, $queryStartTime);
        if($affected === false) {
            return false;
        }
        if($getLastId_pVar) {
            return DB::getPdo()->lastInsertId();
        }
        return true;
    }

    protected static function insertData_gFunc($tableName_pVar, $formatString_pVar, $data_pVar, $fileName_pVar, $lineNum_pVar, $getLastId_pVar = false)
    {
        if($formatString_pVar === false) {
            $formatString_pVar = array();
            foreach($data_pVar as $k=>$v) {
                $formatString_pVar[] = '%s';
            }
            $formatString_pVar = implode(',', $formatString_pVar);
        }
        $sql_pVar = 'INSERT INTO `' . $tableName_pVar . '` ';
        $fields_pVar = array_keys($data_pVar);
        $sql_pVar .= '(`' . implode('`,`', $fields_pVar) . '`)';
        $sql_pVar .= ' VALUES(' . $formatString_pVar . ')';
        return(db_gClass::insert_gFunc($sql_pVar, $fileName_pVar, $lineNum_pVar, $data_pVar, $getLastId_pVar));
    }

    protected static function updateData_gFunc($tableName_pVar, $formatString_pVar, $data_pVar, $whereString_pVar, $whereData_pVar, $fileName_pVar, $lineNum_pVar, $getAffectedRows_pVar = false)
    {
        $sql_pVar = 'UPDATE `' . $tableName_pVar . '` SET ';
        $formats_pVar = explode(',', $formatString_pVar);

        $tmp_pVar = array();
        foreach($data_pVar as $k_pVar=>$v_pVar) {
            $tmp_pVar[] = '`' . $k_pVar . '` = ' . array_shift($formats_pVar);
        }
        $sql_pVar .= implode(', ', $tmp_pVar);

        $sql_pVar .= ' WHERE ' . $whereString_pVar;

        $data_pVar = array_merge($data_pVar, $whereData_pVar);

        return(db_gClass::execute_gFunc($sql_pVar, $fileName_pVar, $lineNum_pVar, $data_pVar, $getAffectedRows_pVar));
    }

    /**
     * selectnem definiciu stlpcov pre tabulku
     *
     * @param unknown_type $table_name_pVar
     * @param unknown_type $fileName_pVar
     * @param unknown_type $lineNum_pVar
     * @return unknown
     */
    protected static function getFields_gFunc($table_name_pVar, $fileName_pVar, $lineNum_pVar)
    {
        return(self::getResultArray_gFunc('SHOW COLUMNS FROM %t' . $table_name_pVar, $fileName_pVar, $lineNum_pVar, false, 'Field'));
    }

    /**
     * DEPRECATED
     * z pola vytvori retazec do sql prikazu.
     * Ale nepodporuje mysql_real_escape_string
     *
     * @param unknown_type $resultArray_pVar
     * @param unknown_type $useColumnName_pVar
     * @param unknown_type $operator_pVar
     * @param unknown_type $whereColumnName_pVar
     * @return unknown
     */
    protected static function whereFromResultArray_gFunc(&$resultArray_pVar, $useColumnName_pVar, $operator_pVar = 'or', $whereColumnName_pVar = false)
    {
        error_gClass::deprecated_gFunc(__FILE__, __LINE__, __METHOD__);
        if($whereColumnName_pVar === false) {
            $whereColumnName_pVar = $useColumnName_pVar;
        }

        $ret_pVar = '';
        $n_pVar = 0;
        foreach($resultArray_pVar as $k_pVar=>$v_pVar) {
            if($n_pVar) {
                $ret_pVar .= $operator_pVar;
            }
            $ret_pVar .= ' `' . $whereColumnName_pVar . '` ';
            $vv_pVar = $v_pVar[$useColumnName_pVar];
            if(is_null($v_pVar)) {
                $v_pVar = 'IS NULL';
            }
            elseif(!is_numeric($v_pVar)) {
                $v_pVar = '\'' . string_gClass::addSlashes_magic_gFunc($v_pVar) . '\'';
            }
            $ret_pVar .= $v_pVar . ' ';
        }
        return($ret_pVar);
    }

    /**
     * Selectnem stromovu strukturu z databazy.
     *
     * @param unknown_type $table_pVar - nazov tabulky v databaze bez prefixu
     * @param unknown_type $parent_id_pVar - id korena stromu ktory sa selectuje
     * @param unknown_type $tree_ext_pVar - rozsirujuce info pre vytvorenie dotazu (napr. join, where, alebo status)
     * @param unknown_type $callbackFunc_pVar - callback funkcia, ktora bude zavolana pre kazdu polozku stromu (mozem v nej upravit strukturu)
     * @return unknown
     */
    protected static function tree_get_gFunc($table_pVar, $parent_id_pVar, $tree_ext_pVar = array(), $callbackFunc_pVar =false)
    {
        $join_pVar = isset($tree_ext_pVar['join_pVar'])?$tree_ext_pVar['join_pVar']:false;
        $whereCond_pVar = isset($tree_ext_pVar['whereCond_pVar'])?$tree_ext_pVar['whereCond_pVar']:false;
        $selectedFieldsNames_pVar = isset($tree_ext_pVar['selectedFieldsNames_pVar'])?$tree_ext_pVar['selectedFieldsNames_pVar']:'*';

        // selectnem si info o strome
        $query_string_pVar = 'SELECT `tree_left_value`, `tree_right_value` FROM `%t' . $table_pVar . '` '
            . 'WHERE `tree_id` = %d';
        $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, array($parent_id_pVar));

        // a teraz select dat
        if(is_array($data_pVar)) {
            $query_string_pVar = 'SELECT ';
            $query_string_pVar .= $selectedFieldsNames_pVar;
            $query_string_pVar .= ' FROM %t'.$table_pVar;

            if($join_pVar !== false) {
                /**
                 * TODO: naimplementovat JOIN. Ale musim nejako poriesit parametre, aby som ich vedel dosadzovat pomocou %
                 * asi $join_pVar bude array, prva polozka bude query, a ostatne budu parametre...
                 **/
            }

            $query_string_pVar .= ' WHERE `tree_left_value` >= ' . $data_pVar['tree_left_value']
                . ' AND `tree_right_value` <= ' . $data_pVar['tree_right_value'];

            if(isset($tree_ext_pVar['status_pVar'])) {
                $query_string_pVar .= ' AND `tree_status` = \''.$tree_ext_pVar['status_pVar'].'\'';
            }
            if($whereCond_pVar !== false) {
                // TODO: naimplementovat WHERE. Ale musim nejako poriesit parametre, aby som ich vedel dosadzovat pomocou %
                // asi $whereCond_pVar bude array, prva polozka bude query, a ostatne budu parametre...
            }

            $query_string_pVar .= ' ORDER BY `tree_left_value`';
            $tree_pVar = self::getResultArray_gFunc($query_string_pVar, __FILE__, __LINE__);

            // rozparsujem strom
            $data_pVar = array();
            self::_tree_get_parse_gFunc($tree_pVar, $data_pVar, 0, $callbackFunc_pVar, $data_pVar);
        }
        else {
            $data_pVar = array();
        }
        return($data_pVar);
    }

    /**
     * pomocna metoda pre tree_get_gFunc
     * Selectnute ploche data prerobi na stromovu datovu strukturu
     * V root iteme vytvori pole paths_pVar
     *
     * @param unknown_type $dbData_pVar
     * @param unknown_type $subTree_pVar
     * @param unknown_type $dbDataIndex_pVar
     * @param unknown_type $leftColumnName_pVar
     * @param unknown_type $rightColumnName_pVar
     * @param unknown_type $callback_pVar
     * @param unknown_type $target_root_item_pVar
     * @return unknown
     */
    private static function _tree_get_parse_gFunc(&$dbData_pVar, &$subTree_pVar, $dbDataIndex_pVar, &$callback_pVar, &$target_root_item_pVar)
    {
        if(!$dbDataIndex_pVar) {
            if(!isset($dbData_pVar[$dbDataIndex_pVar])) {
                return;
            }
            foreach ($dbData_pVar[0] as $k_pVar=>$v_pVar) {
                $subTree_pVar[$k_pVar] = $v_pVar;
            }
            if($callback_pVar !== false) {
                // volam callback pre parenta? zatial nie.. mozno neskor implementujem
            }
            return(self::_tree_get_parse_gFunc($dbData_pVar, $subTree_pVar, $dbDataIndex_pVar + 1, $callback_pVar, $target_root_item_pVar));
        }

        $n = 0;
        while (1) {
            if(!isset($dbData_pVar[$dbDataIndex_pVar])) {
                return($dbDataIndex_pVar);
            }
            if($dbData_pVar[$dbDataIndex_pVar]['tree_left_value'] > $subTree_pVar['tree_left_value']
                && $dbData_pVar[$dbDataIndex_pVar]['tree_right_value'] < $subTree_pVar['tree_right_value']) {
                // je to dieta, pridam ho
                if(!isset($subTree_pVar['tree_childs'])) {
                    $subTree_pVar['tree_childs'] = array();
                }
                $subTree_pVar['tree_childs'][$n] = $dbData_pVar[$dbDataIndex_pVar];
                if($callback_pVar !== false) {
                    dd('first and second arguments of ' . $callback_pVar . ' function must be defined as reference');

                    call_user_func($callback_pVar, $subTree_pVar['tree_childs'][$n], $subTree_pVar);
                }
                $target_root_item_pVar['paths_pVar'][] = array('id_pVar'=>$subTree_pVar['tree_childs'][$n]['id'], 'link_pVar'=>$subTree_pVar['tree_childs'][$n][main_gClass::getLanguage_gFunc() . '_tree_path']);
                $n++;
            }
            else {
                // nie je to dieta, nespracujem ho
                return($dbDataIndex_pVar-1);
            }

            // ak ma vlozene dieta deti, tak ich vlozim
            if($dbData_pVar[$dbDataIndex_pVar]['tree_left_value']+1 != $dbData_pVar[$dbDataIndex_pVar]['tree_right_value']) {
                // ma deti
                $dbDataIndex_pVar = self::_tree_get_parse_gFunc($dbData_pVar, $subTree_pVar['tree_childs'][$n-1], $dbDataIndex_pVar + 1, $callback_pVar, $target_root_item_pVar);
            }
            $dbDataIndex_pVar++;
        }
    }

    /**
     * Vymaze podstrom.
     * Ak $deleteOnlyChilds_pVar = true, tak ponecha koren, a vymaze iba detske polozky.
     *
     * Vracia:
     * - false, ak sa z nejakeho dovodu nepodarilo koren zmazat
     * - true, ak koren neexistoval (teda ho nebolo treba mazat), alebo ak som uspesne zmazal strom
     *
     * @param unknown_type $table_pVar
     * @param unknown_type $parent_id_pVar
     * @param unknown_type $deleteOnlyChilds_pVar
     * @return unknown
     */
    protected static function tree_deleteNode_gFunc($table_pVar, $parent_id_pVar, $deleteOnlyChilds_pVar = false)
    {
        self::startTransaction_gFunc(__FILE__, __LINE__);
        $query_string_pVar = 'SELECT `tree_left_value`, `tree_right_value` FROM `%t' . $table_pVar . '` '
            . 'WHERE `tree_id` = %d';
        $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, array($parent_id_pVar));
        if(!is_array($data_pVar)) { // neexistuje, nemusim mazat
            self::commit_gFunc(__FILE__, __LINE__);
            return(true);
        }

        if($deleteOnlyChilds_pVar) {
            $op_pVar = '';
        }
        else {
            $op_pVar = '=';
        }
        $query_string_pVar = 'DELETE FROM `%t' . $table_pVar . '` WHERE `tree_left_value` >'.$op_pVar.' '.$data_pVar['tree_left_value'].' AND `tree_right_value` <'.$op_pVar.' ' . $data_pVar['tree_right_value'];
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }

        // precislujem
        $limit_pVar = $data_pVar['tree_left_value'];
        $updateValue_pVar = $data_pVar['tree_right_value'] - $data_pVar['tree_left_value'] - 1;
        $query_string_pVar = 'UPDATE `%t' . $table_pVar . '` SET `tree_left_value` = `tree_left_value` - ' . $updateValue_pVar . ' WHERE `tree_left_value` > ' . $limit_pVar;
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }
        $query_string_pVar = 'UPDATE `%t' . $table_pVar . '` SET `tree_right_value` = `tree_right_value` - ' . $updateValue_pVar . ' WHERE `tree_right_value` > ' . $limit_pVar;
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }

        self::commit_gFunc(__FILE__, __LINE__);
        return(true);
    }

    /**
     * Ulozi datovu strukturu do databazy ako detske polozky pre $parent_id_pVar
     * Existujuce detske polozky vymaze.
     *
     * @param unknown_type $table_pVar
     * @param unknown_type $parent_id_pVar
     * @param unknown_type $treeData_pVar
     * @return unknown
     */
    protected static function tree_saveAsChilds_gFunc($table_pVar, $parent_id_pVar, $treeData_pVar)
    {
        self::startTransaction_gFunc(__FILE__, __LINE__);
        // najskor odmazem povodne polozky parenta
        if(!self::tree_deleteNode_gFunc($table_pVar, $parent_id_pVar, true)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }

        $query_string_pVar = 'SELECT `tree_left_value`, `tree_right_value` FROM `%t' . $table_pVar . '` '
            . 'WHERE `tree_id` = %d';
        $data_pVar = self::getResult_gFunc($query_string_pVar, __FILE__, __LINE__, array($parent_id_pVar));
        if(!is_array($data_pVar)) { // neexistuje, teda mu neviem pridat detske polozky
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }

        // normalizujem data (vypocitam left/right)
        $parentRight_pVar = self::_tree_normalizeData_gFunc($treeData_pVar, $data_pVar['tree_left_value'] + 1);

        // teraz pridam polozky
        self::_tree_saveNormalizedData_gFunc($table_pVar, $parent_id_pVar, $treeData_pVar);
        self::commit_gFunc(__FILE__, __LINE__);
    }

    /**
     * V datovom poli ponastavuje left a right, na zaklade dodanej hodnoty $left_pVar
     *
     * @param unknown_type $data_pVar
     * @param unknown_type $left_pVar
     */
    private static function _tree_normalizeData_gFunc(&$data_pVar, $left_pVar)
    {
        if(isset($data_pVar['tree_childs']) && count($data_pVar['tree_childs'])) {
            // iba childs (koren stromu)
            $data_pVar['rootLeft_pVar'] = $left_pVar;
            $data_pVar['rootRight_pVar'] = self::_tree_normalizeData_gFunc($data_pVar['tree_childs'], $left_pVar);
            return($data_pVar['rootRight_pVar']);
        }
        foreach($data_pVar as $k_pVar=>$v_pVar)
        {
            $data_pVar[$k_pVar]['left_pVar'] = $left_pVar;
            if(isset($v_pVar['tree_childs'])) {
                $left_pVar = self::_tree_normalizeData_gFunc($data_pVar[$k_pVar]['tree_childs'], $left_pVar + 1);
            }
            else {
                $left_pVar++;
            }
            $data_pVar[$k_pVar]['right_pVar'] = $left_pVar;
            $left_pVar++;
        }
        return($left_pVar);
    }


    /**
     * normalizovany strom ulozi do databazy.
     * Strom normalizujem metodou _tree_normalizeData_gFunc
     *
     * @param unknown_type $table_pVar
     * @param unknown_type $parent_id_pVar
     * @param unknown_type $treeData_pVar
     * @return unknown
     */
    private static function _tree_saveNormalizedData_gFunc($table_pVar, $parent_id_pVar, &$treeData_pVar)
    {
        // prerobm si strukturu na fields/values
        $data_pVar = array('fields_pVar'=>array(), 'values_pVar'=>array());
        $left_pVar = $treeData_pVar['rootLeft_pVar'];
        $right_pVar = $treeData_pVar['rootRight_pVar'];
        self::_tree_saveNormalizedData_parse_gFunc($treeData_pVar, $data_pVar);
        // vysledok mam v $data_pVar, dalej pracujem s tymto polom

        $sql_pVar = 'INSERT INTO %t' . $table_pVar .' (';
        $n_pVar = 0;
        foreach ($data_pVar['fields_pVar'] as $k_pVar=>$v_pVar) {
            if($n_pVar) {
                $sql_pVar .= ', ';
            }
            if($k_pVar === 'left_pVar') {
                $k_pVar = 'tree_left_value';
            }
            if($k_pVar === 'right_pVar') {
                $k_pVar = 'tree_right_value';
            }
            $sql_pVar .= '`'.$k_pVar.'`';
            $n_pVar++;
        }

        $sql_pVar .= ') VALUES ';

        $n_pVar = 0;
        foreach ($data_pVar['values_pVar'] as $k_pVar=>$v_pVar) {
            $nn_pVar = 0;
            if($n_pVar) {
                $sql_pVar .= ', ';
            }
            $sql_pVar .= '(';
            foreach ($data_pVar['fields_pVar'] as $kk_pVar=>$vv_pVar) {
                if($nn_pVar) {
                    $sql_pVar .= ', ';
                }
                if($kk_pVar === 'left_pVar') {
                    $kk_pVar = 'tree_left_value';
                }
                if($kk_pVar === 'right_pVar') {
                    $kk_pVar = 'tree_right_value';
                }
                if(isset($v_pVar[$vv_pVar])) {
                    if(is_numeric($v_pVar[$vv_pVar])) {
                        $sql_pVar .= $v_pVar[$vv_pVar];
                    }
                    else {
                        $sql_pVar .= '\'' . $v_pVar[$vv_pVar] . '\'';
                    }
                }
                else {
                    $sql_pVar .= 'NULL';
                }
                $nn_pVar++;
            }
            $sql_pVar .= ')';
            $n_pVar++;
        }

        self::startTransaction_gFunc(__FILE__, __LINE__);
        // precislujem
        $limit_pVar = $left_pVar;
        $updateValue_pVar = $right_pVar - $left_pVar;

        $query_string_pVar = 'UPDATE `%t' . $table_pVar . '` SET `tree_left_value` = `tree_left_value` + ' . $updateValue_pVar . ' WHERE `tree_left_value` >= ' . $limit_pVar;
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }
        $query_string_pVar = 'UPDATE `%t' . $table_pVar . '` SET `tree_right_value` = `tree_right_value` + ' . $updateValue_pVar . ' WHERE `tree_right_value` >= ' . $limit_pVar;
        if(!self::execute_gFunc($query_string_pVar, __FILE__, __LINE__)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }

        if(!self::execute_gFunc($sql_pVar, __FILE__, __LINE__)) {
            self::rollback_gFunc(__FILE__, __LINE__);
            return(false);
        }

        self::commit_gFunc(__FILE__, __LINE__);
    }

    /**
     * Polozky normalizovanej stromovej struktury prerobi na fields a values pre databazov dotaz
     * Vstupny parameter ret je pole: array('fields_pVar'=>array(), 'values_pVar'=>array());
     * Toto pole naplni (referencia);
     *
     * @param unknown_type $treeData_pVar
     * @param unknown_type $ret_pVar
     */
    private static function _tree_saveNormalizedData_parse_gFunc(&$treeData_pVar, &$ret_pVar)
    {
        $myValues_pVar = array();
        foreach($treeData_pVar as $k_pVar=>$v_pVar) {
            if($k_pVar === 'tree_childs' || $k_pVar === 'rootLeft_pVar' || $k_pVar === 'rootRight_pVar') {
                continue;
            }
            if(!isset($ret_pVar['fields_pVar'][$k_pVar])) {
                $ret_pVar['fields_pVar'][$k_pVar] = count($ret_pVar['fields_pVar']);
            }
            $myValues_pVar[$ret_pVar['fields_pVar'][$k_pVar]] = $v_pVar;
        }
        if(count($myValues_pVar)) {
            $ret_pVar['values_pVar'][] = $myValues_pVar;
        }

        if(isset($treeData_pVar['tree_childs'])) {
            foreach($treeData_pVar['tree_childs'] as $k_pVar=>$v_pVar) {
                self::_tree_saveNormalizedData_parse_gFunc($treeData_pVar['tree_childs'][$k_pVar], $ret_pVar);
            }
        }
    }
}
