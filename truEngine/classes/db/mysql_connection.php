<?php

class mysql_connection_gClass extends truEngineBaseClass_gClass {

    const MYSQL_CONNECT_CONNECTED_pVar = 1;
    const MYSQL_CONNECT_DISCONNECTED_pVar = 2;
    const MYSQL_CONNECT_ERROR_pVar = 3;

    const MYSQL_TRANS_DISABLED_pVar = 0;
    const MYSQL_TRANS_STARTNEW_pVar = 1;
    const MYSQL_TRANS_START_pVar = 2;
    const MYSQL_TRANS_COMMIT_pVar = 3;
    const MYSQL_TRANS_COMMITTED_pVar = 4;
    const MYSQL_TRANS_ROLLBACK_pVar = 5;
    const MYSQL_TRANS_ROLLEDBACK_pVar = 6;
    const MYSQL_TRANS_ERROR_pVar = 7;

    private static $objects_pVar = array();
    private static $lastObjectId_pVar = -1;
    private static $noRollbackFromError_pVar = false;

    // konektivita
    private $mysqlLink_pVar;
    private $isConnected_pVar;
    private $myObjectId_pVar = -1;

    private $persistentConnection_pVar;
    private $delayedInsertsEnabled_pVar;
    private $charset_pVar;

    private $login_hostName_pVar;
    private $login_loginName_pVar;
    private $login_password_pVar;
    private $login_port_pVar;
    private $databaseName_pVar;
    private $tbPrefix_pVar;

    // transakcie
    private $inTransaction_pVar;
    private $rollingBack_pVar;
    private $transactionsDisabled_pVar;

    // ine

    private function __construct()
    {
        $this->mysqlLink_pVar = false;
        $this->isConnected_pVar = self::MYSQL_CONNECT_DISCONNECTED_pVar;

        $this->persistentConnection_pVar = false;
        $this->delayedInsertsEnabled_pVar = false;
        $this->charset_pVar = 'utf8';

        $this->inTransaction_pVar = 0;
        $this->rollingBack_pVar = false;
        $this->transactionsDisabled_pVar = false;
    }

    static public function &getObject_gFunc()
    {
        self::$lastObjectId_pVar++;
        self::$objects_pVar[self::$lastObjectId_pVar] = new mysql_connection_gClass();
        $ref_pVar = &self::$objects_pVar[self::$lastObjectId_pVar];
        return($ref_pVar);
    }

    function __destruct()
    {
        if($this->isConnected_pVar == self::MYSQL_CONNECT_CONNECTED_pVar) {
            error_gClass::error_gFunc(__FILE__, __LINE__, string_gClass::get('str__db_destruct_opened_sVar'));
        }
    }

    // Prevent users to clone the instance
    public function __clone()
    {
        error_gClass::fatal_gFunc(__FILE__, __LINE__, 'Clone is not allowed.');
    }

    static public function rollbackAllObjects_gFunc($fileName_pVar, $lineNum_pVar)
    {
        foreach(self::$objects_pVar as $k_pVar=>$v_pVar) {
            $v_pVar->rollbackAll_gFunc($fileName_pVar, $lineNum_pVar);
        }
    }

    static public function rollbackAllObjectsFromError_gFunc($fileName_pVar, $lineNum_pVar)
    {
        if(self::$noRollbackFromError_pVar) {
            return;
        }
        self::$noRollbackFromError_pVar = true;
        self::rollbackAllObjects_gFunc($fileName_pVar, $lineNum_pVar);
        self::$noRollbackFromError_pVar = false;
    }

    public function setConnectionParams_gFunc($hostName_pVar, $loginName_pVar, $password_pVar, $databaseName_pVar, $charset_pVar = 'utf8', $tbPrefix_pVar = '', $port_pVar = 3306)
    {
        $this->login_hostName_pVar = $hostName_pVar;
        $this->login_loginName_pVar = $loginName_pVar;
        $this->login_password_pVar = $password_pVar;
        $this->databaseName_pVar = $databaseName_pVar;
        $this->charset_pVar = $charset_pVar;
        $this->tbPrefix_pVar = $tbPrefix_pVar;
        $this->login_port_pVar = $port_pVar;
    }

    public function connect_gFunc($persistent_connection_pVar = false)
    {
        $this->persistentConnection_pVar = $persistent_connection_pVar;
        $this->mysqlLink_pVar = true;
        $this->isConnected_pVar = self::MYSQL_CONNECT_CONNECTED_pVar;
        return true;
    }

    public function disconnect_gFunc($fileName_pVar, $lineNum_pVar)
    {
        if($this->inTransaction_pVar) {
            error_gClass::error_gFunc($fileName_pVar, $lineNum_pVar, string_gClass::get('str__db_close_in_transaction_sVar', $this->inTransaction_pVar));
        }

        $this->mysqlLink_pVar=false;
        $this->isConnected_pVar = self::MYSQL_CONNECT_DISCONNECTED_pVar;
        return(true);
    }

    public static function disconnectAll_gFunc($fileName_pVar, $lineNum_pVar)
    {
        for($i_pVar = 0; $i_pVar <= self::$lastObjectId_pVar; $i_pVar++) {
            if(isset(self::$objects_pVar[$i_pVar])) {
                self::$objects_pVar[$i_pVar]->disconnect_gFunc($fileName_pVar, $lineNum_pVar);
            }
        }
    }

    public function isConnected_gFunc()
    {
        if($this->isConnected_pVar === self::MYSQL_CONNECT_CONNECTED_pVar) {
            return(true);
        }
        else {
            return(false);
        }
    }

    public function getConnectionStatus_gFunc()
    {
        return($this->isConnected_pVar === self::MYSQL_CONNECT_ERROR_pVar ? false:true);
    }

    public function processParams($query_string_pVar, $params_pVar = false)
    {

        if($params_pVar !== false) {
            if(!is_array($params_pVar)) {
                $params_pVar = array($params_pVar);
            }
        }

        if($params_pVar === false || !count($params_pVar)) {
            $params_pVar = array('noParams_pVar'=>true);
        }

        $params_pVar['endParams_pVar'] = true;

        $offset_pVar = 0;
        foreach ($params_pVar as $k_pVar=>$v_pVar) {
            while(1) {
                $p_pVar = strpos($query_string_pVar, '%', $offset_pVar);
                if($p_pVar === false) { // koniec retazca
                    break;
                }
                $nextChar_pVar = substr($query_string_pVar, $p_pVar + 1, 2);
                if(!isset($nextChar_pVar[0])) { // koniec retazca
                    break;
                }
                $nextChar_pVar = strtolower($nextChar_pVar);
                if($nextChar_pVar[0] !== 'x' && $nextChar_pVar[0] !== 'a') {
                    $nextChar_pVar = $nextChar_pVar[0];
                }

                if($k_pVar === 'noParams_pVar' && $nextChar_pVar !== 't' && $nextChar_pVar !== 'n' && $nextChar_pVar !== '%') {
                    // nie su parametre, neurobim replace
                    $offset_pVar = $p_pVar + 1;
                    continue;
                }

                if($k_pVar === 'endParams_pVar' && $nextChar_pVar !== 't' && $nextChar_pVar !== 'n' && $nextChar_pVar !== '%') {
                    // nie su parametre, neurobim replace
                    $offset_pVar = $p_pVar + 1;
                    continue;
                }
                switch ($nextChar_pVar) {
                    case '%':
                        $v_pVar = '%';
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + 2);
                        $offset_pVar = $p_pVar + strlen($v_pVar);
                        continue 2; // continue while;
                    case 's':
                        // string
                        if(is_null($v_pVar)) {
                            $v_pVar = 'NULL';
                        }
                        else {
                            $v_pVar = '"'. addslashes($v_pVar) . '"';
                            //$v_pVar =  mysql_real_escape_string($v_pVar, $this->mysqlLink_pVar);
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'xs':
                        // string. Ak je prazdny, dosadi NULL
                        if(!strlen($v_pVar)) {
                            $v_pVar = 'NULL';
                        }
                        else {
                            $v_pVar = mysql_real_escape_string($v_pVar, $this->mysqlLink_pVar);
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'r':
                        // bez zmeny
                        $v_pVar = $v_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'b':
                        // boolean
                        if($v_pVar) {
                            $v_pVar = 'TRUE';
                        }
                        else {
                            $v_pVar = 'FALSE';
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'i':
                    case 'd':
                        // integer
                        $v_pVar = intval($v_pVar);
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'u':
                        // unsigned
                        $v_pVar = intval($v_pVar);
                        if($v_pVar < 0) {
                            $v_pVar = 0;
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'f':
                        // float
                        $v_pVar = floatval($v_pVar);
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'ai':
                    case 'ad':
                        if(!is_array($v_pVar)) {
                            $v_pVar = array($v_pVar);
                        }
                        $tmp_pVar = '';
                        foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                            if(!empty($tmp_pVar)) {
                                $tmp_pVar .= ', ';
                            }
                            $tmp_pVar .= intval($vv_pVar);
                        }
                        $v_pVar = $tmp_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'af':
                        if(!is_array($v_pVar)) {
                            $v_pVar = array($v_pVar);
                        }
                        $tmp_pVar = '';
                        foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                            if(!empty($tmp_pVar)) {
                                $tmp_pVar .= ', ';
                            }
                            $tmp_pVar .= floatval($vv_pVar);
                        }
                        $v_pVar = $tmp_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'as':
                        if(!is_array($v_pVar)) {
                            $v_pVar = array($v_pVar);
                        }
                        $tmp_pVar = '';
                        foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                            if(!empty($tmp_pVar)) {
                                $tmp_pVar .= ', ';
                            }
                            if(is_null($vv_pVar)) {
                                $tmp_pVar .= 'NULL';
                            }
                            else {
                                $tmp_pVar .= mysql_real_escape_string($vv_pVar, $this->mysqlLink_pVar);
                            }
                        }
                        $v_pVar = $tmp_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'n': // neberie polozku z pola
                        // NULL
                        $x_pVar = $v_pVar;
                        $v_pVar = 'NULL';
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        $offset_pVar = $p_pVar + strlen($v_pVar);
                        $v_pVar = $x_pVar;
                        continue 2; // continue while;
                    case 't': // neberie polozku z pola
                        // table prefix
                        $x_pVar = $v_pVar;
                        $v_pVar = $this->tbPrefix_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        $offset_pVar = $p_pVar + strlen($v_pVar);
                        $v_pVar = $x_pVar;
                        continue 2; // continue while;
                    default:
                        break;
                }
                $offset_pVar = $p_pVar + strlen($v_pVar);
                break; // ide po dalsiu polozku z pola (break while, continue foreach)
            }
        }


        return $query_string_pVar;
    }


    public function _query_pFunc(&$result_pVar, $query_string_pVar, $fileName_pVar, $lineNum_pVar, $params_pVar = false, $getAffectedRows_pVar = false, $getLastId_pVar = false)
    {
        if(!$this->isConnected_gFunc()) {
            if(!$this->connect_gFunc($this->persistentConnection_pVar)) {
                return(false);
            }
        }

        $baseQuery_pVar = $query_string_pVar;
        $baseParams_pVar = $params_pVar;
        $startTime_pVar = debug_gClass::getMicrotime_gFunc();

        if($params_pVar !== false) {
            if(!is_array($params_pVar)) {
                $params_pVar = array($params_pVar);
            }
        }

        if($params_pVar === false || !count($params_pVar)) {
            $params_pVar = array('noParams_pVar'=>true);
        }

        $params_pVar['endParams_pVar'] = true;

        $offset_pVar = 0;
        foreach ($params_pVar as $k_pVar=>$v_pVar) {
            while(1) {
                $p_pVar = strpos($query_string_pVar, '%', $offset_pVar);
                if($p_pVar === false) { // koniec retazca
                    break;
                }
                $nextChar_pVar = substr($query_string_pVar, $p_pVar + 1, 2);
                if(!isset($nextChar_pVar[0])) { // koniec retazca
                    break;
                }
                $nextChar_pVar = strtolower($nextChar_pVar);
                if($nextChar_pVar[0] !== 'x' && $nextChar_pVar[0] !== 'a') {
                    $nextChar_pVar = $nextChar_pVar[0];
                }

                if($k_pVar === 'noParams_pVar' && $nextChar_pVar !== 't' && $nextChar_pVar !== 'n' && $nextChar_pVar !== '%') {
                    // nie su parametre, neurobim replace
                    $offset_pVar = $p_pVar + 1;
                    continue;
                }

                if($k_pVar === 'endParams_pVar' && $nextChar_pVar !== 't' && $nextChar_pVar !== 'n' && $nextChar_pVar !== '%') {
                    // nie su parametre, neurobim replace
                    $offset_pVar = $p_pVar + 1;
                    continue;
                }

                switch ($nextChar_pVar) {
                    case '%':
                        $v_pVar = '%';
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + 2);
                        $offset_pVar = $p_pVar + strlen($v_pVar);
                        continue 2; // continue while;
                    case 's':
                        // string
                        if(is_null($v_pVar)) {
                            $v_pVar = 'NULL';
                        }
                        else {
                            $v_pVar = '\'' . mysql_real_escape_string($v_pVar, $this->mysqlLink_pVar) . '\'';
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'xs':
                        // string. Ak je prazdny, dosadi NULL
                        if(!strlen($v_pVar)) {
                            $v_pVar = 'NULL';
                        }
                        else {
                            $v_pVar = '\'' . mysql_real_escape_string($v_pVar, $this->mysqlLink_pVar) . '\'';
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'r':
                        // bez zmeny
                        $v_pVar = $v_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'b':
                        // boolean
                        if($v_pVar) {
                            $v_pVar = 'TRUE';
                        }
                        else {
                            $v_pVar = 'FALSE';
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'i':
                    case 'd':
                        // integer
                        $v_pVar = intval($v_pVar);
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'u':
                        // unsigned
                        $v_pVar = intval($v_pVar);
                        if($v_pVar < 0) {
                            $v_pVar = 0;
                        }
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'f':
                        // float
                        $v_pVar = floatval($v_pVar);
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'ai':
                    case 'ad':
                        if(!is_array($v_pVar)) {
                            $v_pVar = array($v_pVar);
                        }
                        $tmp_pVar = '';
                        foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                            if(!empty($tmp_pVar)) {
                                $tmp_pVar .= ', ';
                            }
                            $tmp_pVar .= intval($vv_pVar);
                        }
                        $v_pVar = $tmp_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'af':
                        if(!is_array($v_pVar)) {
                            $v_pVar = array($v_pVar);
                        }
                        $tmp_pVar = '';
                        foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                            if(!empty($tmp_pVar)) {
                                $tmp_pVar .= ', ';
                            }
                            $tmp_pVar .= floatval($vv_pVar);
                        }
                        $v_pVar = $tmp_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'as':
                        if(!is_array($v_pVar)) {
                            $v_pVar = array($v_pVar);
                        }
                        $tmp_pVar = '';
                        foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                            if(!empty($tmp_pVar)) {
                                $tmp_pVar .= ', ';
                            }
                            if(is_null($vv_pVar)) {
                                $tmp_pVar .= 'NULL';
                            }
                            else {
                                $tmp_pVar .= '\'' . mysql_real_escape_string($vv_pVar, $this->mysqlLink_pVar) . '\'';
                            }
                        }
                        $v_pVar = $tmp_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        break;
                    case 'n': // neberie polozku z pola
                        // NULL
                        $x_pVar = $v_pVar;
                        $v_pVar = 'NULL';
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        $offset_pVar = $p_pVar + strlen($v_pVar);
                        $v_pVar = $x_pVar;
                        continue 2; // continue while;
                    case 't': // neberie polozku z pola
                        // table prefix
                        $x_pVar = $v_pVar;
                        $v_pVar = $this->tbPrefix_pVar;
                        $query_string_pVar = substr($query_string_pVar, 0, $p_pVar) . $v_pVar . substr($query_string_pVar, $p_pVar + strlen($nextChar_pVar) + 1);
                        $offset_pVar = $p_pVar + strlen($v_pVar);
                        $v_pVar = $x_pVar;
                        continue 2; // continue while;
                    default:
                        break;
                }
                $offset_pVar = $p_pVar + strlen($v_pVar);
                break; // ide po dalsiu polozku z pola (break while, continue foreach)
            }
        }

        $result_pVar = DB::select($query_string_pVar);
        $endTime_pVar = debug_gClass::getMicrotime_gFunc();
        if(!is_array($result_pVar)) {
            error_gClass::error_gFunc($fileName_pVar, $lineNum_pVar, string_gClass::get('str__db_query_sVar', 'MYSQL ERROR ' . '[' . $query_string_pVar . ']'));
            //// rollback sa spravi automaticky v error_gFunc
            return(false);
        }

        $ret_pVar = array();

        if($getAffectedRows_pVar) {
            $ret_pVar[] = mysql_affected_rows($this->mysqlLink_pVar);
        }
        if($getLastId_pVar) {
            $ret_pVar[] = mysql_insert_id($this->mysqlLink_pVar);
        }

        log_gClass::writeQuery_gFunc($query_string_pVar, $endTime_pVar - $startTime_pVar, $fileName_pVar, $lineNum_pVar);
        log_gClass::writeBaseQuery_gFunc($baseQuery_pVar, $baseParams_pVar, $endTime_pVar - $startTime_pVar, $fileName_pVar, $lineNum_pVar);

        $n_pVar = count($ret_pVar);
        if(!$n_pVar) {
            return(true);
        }
        if($n_pVar === 1) {
            return($ret_pVar[0]);
        }
        return($ret_pVar);
    }

    public function disableTransactions_gFunc($disable_pVar = true)
    {
        $this->transactionsDisabled_pVar = $disable_pVar ? true : false;
    }

    /**
     * zaciatok transakcie
     * Vracia jednu z hodnot:
     * 		MYSQL_TRANS_STARTNEW_pVar
     * 		MYSQL_TRANS_START_pVar
     * 		MYSQL_TRANS_ERROR_pVar
     * 		MYSQL_TRANS_DISABLED_pVar
     */
    public function startTransaction_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if($this->transactionsDisabled_pVar) {
            log_gClass::write_gFunc('mySQL_transaction_start', $this->inTransaction_pVar);
            return(self::MYSQL_TRANS_DISABLED_pVar);
        }
        if(!$this->isConnected_gFunc()) {
            if(!$this->connect_gFunc($this->persistentConnection_pVar)) {
                log_gClass::write_gFunc('mySQL_transaction_start', $this->inTransaction_pVar);
                return(self::MYSQL_TRANS_ERROR_pVar);
            }
        }
        if(!$this->inTransaction_pVar) {
            DB::beginTransaction();
        }
        $this->inTransaction_pVar++;
        log_gClass::write_gFunc('mySQL_transaction_start', $this->inTransaction_pVar . ' ' .$fileName_pVar.':'.$fileLine_pVar);
        if($this->inTransaction_pVar > 1) {
            return(self::MYSQL_TRANS_START_pVar);
        }
        return(self::MYSQL_TRANS_STARTNEW_pVar);
    }

    /**
     * mysql commit
     * commitnem len ak ukoncujem poslednu transakciu. Inak iba znizim counter
     * Vracia jednu z hodnot:
     * 		MYSQL_TRANS_COMMIT_pVar
     * 		MYSQL_TRANS_COMMITED_pVar
     * 		MYSQL_TRANS_ROLLBACK_pVar
     * 		MYSQL_TRANS_ROLLEDBACK_pVar
     * 		MYSQL_TRANS_ERROR_pVar
     * 		MYSQL_TRANS_DISABLED_pVar
     *
     * @return unknown
     */
    public function commit_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if(!$this->isConnected_gFunc()) {
            return(self::MYSQL_TRANS_DISABLED_pVar);
        }
        if($this->transactionsDisabled_pVar) {
            return(self::MYSQL_TRANS_DISABLED_pVar);
        }
        if($this->inTransaction_pVar <= 0) {
            error_gClass::error_gFunc($fileName_pVar, $fileLine_pVar, string_gClass::get('str__db_commit_sVar'));
            return(self::MYSQL_TRANS_ERROR_pVar);
        }
        if($this->rollingBack_pVar) {
            return($this->rollback_gFunc($fileName_pVar, $fileLine_pVar));
        }
        log_gClass::write_gFunc('mySQL_transaction_commit', $this->inTransaction_pVar);
        $this->inTransaction_pVar--;
        if($this->inTransaction_pVar == 0) {
            DB::commit();
            return(self::MYSQL_TRANS_COMMITTED_pVar);
        }
        return(self::MYSQL_TRANS_COMMIT_pVar);
    }

    /**
     * mysql rollback
     * rollback spravim vzdy pri kazdej transakcii... Znizim counter a nastavim priznak rollingBack
     * Vracia jednu z hodnot:
     * 		MYSQL_TRANS_ROLLBACK_pVar
     * 		MYSQL_TRANS_ROLLEDBACK_pVar
     * 		MYSQL_TRANS_ERROR_pVar
     * 		MYSQL_TRANS_DISABLED_pVar
     *
     * @return unknown
     */
    public function rollback_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if(!$this->isConnected_gFunc()) {
            return(self::MYSQL_TRANS_DISABLED_pVar);
        }
        if($this->transactionsDisabled_pVar) {
            return(self::MYSQL_TRANS_DISABLED_pVar);
        }
        $this->rollingBack_pVar = true;
        if($this->inTransaction_pVar <= 0) {
            error_gClass::error_gFunc($fileName_pVar, $fileLine_pVar, string_gClass::get('str__db_rollback_sVar'));
            return(self::MYSQL_TRANS_ERROR_pVar);
        }

        log_gClass::write_gFunc('mySQL_transaction_rollback', $this->inTransaction_pVar);
        $this->inTransaction_pVar--; // odpocitam za kazdu cenu, aj keby sa rollback nepodaril

        DB::rollBack();

        if($this->inTransaction_pVar>0) {
            // otvorim transakciu, aby som ju potom mohol rollbacknut
            DB::beginTransaction();
            return(self::MYSQL_TRANS_ROLLBACK_pVar);
        }
        $this->rollingBack_pVar = false; // uz som zrusil vsetky transakcie
        return(self::MYSQL_TRANS_ROLLEDBACK_pVar);
    }

    /**
     * zrusi vsetky transakcie jednym vrzom.
     */
    public function rollbackAll_gFunc($fileName_pVar, $fileLine_pVar)
    {
        if(!$this->isConnected_gFunc()) {
            return(self::MYSQL_TRANS_ROLLEDBACK_pVar);
        }
        if($this->inTransaction_pVar > 0) {
            $this->inTransaction_pVar = 1;
        }
        else {
            return(self::MYSQL_TRANS_ROLLEDBACK_pVar);
        }
        return($this->rollback_gFunc($fileName_pVar, $fileLine_pVar));
    }

}
