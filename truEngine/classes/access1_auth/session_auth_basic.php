<?php

class session_auth_basic_gClass extends session_gClass
{

    public function isLoggedOn_gFunc()
    {
        return auth()->check();
    }

    protected function authSession_gFunc()
    {
        $timeout_pVar = (int)main_gClass::getInputInteger_gFunc('auth_timeout_pVar', main_gClass::SRC_SESSION_pVar);
        if($timeout_pVar) {
            if($timeout_pVar < time()) {
                // timeouted
                // dokument sa mi zobrazi, ak po timeouted stlacim cancel
                $this->destroySession_gFunc();
                log_gClass::write_gFunc('AUTH_BASIC_TIMEOUT');
                return(false);
            }
        }
        if(self::$loginPrompt_pVar === false) {
            // sem sa dostanem ak zacinam autentifikaciu, alebo ak sa odlogovavam.
            // ak slacim cancel (bez zadania mena/hesla), tak sa mi zobrazi tento dokument.
            if(modules_gClass::isModuleRegistred_gFunc('https') && https_gClass::enabled_gFunc()) {
                https_gClass::check_gFunc(); // redirect
            }
            $this->clearSesion_gFunc();
            $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
            header('WWW-Authenticate: Basic realm="' . $realm_pVar . '"');
            header('HTTP/1.0 401 Unauthorized');
            log_gClass::write_gFunc('AUTH_BASIC_LOGINPROMPT');
            return(false);
        }
        else {
            $login_pVar = trim(main_gClass::getConfigVar_gFunc('login', 'access1_auth'));
            $password_pVar = trim(main_gClass::getConfigVar_gFunc('password', 'access1_auth'));
            $passOk_pVar = false;
            $loggedNow_pVar = false;

            // ziskam hodnoty zo session,
            $remoteAddr_pVar = main_gClass::getInputString_gFunc('auth_remote_addr_pVar', main_gClass::SRC_SESSION_pVar);
            $userAgent_pVar = main_gClass::getInputString_gFunc('auth_user_agent_pVar', main_gClass::SRC_SESSION_pVar);
            $userName_pVar = main_gClass::getInputString_gFunc('auth_user_name_pVar', main_gClass::SRC_SESSION_pVar);
            $authMethod_pVar = main_gClass::getInputString_gFunc('auth_method_pVar', main_gClass::SRC_SESSION_pVar);

            if(strlen($login_pVar) && strlen($password_pVar)) {
                if($login_pVar === main_gClass::getServerVar_gFunc('PHP_AUTH_USER')
                    && $password_pVar === main_gClass::getServerVar_gFunc('PHP_AUTH_PW')) {
                    if(empty($remoteAddr_pVar)) {
                        $loggedNow_pVar = true;
                        $remoteAddr_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR');
                        $userAgent_pVar = main_gClass::getServerVar_gFunc('HTTP_USER_AGENT');
                        $authMethod_pVar = 'basic';
                        $userName_pVar = $login_pVar;
                    }
                    if($remoteAddr_pVar === main_gClass::getServerVar_gFunc('REMOTE_ADDR')
                        && $userAgent_pVar === main_gClass::getServerVar_gFunc('HTTP_USER_AGENT')
                        && $userName_pVar === main_gClass::getServerVar_gFunc('PHP_AUTH_USER')
                        && $authMethod_pVar === 'basic') {
                        $passOk_pVar = true;
                        if($loggedNow_pVar) {
                            main_gClass::regenerateSessionId_gFunc();
                            log_gClass::write_gFunc('AUTH_BASIC_LOGGEDON', $login_pVar);

                            main_gClass::setPhpSessionVar_gFunc('auth_remote_addr_pVar', $remoteAddr_pVar, true, true);
                            main_gClass::setPhpSessionVar_gFunc('auth_user_agent_pVar', $userAgent_pVar, true, true);
                            main_gClass::setPhpSessionVar_gFunc('auth_user_name_pVar', $login_pVar, true, true);
                            main_gClass::setPhpSessionVar_gFunc('auth_method_pVar', 'basic', true, true);
                        }
                    }
                }
            }

            if($passOk_pVar === true) {
                main_gClass::setPhpSessionVar_gFunc('auth_timeout_pVar', time()+120);
                if(modules_gClass::isModuleRegistred_gFunc('https') && https_gClass::enabled_gFunc()) {
                    if(!main_gClass::getInputBoolean_gFunc('loggedon_pVar', main_gClass::SRC_COOKIE_pVar)) {
                        setcookie('loggedon_pVar', '1', null, main_gClass::getConfigString_gFunc('web_dir_gVar', 'runtime_pVar'), null, null, true);
                    }
                }
                $this->userID_pVar = $login_pVar;
                log_gClass::write_gFunc('AUTH_BASIC_ACCESS', $login_pVar);
            }
            else {
                //// sem sa dostanem ak zadam zle heslo alebo login, alebo mi nesedi ip
                $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
                header('WWW-Authenticate: Basic realm="'.$realm_pVar.'"');
                header('HTTP/1.0 401 Unauthorized');
                log_gClass::write_gFunc('AUTH_BASIC_UNAUTHORIZED');
                return(false);
            }
        }
        return(true);
    }

    protected function authUser_gFunc()
    {
        // nie je potreba autentifikovat usera.. Vzdy mi staci autentifikovat session. (je to o tom istom)
    }

    protected function destroySession_gFunc()
    {
        main_gClass::setPhpSessionVar_gFunc('auth_timeout_pVar', 0);
        if(modules_gClass::isModuleRegistred_gFunc('https') && https_gClass::enabled_gFunc()) {
            setcookie('loggedon_pVar', '0', null, main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar'), null, null, true);
            main_gClass::regenerateSessionId_gFunc();
        }
        $realm_pVar = main_gClass::getConfigVar_gFunc('realm', 'access1_auth');
        header('WWW-Authenticate: Basic realm="'.$realm_pVar.'"');
        header('HTTP/1.0 401 Unauthorized');
    }

    private function clearSesion_gFunc()
    {
        main_gClass::setPhpSessionVar_gFunc('auth_remote_addr_pVar', 0);
        main_gClass::setPhpSessionVar_gFunc('auth_user_agent_pVar', 0);
        main_gClass::setPhpSessionVar_gFunc('auth_user_name_pVar', 0);
        main_gClass::setPhpSessionVar_gFunc('auth_method_pVar', 0);
    }

    protected function _userHasRights_gFunc($actionID_pVar, $objectID_pVar = false, $status_pVar = self::ACCESS_ALL_pVar, $logValue_pVar = false, $userID_pVar = false, $childResult_pVar = null)
    {
        if(($userID_pVar === false || $userID_pVar === $this->userID_pVar) && $this->isLoggedOn_gFunc()) {
            // ak je prihlaseny, ma administratorske prava
            return(parent::_userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar, $userID_pVar, true));
        }
        else {
            return(parent::_userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar, $userID_pVar, false));
        }
    }
}
