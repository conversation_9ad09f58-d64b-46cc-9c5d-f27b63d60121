<?php


class list_items_gClass extends source_gClass
{

    protected function getData()
    {
        if(!isset($this->params['_type'])) {
            return(array());
        }
        $type_pVar = $this->params['_type'];

        $filter_pVar = $this->params;
        unset($filter_pVar['_type']);

        $items_pVar = items_gClass::getItems_gFunc($type_pVar, $filter_pVar);
        unset($items_pVar['filter']);
        return($items_pVar);
    }
}

class list_items extends list_items_gClass {}
