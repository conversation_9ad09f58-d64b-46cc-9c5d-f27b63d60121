<?php

class itemsAdapter_gClass extends truEngineBaseClass_gClass
{
    protected $systemName_pVar;
    protected $dbItem_pVar;

    function __construct($systemName_pVar = false)
    {
        if($systemName_pVar === false) {
            $className_pVar = self::getClassName_gFunc();
            if(substr($className_pVar, 0, 13) == 'itemsAdapter_') {
                $this->systemName_pVar = substr($className_pVar, 13);
            }
            $this->dbItem_pVar = false;
        }
        else {
            $this->systemName_pVar = $systemName_pVar;
            $this->dbItem_pVar = true;
        }
    }

    function getItemsFields_gFunc()
    {
        return(db_items_gClass::getItemsFields_gFunc($this->systemName_pVar));
    }
    function getItemsFilterFields_gFunc()
    {
        $fields_pVar = db_items_gClass::getItemsFields_gFunc($this->systemName_pVar);
        if($this->systemName_pVar == 'test_templates') {
            $tmp_pVar = $fields_pVar;
            $fields_pVar = array();
            $fields_pVar['owner_id'] = $tmp_pVar['owner_id'];
            $fields_pVar['insert_time'] = $tmp_pVar['insert_time'];
            $fields_pVar['update_time'] = $tmp_pVar['update_time'];
            $fields_pVar['name'] = $tmp_pVar['name'];
            $fields_pVar['smer'] = $tmp_pVar['smer'];
            $fields_pVar['rocnik'] = $tmp_pVar['rocnik'];
            $fields_pVar['pocet_otazok'] = $tmp_pVar['pocet_otazok'];
            $fields_pVar['language'] = $tmp_pVar['language'];
        }

        return($fields_pVar);
    }

    function getItemFormFields_gFunc($systemName_pVar, $indexByTag_pVar = false)
    {
        return(db_items_gClass::getItemFormFields_gFunc($systemName_pVar, $indexByTag_pVar));
    }

    function getInfo_gFunc()
    {
        return(db_items_gClass::getInfo_gFunc($this->systemName_pVar));
    }

    function getEnumFieldsValues_gFunc($where_str_pVar = '', $where_data_pVar = array(), $columns_pVar = '*', $index_by_pVar = false)
    {
        return(db_items_gClass::getEnumFieldsValues_gFunc($this->systemName_pVar, $where_str_pVar, $where_data_pVar, $columns_pVar, $index_by_pVar));
    }

    function getItems_gFunc($filter_pVar = array())
    {
        if($this->dbItem_pVar) {
            return(items_gClass::getItems_gFunc($this->systemName_pVar, $filter_pVar));
        }
        else {
            return($this->custom_getItems_gFunc($filter_pVar));
        }
    }

    protected function getItems_checkRights_gFunc($filter_pVar)
    {
        return(true);
    }

    protected function _pagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar, $offset_pVar, $pageLen_pVar)
    {
        return(array());
    }

    protected function _nonPagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar)
    {
        return(array());
    }

    protected function _getCountData_gFunc($where_str_pVar, $where_data_pVar)
    {
        return(0);
    }

    protected function _getOrderByFields_gFunc()
    {
        return(array());
    }

    protected function custom_getFilterQuery_gFunc($filter_pVar)
    {
        return(db_items_gClass::getFilterQuery_gFunc($filter_pVar, $this->systemName_pVar));
    }

    protected function custom_getItems_gFunc($filter_pVar)
    {
        $filter_pVar = items_gClass::_prepareFilter_gFunc($this->systemName_pVar, $filter_pVar, true);
        if($filter_pVar === false) {
            return(false);
        }

        if(!$this->getItems_checkRights_gFunc($filter_pVar)) {
            return(array());
        }

        $data_format_pVar = db_items_gClass::readDataFormatParameters_gFunc($filter_pVar);

        if(isset($filter_pVar['pager'])) {
            $pager_pVar = explode(',', $filter_pVar['pager']);
            if(strlen($pager_pVar[1]) && intval($pager_pVar[1]) >= 0) {
                $pager_pVar[1] = intval($pager_pVar[1]) - 1;
            }
            unset($filter_pVar['pager']);

            if($pager_pVar[1] < 0) {
                $pager_pVar[0] = 10000;
                $pager_pVar[1] = 0;
            }

        }
        else {
            $pager_pVar = false;
        }


        list($where_str_pVar, $where_data_pVar, $order_by_pVar) = $this->custom_getFilterQuery_gFunc($filter_pVar);

        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;

            $data_pVar = $this->_pagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar, $offset_pVar, $pageLen_pVar);
        }
        else {
            $data_pVar = $this->_nonPagerData_gFunc($where_str_pVar, $where_data_pVar, $order_by_pVar);
            $pager_pVar = false;
        }

        //echo '<pre>'; print_r($data_pVar); echo '</pre>';

        // ref fieldy su ako polia obsahujuce jednu polozku, zmenim ich na obycajne premenne
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            foreach ($data_pVar[$k_pVar] as $kk_pVar=>$vv_pVar) {
                if(is_array($vv_pVar) && count($vv_pVar) == 1 && array_key_exists(0, $vv_pVar)) {
                    $i_pVar = strpos($kk_pVar, '.');
                    if($i_pVar !== false && isset($refs_pVar[substr($kk_pVar, 0, $i_pVar)])) {
                        $data_pVar[$k_pVar][$kk_pVar] = $vv_pVar[0];
                    }
                }
            }
        }


        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            $lng_pVar = main_gClass::getLanguage_gFunc() . '_';
            foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
                if(substr($kk_pVar, 0, 3) === $lng_pVar) {
                    $data_pVar[$k_pVar][substr($kk_pVar, 3)] = $vv_pVar;
                }
            }
        }

        if($data_format_pVar['data_format'] !== 'data') {
            $data_pVar = db_items_gClass::formatData_gFunc($data_pVar, $data_format_pVar);
        }

        $data_pVar['filter'] = $filter_pVar;

        if($pager_pVar !== false) {
            $pager_pVar[2] = $this->_getCountData_gFunc($where_str_pVar, $where_data_pVar);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }

        $data_pVar['_order_by_fields'] = $this->_getOrderByFields_gFunc();

        return $data_pVar;
    }
}
