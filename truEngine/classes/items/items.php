<?php

class items_gClass
{
    static public function getAdapter_gFunc($systemName_pVar)
    {
        $info_pVar = db_items_gClass::getInfo_gFunc($systemName_pVar);

        if(is_array($info_pVar)) {
            return(new itemsAdapter_gClass($systemName_pVar));
        }
        else {
            $className_pVar = 'itemsAdapter_' . $systemName_pVar;

            if(modules_gClass::initClass_gFunc($className_pVar)) {
                return(new $className_pVar);
            }

            // este skusim source
            if(modules_gClass::initClass_gFunc($systemName_pVar)) {
                if(is_subclass_of($systemName_pVar, 'source_gClass')) {
                    return(new $systemName_pVar);
                }
            }
            return(false);
        }
    }

    static public function itemAction_gFunc($systemName_pVar, $attrs_pVar = array())
    {
        if(!isset($attrs_pVar['action'])) {
            return(false);
        }
        $action_pVar = $attrs_pVar['action'];
        unset($attrs_pVar['action']);
        switch ($action_pVar) {
            case 'itemget':
                return(self::getItems_gFunc($systemName_pVar, $attrs_pVar));
                break;
        }
    }

    static public function initFormRef_gFunc($systemName_pVar, form_gClass $formObject_pVar, $initData_pVar = array(), $isSearchForm_pVar = false, $formType_pVar = 'edit_item')
    {
        $adapter_pVar = self::getAdapter_gFunc($systemName_pVar);

        $trees_pVar = array();
        $fields_pVar = $adapter_pVar->getItemFormFields_gFunc($systemName_pVar, false); // tu musi byt false, inak nevidim values.. aj ked neviem presne preco.. neskumal som to

        $fileItems_pVar = array();
        //$initData_pVar['item_id'];
        //$initData_pVar['url_filter'];

        // loadnem info
        $info_pVar = db_items_gClass::getInfo_gFunc($systemName_pVar);
        // nastavim jazyky
        $baseLng_pVar = main_gClass::getLanguage_gFunc();
        if($isSearchForm_pVar) {
            $multilingual_pVar = false;
        }
        else {
            $multilingual_pVar = true;
        }

        // vynimka pre zakladnu strukturu
        if($systemName_pVar === '') {
            $isMainSystem_pVar = true;
            $multilingual_pVar = false;
            $lngPrefix_pVar = '';
        }
        else {
            $isMainSystem_pVar = false;
            $lngPrefix_pVar = $baseLng_pVar . '_';
        }

        // selectnem form rules
        $formRules_pVar = db_items_gClass::getFormRules_gFunc($systemName_pVar, $formType_pVar);

        // nastavim prefix
        $oldFieldPrefix_pVar = $formObject_pVar->getFieldPrefix_gFunc();
        $formObject_pVar->setFieldPrefix_gFunc($oldFieldPrefix_pVar . $systemName_pVar . '_');

        //$formObject_pVar->setFieldsetPrefix_gFunc($systemName_pVar . '_');

        // prechadzam fieldy, pridavam a inicializujem na defaultnu hodnotu
        $lastFieldId_pVar = -1;
        foreach ($fields_pVar as $k_pVar=>$v_pVar)
        {
            if($isSearchForm_pVar) {
                $search_pVar = explode(',', $v_pVar['search']);
                if(array_search('extended', $search_pVar) === false) {
                    continue;
                }
            }
            else {
                if($v_pVar['field_order'] === '0') {
                    continue;
                }
            }

            if(!isset($formRules_pVar[$v_pVar['tag']]) || $formRules_pVar[$v_pVar['tag']]['field_access'] === 'none') {
                continue;
            }

            if($formRules_pVar[$v_pVar['tag']]['field_access'] === 'hidden') {
                $v_pVar['type'] = 'hidden';
            }

            if($formRules_pVar[$v_pVar['tag']]['field_access'] === 'edit') {
                $fieldAccess_pVar = true;
            }
            else {
                $fieldAccess_pVar = false;
            }

            if(!db_items_gClass::checkRights_gFunc($systemName_pVar, 'update', $initData_pVar, $v_pVar['tag'], true)) {
                $fieldAccess_pVar = false;
            }

            if(!$formObject_pVar->isFieldset_gFunc($v_pVar['fieldset_name']) && $v_pVar['type'] !== 'hidden') {
                $formObject_pVar->addFieldset_gFunc($v_pVar['fieldset_name'], $v_pVar[$lngPrefix_pVar . 'fieldset_legend']);
            }

            if($lastFieldId_pVar !== $v_pVar['tag']) {
                $lastFieldId_pVar = $v_pVar['tag'];
                if(!$isSearchForm_pVar) {
                    if($multilingual_pVar &&
                        ($v_pVar['type'] === 'varchar' || $v_pVar['type'] === 'text'
                            || $v_pVar['type'] === 'filelist' || $v_pVar['type'] === 'imagelist'
                        )) {
                        $user_default_language_pVar = session_gClass::getUserDetail_gFunc('default_language');
                        $lngId_pVar = 0;
                        foreach ($info_pVar['languages'] as $language_pVar) {
                            if($v_pVar['type'] === 'hidden') {
                                if($formRules_pVar[$v_pVar['tag']]['field_init'] === 'default') {
                                    $value_pVar = $v_pVar[$lngPrefix_pVar . 'default_value'];
                                }
                                else {
                                    $value_pVar = $formRules_pVar[$v_pVar['tag']]['field_value'];
                                }
                                $formObject_pVar->addHiddenField_gFunc($language_pVar . '_' . $v_pVar['tag'], null, null, $value_pVar);
                            }
                            else {
                                $required_pVar = $v_pVar['not_null']=='yes'?true:false;
                                if($lngId_pVar) {
                                    $required_pVar = false;
                                }
                                $formObject_pVar->addField_gFunc($v_pVar['fieldset_name'], $language_pVar . '_' . $v_pVar['tag'], $v_pVar['type'], '[' .$language_pVar .'] ' . $v_pVar[$baseLng_pVar . '_name'], $required_pVar, !empty($v_pVar['pattern'])?$v_pVar['pattern']:null, $fieldAccess_pVar);
                                if(isset($v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info']) && !empty($v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info'])) {
                                    $formObject_pVar->setFieldInfo_gFunc($language_pVar . '_' . $v_pVar['tag'], $v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info']);
                                }
                            }
                            $lngId_pVar++;
                        }
                    }
                    else {
                        $type_pVar = $v_pVar['type'];
                        if($type_pVar === 'xvarchar') {
                            $type_pVar = 'varchar';
                        }
                        if($type_pVar === 'xtext') {
                            $type_pVar = 'text';
                        }
                        if($v_pVar['type'] === 'hidden') {
                            if($formRules_pVar[$v_pVar['tag']]['field_init'] === 'default') {
                                $value_pVar = $v_pVar[$lngPrefix_pVar . 'default_value'];
                            }
                            else {
                                $value_pVar = $formRules_pVar[$v_pVar['tag']]['field_value'];
                            }
                            $formObject_pVar->addHiddenField_gFunc($v_pVar['tag'], null, null, $value_pVar);
                        }
                        else {
                            if($type_pVar === 'itemlist') {
                                /*
                                                            $fsetPrefix_pVar = $formObject_pVar->getFieldsetPrefix_gFunc();
                                                            $fPrefix_pVar = $formObject_pVar->getFieldPrefix_gFunc();
                                                            for($i=0; $i<5; $i++) {
                                                                $fPrefix_pVar = chr(ord('A') + $i) . '_';
                                                                $formObject_pVar->setFieldPrefix_gFunc($fPrefix_pVar);
                                                                self::initFormRef_gFunc($v_pVar['pattern'], $formObject_pVar, $initData_pVar, $isSearchForm_pVar, $formType_pVar);
                                                            }
                                                            $formObject_pVar->setFieldsetPrefix_gFunc($fsetPrefix_pVar);
                                                            $formObject_pVar->setFieldPrefix_gFunc($fPrefix_pVar);
                                */
                                // tu pridam polozky joinu
                                /*

                                je to haluz... lepsie bude ked spravim len nejaky odkaz na tieto polozky, a pridavat ich budem
                                samostatnym formularom pri EDITOVANI (nie vkladani). Len toto nejak zautomatizovat. Ale neriesit to cez jeden formular.
                                cize pridat odpoved... form.. dalsia, form... dalsia... a tabulka zadanych odpovedi.
                                teda zoznam relevantnych odpovedi + formular vlozenia (editovania) vramci jednej stranky.
                                obmedzenie poctu poloziek.

                                - do formularovej struktuty pridam pole items

                                ---- alebo to cele spravit ako novy tab? cize povodne riesenie?
                                a pre kazdy takyti includnuty item samostatny tab.


                                $formSubObject_pVar = new form_gClass('get', $formObject_pVar->getFormId_gFunc() . $v_pVar['pattern']);
                                $itemSubObj_pVar = new items_gClass();
                                $itemSubObj_pVar->initFormRef_gFunc($v_pVar['pattern'], $formSubObject_pVar, $initData_pVar, $isSearchForm_pVar, $formType_pVar);

                                echo '<pre>'; print_r($formSubObject_pVar); echo '</pre>';
                                */
                            }
                            else {
                                $pattern_pVar = null;

                                if($v_pVar['type'] !== 'ref' && !empty($v_pVar['pattern'])) {
                                    $pattern_pVar = $v_pVar['pattern'];
                                }

                                $formObject_pVar->addField_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], $type_pVar, $v_pVar[$lngPrefix_pVar . 'name'], $v_pVar['not_null']=='yes'?true:false, $pattern_pVar, $fieldAccess_pVar);
                                if(isset($v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info']) && !empty($v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info'])) {
                                    $formObject_pVar->setFieldInfo_gFunc($v_pVar['tag'], $v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info']);
                                }
                            }
                        }
                    }
                }
                else {
                    // ak je to searchbox, tak
                    //   - enum sa zobrazi ako set
                    //   - int, float sa zobrazi ako intInterval, floatInterval
                    //   - podobne date, time, datetime... a ine
                    switch ($v_pVar['type']) {
                        case 'enum':
                            $type_pVar = 'set';
                            break;
                        case 'int':
                            $type_pVar = 'intInterval';
                            break;
                        case 'float':
                            $type_pVar = 'floatInterval';
                            break;
                        default:
                            $type_pVar = $v_pVar['type'];
                            break;
                    }
                    $formObject_pVar->addField_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], $type_pVar, $v_pVar[$baseLng_pVar . '_name'], false, !empty($v_pVar['pattern'])?$v_pVar['pattern']:null, $fieldAccess_pVar);
                    if(isset($v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info']) && !empty($v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info'])) {
                        $formObject_pVar->setFieldInfo_gFunc($v_pVar['tag'], $v_pVar[main_gClass::getLanguage_gFunc() . '_' . 'field_info']);
                    }
                }
                if($v_pVar['type'] === 'imagelist' || $v_pVar['type'] === 'filelist'
                    || $v_pVar['type'] === 'xfilelist' || $v_pVar['type'] === 'xfilelist') {
                    $fileItems_pVar[$v_pVar['tag']] =  $v_pVar['type'][0]==='x'?false:true;
                }
            }

            if($multilingual_pVar &&
                ($v_pVar['type'] === 'varchar' || $v_pVar['type'] === 'text'
                    || $v_pVar['type'] === 'filelist' || $v_pVar['type'] === 'imagelist'
                )) {
                $lngId_pVar = 0;
                foreach ($info_pVar['languages'] as $language_pVar) {
                    // field options nemusim nastavovat, lebo varchar,text,filelist a imagelist nepodporuju options
                    if($v_pVar[$language_pVar . '_default_value'] !== null) {
                        if($formRules_pVar[$v_pVar['tag']]['field_access'] === 'value') {
                            $formObject_pVar->setDefaultValue_pVar($v_pVar['fieldset_name'], $language_pVar . '_' . $v_pVar['tag'], $formRules_pVar[$v_pVar['tag']]['field_value']);
                        }
                        else {
                            $formObject_pVar->setDefaultValue_pVar($v_pVar['fieldset_name'], $language_pVar . '_' . $v_pVar['tag'], $v_pVar[$language_pVar . '_default_value']);
                        }
                    }
                    $formObject_pVar->setFieldLng_gFunc($language_pVar . '_' . $v_pVar['tag'], $language_pVar, $lngId_pVar?false:true, $info_pVar['languages']);
                    $formObject_pVar->initField_gFunc($v_pVar['fieldset_name'], $language_pVar . '_' . $v_pVar['tag']);
                    $lngId_pVar++;
                }
            }
            elseif ($v_pVar['type'] === 'itemlist') {
                // naplnim options
                $formObject_pVar->usePreparedOtionsForField_gFunc($v_pVar['tag']);
                // nastavim default values

                $formObject_pVar->initField_gFunc($v_pVar['fieldset_name'], $v_pVar['tag']);

            }
            elseif ($v_pVar['type'] === 'hidden') {
                //...zatial nic netreba
                $formObject_pVar->initField_gFunc($v_pVar['fieldset_name'], $v_pVar['tag']);
            }
            else {
                if($v_pVar[$lngPrefix_pVar . 'default_value'] === null && !$isSearchForm_pVar && $v_pVar['type'] !== 'set') {
                    if(!$formObject_pVar->countOptions_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'])) {
                        $formObject_pVar->addFieldOption_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], '-1', '');
                    }
                }
                $formObject_pVar->addFieldOption_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], $v_pVar['enum_field_value'], $v_pVar[$lngPrefix_pVar . 'enum_field_name_item']);
                if(!empty($v_pVar[$lngPrefix_pVar . 'enum_confirm_set'])) {
                    $formObject_pVar->setFieldOptionConfirm_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], $v_pVar['enum_field_value'], $v_pVar[$lngPrefix_pVar . 'enum_confirm_set']);
                }
                if(!empty($v_pVar[$lngPrefix_pVar . 'enum_confirm_unset'])) {
                    $formObject_pVar->setFieldOptionConfirm_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], $v_pVar['enum_field_value'], false, $v_pVar[$lngPrefix_pVar . 'enum_confirm_unset']);
                }

                if($formRules_pVar[$v_pVar['tag']]['field_access'] === 'value') {
                    $formObject_pVar->setDefaultValue_pVar($v_pVar['fieldset_name'], $v_pVar['tag'], $formRules_pVar[$v_pVar['tag']]['field_value']);
                }
                else {
                    if($v_pVar[$lngPrefix_pVar . 'default_value'] !== null) {
                        $formObject_pVar->setDefaultValue_pVar($v_pVar['fieldset_name'], $v_pVar['tag'], $v_pVar[$lngPrefix_pVar . 'default_value']);
                    }
                }
                $formObject_pVar->initField_gFunc($v_pVar['fieldset_name'], $v_pVar['tag']);

                if($info_pVar['tree_defs'] == 'yes') {
                    $tree_defined_pVar = false;
                    foreach ($trees_pVar as $vTree_pVar) {
                        if(isset($vTree_pVar['_defs'][$v_pVar['tag']])) {
                            $tree_defined_pVar = true;
                            $formObject_pVar->setFieldEvent_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], 'onchange', 'selectFilterChanged('.$systemName_pVar.'_'.$tree_pVar['_tree'].'_selector, \''.$v_pVar['tag'].'\', \''.$systemName_pVar.'_\');');
                        }
                    }
                    if(!$tree_defined_pVar) {
                        $tree_pVar = db_items_gClass::getTreeForField_gFunc($systemName_pVar, $v_pVar['tag']);
                        if($tree_pVar) {
                            $trees_pVar[] = $tree_pVar;
                            if($tree_pVar) {
                                $formObject_pVar->addJavaScript_gFunc($tree_pVar['_js']);
                                $formObject_pVar->setFieldEvent_gFunc($v_pVar['fieldset_name'], $v_pVar['tag'], 'onchange', 'selectFilterChanged('.$systemName_pVar.'_'.$tree_pVar['_tree'].'_selector, \''.$v_pVar['tag'].'\', \''.$systemName_pVar.'_\');');
                            }
                        }
                    }
                }
            }

        }

        if($formObject_pVar->isData_gFunc()) {
            // obnovenie hidden poloziek pre uploady (XXX_delete_N)

            foreach ($fileItems_pVar as $k_pVar=>$v_pVar) {
                if($multilingual_pVar && $v_pVar === true) {
                    foreach ($info_pVar['languages'] as $language_pVar) {
                        $i_pVar = 0;
                        while (1) {
                            $fieldName_pVar = $language_pVar . '_' . $k_pVar . '_delete_' . $i_pVar;
                            if(!$formObject_pVar->isFieldInRequest_gFunc($fieldName_pVar, '/[0-9]+/')) {
                                break;
                            }
                            $i_pVar++;
                            $formObject_pVar->addHiddenField_gFunc($fieldName_pVar, 0, '/[0-9]+/');
                        }
                    }
                }
                else {
                    $i_pVar = 0;
                    while (1) {
                        $fieldName_pVar = $k_pVar . '_delete_' . $i_pVar;
                        if(!$formObject_pVar->isFieldInRequest_gFunc($fieldName_pVar, '/[0-9]+/')) {
                            break;
                        }
                        $i_pVar++;
                        $formObject_pVar->addHiddenField_gFunc($fieldName_pVar, 0, '/[0-9]+/');
                    }
                }
            }
        }

        $formObject_pVar->addHiddenField_gFunc('item_id', 0, '/[0-9]+/');
        $formObject_pVar->addHiddenField_gFunc('url_filter', '');

        $itemIdUsed_pVar = false;
        if(isset($initData_pVar['item_id']) && !empty($initData_pVar['item_id']) && is_numeric($initData_pVar['item_id'])) {
            $initItemData_pVar = db_items_gClass::getItem_gFunc($systemName_pVar, $initData_pVar['item_id']);
            if(is_array($initItemData_pVar)) {
                $itemIdUsed_pVar = true;
                foreach ($initItemData_pVar as $k_pVar=>$v_pVar) {
                    $formObject_pVar->setFieldDefaultValue_gFunc($k_pVar, $v_pVar);
                    if(isset($fileItems_pVar[$k_pVar]) && $fileItems_pVar[$k_pVar] === false) {
                        foreach ($v_pVar as $kImage_pVar => $image_pVar) {
                            $formObject_pVar->addHiddenField_gFunc($k_pVar . '_delete_' . $kImage_pVar, '0', '/[0-9]+/');
                        }
                    }

                    if($multilingual_pVar) {
                        foreach ($info_pVar['languages'] as $language_pVar) {
                            if(substr($k_pVar, 0, 3) === $language_pVar . '_') {
                                $tmpK_pVar = substr($k_pVar, 3);
                                if(isset($fileItems_pVar[$tmpK_pVar]) && $fileItems_pVar[$tmpK_pVar] === true) {
                                    foreach ($v_pVar as $kImage_pVar => $image_pVar) {
                                        $formObject_pVar->addHiddenField_gFunc($k_pVar . '_delete_' . $kImage_pVar, '0', '/[0-9]+/');
                                    }
                                }
                            }
                        }
                    }
                }
                $formObject_pVar->setHiddenFieldDefaultValue_gFunc('item_id', $initData_pVar['item_id']);
            }
        }
        if($itemIdUsed_pVar !== true) {
            // ak nebolo pouzite item_id, skusim inicializovat podla filtra.
            if(isset($initData_pVar['url_filter']) && !empty($initData_pVar['url_filter'])) {
                $formObject_pVar->setHiddenFieldDefaultValue_gFunc('url_filter', $initData_pVar['url_filter']);
                $filter_pVar = self::_prepareFilter_gFunc($systemName_pVar, array('url_filter'=>$initData_pVar['url_filter']));
                foreach ($filter_pVar as $k_pVar=>$v_pVar) {
                    if(substr($k_pVar, 0, 1) === '_') {
                        continue;
                    }
                    if($k_pVar === 'item_id') {
                        continue;
                    }
                    $formObject_pVar->setFieldDefaultValue_gFunc($k_pVar, $v_pVar);
                }
            }
        }

        $fieldsNames_pVar = array();
        foreach($fields_pVar as $k_pVar=>$v_pVar) {
            $fieldsNames_pVar[$k_pVar] = $v_pVar['tag'];
        }
        foreach ($initData_pVar as $k_pVar=>$v_pVar) {
            if(in_array($k_pVar, $fieldsNames_pVar)) {
                $formObject_pVar->setFieldDefaultValue_gFunc($k_pVar, $v_pVar);
            }
            $formObject_pVar->setVar_gFunc($k_pVar, $v_pVar);
        }
        $itemId_pVar = (int)$formObject_pVar->getFieldValue_gFunc('item_id');
        $urlFilter_pVar = $formObject_pVar->getFieldValue_gFunc('url_filter');

        $formObject_pVar->setVar_gFunc('item_id', $itemId_pVar);

        if($formObject_pVar->getMultieditLevel_gFunc() === false || $formObject_pVar->getMultieditLevel_gFunc() === 1) {
            if($itemId_pVar) {
                if(isset($initData_pVar['submit_button_title_update'])) {
                    $formObject_pVar->setVar_gFunc('submit_button_title', $initData_pVar['submit_button_title_update'], false);
                }
                else {
                    $formObject_pVar->setVar_gFunc('submit_button_title', string_gClass::get('str_forms_submit_button_title_pVar', false));
                }
            }
            else {
                if(isset($initData_pVar['submit_button_title_add'])) {
                    $formObject_pVar->setVar_gFunc('submit_button_title', $initData_pVar['submit_button_title_add'], false);
                }
                else {
                    $formObject_pVar->setVar_gFunc('submit_button_title', string_gClass::get('str_forms_submit_button_title_pVar'), false);
                }
            }
        }

        if(!empty($urlFilter_pVar)) {
            $formObject_pVar->setVar_gFunc('url_filter', $urlFilter_pVar);
        }

        if(isset(varStack_gClass::$vars['doc']) && !empty(varStack_gClass::$vars['doc'])) {
            $tmp_pVar = varStack_gClass::$vars['doc'];
            if($tmp_pVar[0] !== '/') {
                $tmp_pVar = '/' . $tmp_pVar;
            }
            $formObject_pVar->setFormAttribute_gFunc('action', $tmp_pVar);
        }

        $formObject_pVar->setFieldPrefix_gFunc($oldFieldPrefix_pVar);
    }

    /**
     * Vrati zoznam poloziek podla filtra
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $filter_pVar
     * @return unknown
     */
    static public function getItems_gFunc($systemName_pVar, $filter_pVar = array(), $readExtInfo_pVar = true)
    {
        /**
         * dd($filter_pVar);
         *
         * array:3 [▼
         *      "filter" => "status=active"
         *      "pager" => "20,"
         *      "_order_by" => ""
         * ]
         */


        $filter_pVar = self::_prepareFilter_gFunc($systemName_pVar, $filter_pVar, true);


        return(db_items_gClass::getItems_gFunc($systemName_pVar, $filter_pVar, $readExtInfo_pVar));
    }

    static public function getItemsIds_gFunc($systemName_pVar, $filter_pVar = array())
    {
        $items_pVar = self::getItems_gFunc($systemName_pVar, $filter_pVar);
        $ids_pVar = self::getItemsIdsFromData_gFunc($items_pVar);
        return($ids_pVar);
    }

    static public function getItemsIdsFromData_gFunc($items_pVar)
    {
        $ids_pVar = array();
        foreach ($items_pVar as $ik_pVar=>$iv_pVar) {
            if(substr($ik_pVar, 0, 1) == '_') {
                continue;
            }
            if(isset($iv_pVar['item_id'])) {
                $ids_pVar[] = $iv_pVar['item_id'];
            }
        }
        return($ids_pVar);
    }

    static public function getLog_gFunc($systemName_pVar, $ids_pVar = array(), $addToLog_pVar = array(), $sortIndex_pVar = '0')
    {
        if(is_numeric($ids_pVar)) {
            $ids_pVar = array($ids_pVar);
        }
        if(!is_array($ids_pVar)) {
            $ids_pVar = array();
        }

        $tmpLog_pVar = db_items_gClass::getLog_pVar($systemName_pVar, $ids_pVar, $sortIndex_pVar);
        if(!is_array($addToLog_pVar) || !count($addToLog_pVar)) {
            return($tmpLog_pVar);
        }

        // spojim logy
        $log_pVar = array_merge($addToLog_pVar, $tmpLog_pVar);
        array_multisort($log_pVar, SORT_ASC);

        return($log_pVar);
    }

    static public function searchItems_gFunc($systemName_pVar, $params_pVar = array())
    {
        $filter_pVar = self::_prepareSearchFilter_gFunc($systemName_pVar, $params_pVar);
        return(db_items_gClass::getItems_gFunc($systemName_pVar, $filter_pVar));
    }

    /**
     * Vrati jeden item.
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $default_values_pVar
     * @return unknown
     */
    static public function getItem_gFunc($systemName_pVar, $itemId_pVar)
    {
        return(db_items_gClass::getItem_gFunc($systemName_pVar, $itemId_pVar));
    }


    /**
     * Update itemu podla struktury formularu
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $formObject_pVar
     * @return unknown
     */
    static public function editItemByForm_gFunc($systemName_pVar, $formObject_pVar, $overwrite_data_pVar = array())
    {
        // nastavim cestu na upload
        $uploadLocation_pVar = 'secured';
        if($systemName_pVar === 'eshop') {
            $uploadLocation_pVar = 'products';
        }
        if($systemName_pVar === 'test_questions') {
            $uploadLocation_pVar = 'questions';
        }
        if($systemName_pVar === 'test_answers') {
            $uploadLocation_pVar = 'questions';
        }

        $multieditLevel_pVar = $formObject_pVar->getMultieditLevel_gFunc();
        for($i_pVar = 0; $i_pVar < ($multieditLevel_pVar===false?1:$multieditLevel_pVar); $i_pVar++) {
            if($multieditLevel_pVar === false) {
                $prefix_pVar = '';
                $formObject_pVar->setFieldsetPrefix_gFunc('');
                $formObject_pVar->setFieldPrefix_gFunc('');
            }
            else {
                $formObject_pVar->setFieldsetPrefix_gFunc('a' . $i_pVar . '_');
                $formObject_pVar->setFieldPrefix_gFunc('a' . $i_pVar . '_');

                // vynecham neaktivne bloky
                if($multieditLevel_pVar !== false) {
                    if(!$formObject_pVar->getFieldValue_gFunc($systemName_pVar . '_active_formpart')) {
                        continue;
                    }
                }
            }

            $itemId_pVar = $formObject_pVar->getFieldValue_gFunc($systemName_pVar . '_item_id');
            if($formObject_pVar->uploadFiles_gFunc('items_' . $systemName_pVar . '_', $uploadLocation_pVar, $itemId_pVar)) {
                $values_pVar = $formObject_pVar->extractItemValues_gFunc($systemName_pVar);
                if(count($overwrite_data_pVar)) {
                    foreach($overwrite_data_pVar as $k_pVar=>$v_pVar) {
                        $values_pVar[$k_pVar] = $v_pVar;
                    }
                }
                $itemId_pVar = self::saveOrUpdateItem_gFunc($systemName_pVar, $values_pVar);
                $formObject_pVar->setVar_gFunc('item_id', $itemId_pVar);
                if($systemName_pVar !== '' && $systemName_pVar !== 'downloads') {
                    $formObject_pVar->setVar_gFunc('item_url', db_items_gClass::getUrlForItem_gFunc($systemName_pVar, $values_pVar, false));
                    $formObject_pVar->setVar_gFUnc('category_url', db_items_gClass::getUrlForItem_gFunc($systemName_pVar, $values_pVar, true));
                }
            }

        }

        $formObject_pVar->setFieldsetPrefix_gFunc('');
        $formObject_pVar->setFieldPrefix_gFunc('');

        return(true);
    }


    /**
     * Aktualizuje alebo vytvori novy item.
     * Ak je nastavene $itemData_pVar['item_id'], spravi aktualizaciu.
     *
     * @param unknown_type $itemData_pVar
     */
    static public function saveOrUpdateItem_gFunc($systemName_pVar, &$itemData_pVar)
    {
        return(db_items_gClass::saveOrUpdateItem_gFunc($systemName_pVar, $itemData_pVar));
    }

    /**
     * Vrati data z $_REQUEST.
     * Ak $onlyFormData_pVar = true, tak ich vrati iba ak boli odoslane z formulara.
     * Ak $onlyFormData_pVar = false, tak vrati aj inicializacne data
     *
     * vrati aj priznak ci su z formulara _isSubmitedData
     *
     * @param unknown_type $onlyFormData_pVar
     */
    static public function  getDataFromRequest_gFunc($systemName_pVar, $onlyFormData_pVar = false)
    {
        $data_pVar = array();
        if(!main_gClass::getInputInteger_gFunc('item_data_'.$systemName_pVar, main_gClass::SRC_REQUEST_pVar, 0)) {
            if($onlyFormData_pVar) {
                return(false);
            }
        }
        else {
            $data_pVar['_isSubmitedData'] = true;
        }

        $fields_pVar = db_items_gClass::getItemsFields_gFunc($systemName_pVar);

        foreach ($fields_pVar as $k_pVar=>$v_pVar) {
            $value_pVar = main_gClass::getInputString_gFunc($v_pVar['tag']);
            if($value_pVar !== null) {
                $data_pVar[$v_pVar['tag']] = $value_pVar;
            }
        }
        $item_id_pVar = main_gClass::getInputInteger_gFunc('item_id', main_gClass::SRC_REQUEST_pVar, 0);
        if($item_id_pVar) {
            $data_pVar['item_id'] = $item_id_pVar;
        }
        return($data_pVar);
    }

    /**
     * Spracuje polozky filtra do standardnej formy
     * napr. ak nastavim do filtra url parameter, rozlozi ho na jednotlive elementy.
     *
     * Ak nastavim strict, tak ak polozky nebudu ok, vrati false.
     * 		Teda napr. url nebude vyhovovat pre ziadne polozky, vratim false.
     *
     * @param unknown_type $systemName_pVar
     * @param unknown_type $filter_pVar
     * @param unknown_type $strict_pVar
     * @return unknown
     */
    static public function _prepareFilter_gFunc($systemName_pVar, $filter_pVar = array(), $strict_pVar = true)
    {
        // ak je _isSubmitedData, tak som bral filter z requestu, a nie je to filter, ale data odoslane
        // z formulara... Preto vraciam false.
        if(isset($filter_pVar['_isSubmitedData']) && $filter_pVar['_isSubmitedData']) {
            return(false);
        }

        $baseLanguage_pVar = main_gClass::getLanguage_gFunc();

        // ak je nastavene pole attrs_pVar, tak ho najskor rozbalim.
        if(isset($filter_pVar['attrs_pVar'])) {
            $attrs_pVar = $filter_pVar['attrs_pVar'];
            if(isset($attrs_pVar['filter'])) {
                $filter_pVar['filter'] = $attrs_pVar['filter'];
            }
            if(isset($attrs_pVar['url_filter'])) {
                $filter_pVar['url_filter'] = $attrs_pVar['url_filter'];
            }
            if(isset($attrs_pVar['item_id'])) {
                $filter_pVar['item_id'] = $attrs_pVar['item_id'];
            }
            unset($filter_pVar['attrs_pVar']);
        }

        // ak je item_id, ostatne polozky nepotrebujem, a su mi nanic
        if(isset($filter_pVar['item_id'])) {
            $item_id = intval($filter_pVar['item_id']);
            if($item_id) {
                $filter_pVar = array('item_id'=>$item_id);
                return($filter_pVar);
            }
            else {
                unset($filter_pVar['item_id']);
            }
        }

        $adapter_pVar = items_gClass::getAdapter_gFunc($systemName_pVar);
        $fields_pVar = $adapter_pVar->getItemsFields_gFunc();

        if(isset($filter_pVar['url_filter'])) {
            $filter_pVar['url_filter'] = self::_normalizeUrlForFilter_gFunc($filter_pVar['url_filter']);
            if(is_array($filter_pVar['url_filter'])) {
                $urlFilter_pVar = array();
                // ziskam field id pre url fragmenty
                $maxUrlOrder_pVar = -1;
                for($i_pVar=0; isset($filter_pVar['url_filter'][$i_pVar]); $i_pVar++) {
                    // find field url
                    $fieldKey_pVar = false;
                    foreach ($fields_pVar as $k_pVar=>$v_pVar) {
                        if($v_pVar['url_order'] !== null && is_numeric($v_pVar['url_order'])) {
                            $maxUrlOrder_pVar = max($maxUrlOrder_pVar, $v_pVar['url_order']);
                        }
                        if($v_pVar['url_order'] === null || $v_pVar['url_order'] != ($i_pVar + 1)) {
                            continue;
                        }
                        $fieldKey_pVar = $k_pVar;
                        break;
                    }

                    if($i_pVar == $maxUrlOrder_pVar) {
                        // hladam produkt_id
                        $items_pVar = db_items_gClass::getItems_gFunc($systemName_pVar, array($baseLanguage_pVar . '_url'=>$filter_pVar['url_filter'][$i_pVar]));
                        if(is_array($items_pVar) && count($items_pVar) == 2) {
                            $fieldKey_pVar = $baseLanguage_pVar . '_url';
                            $urlFilter_pVar['item_id'] = $items_pVar[0]['item_id'];
                        }
                    }

                    if($fieldKey_pVar === false && $strict_pVar) {
                        $urlFilter_pVar = false;
                        break;
                    }

                    if($fieldKey_pVar !== false && $fieldKey_pVar !== $baseLanguage_pVar . '_url') {
                        $urlFilter_pVar[$fields_pVar[$fieldKey_pVar]['tag']] = array(
                            'id_pVar' => $fields_pVar[$fieldKey_pVar]['field_id'],
                            'url_pVar' => $filter_pVar['url_filter'][$i_pVar]
                        );
                    }
                }

                if($urlFilter_pVar !== false) {
                    // zo ziskanych field_id ziskam enum_field_value
                    $where_str_pVar = '';
                    $where_data_pVar = array();
                    foreach ($urlFilter_pVar as $k_pVar=>$v_pVar) {
                        if(is_array($v_pVar)) {
                            if(!empty($where_str_pVar)) {
                                $where_str_pVar .= ' OR ';
                            }
                            $where_str_pVar .= '(`enum_field_id`=%d AND `' . $baseLanguage_pVar . '_url_name`=%s)';
                            $where_data_pVar[] = $v_pVar['id_pVar'];
                            $where_data_pVar[] = $v_pVar['url_pVar'];
                        }
                    }

                    $fieldValues_pVar = db_items_gClass::getEnumFieldsValues_gFunc($systemName_pVar, $where_str_pVar, $where_data_pVar ,'`enum_id`, `enum_field_id`, `enum_field_value`, `' . $baseLanguage_pVar . '_url_name`, `' . $baseLanguage_pVar . '_enum_field_name_group`', 'enum_field_id');
                    foreach ($urlFilter_pVar as $k_pVar=>$v_pVar) {
                        if(is_array($v_pVar)) {
                            if(!isset($fieldValues_pVar[$v_pVar['id_pVar']])) {
                                if($strict_pVar) {
                                    $urlFilter_pVar = false;
                                    break;
                                }
                            }
                            else {
                                $urlFilter_pVar[$k_pVar] = $fieldValues_pVar[$v_pVar['id_pVar']]['enum_field_value'];
                                $urlFilter_pVar['_category_titles'][] = $fieldValues_pVar[$v_pVar['id_pVar']][$baseLanguage_pVar . '_enum_field_name_group'];
                            }
                        }
                        else {
                            $urlFilter_pVar[$k_pVar] = $v_pVar;
                        }
                    }
                }

                if(is_array($urlFilter_pVar)) {
                    // url filter nastavim do filtra
                    foreach ($urlFilter_pVar as $k_pVar=>$v_pVar) {
                        $filter_pVar[$k_pVar] = $v_pVar;
                    }
                }
                else {
                    if($strict_pVar) { // ak je strict, a url nevyhovuje, nastavim false
                        $filter_pVar = false;
                    }
                }
            }
            else {
                if($filter_pVar['url_filter'] === false) {
                    // nic nespravim, iba ho unsetnem, lebo som ho ja nastavil na false
                }
                if($strict_pVar) { // ak je strict, a url nie je pole, nastavim false
                    $filter_pVar = false;
                }
            }

            if($filter_pVar === false) {
                return(false);
            }
            unset($filter_pVar['url_filter']);
        }

        if(isset($filter_pVar['filter'])) { // filter retazec - rozparsujem ho
            $fitems_pVar = array();
            $tmp_pVar = str_replace('%', '%25', $filter_pVar['filter']);
            $f_pVar = parse_str($tmp_pVar, $fitems_pVar);
            if(is_array($fitems_pVar) && count($fitems_pVar)) {
                foreach ($fitems_pVar as $k_pVar=>$v_pVar) {
                    $filter_pVar[$k_pVar] = $v_pVar;
                }
            }
            unset($filter_pVar['filter']);
        }

        return($filter_pVar);
    }

    static private function _prepareSearchFilter_gFunc($systemName_pVar, $filter_pVar)
    {
        if(isset($filter_pVar['search-cond'])) {
            $filter_pVar = string_gClass::stringToArray_gFunc($filter_pVar['search-cond']);
        }

        if(isset($filter_pVar['search-phrase'])) {
            $fields_pVar = db_items_gClass::getItemsFields_gFunc($systemName_pVar);
            $conds_pVar = array();
            foreach ($fields_pVar as $k_pVar=>$v_pVar) {
                $search_pVar = explode(',', $v_pVar['search']);
                if(array_search('fulltext', $search_pVar) !== false) {
                    if($v_pVar['type'] === 'text' || $v_pVar['type'] === 'varchar') {
                        $tagName_pVar = main_gClass::getLanguage_gFunc() . '_' . $v_pVar['tag'];
                        $conds_pVar[] = array('__operator'=>'LIKE', $tagName_pVar =>'%'.$filter_pVar['search-phrase'].'%');
                    }
                    else {
                        $conds_pVar[] = array('__operator'=>'LIKE', $v_pVar['tag']=>'%'.$filter_pVar['search-phrase'].'%');
                    }
                    $conds_pVar['_operator'] = 'OR';
                }
            }
            unset($filter_pVar['search-phrase']);
            $filter_pVar[] = $conds_pVar;
        }
        else {
            /*
            if(is_array($filter_pVar)) {
                $tmpFilter_pVar = $filter_pVar;
                $filter_pVar = array();
                foreach ($tmpFilter_pVar as $k_pVar=>$v_pVar) {

                }
            }*/
        }
        return(self::_prepareFilter_gFunc($systemName_pVar, $filter_pVar, false));
    }

    static private function _normalizeUrlForFilter_gFunc($url_pVar)
    {
        if(is_array($url_pVar)) {
            return($url_pVar);
        }
        if(empty($url_pVar)) {
            return(false);
        }
        $lng_pVar = false;
        $tmp_pVar = explode('/', $url_pVar);
        $dirs_pVar = array();
        $i_pVar = 0;
        if($tmp_pVar[0] == 'sk' || $tmp_pVar[0] == 'en') {
            $i_pVar++;
            $lng_pVar = $tmp_pVar[0];
        }
        // preindexujem pole
        for(; isset($tmp_pVar[$i_pVar]); $i_pVar++)
        {
            if(!empty($tmp_pVar[$i_pVar])) {
                $dirs_pVar[] = $tmp_pVar[$i_pVar];
            }
        }

        if($lng_pVar !== false) {
            $dirs_pVar['lng_pVar'] = $lng_pVar;
        }

        return($dirs_pVar);
    }

    static public function getValues_gFunc($systemName_pVar, $fieldTagName_pVar, $order_by_pVar = false)
    {
        $fields_pVar = db_items_gClass::getItemsFields_gFunc($systemName_pVar);
        $field_id = $fields_pVar[$fieldTagName_pVar]['field_id'];
        $values_pVar = db_items_gClass::getEnumFieldsValues_gFunc($systemName_pVar, '`enum_field_id`=%d', $field_id, false, false, $order_by_pVar);

        return($values_pVar);
    }

    static public function reorderItems_gFunc($systemName_pVar, $fieldName_pVar, $step_pVar = 10, $whereStr_pVar = '')
    {
        db_items_gClass::reorderItems_gFunc($systemName_pVar, $fieldName_pVar, $step_pVar, $whereStr_pVar);
    }

    static public function getItemComment_gFunc($systemName_pVar, $commentId_pVar)
    {
        return(db_items_gClass::getItemComment_gFunc($systemName_pVar, $commentId_pVar));
    }

    static public function getItemComments_gFunc($systemName_pVar, $itemId_pVar = 0, $creator_id_pVar = 0, $owner_id_pVar = 0, $order_by_pVar = false)
    {
        return(db_items_gClass::getItemComments_gFunc($systemName_pVar, $itemId_pVar, $creator_id_pVar, $owner_id_pVar, $order_by_pVar));
    }

    static public function addItemComment_gFunc($systemName_pVar, $itemId_pVar, $commentText_pVar)
    {
        db_items_gClass::addItemComment_gFunc($systemName_pVar, $itemId_pVar, $commentText_pVar);
    }

    static public function deleteItemComment_gFunc($systemName_pVar, $commentId_pVar)
    {
        db_items_gClass::deleteItemComment_gFunc($systemName_pVar, $commentId_pVar);
    }

    static public function getRawData_gFunc($systemName_pVar, $applyJoins_pVar = true)
    {
        return(db_items_gClass::getRawData_gFunc($systemName_pVar, $applyJoins_pVar));
    }
}
