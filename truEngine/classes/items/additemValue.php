<?php

class additemValue_gClass extends form_gClass
{
    private $itemType_pVar;
    private $fieldTag_pVar;
    private $tree_name_pVar;

    function __construct($action_pVar = 'get')
    {
        parent::__construct($action_pVar);
        $this->itemType_pVar = false;
        $this->fieldTag_pVar = false;
        $this->tree_name_pVar = false;
    }

    protected function initForm_gFunc($multiedit_pVar = false)
    {
        parent::initForm_gFunc($multiedit_pVar);
        $data_pVar = array();
        if(isset($this->params) && count($this->params)) {
            $data_pVar = $this->params;
        }
        $this->form_init_pVar = true;

        if(!isset($data_pVar['itemtype'])) {
            return(false);
        }
        $this->itemType_pVar = $data_pVar['itemtype'];
        unset($data_pVar['itemtype']);
        $this->fieldTag_pVar = $data_pVar['field_tag'];
        unset($data_pVar['field_tag']);
        if(isset($data_pVar['tree_name'])) {
            $this->tree_name_pVar = $data_pVar['tree_name'];
            unset($data_pVar['tree_name']);
        }

        $this->addHiddenField_gFunc('value_id', isset($data_pVar['value_id'])?$data_pVar['value_id']:0);
        $value_id = $this->getFieldValue_gFunc('value_id');

        $this->addFieldset_gFunc('main_sk', 'SK');
        $this->addFieldset_gFunc('main_en', 'EN');
        $this->addFieldset_gFunc('main_cz', 'CZ');

        if(!empty($value_id)) {
            $this->addField_gFunc('main', 'status', 'enum', 'Status', true, '/(active|deleted)/i');
            $this->setFieldOptions_gFunc('main','status', array('active'=>'active','deleted'=>'deleted'));
        }
        $this->addField_gFunc('main', 'enum_field_value', 'varchar', 'Enum Tag', true, false, false);

        $this->addField_gFunc('main_sk', 'sk_enum_field_name_item', 'varchar', 'Názov [sk]');
        //$this->addField_gFunc('main_sk', 'sk_enum_field_name_group', 'varchar', 'Názov - mn.č. [sk]');
        //$this->addField_gFunc('main_sk', 'sk_url_name', 'varchar', 'URL [sk]');
        $this->addField_gFunc('main_sk', 'sk_enum_confirm_set', 'varchar', 'Text potvrdenia pri nastavení (ak je prázdny,<br />potvrdenie sa nevyžaduje) [sk]');
        $this->addField_gFunc('main_sk', 'sk_enum_confirm_unset', 'varchar', 'Text potvrdenia pri zrušení (ak je prázdny,<br />potvrdenie sa nevyžaduje) [sk]');

        $this->addField_gFunc('main_en', 'en_enum_field_name_item', 'varchar', 'Názov [en]');
        //$this->addField_gFunc('main_en', 'en_enum_field_name_group', 'varchar', 'Názov - mn.č. [en]');
        //$this->addField_gFunc('main_en', 'en_url_name', 'varchar', 'URL [en]');
        $this->addField_gFunc('main_en', 'en_enum_confirm_set', 'varchar', 'Text potvrdenia pri nastavení (ak je prázdny,<br />potvrdenie sa nevyžaduje) [en]');
        $this->addField_gFunc('main_en', 'en_enum_confirm_unset', 'varchar', 'Text potvrdenia pri zrušení (ak je prázdny,<br />potvrdenie sa nevyžaduje) [en]');

        $this->addField_gFunc('main_cz', 'cz_enum_field_name_item', 'varchar', 'Názov [cz]');
        //$this->addField_gFunc('main_cz', 'cz_enum_field_name_group', 'varchar', 'Názov - mn.č. [cz]');
        //$this->addField_gFunc('main_cz', 'cz_url_name', 'varchar', 'URL [cz]');
        $this->addField_gFunc('main_cz', 'cz_enum_confirm_set', 'varchar', 'Text potvrdenia pri nastavení (ak je prázdny,<br />potvrdenie sa nevyžaduje) [cz]');
        $this->addField_gFunc('main_cz', 'cz_enum_confirm_unset', 'varchar', 'Text potvrdenia pri zrušení (ak je prázdny,<br />potvrdenie sa nevyžaduje) [cz]');

        //$this->addField_gFunc('main', 'enum_value_order', 'int', 'Index usporiadania');

        if(!empty($value_id)) { // inicializacia
            $init_pVar = db_items_gClass::getEnumFieldsValues_gFunc($this->itemType_pVar, '`enum_id`=%d', array($value_id));
            if(isset($init_pVar[0])) {
                $this->setFieldDefaultValue_gFunc('status', 'active');
                $this->setFieldDefaultValue_gFunc('enum_field_value', $init_pVar[0]['enum_field_value']);
                $this->setFieldDefaultValue_gFunc('sk_enum_field_name_item', $init_pVar[0]['sk_enum_field_name_item']);
                //$this->setFieldDefaultValue_gFunc('sk_enum_field_name_group', $init_pVar[0]['sk_enum_field_name_group']);
                $this->setFieldDefaultValue_gFunc('sk_enum_confirm_set', $init_pVar[0]['sk_enum_confirm_set']);
                $this->setFieldDefaultValue_gFunc('sk_enum_confirm_unset', $init_pVar[0]['sk_enum_confirm_unset']);
                //$this->setFieldDefaultValue_gFunc('sk_url_name', $init_pVar[0]['sk_url_name']);

                $this->setFieldDefaultValue_gFunc('en_enum_field_name_item', $init_pVar[0]['en_enum_field_name_item']);
                //$this->setFieldDefaultValue_gFunc('en_enum_field_name_group', $init_pVar[0]['en_enum_field_name_group']);
                $this->setFieldDefaultValue_gFunc('en_enum_confirm_set', $init_pVar[0]['en_enum_confirm_set']);
                $this->setFieldDefaultValue_gFunc('en_enum_confirm_unset', $init_pVar[0]['en_enum_confirm_unset']);
                //$this->setFieldDefaultValue_gFunc('en_url_name', $init_pVar[0]['en_url_name']);


                $this->setFieldDefaultValue_gFunc('cz_enum_field_name_item', $init_pVar[0]['cz_enum_field_name_item']);
                //$this->setFieldDefaultValue_gFunc('cz_enum_field_name_group', $init_pVar[0]['cz_enum_field_name_group']);
                $this->setFieldDefaultValue_gFunc('cz_enum_confirm_set', $init_pVar[0]['cz_enum_confirm_set']);
                $this->setFieldDefaultValue_gFunc('cz_enum_confirm_unset', $init_pVar[0]['cz_enum_confirm_unset']);
                //$this->setFieldDefaultValue_gFunc('cz_url_name', $init_pVar[0]['cz_url_name']);


                //$this->setFieldDefaultValue_gFunc('enum_value_order', $init_pVar[0]['enum_value_order']);
            }
        }

        // tree
        if($this->tree_name_pVar !== false) {
            $tree_pVar = db_items_gClass::getTree_gFunc($this->itemType_pVar, $this->tree_name_pVar);
            if($tree_pVar) {
                $lastParentCategory_pVar = false;
                // do kategries pridam iba podradene polozky
                $categoies_pVar = array();
                foreach ($tree_pVar['_defs'] as $k_pVar=>$v_pVar) {
                    if($k_pVar == $this->fieldTag_pVar) {
                        break;
                    }
                    $categoies_pVar[$k_pVar] = $v_pVar;
                    $lastParentCategory_pVar = $v_pVar;
                }

                // nastavim si chcecked hodnoty
                $checked_pVar = array();

                foreach ($tree_pVar['_rules'] as $k_pVar=>$v_pVar) {
                    $key_pVar = array_search($value_id, $v_pVar['tv.enum_id']);
                    foreach ($v_pVar['tv.enum_id'] as $key_pVar=>$value_pVar) {
                        if($value_id == $value_pVar) {
                            if($v_pVar['tv.parent_value_id'][$key_pVar]) {
                                $checked_pVar[] = $v_pVar['tv.parent_value_id'][$key_pVar];
                            }
                            if($v_pVar['tv.grandparent_value_id'][$key_pVar]) {
                                $checked_pVar[] = $v_pVar['tv.grandparent_value_id'][$key_pVar];
                            }
                        }
                    }
                }


                if(count($categoies_pVar)) {
                    $this->addFieldset_gFunc('tree', 'Stromová štruktúra');
                    foreach ($categoies_pVar as $k_pVar=>$v_pVar) {
                        $namePrefix_pVar = $lastParentCategory_pVar == $v_pVar? 'treex_':'tree_';
                        $this->addField_gFunc('tree', $namePrefix_pVar . $v_pVar, 'set', $k_pVar);
                        $values_pVar = array();
                        foreach ($tree_pVar['_values'][$v_pVar] as $vv_pVar) {
                            $values_pVar[$vv_pVar['enum_id']] = $vv_pVar['sk_enum_field_name_item'];
                        }
                        $this->setFieldOptions_gFunc('tree', $namePrefix_pVar.$v_pVar, $values_pVar);
                        $this->setDefaultValue_pVar('tree', $namePrefix_pVar.$v_pVar, implode(',',$checked_pVar));
                    }
                }
            }
        }


        if(!empty($value_id)) {
            if(isset($data_pVar['submit_button_title_update'])) {
                $this->setVar_gFunc('submit_button_title', $data_pVar['submit_button_title_update']);
            }
            else {
                $this->setVar_gFunc('submit_button_title', string_gClass::get('str_forms_submit_button_title_pVar'));
            }
        }
        else {
            if(isset($data_pVar['submit_button_title_add'])) {
                $this->setVar_gFunc('submit_button_title', $data_pVar['submit_button_title_add']);
            }
            else {
                $this->setVar_gFunc('submit_button_title', string_gClass::get('str_forms_submit_button_title_pVar'));
            }
        }

    }

    protected function getData()
    {
        $data_pVar = $this->getFormData_gFunc();
        if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($data_pVar);
        }

        // ulozim
        $this->saveData_gFunc();

        $data_pVar = $this->getFormData_gFunc();

        return($data_pVar);
    }

    protected function saveData_gFunc()
    {
        $value_id = $this->getFieldValue_gFunc('value_id');
        $data_pVar = array();

        if($value_id) { /// editovanie
            $data_pVar['enum_id'] = $value_id;
            if($this->getFieldValue_gFunc('status') === 'deleted') {
                $this->deleteValue_gFunc($value_id);
                return;
            }
        }
        else { /// pridanie noveho zaznamu
            $data_pVar['enum_field_value'] = $this->getFieldValue_gFunc('sk_enum_field_name_item');
            $data_pVar['enum_field_value'] = string_gClass::removeDiacritic_gFunc($data_pVar['enum_field_value']);
            $data_pVar['enum_field_value'] = strtolower($data_pVar['enum_field_value']);
            for($i_pVar = 0; $i_pVar < strlen($data_pVar['enum_field_value']); $i_pVar++) {
                if(!ctype_alnum($data_pVar['enum_field_value'][$i_pVar])) {
                    $data_pVar['enum_field_value'][$i_pVar] = '_';
                }
            }
        }
        $data_pVar['sk_enum_field_name_item'] = $this->getFieldValue_gFunc('sk_enum_field_name_item');
        //$data_pVar['sk_enum_field_name_group'] = $this->getFieldValue_gFunc('sk_enum_field_name_group');
        $data_pVar['sk_enum_confirm_set'] = $this->getFieldValue_gFunc('sk_enum_confirm_set');
        $data_pVar['sk_enum_confirm_unset'] = $this->getFieldValue_gFunc('sk_enum_confirm_unset');
        //$data_pVar['sk_url_name'] = $this->getFieldValue_gFunc('sk_url_name');

        $data_pVar['en_enum_field_name_item'] = $this->getFieldValue_gFunc('en_enum_field_name_item');
        //$data_pVar['en_enum_field_name_group'] = $this->getFieldValue_gFunc('en_enum_field_name_group');
        $data_pVar['en_enum_confirm_set'] = $this->getFieldValue_gFunc('en_enum_confirm_set');
        $data_pVar['en_enum_confirm_unset'] = $this->getFieldValue_gFunc('en_enum_confirm_unset');
        //$data_pVar['en_url_name'] = $this->getFieldValue_gFunc('en_url_name');

        $data_pVar['cz_enum_field_name_item'] = $this->getFieldValue_gFunc('cz_enum_field_name_item');
        //$data_pVar['cz_enum_field_name_group'] = $this->getFieldValue_gFunc('cz_enum_field_name_group');
        $data_pVar['cz_enum_confirm_set'] = $this->getFieldValue_gFunc('cz_enum_confirm_set');
        $data_pVar['cz_enum_confirm_unset'] = $this->getFieldValue_gFunc('cz_enum_confirm_unset');
        //$data_pVar['cz_url_name'] = $this->getFieldValue_gFunc('cz_url_name');


        //$data_pVar['enum_value_order'] = $this->getFieldValue_gFunc('enum_value_order');

        if(!empty($this->tree_name_pVar)) {
            $data_pVar['_standard_tree_data'] = array('tree_name'=>$this->tree_name_pVar, 'parent' => array(), 'grandparent' => array());

            $fieldsNames_pVar = $this->getFieldsNames_gFunc(false, true);
            foreach ($fieldsNames_pVar as $v_pVar) {
                if(substr($v_pVar, 0, 6) === 'treex_') {
                    $value_pVar = false;
                    $value_pVar = $this->getFieldValue_gFunc($v_pVar);
                    if(!empty($value_pVar)) {
                        $data_pVar['_standard_tree_data']['parent'][] = explode(',', $value_pVar);
                    }
                }
                elseif(substr($v_pVar, 0, 5) === 'tree_') {
                    $value_pVar = false;
                    $value_pVar = $this->getFieldValue_gFunc($v_pVar);
                    if(!empty($value_pVar)) {
                        $data_pVar['_standard_tree_data']['grandparent'][] = explode(',', $this->getFieldValue_gFunc($v_pVar));
                    }
                }
            }
        }

        $fields_pVar = db_items_gClass::getItemsFields_gFunc($this->itemType_pVar);
        $data_pVar['enum_field_id'] = $fields_pVar[$this->fieldTag_pVar]['field_id'];

        db_items_gClass::saveOrUpdateItemValue_gFunc($this->itemType_pVar, $data_pVar);
        db_items_gClass::reorderItemValues_gFunc($this->itemType_pVar, $this->fieldTag_pVar);
        db_items_gClass::applyFieldsToDataTable_gFunc($this->itemType_pVar);
    }

    protected function deleteValue_gFunc($value_id)
    {
        db_items_gClass::deleteItemValue_gFunc($this->itemType_pVar, $value_id);
    }

}
