<?php

use Illuminate\Support\Facades\Auth;

class session_session_gClass extends session_gClass
{
    private $data_pVar = false;
    private $properties_pVar = false;
    private $rights_pVar = array();
    private $groups_pVar = array();
    private $rightsLoaded_pVar = false;

    protected function startTransaction_gFunc($fileName_pVar, $lineNum_pVar)
    {
        if(!db_session_gClass::startTransaction_gFunc($fileName_pVar, $lineNum_pVar)) {
            return(false);
        }
        return(true);
    }

    protected function commit_gFunc($fileName_pVar, $lineNum_pVar)
    {
        if(!db_session_gClass::commit_gFunc($fileName_pVar, $lineNum_pVar)) {
            return(false);
        }
        return(true);
    }

    protected function rollback_gFunc($fileName_pVar, $lineNum_pVar)
    {
        if(!db_session_gClass::rollback_gFunc($fileName_pVar, $lineNum_pVar)) {
            return(false);
        }
        return(true);
    }

    public function isLoggedOn_gFunc()
    {
        return auth()->check();
    }

    public function setRight_gFunc($right_id_pVar, $enabled_pVar = true)
    {
        if($enabled_pVar) {
            $enabled_pVar = true;
        }
        else {
            $enabled_pVar = false;
        }
        $this->rights_pVar[$right_id_pVar] = $enabled_pVar;
    }

    public function setData_gFunc($data_pVar)
    {
        $this->data_pVar = $data_pVar;
    }

    protected function authSession_gFunc()
    {
        // neaktivne session oznacim ako timeouted
        if(db_session_gClass::setTimeoutedSessions_gFunc()) {
            db_session_gClass::destroyTimeoutedSessions_gFunc();
        }

        $sessionData_pVar = db_session_gClass::getUserDataBySessionId_gFunc(self::$session_id_pVar);

        if(!is_array($sessionData_pVar)) {
            // session neexistuje v databaze
            $this->destroySession_gFunc();
            log_gClass::write_gFunc('SESSION_DESTROYED_SESSION_ID', self::$session_id_pVar);
            //main_gClass::$doc_pVar = $this->login_doc_pVar;
            return(false);
        }

        // otestujem ci nedoslo k zmene IP alebo user agenta alebo pcid
        $check_ip_pVar = main_gClass::getConfigVar_gFunc('check_ip', 'access2_session');
        if($check_ip_pVar) {
            $ip_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR', false, '/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/');
            if($sessionData_pVar['remote_ip'] !== $ip_pVar) {
                // doslo k zmene IP. Moze tak nastat ukradnutim session, alebo ak je clovek na modemovom pripojeni, pripadne
                // za sustavou proxy (ked nechodi kazdy request vzdy cez ten isty proxy)
                $this->destroySession_gFunc($sessionData_pVar['online_user_id']);

                main_gClass::$doc_pVar = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_msg'] = 'ip_error';
                main_gClass::$mainVars_pVar['_login_follow'] = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
                log_gClass::write_gFunc('SESSION_ERROR_IP', $sessionData_pVar['remote_ip'] . ' -> ' . $ip_pVar);
                return(false);
            }
        }
        $check_agent_pVar = main_gClass::getConfigVar_gFunc('check_agent', 'access2_session');
        if($check_agent_pVar) {
            $user_agent_pVar = main_gClass::getServerVar_gFunc('HTTP_USER_AGENT');
            if($sessionData_pVar['user_agent'] !== $user_agent_pVar) {
                $this->destroySession_gFunc($sessionData_pVar['online_user_id']);

                main_gClass::$doc_pVar = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_msg'] = 'agent_error';
                main_gClass::$mainVars_pVar['_login_follow'] = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
                log_gClass::write_gFunc('SESSION_ERROR_AGENT', $sessionData_pVar['user_agent'] . ' -> ' . $user_agent_pVar);
                return(false);
            }
        }
        /*
                 if($sessionData_pVar['pc_str_id'] !== self::getPcId_gFunc()) {
                         $this->destroySession_gFunc($sessionData_pVar['online_user_id']);

                         main_gClass::$doc_pVar = $this->login_doc_pVar;
                         main_gClass::$mainVars_pVar['_login_msg'] = 'pcid_error';
                         main_gClass::$mainVars_pVar['_login_follow'] = $this->login_doc_pVar;
                         main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
                         log_gClass::write_gFunc('SESSION_ERROR_PCID', $sessionData_pVar['pc_str_id'] . ' -> ' . self::getPcId_gFunc());
                         return(false);
                 }*/

        // ci je session timeoutovana
        if($sessionData_pVar['session_status'] === 'online') {


            // nie je timeoutovana, je online.
            db_session_gClass::logLastAccess_gFunc($sessionData_pVar['user_id'], $sessionData_pVar['online_user_id']);
            $this->userID_pVar = $sessionData_pVar['user_id'];

            \Mixedtype\Tracker\Tracker::getInstance()->track('user_id', $this->userID_pVar);

            $this->data_pVar = $sessionData_pVar;
            log_gClass::write_gFunc('SESSION_USER_ID', $this->userID_pVar);
            $this->rights_pVar[s_system_logged_on] = true;
            $this->restoreCachedRights_gFunc($this->data_pVar['rights']);
            return(true);
        }

        // session je timeoutovana
        if(strtotime($sessionData_pVar['time_next_timeout']) < strtotime($sessionData_pVar['now'])) {
            // uz sa jej neda pomoct, prepasla posledny limit
            db_session_gClass::destroyTimeoutedSessions_gFunc();
            $this->destroySession_gFunc();
            log_gClass::write_gFunc('SESSION_TIMEOUTED', $this->userID_pVar);
            request()->session()->regenerate();

            return(false);
        }

        if(!is_array(self::$loginPrompt_pVar)) {
            if($sessionData_pVar['session_status'] === 'restoring') {
                // zrusim session, ak nie je login prompt
                // a pokracujem v normalnom vykonavani requestu ako odhlaseny user
                $this->destroySession_gFunc();
                log_gClass::write_gFunc('SESSION_DROPPED', $this->userID_pVar);
                return(false);
            }

            $old_id = session()->getId();
            \Illuminate\Support\Facades\DB::table('kega_users_onlineusers')->where('session_str_id', $old_id)->delete();

            // zaserializujem request a nastavim na restoring
            db_session_gClass::setSessionForRestoring_gFunc($sessionData_pVar['online_user_id']);
            main_gClass::serializeRequestData_gFunc();
            main_gClass::$doc_pVar = $this->login_doc_pVar;
            main_gClass::$mainVars_pVar['_login_timeouted'] = true;
            main_gClass::$mainVars_pVar['_login_restore_checked'] = '';
            //main_gClass::$mainVars_pVar['_login_restore_checked'] = 'checked="checked"';
            log_gClass::write_gFunc('SESSION_RESTORING', $this->userID_pVar);
            return(false);
        }

        if($sessionData_pVar['login'] !== self::$loginPrompt_pVar['login_pVar']) {
            // iny pouzivatel
            // zrusim session, pokracujem vo vykonavani.
            // Mala by mi este zabrat autentifikacia usera, teda sa prihlasi ako iny user
            $this->destroySession_gFunc();
            log_gClass::write_gFunc('SESSION_SWITCH', $this->userID_pVar);
            return(false);
        }

        // ten isty pouzivatel, overim heslo
        if(!$this->authUser_gFunc(false)) {
            // zle heslo, vsetky hlasky uz nastavila authUser funkcia, uz nastavim iba checkbox
            main_gClass::$mainVars_pVar['_login_timeouted'] = true;
            if(main_gClass::getInputString_gFunc('restore_session') == 'on') {
                main_gClass::$mainVars_pVar['_login_restore_checked'] = 'checked="checked"';
            }
            else {
                main_gClass::$mainVars_pVar['_login_restore_checked'] = '';
            }
            log_gClass::write_gFunc('SESSION_RESTORING_FAILED', $this->userID_pVar);
            return(false);
        }

        // tu je pouzivatel ok, aj heslo zadal spravne, teda obnovim session
        if(main_gClass::getInputString_gFunc('restore_session') == 'on') {
            // obnovim request
            main_gClass::unserializeRequestData_gFunc();
            log_gClass::write_gFunc('SESSION_REQUEST_RESTORED');
        }
        main_gClass::$mainVars_pVar['_login_msg'] = 'logged_in';
        main_gClass::$mainVars_pVar['_login_form_disabled'] = true;

        db_session_gClass::restoreSession_gFunc($sessionData_pVar['user_id'], $sessionData_pVar['online_user_id']);
        $this->userID_pVar = $sessionData_pVar['user_id'];
        $this->data_pVar = $sessionData_pVar;
        $this->rights_pVar[s_system_logged_on] = true;
        $this->restoreCachedRights_gFunc($this->data_pVar['rights']);

        log_gClass::write_gFunc('SESSION_RESTORED', $this->userID_pVar);
        return(true);
    }

    protected function authLaravelUser_gFunc($logIn_pVar = true)
    {
        if(!auth()->check()) {
            return(false);
        }

        $sessionId_pVar = request()->session()->getId();

        $this->userID_pVar = auth()->id();
        $this->rights_pVar[s_system_logged_on] = true;

        log_gClass::write_gFunc('SESSION_USER_ID_FROM_LARAVEL_AUTH', $this->userID_pVar);
        log_gClass::write_gFunc('SESSION_USER_ID', $this->userID_pVar);
        log_gClass::write_gFunc('LOGGED_IN', auth()->id(), false, true);
        $currentKey_pVar = false;
            // insertnem do onlineusers
            \Illuminate\Support\Facades\DB::table('kega_users_onlineusers')->where('session_str_id', $sessionId_pVar)->delete();
            $this->data_pVar =  db_session_gClass::createSessionOnlineusers_gFunc(auth()->id(), $sessionId_pVar, $currentKey_pVar);
            //$this->restoreCachedRights_gFunc($this->data_pVar['rights']);

            $session_settings_serialized_pVar = session_session_gClass::getUserDetailStatic_gFunc($this->userID_pVar, 'session_settings');
            if(empty($session_settings_serialized_pVar)) {
                $session_settings_pVar = array();
            }
            else {
                $session_settings_pVar = unserialize($session_settings_serialized_pVar);
            }
            main_gClass::setPhpSessionVar_gFunc('saved_session_settings', $session_settings_pVar);
            main_gClass::setPhpSessionVar_gFunc('saved_session_settings_for_user_id', $this->userID_pVar);



        main_gClass::$mainVars_pVar['_login_msg'] = 'logged_in';
        main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
        return(true);
    }

    protected function authUser_gFunc($logIn_pVar = true)
    {

        if(self::$loginPrompt_pVar === false || self::$loginPrompt_pVar['type_pVar'] !== self::AUTH_TYPE_SESSION_pVar) {
            return(false);
        }

        if(!isset(self::$loginPrompt_pVar['login_pVar'])) {
            if(modules_gClass::isModuleRegistred_gFunc('kega')) {
                if(kega_gClass::authUser_gFunc($this, self::$loginPrompt_pVar)) {
                    main_gClass::$mainVars_pVar['_login_msg'] = 'logged_in';
                    main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
                    return(true);
                }
                return(false);
            }
        }

        self::$loginPrompt_pVar['login_pVar'] = $this->checkLoginNameString_gFunc(self::$loginPrompt_pVar['login_pVar']);
        self::$loginPrompt_pVar['password_pVar'] = self::checkPasswordSyntax_gFunc(self::$loginPrompt_pVar['password_pVar']);
        if(self::$loginPrompt_pVar['login_pVar'] === false || self::$loginPrompt_pVar['password_pVar'] === false) {
            main_gClass::$doc_pVar = $this->login_doc_pVar;
            main_gClass::$mainVars_pVar['_login_msg'] = 'no_loginname_or_password';
            main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
            log_gClass::write_gFunc('LOGIN_NO_LOGINNAME_OR_PASSWORD', self::$loginPrompt_pVar['login_pVar']);
            return(false);
        }

        // overovanie musi vzdy trvat nejaky cas (security), inak by nemusel utocnik cakat na skoncenie skriptu
        // a riesil by iba rychle odozvy
        main_gClass::sleep_delayed(1);

        // ziskam data, overim password - heslo sa overuje az dalej
        $data_pVar = db_session_gClass::getUserDataByLogin_gFunc(self::$loginPrompt_pVar['login_pVar']);

        if(!is_array($data_pVar)) {
            // nespravny login alebo heslo
            main_gClass::$doc_pVar = $this->login_doc_pVar;
            main_gClass::$mainVars_pVar['_login_msg'] = 'bad_login_or_password';
            main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
            main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
            log_gClass::write_gFunc('LOGIN_BAD_LOGINNAME', self::$loginPrompt_pVar['login_pVar'], false, true);
            main_gClass::sleep_delayed(10);
            return(false);
        }

        // admin security
        if($data_pVar['user_role'] === 'superuser') {
            $adminSecurityOff_pVar = main_gClass::getConfigVar_gFunc('admin_security_off', 'access2_session');
            if(strlen($adminSecurityOff_pVar)) {
                $adminSecurityOff_pVar = (int)strtotime($adminSecurityOff_pVar);
            }
            else {
                $adminSecurityOff_pVar = 0;
            }

            if(time() > $adminSecurityOff_pVar && time() < $adminSecurityOff_pVar + 10*60) {
                $adminSecurityOff_pVar = true;
                log_gClass::write_gFunc('LOGIN_ADMIN_SECURITY_OFF', self::$loginPrompt_pVar['login_pVar']);
            }
            else {
                $adminSecurityOff_pVar = false;
            }
        }
        else {
            $adminSecurityOff_pVar = false;
        }

        if($data_pVar['disabled_login_level'] === 'disabled' && $adminSecurityOff_pVar === false) {
            main_gClass::$doc_pVar = $this->login_doc_pVar;
            main_gClass::$mainVars_pVar['_login_msg'] = 'user_temporary_disabled';
            main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
            main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
            log_gClass::write_gFunc('LOGIN_TEMPORARY_DISABLED', self::$loginPrompt_pVar['login_pVar']);
            main_gClass::sleep_delayed(10);
            return(false);
        }

        // overim ci je tato adresa povolena pre tohto pouzivatela
        if(strlen($data_pVar['enabled_remote_ips']) && $adminSecurityOff_pVar === false) {
            $ips_pVar = explode(',', $data_pVar['enabled_remote_ips']);
            $ip_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR', false, '/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/');
            $ipEnabled_pVar = false;
            foreach($ips_pVar as $v_pVar) {
                if(self::checkIp_gFunc($ip_pVar, $v_pVar, true)) {
                    $ipEnabled_pVar = true;
                    break;
                }
            }
            if(!$ipEnabled_pVar) {
                // ip nie je povolena
                $ip_pVar = main_gClass::getServerVar_gFunc('REMOTE_ADDR');
                main_gClass::$doc_pVar = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_msg'] = 'unallowed_ip_for_user';
                main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
                main_gClass::$mainVars_pVar['_login_ip'] = $ip_pVar;
                log_gClass::write_gFunc('LOGIN_UNALLOWED_IP', $ip_pVar);
                main_gClass::sleep_delayed(10);
                return(false);
            }
        }

        // overim ci ma platny kluc, ak je pozadovany
        $currentKey_pVar = false;
        if(strlen($data_pVar['enabled_remote_keys']) && $adminSecurityOff_pVar === false) {
            $myKeys_pVar = explode(',', $data_pVar['enabled_remote_keys']);
            $keys_pVar = main_gClass::getInputKeys_gFunc();
            $currentKey_pVar = self::checkKeys_gFunc($myKeys_pVar, $keys_pVar);
            if($currentKey_pVar === false) {
                if(count($keys_pVar)) {
                    main_gClass::$doc_pVar = $this->login_doc_pVar;
                    main_gClass::$mainVars_pVar['_login_msg'] = 'unallowed_key_for_user';
                    main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
                    log_gClass::write_gFunc('LOGIN_UNALLOWED_KEY', self::$loginPrompt_pVar['login_pVar']);
                    main_gClass::sleep_delayed(10);
                }
                else {
                    main_gClass::$doc_pVar = $this->login_doc_pVar;
                    main_gClass::$mainVars_pVar['_login_msg'] = 'key_required_for_user';
                    main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
                    log_gClass::write_gFunc('LOGIN_KEY_REQUIRED', self::$loginPrompt_pVar['login_pVar']);
                    main_gClass::sleep_delayed(10);
                }
                $levelTimeout_pVar = db_session_gClass::incrementDisableLevel_gFunc($data_pVar['user_id'], 'level1');
                log_gClass::write_gFunc('LOGIN_DISABLING_TIMEOUT', self::$loginPrompt_pVar['login_pVar'] . '/' . ceil($levelTimeout_pVar / 60));
                return(false);
            }
        }

        if($data_pVar['disabled_login_level'] !== 'enabled' && $adminSecurityOff_pVar === false) {
            if (strtotime($data_pVar['disabled_login']) > time()) {
                $timeout_pVar = strtotime($data_pVar['disabled_login']) - time();
                main_gClass::$doc_pVar = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_msg'] = 'user_temporary_disabled_timeout';
                main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
                main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
                main_gClass::$mainVars_pVar['_login_disabled_timeout'] = ceil($timeout_pVar / 60);
                log_gClass::write_gFunc('LOGIN_TEMPORARY_DISABLED_TIMEOUT', self::$loginPrompt_pVar['login_pVar'] . '('.ceil($timeout_pVar / 60).')');
                main_gClass::sleep_delayed(10);
                return(false);
            }
            // cas vyprsal,takze pokracujemdalej.. Ponecham level.
        }

        if(!strlen($data_pVar['password'])) {
            // pouzivatel nema nastavene heslo
            if(self::$loginPrompt_pVar['login_pVar'] === 'admin') {
                $data_pVar['password'] = db_session_gClass::setPassword_gFunc($data_pVar['user_id'], $data_pVar['login'], self::$loginPrompt_pVar['password_pVar']);
            }
            else {
                // aplikujem akoze nespravny login alebo heslo
                main_gClass::$doc_pVar = $this->login_doc_pVar;
                main_gClass::$mainVars_pVar['_login_msg'] = 'bad_login_or_password';
                main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
                main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
                log_gClass::write_gFunc('LOGIN_BAD_LOGINNAME', self::$loginPrompt_pVar['login_pVar'], false, true);
                main_gClass::sleep_delayed(10);
                return(false);
            }
        }


        // overenie hesla
        if(  //0&&

            strlen($data_pVar['password']) < 42 /* legacy password */ ||
            // kontrola laraveloveho hesla
                !\Illuminate\Support\Facades\Hash::check(self::$loginPrompt_pVar['password_pVar'], $data_pVar['password'])
            ) {

                // ak laravelove heslo nepreslo, tak skusim este legacy
                if($data_pVar['password'] !== db_session_gClass::legacyEncodePassword_gFunc(
                        self::$loginPrompt_pVar['login_pVar'],
                        self::$loginPrompt_pVar['password_pVar']
                    )) {
                    // nespravny login alebo heslo
                    main_gClass::$doc_pVar = $this->login_doc_pVar;
                    main_gClass::$mainVars_pVar['_login_msg'] = 'bad_login_or_password';
                    main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
                    main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
                    log_gClass::write_gFunc('LOGIN_BAD_PASSWORD', self::$loginPrompt_pVar['login_pVar'], false, true);
                    $levelTimeout_pVar = db_session_gClass::incrementDisableLevel_gFunc($data_pVar['user_id']);
                    main_gClass::sleep_delayed(10);
                    if ($levelTimeout_pVar > 15) {
                        main_gClass::$mainVars_pVar['_login_disabled_timeout'] = ceil($levelTimeout_pVar / 60);
                        log_gClass::write_gFunc('LOGIN_DISABLING_TIMEOUT', self::$loginPrompt_pVar['login_pVar'] . '/' . $levelTimeout_pVar);
                    } elseif ($levelTimeout_pVar === 0) {
                        // zablokovany
                        main_gClass::$mainVars_pVar['_login_msg'] = 'user_temporary_disabled';
                        log_gClass::write_gFunc('LOGIN_DISABLING_X', self::$loginPrompt_pVar['login_pVar']);
                    } else {
                        log_gClass::write_gFunc('LOGIN_DISABLING_TIMEOUT', self::$loginPrompt_pVar['login_pVar'] . '/' . $levelTimeout_pVar);
                    }
                    return (false);
                }

                if($data_pVar['password'] === db_session_gClass::legacyEncodePassword_gFunc(
                        self::$loginPrompt_pVar['login_pVar'],
                        self::$loginPrompt_pVar['password_pVar']
                    )) {
                        /// store new password hash
                        $data_pVar['password'] = db_session_gClass::encodePassword_gFunc(
                            self::$loginPrompt_pVar['login_pVar'],
                            self::$loginPrompt_pVar['password_pVar']
                        );
                        \Illuminate\Support\Facades\DB::table('kega_items_users__data')
                            ->where('item_id', '=', $data_pVar['item_id'])
                            ->update([
                               'password' =>  $data_pVar['password']
                            ]);
                }
        }

        // rehash if needed
        if ($data_pVar['item_id'] && Hash::needsRehash($data_pVar['password'])) {
            $data_pVar['password'] = db_session_gClass::encodePassword_gFunc(
                self::$loginPrompt_pVar['login_pVar'],
                self::$loginPrompt_pVar['password_pVar']
            );
            \Illuminate\Support\Facades\DB::table('kega_items_users__data')
                ->where('item_id', '=', $data_pVar['item_id'])
                ->update([
                    'password' =>  $data_pVar['password']
                ]);
        }


        if($data_pVar['status'] !== 'active') {
            // deleted... vratim nespravny login alebo heslo
            main_gClass::$doc_pVar = $this->login_doc_pVar;
            main_gClass::$mainVars_pVar['_login_msg'] = 'bad_login_or_password';
            main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
            main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
            log_gClass::write_gFunc('LOGIN_UNALLOWED_USERTYPE', self::$loginPrompt_pVar['login_pVar'] . '(' . $data_pVar['status'] . ')');
            main_gClass::sleep_delayed(10);
            return(false);
        }

        if($data_pVar['login_enabled'] !== 'enabled') {
            main_gClass::$doc_pVar = $this->login_doc_pVar;
            main_gClass::$mainVars_pVar['_login_msg'] = 'user_disabled';
            main_gClass::$mainVars_pVar['_login_follow'] = main_gClass::getRequestedDocName_gFunc();
            main_gClass::$mainVars_pVar['_login_username'] = self::$loginPrompt_pVar['login_pVar'];
            log_gClass::write_gFunc('LOGIN_PERMANENTLY_DISABLED', self::$loginPrompt_pVar['login_pVar']);
            main_gClass::sleep_delayed(10);
            return(false);
        }

        // zrusim disabled level
        if($data_pVar['disabled_login_level'] !== 'enabled') {
            db_session_gClass::resetDisableLevel_gFunc($data_pVar['user_id']);
        }

        if($logIn_pVar) {

            $loginFields = config('mixedtype.auth.login_fields');
            if(!is_array($loginFields)) {
                $loginFields = ['email'];
            }

            $isAuth = false;
            foreach ($loginFields as $field) {
                if (Auth::attempt([
                    $field => self::$loginPrompt_pVar['login_pVar'],
                    'password' => self::$loginPrompt_pVar['password_pVar']
                ], true)) {
                    $isAuth = true;

                }
            }

            /////////////////////////////////
            //// prihlasim pouzivatela, vsetko je ok

            // reset session
            main_gClass::regenerateSessionId_gFunc();
            $sessionId_pVar = request()->session()->getId();

            $this->userID_pVar = $data_pVar['user_id'];
            $this->rights_pVar[s_system_logged_on] = true;

            log_gClass::write_gFunc('SESSION_USER_ID', $this->userID_pVar);
            log_gClass::write_gFunc('LOGGED_IN', $data_pVar['user_id'], false, true);

            // insertnem do onlineusers
            \Illuminate\Support\Facades\DB::table('kega_users_onlineusers')->where('session_str_id', $sessionId_pVar)->delete();
            $this->data_pVar =  db_session_gClass::createSessionOnlineusers_gFunc($data_pVar['user_id'], $sessionId_pVar, $currentKey_pVar);
            $this->restoreCachedRights_gFunc($this->data_pVar['rights']);

            $session_settings_serialized_pVar = session_session_gClass::getUserDetailStatic_gFunc($this->userID_pVar, 'session_settings');
            if(empty($session_settings_serialized_pVar)) {
                $session_settings_pVar = array();
            }
            else {
                $session_settings_pVar = unserialize($session_settings_serialized_pVar);
            }
            main_gClass::setPhpSessionVar_gFunc('saved_session_settings', $session_settings_pVar);
            main_gClass::setPhpSessionVar_gFunc('saved_session_settings_for_user_id', $this->userID_pVar);

            // overim isic
            if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
                $cdo_pVar = new cdouk_gClass();
                $data_pVar = $cdo_pVar->getData_gFunc($data_pVar['isic'], false, false, true);
            }
        }

        main_gClass::$mainVars_pVar['_login_msg'] = 'logged_in';
        main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
        return(true);
    }

    public function restoreCachedRights_gFunc($strRights_pVar)
    {
        if(!strlen($strRights_pVar)) {
            return;
        }
        $tmp_pVar = explode('#', $strRights_pVar);
        if(count($tmp_pVar) != 3) {
            return;
        }
        $tmp_pVar[0] = explode(',', $tmp_pVar[0]);
        $tmp_pVar[1] = explode(',', $tmp_pVar[1]);
        $tmp_pVar[2] = explode(',', $tmp_pVar[2]);

        foreach ($tmp_pVar[0] as $v_pVar) {
            $this->rights_pVar[(int)$v_pVar] = true;
        }
        foreach ($tmp_pVar[1] as $v_pVar) {
            $this->rights_pVar[(int)$v_pVar] = false;
        }

        $this->groups_pVar = $tmp_pVar[2];

        $this->rightsLoaded_pVar = true;
        log_gClass::write_gFunc('RESTORED_CACHED_RIGHTS', $strRights_pVar);
    }

    protected function destroySession_gFunc($online_user_id_pVar = false)
    {
        if($online_user_id_pVar !== false) {
            db_session_gClass::destroySession_gFunc($online_user_id_pVar);
        }
        main_gClass::regenerateSessionId_gFunc();
        $this->data_pVar = false;
        $this->userID_pVar = 0;
    }


    public  function generateSessionId_gFunc()
    {
        $sessId_pVar = uniqid('sid', true);
        $sessId_pVar = str_replace('.', 'x', $sessId_pVar);
        main_gClass::setPhpSessionVar_gFunc('session_id_pVar', $sessId_pVar);
        return($sessId_pVar);
    }

    private static function checkKeys_gFunc($myKeys_pVar, $keys_pVar)
    {
        foreach($myKeys_pVar as $v_pVar) {
            foreach($keys_pVar as $vv_pVar) {
                if($v_pVar === $vv_pVar) {
                    return($v_pVar);
                }
            }
        }
        return(false);
    }

    private static function checkIp_gFunc($ip_pVar, $ipMask_pVar, $hexaIpMask_pVar = false)
    {
        if($hexaIpMask_pVar === true) {
            $hexaIpMask_pVar = $ipMask_pVar;
            if(strlen($hexaIpMask_pVar) !== 10) {
                return(false);
            }
            $ipMask_pVar = array();
            $ipMask_pVar[0] = hexdec(substr($hexaIpMask_pVar, 0, 2));
            $ipMask_pVar[1] = hexdec(substr($hexaIpMask_pVar, 2, 2));
            $ipMask_pVar[2] = hexdec(substr($hexaIpMask_pVar, 4, 2));
            $ipMask_pVar[3] = hexdec(substr($hexaIpMask_pVar, 6, 2));
            $ipMask_pVar[4] = hexdec(substr($hexaIpMask_pVar, 8, 2));
        }
        else {
            $x_pVar =explode('/', $ipMask_pVar);
            if(count($x_pVar) !== 2) {
                if(count($x_pVar) === 1) { // ip bez masky, pridam masku /32
                    $x[1] = 32;
                }
                else {
                    return(false);
                }
            }

            $ipMask_pVar = explode('.', $x_pVar[0]);
            if(count($ipMask_pVar) !== 4) {
                return(false);
            }
            $ipMask_pVar[4] = $x_pVar[1];
        }

        $ip_pVar = explode('.', $ip_pVar);
        if(count($ip_pVar) !== 4) {
            return(false);
        }

        if($ipMask_pVar[4] <= 0 || $ipMask_pVar[4] >32) {
            return(false);
        }

        // osetrim na (int)
        foreach($ip_pVar as $k_pVar=>$v_pVar) {
            if(!is_numeric($v_pVar)) return(false);
            $ip_pVar[$k_pVar] = (int)$v_pVar;
        }
        foreach($ipMask_pVar as $k_pVar=>$v_pVar) {
            if(!is_numeric($v_pVar)) return(false);
            $ipMask_pVar[$k_pVar] = (int)$v_pVar;
        }

        // <-- tu mam obe ipcky v poliach $ip_pVar a $ipMask_pVar (v rovnakom formate)

        // aplikujem masku na obidve IP
        if($mask_pVar <= 8) {
            $myMask_pVar = $mask_pVar;
            $ipMask_pVar[0] = $ipMask_pVar[0] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
            $ip_pVar[0] = $ip_pVar[0] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
        }
        elseif ($mask_pVar <= 16) {
            $myMask_pVar = $mask_pVar - 8;
            $ipMask_pVar[1] = $ipMask_pVar[1] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
            $ip_pVar[1] = $ip_pVar[1] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
        }
        elseif ($mask_pVar <= 24) {
            $myMask_pVar = $mask_pVar - 16;
            $ipMask_pVar[2] = $ipMask_pVar[2] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
            $ip_pVar[2] = $ip_pVar[2] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
        }
        else { // elseif ($mask_pVar <= 32) {
            $myMask_pVar = $mask_pVar - 24;
            $ipMask_pVar[3] = $ipMask_pVar[3] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
            $ip_pVar[3] = $ip_pVar[3] &  bindec(str_repeat('1',$myMask_pVar) . str_repeat('0', 8 - $myMask_pVar));
        }

        unset($ipMask_pVar[4]);
        $ip1_string_pVar = implode('.', $ip_pVar);
        $ip2_string_pVar = implode('.', $ipMask_pVar);

        if($ip1_string_pVar !== $ip2_string_pVar) {
            return(false);
        }
        return(true);
    }


    public function checkLoginNameString_gFunc($loginName_pVar)
    {
        $loginName_pVar = strtolower($loginName_pVar);
        $loginName_pVar = string_gClass::removeDiacritic_gFunc($loginName_pVar);

        if(strlen($loginName_pVar) < 2) { // toto je kompatibilne s utf-8, pretoze som odstranil diakritiku
            return(false);
        }

        $zavinac_pVar = 0;
        for($i_pVar=0; isset($loginName_pVar[$i_pVar]); $i_pVar++) {
            // toto je kompatibilne s utf-8, pretoze som odstranil diakritiku
            if(ctype_alpha($loginName_pVar[$i_pVar])) {
                continue;
            }
            if($i_pVar && ctype_digit($loginName_pVar[$i_pVar])) {
                continue;
            }

            if($loginName_pVar[$i_pVar] == '@' && !$zavinac_pVar) {
                $zavinac_pVar++;
                continue;
            }

            if($loginName_pVar[$i_pVar] == '.') {
                continue;
            }

            if($loginName_pVar[$i_pVar] == '_') {
                continue;
            }

            return(false);
        }

        if(!ctype_alpha($loginName_pVar[0])) {
            return(false);
        }

        return($loginName_pVar);
    }

    public static function checkPasswordSyntax_gFunc($password_pVar)
    {
        $password_pVar = string_gClass::removeDiacritic_gFunc($password_pVar);
        // toto je kompatibilne s utf-8, pretoze som odstranil diakritiku
        if(strlen($password_pVar) < 4) {
            return(false);
        }
        return($password_pVar);
    }

    public static function getPcId_gFunc()
    {
        return(main_gClass::getPcId_gFunc());
    }

    static function setFromRights_gFunc(&$fromArray_pVar, $accessID_pVar, $access_pVar, $fromTag_pVar, $fromParam_pVar = false)
    {
        if(!isset($fromArray_pVar[(int)$accessID_pVar])) {
            $fromArray_pVar[(int)$accessID_pVar] = array();
        }
        if(!isset($fromArray_pVar[(int)$accessID_pVar][$fromTag_pVar])) {
            $fromArray_pVar[(int)$accessID_pVar][$fromTag_pVar] = array();
        }
        if(!isset($fromArray_pVar[(int)$accessID_pVar][$fromTag_pVar][$fromParam_pVar])) {
            $fromArray_pVar[(int)$accessID_pVar][$fromTag_pVar][$fromParam_pVar] = array();
        }
        $fromArray_pVar[(int)$accessID_pVar][$fromTag_pVar][$fromParam_pVar][] = $access_pVar ? true:false;
    }

    /**
     * Ziska prava z databazy.
     *
     * Ak chcem ziskat kompletne prava pre konkretneho pouzivatela, musim nastavit rolu, a userID. addGroups necham default
     *
     * Ak chcem ziskat prava pre rolu, nastavim iba rolu.
     * Ak chcem ziskat prava pre skupinu/skupiny, nastavim skupinu.
     *
     * @param $role_pVar
     * @param $userID_pVar
     * @param $addGroups_pVar
     * @return unknown_type
     */
    static public function getUserRightsFromDb_gFunc($role_pVar = false, $userID_pVar = false, $addGroups_pVar = false)
    {
        $rights_pVar = array();
        $groups_pVar = array();
        $from_pVar = array();

        if($role_pVar == 'admin' || $role_pVar == 'superuser') {
            // ak som admin alebo superadmin, nacitam vsetky prava
            $rights_pVar = db_session_gClass::getAllRights_gFunc($from_pVar, $role_pVar);
        }

        if(modules_gClass::isModuleRegistred_gFunc('access3_rights')) {
            $rights_pVar = rights_rights_gClass::loadRights_gFunc($rights_pVar, $userID_pVar, $role_pVar, $groups_pVar, $from_pVar, $addGroups_pVar);
        }

        if($userID_pVar !== false) {
            // ak nie je instalovany modul access3_rights, je po prihlaseni obycajnym userom (nerozlisujem roly) (ak nie je admin, alebo superadmin)
            $default_user_rights_pVar = main_gClass::getUserDefaultRights_gFunc();
            self::mergeRights_gFunc($rights_pVar, $default_user_rights_pVar);
            foreach($default_user_rights_pVar as $k_pVar=>$v_pVar) {
                session_session_gClass::setFromRights_gFunc($from_pVar, $k_pVar, $v_pVar, 'system', 'user_default');
            }
        }

        if($role_pVar !== false) {
            if($role_pVar == 'superuser') {
                $rights_pVar[s_system_superadmin] = true;
                $rights_pVar[s_system_admin] = true;
                session_session_gClass::setFromRights_gFunc($from_pVar, s_system_superadmin, true, 'system', 'superuser');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_system_admin, true, 'system', 'superuser');
            }
            elseif($role_pVar == 'admin') {
                $rights_pVar[s_system_superadmin] = false;
                $rights_pVar[s_system_admin] = true;
                session_session_gClass::setFromRights_gFunc($from_pVar, s_system_superadmin, false, 'system', 'admin');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_system_admin, true, 'system', 'admin');
                $rights_pVar[s_users_edit_superadmin] = false;
                $rights_pVar[s_users_add_superadmin] = false;
                $rights_pVar[s_users_delete_superadmin] = false;
                session_session_gClass::setFromRights_gFunc($from_pVar, s_users_edit_superadmin, false, 'system', 'admin');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_users_add_superadmin, false, 'system', 'admin');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_users_delete_superadmin, false, 'system', 'admin');
            }
            else {
                $rights_pVar[s_system_superadmin] = false;
                $rights_pVar[s_system_admin] = false;
                session_session_gClass::setFromRights_gFunc($from_pVar, s_system_superadmin, false, 'system');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_system_admin, false, 'system');
                $rights_pVar[s_users_edit_superadmin] = false;
                $rights_pVar[s_users_add_superadmin] = false;
                $rights_pVar[s_users_delete_superadmin] = false;
                session_session_gClass::setFromRights_gFunc($from_pVar, s_users_edit_superadmin, false, 'system', 'admin');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_users_add_superadmin, false, 'system', 'admin');
                session_session_gClass::setFromRights_gFunc($from_pVar, s_users_delete_superadmin, false, 'system', 'admin');
            }
        }
        return(array($rights_pVar, $groups_pVar, $from_pVar));
    }

    protected function _rightsLoaded_gFunc()
    {
        return($this->rightsLoaded_pVar);
    }

    protected function _userHasRights_gFunc($actionID_pVar, $objectID_pVar = false, $status_pVar = self::ACCESS_ALL_pVar, $logValue_pVar = false, $userID_pVar = false, $childResult_pVar = null)
    {
        $ret_pVar = false;
        if(!$this->rightsLoaded_pVar) {
            $this->rightsLoaded_pVar = true;
            $this->rights_pVar = array();
            $this->groups_pVar = array();

            list($this->rights_pVar, $this->groups_pVar) = self::getUserRightsFromDb_gFunc($this->data_pVar['user_role'], $this->userID_pVar);

            $this->rights_pVar[s_system_logged_on] = true;

            // prava ulozim do tabulky onlineusers
            $onlineUserId_pVar = $this->data_pVar['online_user_id'];
            db_session_gClass::cacheUserRights_gFunc($onlineUserId_pVar, $this->rights_pVar, $this->groups_pVar);
        }

        // ak som SUPERadmin (neplati pre obycajneho admina), tak vraciam vzdy true...
        if(isset($this->rights_pVar[s_system_superadmin]) && $this->rights_pVar[s_system_superadmin] === true) {
            // superadministrator
            $ret_pVar = true;
        }
        elseif (isset($this->rights_pVar[$actionID_pVar]) && $this->rights_pVar[$actionID_pVar] === true) {
            $ret_pVar = true;
        }
        else {
            $ret_pVar = false;
        }

        return(parent::_userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar, $userID_pVar, $ret_pVar));
    }

    static public function logout_gFunc()
    {
        if(self::$instance !== false) {
            if(self::$instance->userID_pVar) {
                db_session_gClass::logoutUser_gFunc(self::$instance->userID_pVar, self::$instance->data_pVar['online_user_id']);
            }
        }
        parent::logout_gFunc();
    }

    protected function getUserDetailsValue_gFunc($user_id_pVar)
    {
        if($this->userID_pVar == $user_id_pVar && is_array($this->data_pVar)) {
            return($this->data_pVar);
        }
        return(db_session_gClass::getUserDataByID_gFunc($user_id_pVar));
    }

    protected function getUserDetailValue_gFunc($userDetailName_pVar)
    {
        if(!$this->userID_pVar) {
            return(false);
        }
        return(self::getUserDetailFromStruct_gFunc($this->data_pVar, $userDetailName_pVar));
    }

    public static function getUserDetailStatic_gFunc($user_id_pVar, $detail_name_pVar)
    {
        $data_pVar = db_session_gClass::getUserDataByID_gFunc($user_id_pVar);
        return(self::getUserDetailFromStruct_gFunc($data_pVar, $detail_name_pVar));
    }

    private static function getUserDetailFromStruct_gFunc($userData_pVar, $userDetailName_pVar)
    {
        if(is_array($userData_pVar)) {
            if(isset($userData_pVar[$userDetailName_pVar])) {
                if($userDetailName_pVar[0] === 'a' && substr($userDetailName_pVar, 0, 11) === 'address_id_') {
                    return(db_session_gClass::getUserAddress_gFunc($userData_pVar['item_id'], $userData_pVar[$userDetailName_pVar]));
                }
                return($userData_pVar[$userDetailName_pVar]);
            }
            if($userDetailName_pVar === 'real_name') {
                $tmp_pVar = array();
                if(isset($userData_pVar['titul_pred']) && !empty($userData_pVar['titul_pred'])) {
                    $tmp_pVar[] = $userData_pVar['titul_pred'];
                }
                if(isset($userData_pVar['first_name']) && !empty($userData_pVar['first_name'])) {
                    $tmp_pVar[] = $userData_pVar['first_name'];
                }
                if(isset($userData_pVar['last_name']) && !empty($userData_pVar['last_name'])) {
                    $tmp_pVar[] = $userData_pVar['last_name'];
                }
                $tmp_pVar = implode(' ', $tmp_pVar);
                if(!empty($tmp_pVar)) {
                    if(isset($userData_pVar['titul_za']) && !empty($userData_pVar['titul_za'])) {
                        $tmp_pVar .= ', ' . $userData_pVar['titul_za'];
                    }
                }
                return($tmp_pVar);
            }
            if($userDetailName_pVar === 'foto_thumb') {
                $sql_pVar = 'SELECT * FROM `%tfiles` WHERE ref_tag = \'items_users_foto\' AND ref_value = %d ORDER BY file_id desc LIMIT 1';
                $tmp_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $userData_pVar['item_id']);
                if(is_array($tmp_pVar)) {
                    return('/.files/th1_' . $tmp_pVar['full_name']);
                }
                $sql_pVar = 'SELECT * FROM `%tfiles` WHERE ref_tag = \'items_users_foto_rec\' AND ref_value = %d ORDER BY file_id desc LIMIT 1';
                $tmp_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $userData_pVar['item_id']);
                if(is_array($tmp_pVar)) {
                    return('/.files/isic/th1_' . $tmp_pVar['full_name']);
                }
                return(false);
            }
        }
        return(false);
    }

    public static function setPassword_gFunc($userId_pVar, $userLogin_pVar, $newPass_pVar)
    {
        $newPass_pVar = db_session_gClass::setPassword_gFunc($userId_pVar, $userLogin_pVar, $newPass_pVar);
    }

    protected function isSuperAdmin_gFunc()
    {
        if(isset($this->data_pVar['user_role']) && $this->data_pVar['user_role'] === 'superuser') {
            return(true);
        }
        return(false);
    }

    protected function isAdmin_gFunc()
    {
        if(isset($this->data_pVar['user_role']) && $this->data_pVar['user_role'] === 'admin') {
            return(true);
        }
        return(false);
    }

    static public function checkForUniqueLoginName_gFunc($loginName_pVar, $user_id_pVar = 0)
    {
        return(db_session_gClass::checkForUniqueLoginName_gFunc($loginName_pVar, $user_id_pVar));
    }

    static public function recountUserStats_gFunc($user_id_pVar)
    {
        $user_update_pVar = array();
        $formatData_pVar = array();
        if(modules_gClass::isModuleRegistred_gFunc('kega', false)) {
            if(!main_gClass::getCacheTagStatus_gFunc('user_test_stats', $user_id_pVar)) {
                // treba pregenerovat statistiky testov
                $sql_pVar = 'SELECT
    								SUM(`score`) as `score`,
    								SUM(`time_total`) as `time_total`,
    								SUM(`n_answers_ok`) as `n_answers_ok`,
    								SUM(`n_answers_fail`) as `n_answers_fail`
    								FROM `%ttests_running` WHERE `user_id` = %d';
                $baseStats_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);
                if(is_array($baseStats_pVar)) {
                    $user_update_pVar['stat_time'] = $baseStats_pVar['time_total'];
                    $user_update_pVar['stat_score'] = $baseStats_pVar['score'];
                    $user_update_pVar['stat_total'] = $baseStats_pVar['n_answers_ok'] + $baseStats_pVar['n_answers_fail'];
                    $user_update_pVar['stat_ok'] = $baseStats_pVar['n_answers_ok'];
                    $user_update_pVar['stat_failed'] = $baseStats_pVar['n_answers_fail'];
                    $formatData_pVar[] = '%d';
                    $formatData_pVar[] = '%d';
                    $formatData_pVar[] = '%d';
                    $formatData_pVar[] = '%d';
                    $formatData_pVar[] = '%d';
                }

                if(count($user_update_pVar)) {
                    db_public_gClass::updateData_gFunc('%titems_users__data', implode(',', $formatData_pVar), $user_update_pVar, 'item_id=%d', array($user_id_pVar), __FILE__, __LINE__);
                }
                main_gClass::updateCacheTagStatus_gFunc('user_test_stats', $user_id_pVar);
            }
        }
    }
}
