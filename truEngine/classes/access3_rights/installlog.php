<?php

class installlog_gClass extends table_gClass
{
    private function getBackupLog_gFunc($filter_pVar = array())
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_system_show_install_log)) {
            return(array());
        }

        if(isset($filter_pVar['pager'])) {
            $pager_pVar = explode(',', $filter_pVar['pager']);
            if(intval($pager_pVar[1])) {
                $pager_pVar[1] = intval($pager_pVar[1]) - 1;
            }
            unset($filter_pVar['pager']);
        }
        else {
            $pager_pVar = false;
        }

        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;
            $limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;
        }
        else {
            $limit_str_pVar = '';
            $pager_pVar = false;
        }

        $sql_pVar = 'select * FROM `%tinstall` order by `install_time` desc' . $limit_str_pVar;
        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

        if($pager_pVar !== false) {
            $sql_pVar = 'SELECT count(`id`) as `n` FROM `%tinstall`';
            $pager_pVar[2] = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, false);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }

        return($data_pVar);
    }

    protected function initTable_gFunc()
    {
        $data_pVar = self::getBackupLog_gFunc($this->params);

        $this->setData_gFunc($data_pVar);
        if(isset($this->params['columns'])) {
            $this->setColumnsFromString_gFunc($this->params['columns']);
        }
    }
}

class installlog extends installlog_gClass {}
