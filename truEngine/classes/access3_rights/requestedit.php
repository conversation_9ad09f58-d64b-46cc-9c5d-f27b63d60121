<?php


class requestedit_gClass extends additem_gClass
{
    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','request_edit');
        $this->setParam_gFunc('formtype-edit', 'request_edit');
        $this->setParam_gFunc('itemtype', 'users');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        parent::initForm_gFunc($multiedit_pVar);
        $this->setFieldPrefix_gFunc('users_');
        if(!session_gClass::userHasRightsInfo_gFunc(s_users_set_isic_force_ok)) {
            $this->setFieldEditable_gFunc('isic_force_ok', false);
        }

        $this->setFieldPrefix_gFunc('');
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        $result_pVar = parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar);

        if($result_pVar === true) {
            if($fieldName_pVar === 'users_login' || $fieldName_pVar === 'users_nick') {
                // zistim ci uz existuje takyto login alebo nick
                if(!session_session_gClass::checkForUniqueLoginName_gFunc(
                    $this->getFieldValue_gFunc($fieldName_pVar),
                    $this->getVar_gFunc('item_id'))) {
                    // error
                    $this->setError_gFunc(string_gClass::get('str__session_login_exists_sVar'), $fieldName_pVar);
                    return(false);
                }
            }
        }
        if($fieldName_pVar === 'users_isic') {
            // isic musi byt unikatny vramci registrovanych pouzivatelov
            $isic_pVar = $this->getFieldValue_gFunc($fieldName_pVar);
            $isic_pVar = str_replace(' ', '', $isic_pVar);
            if($isic_pVar !== '0000000000') {
                $sql_pVar = 'SELECT count(*) FROM `%titems_users__data` WHERE `isic` = %s AND `status`<>\'deleted\' AND status<>\'request\'';
                $n_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $isic_pVar);
                if($n_pVar === false || $n_pVar) {
                    $this->setError_gFunc(string_gClass::get('str__session_isic_exists_sVar'), $fieldName_pVar);
                    return(false);
                }
            }
            else {
                $this->isic_failed_pVar = true;
            }
        }
        return($result_pVar);
    }

    protected function saveData_gFunc()
    {
        $check_isic_pVar = false;
        $this->setFieldPrefix_gFunc('users_');
        $user_id_pVar = $this->getFieldValue_gFunc('item_id');
        $isic_pVar = $this->getFieldValue_gFunc('isic');
        if($user_id_pVar) {
            if($this->getFieldValue_gFunc('isic_ok') !== false) {
                $sql_pVar = 'SELECT `isic_force_ok` FROM `%titems_users__data` WHERE `item_id` = %d';
                $oldValue_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);

                $extraData_pVar = array();

                // ak chcem nastavit jeho platnost, tak ju nastavim, a dalej necheckujem.
                if($this->getFieldValue_gFunc('isic_force_ok') == 'yes') {
                    $extraData_pVar['isic_ok'] = 'yes';
                    $check_isic_pVar = false;
                }

                // ak som zrusil natvrdo nastavenu platnost, zrusim platnost, a ocheckujem.
                if($this->getFieldValue_gFunc('isic_force_ok') == 'no' && $oldValue_pVar == 'yes') {
                    $extraData_pVar['isic_ok'] = 'no';
                    $check_isic_pVar = true;
                }

                // ak som zmenil isic, tak zrusim jeho platnost.
                $sql_pVar = 'SELECT `isic` FROM `%titems_users__data` WHERE `item_id` = %d';
                $oldValue_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);
                if($this->getFieldValue_gFunc('isic') != $oldValue_pVar) {
                    $extraData_pVar['isic_ok'] = 'no';
                    $extraData_pVar['isic_force_ok'] = 'no';
                    $check_isic_pVar = true;
                }
            }
        }

        $isic_pVar = $this->getFieldValue_gFunc('isic');
        if(!empty($isic_pVar)) {
            $isic_pVar = str_replace(' ', '', $isic_pVar);
            $extraData_pVar['isic'] = $isic_pVar;
        }

        $this->setFieldPrefix_gFunc('');
        items_gClass::editItemByForm_gFunc('users', $this, $extraData_pVar);
        if($check_isic_pVar && modules_gClass::isModuleRegistred_gFunc('cdouk')) {
            $cdouk_pVar = new cdouk_gClass();
            //23.3.2014 Kanitra -- zakomentovane kvoli nefungujucemu serveru
            //$cdouk_pVar->getData_gFunc($isic_pVar, false, true, true);
        }
    }
}

class requestedit extends requestedit_gClass {}
