<?php


class session_change_password2_gClass extends form_gClass
{
    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        $type_pVar = 'error';
        $data_pVar = false;

        if(!isset($this->params['email']) || !isset($this->params['hash'])
            || empty($this->params['email']) || empty($this->params['hash'])) {
            $type_pVar = 'error';
        }
        else {
            // rozpoznam ci chcem zmenit heslo, alebo iba odblokovat.. A skontrolujem platnost linky.
            $sql_pVar = 'SELECT * FROM `%taccess__change_password` WHERE `hash` = %s AND `email`= %s AND `destroy_time` > now() AND `user_id_changed` IS NULL';
            $data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['hash'], $this->params['email']));

            $this->setVar_gFunc('request_id', $data_pVar['request_id']);
            if(!is_array($data_pVar)) {
                $type_pVar = 'error';
            }
            elseif($data_pVar['no_change_pass'] == '1') {
                $type_pVar = 'access';
            }
            else {
                $type_pVar = 'password';
            }
        }

        if($type_pVar !== 'error') {
            $users_pVar = db_session_gClass::getUserDataByEmail_gFunc($this->params['email']);
            if(!is_array($users_pVar) || !count($users_pVar)) {
                $type_pVar = 'error';
            }
            else {
                $this->addField_gFunc('', 'user', 'enum', 'Prihlasovacie meno', true);
                $users_options_pVar = array(-1=>'');
                foreach($users_pVar as $v_pVar) {
                    $users_options_pVar[$v_pVar['item_id']] = $v_pVar['login'];
                }
                $this->setFieldOptions_gFunc('', 'user', $users_options_pVar);

                if($type_pVar == 'password') {
                    $this->addField_gFunc('', 'password', 'password', 'Heslo', true);
                    $this->addField_gFunc('', 'password_2', 'password', 'Kontrola hesla', true);
                }
                $this->addHiddenField_gFunc('hash', $this->params['hash'], null, $this->params['hash']);
                $this->addHiddenField_gFunc('email', $this->params['email'], null, $this->params['email']);
            }
        }

        $this->setVar_gFunc('type', $type_pVar);
        if($type_pVar == 'error' && isset($data_pVar['request_id'])) {
            $sql_pVar = 'UPDATE `%taccess__change_password` SET `user_id_changed` = 0 WHERE `request_id` = %d';
            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $data_pVar['request_id']);
        }
    }

    protected function getData()
    {
        $formData_pVar = $this->getFormData_gFunc();
        if($formData_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($formData_pVar);
        }

        // save
        $type = $this->getVar_gFunc('type');
        if($type != 'err') {
            $user_id = $this->getFieldValue_gFunc('user');

            if($type == 'password') {
                // zmenim heslo
                $password = $this->getFieldValue_gFunc('password');
                $user = db_session_gClass::getUserDataByID_gFunc($user_id);
                db_session_gClass::setPassword_gFunc($user_id, $user['login'], $password);
            }

            session_gClass::giveMeFullAccess_gFunc();
            $data_pVar =  array('item_id'=>$user_id, 'email_check_status'=>'checked');
            items_gClass::saveOrUpdateItem_gFunc('users', $data_pVar);
            session_gClass::revokeMeFullAccess_gFunc();

            // odblokuje pristup
            db_session_gClass::resetDisableLevel_gFunc($user_id);

            // zrusim platnost hashu
            $sql_pVar = 'UPDATE `%taccess__change_password` SET `user_id_changed` = %d WHERE `request_id` = %d';
            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($user_id, $this->getVar_gFunc('request_id')));
        }

        return(parent::getData());
    }
}

class session_change_password2 extends session_change_password2_gClass {}
