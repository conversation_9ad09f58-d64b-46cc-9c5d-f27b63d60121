<?php

class users_log_gClass extends source_gClass
{
    protected function getData()
    {
        if(!isset($this->params['item_id']) || !is_numeric($this->params['item_id'])) {
            return(array());
        }

        $log_pVar = array();
        $log_pVar = items_gClass::getLog_gFunc('users', $this->params['item_id'], array(), '0');

        return($log_pVar);
    }
}

class users_log extends users_log_gClass {}
