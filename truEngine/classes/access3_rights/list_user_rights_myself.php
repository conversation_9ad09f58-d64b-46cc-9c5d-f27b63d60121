<?php


class list_user_rights_myself_gClass extends list_role_rights_gClass
{
    protected function getData()
    {
        if(!session_gClass::userHasRightsAccess_gFunc(s_system_show_myself_rights)) {
            return(array());
        }

        $this->params['user_id'] = session_gClass::getUserDetail_gFunc('user_id');

        $ret_pVar = array();

        $sql_pVar = 'SELECT * FROM `%titems_users__data` WHERE `item_id` = %d';
        $user_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $this->params['user_id']);
        if(!is_array($user_pVar)) {
            return(array());
        }

        $this->saveRequestData_gFunc('%taccess__users_rights', 'user_right', s_system_edit_user_rights, array('user_id'=>$this->params['user_id']));

        list($rights_pVar, $groups_pVar, $from_pVar) = session_session_gClass::getUserRightsFromDb_gFunc($user_pVar['user_role'], $user_pVar['item_id']);
        $ret_pVar['rights'] = $rights_pVar;
        $ret_pVar['from'] = $from_pVar;

        $sql_pVar = 'SELECT *, `c`.`'.main_gClass::getLanguage_gFunc().'_category_name` as `category_name`, `n`.`'.main_gClass::getLanguage_gFunc().'_name` as `name`  FROM `%taccess__names` as `n`
						LEFT JOIN `%taccess__names_categories` as `c` ON `n`.`access_category_id`=`c`.`access_category_id`
						ORDER BY `c`.`'.main_gClass::getLanguage_gFunc().'_category_name`, `n`.`'.main_gClass::getLanguage_gFunc().'_name`';
        $ret_pVar['actions'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, false);
        $ret_pVar['actions_index'] = array();
        foreach($ret_pVar['actions'] as $k_pVar=>$action_pVar) {
            $ret_pVar['actions_index'][$action_pVar['access_id']] = $k_pVar;
            if(!isset($rights_pVar[$action_pVar['access_id']]) || $rights_pVar[$action_pVar['access_id']] !== true) {
                unset($ret_pVar['actions'][$k_pVar]);
                continue;
            }
            if(!empty($action_pVar['module'])) {
                $modules_pVar = explode(',', $action_pVar['module']);
                foreach($modules_pVar as $module_pVar) {
                    if(!modules_gClass::isModuleRegistred_gFunc(trim($module_pVar))) {
                        unset($ret_pVar['actions'][$k_pVar]);
                    }
                }
            }
        }

        return($ret_pVar);
    }
}

class list_user_rights_myself extends list_user_rights_myself_gClass {}
