<?php


class users_adduser_gClass extends form_gClass
{
    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        if(isset($this->params['item_id']) && $this->params['item_id']>0) {
            $isUserId_pVar = true;
        }
        else {
            $isUserId_pVar = false;
        }

        $this->addFieldset_gFunc('main', '');
        $this->addField_gFunc('main', 'users_first_name', 'varchar', 'Meno:', true);
        $this->addField_gFunc('main', 'users_last_name', 'varchar', 'Priezvisko:', true);
        $this->addField_gFunc('main', 'users_titul_pred', 'varchar', 'Tituly pred menom:', false);
        $this->addField_gFunc('main', 'users_titul_za', 'varchar', 'Tituly za menom:', false);
        $this->addField_gFunc('main', 'users_login', 'varchar', '<PERSON><PERSON><PERSON><PERSON><PERSON>cie meno (e-mail):', true);
        $this->addField_gFunc('main', 'users_password', 'password', 'Heslo:', !$isUserId_pVar);
        $this->addField_gFunc('main', 'users_password2', 'password', 'Heslo znovu:', !$isUserId_pVar);
        $this->addField_gFunc('main', 'users_email', 'email', 'E-mail:', true);

        $this->addFieldset_gFunc('discounts', 'Zľavy');
        $this->addField_gFunc('discounts', 'discount', 'float', 'Zľava na tovar [%]', true);

        // fakturacna adresa
        $this->addFieldset_gFunc('address_invoice', 'Fakturačná adresa');
        $this->addField_gFunc('address_invoice', 'ai_name', 'varchar', 'Názov/Meno', true);
        $this->addField_gFunc('address_invoice', 'ai_street_number', 'varchar', 'Ulica, č.d.', true);
        $this->addField_gFunc('address_invoice', 'ai_city', 'varchar', 'Mesto', true);
        $this->addField_gFunc('address_invoice', 'ai_zip', 'varchar', 'PSČ', true);
        $this->addField_gFunc('address_invoice', 'ai_phone', 'varchar', 'Telefón', false);
        $this->addField_gFunc('address_invoice', 'ai_email', 'varchar', 'E-mail', false);
        $this->addField_gFunc('address_invoice', 'ico', 'varchar', 'IČO', true);
        $this->addField_gFunc('address_invoice', 'dic', 'varchar', 'DIČ', false);
        $this->addField_gFunc('address_invoice', 'ic_dph', 'varchar', 'IČ DPH', false);

        // dorucovacia adresa
        $this->addFieldset_gFunc('address_delivery', 'Adresa dodania tovaru');
        $this->addField_gFunc('address_delivery', 'ad_name', 'varchar', 'Názov/Meno', false);
        $this->addField_gFunc('address_delivery', 'ad_street_number', 'varchar', 'Ulica, č.d.', false);
        $this->addField_gFunc('address_delivery', 'ad_city', 'varchar', 'Mesto', false);
        $this->addField_gFunc('address_delivery', 'ad_zip', 'varchar', 'PSČ', false);
        $this->addField_gFunc('address_delivery', 'ad_phone', 'varchar', 'Telefón', false);
        $this->addField_gFunc('address_delivery', 'ad_email', 'varchar', 'E-mail', false);


        if($isUserId_pVar) {
            $this->addHiddenField_gFunc('users_item_id', $this->params['item_id'], '/[0-9]+/');
            $this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_update']);
            // selectnem pouzivatela a inicializujem hodnoty
            $userData_pVar = db_session_gClass::getUserDataByID_gFunc($this->params['item_id']);
            if(is_array($userData_pVar)) {
                $this->setDefaultValue_pVar('main', 'users_login', $userData_pVar['login']);
                $this->setDefaultValue_pVar('main', 'users_first_name', $userData_pVar['first_name']);
                $this->setDefaultValue_pVar('main', 'users_last_name', $userData_pVar['last_name']);
                $this->setDefaultValue_pVar('main', 'users_titul_pred', $userData_pVar['titul_pred']);
                $this->setDefaultValue_pVar('main', 'users_titul_za', $userData_pVar['titul_za']);
                $this->setDefaultValue_pVar('main', 'users_email', $userData_pVar['email']);
                $this->setDefaultValue_pVar('address_invoice', 'ico', $userData_pVar['ico']);
                $this->setDefaultValue_pVar('address_invoice', 'dic', $userData_pVar['dic']);
                $this->setDefaultValue_pVar('address_invoice', 'ic_dph', $userData_pVar['ic_dph']);

                $iAddress_pVar = db_session_gClass::getUserAddressByType_gFunc($this->params['item_id'], 'invoice');
                $this->setDefaultValue_pVar('address_invoice', 'ai_name', $iAddress_pVar['name']);
                $this->setDefaultValue_pVar('address_invoice', 'ai_street_number', $iAddress_pVar['street_number']);
                $this->setDefaultValue_pVar('address_invoice', 'ai_city', $iAddress_pVar['city']);
                $this->setDefaultValue_pVar('address_invoice', 'ai_zip', $iAddress_pVar['zip']);
                $this->setDefaultValue_pVar('address_invoice', 'ai_phone', $iAddress_pVar['phone']);
                $this->setDefaultValue_pVar('address_invoice', 'ai_email', $iAddress_pVar['email']);

                $dAddress_pVar = db_session_gClass::getUserAddressByType_gFunc($this->params['item_id'], 'delivery');
                $this->setDefaultValue_pVar('address_delivery', 'ad_name', $dAddress_pVar['name']);
                $this->setDefaultValue_pVar('address_delivery', 'ad_street_number', $dAddress_pVar['street_number']);
                $this->setDefaultValue_pVar('address_delivery', 'ad_city', $dAddress_pVar['city']);
                $this->setDefaultValue_pVar('address_delivery', 'ad_zip', $dAddress_pVar['zip']);
                $this->setDefaultValue_pVar('address_delivery', 'ad_phone', $dAddress_pVar['phone']);
                $this->setDefaultValue_pVar('address_delivery', 'ad_email', $dAddress_pVar['email']);

                $this->setDefaultValue_pVar('discounts', 'discount', $userData_pVar['discount']);
            }
        }
        else {
            $this->addHiddenField_gFunc('users_item_id', 0, '/[0]+/');
            $this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_add']);
        }

        $this->setVar_gFunc('submit_button_title_add', $this->params['submit_button_title_add']);
        $this->setVar_gFunc('submit_button_title_update', $this->params['submit_button_title_update']);
        return;
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        if($fieldName_pVar === 'users_password' || $fieldName_pVar === 'users_password2') {
            $password_pVar = $this->getFieldValue_gFunc('users_password');
            $password2_pVar = $this->getFieldValue_gFunc('users_password2');
            if(!empty($password_pVar) || !empty($password2_pVar)) {
                if($password_pVar !== $password2_pVar || strlen($password_pVar) < 5) {
                    // hesla nie su rovnake, alebo su kratke
                    $this->setError_gFunc('xx', 'users_password');
                    $this->setError_gFunc('xx', 'users_password2');
                    return(false);
                }
            }
        }
        return(parent::validateField_gFunc($fieldsetName_pVar, $fieldName_pVar));
    }

    protected function getData()
    {
        $data_pVar = $this->getFormData_gFunc();
        if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($data_pVar);
        }

        // ulozim pouzivatela
        $userId_pVar = $this->getFieldValue_gFunc('users_item_id');
        $userData_pVar = array();
        $userData_pVar['first_name'] = $this->getFieldValue_gFunc('users_first_name');
        $userData_pVar['last_name'] = $this->getFieldValue_gFunc('users_last_name');
        $userData_pVar['email'] = $this->getFieldValue_gFunc('users_email');
        $userData_pVar['titul_pred'] = $this->getFieldValue_gFunc('users_titul_pred');
        $userData_pVar['titul_za'] = $this->getFieldValue_gFunc('users_titul_za');
        $userData_pVar['login'] = $this->getFieldValue_gFunc('users_login');

        if($userId_pVar) {
            // aktualizujem
            db_session_gClass::updateUser_gFunc($userId_pVar, $userData_pVar);
        }
        else {
            // novy pouzivatel
            $userId_pVar = db_session_gClass::newUser_gFunc($userData_pVar);
        }

        // fakturacna adresa
        $addressDataInvoice_pVar = array();
        $addressDataInvoice_pVar['email'] = $this->getFieldValue_gFunc('ai_email');
        $addressDataInvoice_pVar['name'] = $this->getFieldValue_gFunc('ai_name');
        $addressDataInvoice_pVar['street_number'] = $this->getFieldValue_gFunc('ai_street_number');
        $addressDataInvoice_pVar['city'] = $this->getFieldValue_gFunc('ai_city');
        $addressDataInvoice_pVar['zip'] = $this->getFieldValue_gFunc('ai_zip');
        $addressDataInvoice_pVar['phone'] = $this->getFieldValue_gFunc('ai_phone');
        $addri_pVar = db_session_gClass::updateAddress_gFunc($userId_pVar, 'invoice', $addressDataInvoice_pVar);

        // adresa dorucenia
        $addressDataDelivery_pVar = array();
        $addressDataDelivery_pVar['email'] = $this->getFieldValue_gFunc('ad_email');
        $addressDataDelivery_pVar['name'] = $this->getFieldValue_gFunc('ad_name');
        $addressDataDelivery_pVar['street_number'] = $this->getFieldValue_gFunc('ad_street_number');
        $addressDataDelivery_pVar['city'] = $this->getFieldValue_gFunc('ad_city');
        $addressDataDelivery_pVar['zip'] = $this->getFieldValue_gFunc('ad_zip');
        $addressDataDelivery_pVar['phone'] = $this->getFieldValue_gFunc('ad_phone');
        $addrd_pVar = db_session_gClass::updateAddress_gFunc($userId_pVar, 'delivery', $addressDataDelivery_pVar);

        // password
        $password_pVar = $this->getFieldValue_gFunc('users_password');
        if(!empty($password_pVar)) {
            session_session_gClass::setPassword_gFunc($userId_pVar, $userData_pVar['login'], $password_pVar);
        }

        // ico, dic, ic_dph
        $properties_pVar = array('ico' => $this->getFieldValue_gFunc('ico')
        , 'dic' => $this->getFieldValue_gFunc('dic')
        , 'ic_dph' =>$this->getFieldValue_gFunc('ic_dph')
        , 'address_id_eshop'=>$addri_pVar
        , 'address_id_eshop_delivery' => $addrd_pVar
        , 'discount' => $this->getFieldValue_gFunc('discount')
        );
        db_session_gClass::updateUserProprties_gFunc($userId_pVar, $properties_pVar);


        $data_pVar = $this->getFormData_gFunc();
        return($data_pVar);
    }

}

class users_adduser extends users_adduser_gClass
{

}


