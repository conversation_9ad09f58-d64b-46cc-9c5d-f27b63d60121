<?php

class session_check_request_gClass extends source_gClass
{
    protected function getData()
    {
        // skontrolujem platnost linky
        $type_pVar = 'error';
        if(isset($this->params['hash']) && isset($this->params['email']) && isset($this->params['user_id'])) {
            $sql_pVar = 'SELECT * FROM `%taccess__check_mail` WHERE `hash` = %s AND `email`= %s AND `user_id` = %d AND `destroy_time` > now() AND `user_id_checked` IS NULL';
            $data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['hash'], $this->params['email'], $this->params['user_id']));
            $type_pVar = 'ok';
            $request_id_pVar = isset($data_pVar['request_id']) ? $data_pVar['request_id'] : null;
        }

        if(!is_array($data_pVar) || !count($data_pVar) || $type_pVar == 'error') {
            return(array('type'=>'error'));
        }

        $ret_pVar = array();
        session_gClass::giveMeFullAccess_gFunc();
        $user_pVar = items_gClass::getItem_gFunc('users', intval($this->params['user_id']));
        session_gClass::revokeMeFullAccess_gFunc();

        if(!$user_pVar || !is_array($user_pVar) || $user_pVar['status'] != 'request') {
            return(array('type'=>'error'));
        }

        if($user_pVar['email'] != $this->params['email']) {
            return(array('type'=>'error'));
        }

        session_gClass::giveMeFullAccess_gFunc();
        $user_data_pVar = array('item_id'=>$user_pVar['item_id'], 'email_check_status'=>'checked');
        items_gClass::saveOrUpdateItem_gFunc('users', $user_data_pVar);
        session_gClass::revokeMeFullAccess_gFunc();

        $str_pVar = '';
        if(isset($user_pVar['isic_ok']) && $user_pVar['isic_ok'] == 'no') {
            if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
                session_gClass::giveMeFullAccess_gFunc();
                $cdouk_pVar = new cdouk_gClass();
                $cdouk_pVar->getData_gFunc($user_pVar['isic'], false, true, true);
                $user_pVar = items_gClass::getItem_gFunc('users', intval($this->params['user_id']));
                session_gClass::revokeMeFullAccess_gFunc();
            }

            if(isset($user_pVar['isic_ok']) && $user_pVar['isic_ok'] == 'no') {
                $str_pVar .= '&isic=true';
            }
        }

        $user_role_pVar = $user_pVar['user_role'];
        $roles_pVar = main_gClass::getConfigVar_gFunc('registration_roles', 'access3_rights');
        $roles2_pVar = main_gClass::getConfigVar_gFunc('registration_autoaccept', 'access3_rights');
        $autoaccept_pVar = false;
        if($roles_pVar !== null && $roles2_pVar !== null) {
            $roles_pVar = explode(',', $roles_pVar);
            $roles2_pVar = explode(',', $roles2_pVar);
            if(count($roles_pVar) && count($roles2_pVar)) {
                if(in_array($user_role_pVar, $roles_pVar) && in_array($user_role_pVar, $roles2_pVar)) {
                    // autoaccept...
                    $autoaccept_pVar = true;
                }
            }
        }

        if(!$autoaccept_pVar) {
            $str_pVar .= '&role=' . $user_pVar['user_role'];
        }

        // odblokuje pristup
        db_session_gClass::resetDisableLevel_gFunc($user_pVar['item_id']);

        // zrusim platnost hashu
        $sql_pVar = 'UPDATE `%taccess__check_mail` SET `user_id_checked` = %d WHERE `request_id` = %d';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($user_pVar['item_id'], $request_id_pVar));

        if(empty($str_pVar)) {
            /// automaticka akceptacia
            session_gClass::giveMeFullAccess_gFunc();
            $user_data_pVar = array('item_id'=>$user_pVar['item_id'], 'status'=>'active');
            items_gClass::saveOrUpdateItem_gFunc('users', $user_data_pVar);
            session_gClass::revokeMeFullAccess_gFunc();
            $ret_pVar['type'] = 'accepted';
            $str_pVar .= '&accepted=true';

            // ulozim do DB
            $data_pVar = array(
                'user_id'=>$user_pVar['item_id'],
                'request_time'=>date('Y-m-d H:i:s'),
                'request_type'=>'autoaccept'
            );
            db_public_gClass::insertData_gFunc('%tusers_requestlog', '%d,%s,%s', $data_pVar, __FILE__, __LINE__);
        }
        else {
            // nemoze byt automaticka registracia, posielam email adminovi
            // poslem notifikaciu
            $subject_pVar = 'Nová registrácia';

            $template_pVar = '/.emails/registration-info';
            $template_pVar = main_gClass::addLngPrefixToDocName_gFunc($template_pVar);

            $vars_pVar = array('mail'=>array());
            $vars_pVar['mail']['user'] = $user_pVar;
            $vars_pVar['mail']['url'] = main_gClass::getServerVar_gFunc('SERVER_NAME') . main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar');
            $email_pVar = main_gClass::getConfigVar_gFunc('admin_requests', 'contacts');

            $templateContent_pVar = callStack_gClass::getDocContent_gFunc($template_pVar, $vars_pVar);
            email_gClass::mailHtml_gFunc($email_pVar, main_gClass::getConfigVar_gFunc('email', 'contacts'), $subject_pVar, $templateContent_pVar);
        }
        $ret_pVar['status'] = $str_pVar;

        return($ret_pVar);
    }
}

class session_check_request extends session_check_request_gClass {}
