<?php


class test_prepare_gClass extends source_gClass
{

    protected function getData()
    {
        $data_pVar = array();
        $data_pVar['running_test_id'] = 0;

        if(isset($this->params['running_test_id']) && $this->params['running_test_id']) {
            $sql_pVar = 'SELECT * FROM `%ttests_running` WHERE `id` = %d AND user_id = %d';
            $data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, array($this->params['running_test_id'], session_gClass::getUserDetail_gFunc('user_id')));
            if(is_array($data_pVar) && isset($data_pVar['id']) && $data_pVar['id'] == $this->params['running_test_id']) {
                $data_pVar['running_test_id'] = $data_pVar['id'];
            }
            else {
                $data_pVar = array();
                $data_pVar['running_test_id'] = 0;
            }
        }
        elseif(isset($this->params['test_id']) && $this->params['test_id']) {
            $sql_pVar = 'SELECT * FROM `%ttests` WHERE `id` = %d AND `status` <> \'deleted\' AND (`time_stop` IS NULL OR `time_stop` > now())';
            $test_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $this->params['test_id']);
            if(!is_array($test_pVar)) {
                $data_pVar = array();
                $data_pVar['running_test_id'] = 0;
            }
            else {
                if(!empty($test_pVar['access_key'])) {
                    if(isset($this->params['access_key']) && !empty($this->params['access_key'])) {
                        $access_key_pVar = $this->params['access_key'];
                        main_gClass::setPhpSessionVar_gFunc('test_access_key', $access_key_pVar);
                    }

                    $access_key_pVar = main_gClass::getSessionData_gFunc('test_access_key', null);
                    if(empty($access_key_pVar) || $access_key_pVar !== $test_pVar['access_key']) {
                        $data_pVar['running_test_id'] = 0;
                        $data_pVar['access_key_required'] = true;
                        return($data_pVar);
                    }
                }

                $settings_pVar = kega_gClass::explodeSettings_gFunc($test_pVar['settings']);
                $questions_pVar = question_selector_gClass::selectQuestionsIdFromTest_gFunc($this->params['test_id'], $settings_pVar);

                if(is_array($questions_pVar)) {
                    $template_pVar =  kega_gClass::implodeSettings_gFunc($settings_pVar);

                    $sql_string_pVar = '%d, %d, %r, %d, %s, %xs, %xs';
                    $sql_data_pVar = array('creator_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                        'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                        'time_create'=>'now()',
                        'source_test_id'=>$this->params['test_id'],
                        'settings'=>$template_pVar
                    );

                    if($test_pVar['time_start'] === NULL) {
                        $sql_data_pVar['time_start'] = NULL;
                    }
                    else {
                        if(strtotime($test_pVar['time_start']) < time()) {
                            $sql_data_pVar['time_start'] = date('Y-m-d H:i:s');
                        }
                        else {
                            $sql_data_pVar['time_start'] = $test_pVar['time_start'];
                        }
                    }

                    if($test_pVar['time_stop'] === NULL) {
                        $sql_data_pVar['time_end'] = NULL;
                    }
                    else {
                        if(strtotime($test_pVar['time_stop']) < time()) {
                            $sql_data_pVar['time_end'] = date('Y-m-d H:i:s');
                        }
                        else {
                            $sql_data_pVar['time_end'] = $test_pVar['time_stop'];
                        }

                        if($sql_data_pVar['time_start'] !== NULL
                            && strtotime($sql_data_pVar['time_start']) > strtotime($sql_data_pVar['time_end'])) {
                            $sql_data_pVar['time_start'] = $sql_data_pVar['time_end'];
                        }
                    }

                    $running_test_id_pVar = db_public_gClass::insertData_gFunc('%ttests_running', $sql_string_pVar, $sql_data_pVar, __FILE__, __LINE__, true);
                    foreach($questions_pVar as $question_pVar) {
                        $question_pVar['running_test_id'] = $running_test_id_pVar;
                        db_public_gClass::insertData_gFunc('%ttests_running__questions', '%d, %d, %s, %d', $question_pVar, __FILE__, __LINE__);
                    }

                    $data_pVar['running_test_id'] = $running_test_id_pVar;

                    log_gClass::write_gFunc('TEST_PREPARE_FROM_TEST', $this->params['test_id'], false, true);

//                    \App\Tracker\Tracker::getInstance()->track('test_prepare_from_test', [
//                        'test_id' => $this->params['test_id'],
//                        'user_id' =>session_gClass::getUserDetail_gFunc('user_id'),
//                        'settings' => $template_pVar,
//                        'running_test_id' => $running_test_id_pVar,
//                        'questions' => $questions_pVar,
//                    ]);
                }
                else {
                    $data_pVar['running_test_id'] = 0;
                }
            }
        }
        elseif(isset($this->params['template_id']) && $this->params['template_id']) {
            $sql_pVar = 'SELECT * FROM `%titems_test_templates__data` WHERE `item_id` = %d';
            $template_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $this->params['template_id']);
            if(!$template_pVar) {
                $data_pVar = array();
                $data_pVar['running_test_id'] = 0;
            }
            else {
                if(!empty($template_pVar['access_key'])) {
                    if(isset($this->params['access_key']) && !empty($this->params['access_key'])) {
                        $access_key_pVar = $this->params['access_key'];
                        main_gClass::setPhpSessionVar_gFunc('test_access_key', $access_key_pVar);
                    }

                    $access_key_pVar = main_gClass::getSessionData_gFunc('test_access_key', null);
                    if(empty($access_key_pVar) || $access_key_pVar !== $template_pVar['access_key']) {
                        $data_pVar = array();
                        $data_pVar['running_test_id'] = 0;
                        $data_pVar['access_key_required'] = true;
                        return($data_pVar);
                    }
                }

                unset($template_pVar['item_id']);
                unset($template_pVar['status']);
                unset($template_pVar['owner_id']);
                unset($template_pVar['insert_time']);
                unset($template_pVar['update_time']);
                unset($template_pVar['smer']);
                unset($template_pVar['rocnik']);
                foreach($template_pVar as $k_pVar=>$v_pVar) {
                    if(isset($k_pVar[2]) && $k_pVar[2] == '_') {
                        unset($template_pVar[$k_pVar]);
                    }
                }

                $template_pVar['miesanie_otazok'] = 'yes'; // pre spustenie zo sblony sa vzdy musia zamiesat
                $template_pVar['miesanie_odpovedi'] = 'yes'; // pre spustenie zo sblony sa vzdy musia zamiesat

                // tu sa generuju otazky pre test zo sablony


                $saveTemplateQuestions_pVar = false;
                if($template_pVar['parametric'] == 'no') {
                    $questions_pVar = question_selector_gClass::selectQuestionsIdFromTemplate_gFunc($this->params['template_id'], $template_pVar);
                    if(!is_array($questions_pVar) || !count($questions_pVar)) {
                        $questions_pVar = question_selector_gClass::selectQuestionsId_gFunc($template_pVar);
                        $saveTemplateQuestions_pVar = true;
                    }
                }
                else {
                    $questions_pVar = question_selector_gClass::selectQuestionsId_gFunc($template_pVar);
                }


                if(is_array($questions_pVar)) {
                    //echo '<div class="teststat">TEST-STATUS '.count($questions_pVar).'</div>';
                    $settings_pVar = kega_gClass::implodeSettings_gFunc($template_pVar);

                    $sql_string_pVar = '%d, %d, %r, %d, %s';
                    $sql_data_pVar = array('creator_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                        'user_id'=>session_gClass::getUserDetail_gFunc('user_id'),
                        'time_create'=>'now()',
                        'source_template_id'=>$this->params['template_id'],
                        'settings'=>$settings_pVar
                    );
                    $running_test_id_pVar = db_public_gClass::insertData_gFunc('%ttests_running', $sql_string_pVar, $sql_data_pVar, __FILE__, __LINE__, true);
                    foreach($questions_pVar as $question_pVar) {
                        if($saveTemplateQuestions_pVar) {
                            $question_pVar['template_id'] = $this->params['template_id'];
                            db_public_gClass::insertData_gFunc('%ttemplate_questions', '%d, %d, %s, %d', $question_pVar, __FILE__, __LINE__);
                            unset($question_pVar['template_id']);
                        }
                        $question_pVar['running_test_id'] = $running_test_id_pVar;
                        db_public_gClass::insertData_gFunc('%ttests_running__questions', '%d, %d, %s, %d', $question_pVar, __FILE__, __LINE__);
                    }

                    $data_pVar['running_test_id'] = $running_test_id_pVar;
                    log_gClass::write_gFunc('TEST_PREPARE_FROM_TEMPLATE', $this->params['template_id'], false, true);

//                    \App\Tracker\Tracker::getInstance()->track('test_prepare_from_template', [
//                        'template_id' => $this->params['template_id'],
//                        'user_id' =>session_gClass::getUserDetail_gFunc('user_id'),
//                        'settings' => $settings_pVar,
//                        'running_test_id' => $running_test_id_pVar,
//                        'questions' => $questions_pVar,
//
//                    ]);
                }
                else {
                    $data_pVar['running_test_id'] = 0;
                }
            }
        }
        else {
            $data_pVar['running_test_id'] = 0;
        }

        return($data_pVar);
    }

}

class test_prepare extends test_prepare_gClass {}

