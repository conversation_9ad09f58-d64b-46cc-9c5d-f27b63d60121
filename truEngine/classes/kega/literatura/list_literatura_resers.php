<?php


class list_literatura_resers_gClass extends source_gClass
{

    protected function getData()
    {
        $filter_pVar = $this->params;
        $test_id_pVar = 0;
        $template_id_pVar = 0;
        if(isset($filter_pVar['test_id'])) {
            $test_id_pVar = intval($filter_pVar['test_id']);
            exit; ///////// tento parameter sa uz nepouziva
        }
        if(isset($filter_pVar['template_id'])) {
            $template_id_pVar = intval($filter_pVar['template_id']);
            $template_pVar = items_gClass::getItem_gFunc('test_templates', intval($filter_pVar['template_id']));
            if($template_pVar['parametric'] == 'no') {
                $test_id_pVar = $template_id_pVar;
            }
        }

        $md5s_pVar = array();
        $questions_pVar = array();
        if($test_id_pVar) {
            $sql_pVar = 'SELECT `db_question_id` as `question_id` FROM `%ttemplate_questions` WHERE `template_id` = %d';
            $result_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $test_id_pVar);
            foreach($result_pVar as $v_pVar) {
                $questions_pVar[] = $v_pVar['question_id'];
            }
        }
        elseif($template_id_pVar) {
            $sql_pVar = 'SELECT * FROM `%titems_test_templates__data` WHERE `item_id` = %d';
            $settings_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $template_id_pVar);
            $settings_pVar['pocet_otazok'] = -1;
            $questions_pVar = question_selector_gClass::selectQuestionsId_gFunc($settings_pVar);
            $questions_pVar = array_keys($questions_pVar);
        }

        if(!count($questions_pVar)) {
            $questions_pVar = array(0);
        }
        $sql_pVar = 'SELECT `literatura_md5`, GROUP_CONCAT(DISTINCT `literatura_strana`) as `literatura_strana` FROM `%titems_test_questions__data` WHERE `item_id` IN (%ai) GROUP BY `literatura_md5`';

        $result_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($questions_pVar), 'literatura_md5');
        foreach($result_pVar as $v_pVar) {
            $md5s_pVar[] = $v_pVar['literatura_md5'];
        }
        unset($filter_pVar['test_id']);
        unset($filter_pVar['template_id']);
        $filter_pVar['qmd5'] = implode('|', $md5s_pVar);
        $literatura_pVar = items_gClass::getItems_gFunc('literatura', $filter_pVar);
        unset($literatura_pVar['filter']);

        foreach($literatura_pVar as $k=>$v) {
            if(isset($v['qmd5']) && isset($result_pVar[$v['qmd5']])) {
                $strany = $result_pVar[$v['qmd5']]['literatura_strana'];
                for($i=0; $i<5; $i++) {
                    $strany = str_replace('  ', ' ', $strany);
                }
                $strany = str_replace('- ', '-', $strany);
                $strany = str_replace(' -', '-', $strany);
                $strany = str_replace(' ,', ',', $strany);
                $strany = str_replace(', ', ',', $strany);
                $strany = str_replace(',', ', ', $strany);
                $literatura_pVar[$k]['strany'] =  $strany;
            }
        }
        return($literatura_pVar);
    }
}

class list_literatura_resers extends list_literatura_resers_gClass {}

