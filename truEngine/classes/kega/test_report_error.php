<?php

class test_report_error_gClass extends source_gClass
{
    protected function getData()
    {
        if(!session_gClass::userHasRightsAccess_gFunc(s_test_report_error)) {
            return(array());
        }

        $question_id_pVar = 0;
        $answer_id_pVar = 0;

        if(isset($this->params['question_id']) && $this->params['question_id']) {
            $question_id_pVar = intVal($this->params['question_id']);
        }
        else {
            return(array());
        }


        if(isset($this->params['type']) && !empty($this->params['type']) && $this->params['type'] !== 'comment') {
            $comment_pVar = $this->params['type'];
        }
        if(isset($this->params['answer_id']) && $this->params['answer_id']) {
            if(!empty($comment_pVar)) {
                $comment_pVar .= ' ';
            }
            $comment_pVar .= '(' . $this->params['answer_id'] . ')';
            $answer_id_pVar = $this->params['answer_id'];
        }

        if(isset($this->params['comment']) && !empty($this->params['comment'])) {
            if(!empty($comment_pVar)) {
                $comment_pVar .= ': ';
            }
            $comment_pVar .= $this->params['comment'];
        }

        items_gClass::addItemComment_gFunc('test_questions', $question_id_pVar, $comment_pVar);

        // a teraz este incrementnem statisticku polozku
        if(isset($this->params['type']) && !empty($this->params['type']) && $this->params['type'] !== 'comment') {
            $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `nekorektnost` = `nekorektnost` + 1 WHERE `item_id` = %d';
            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($question_id_pVar));
            if($answer_id_pVar) {
                $sql_pVar = 'UPDATE `%titems_test_answers__data` SET `nekorektnost` = `nekorektnost` + 1 WHERE `item_id` = %d';
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($answer_id_pVar));
            }
        }

        return(array());
    }
}

class test_report_error extends test_report_error_gClass { }
