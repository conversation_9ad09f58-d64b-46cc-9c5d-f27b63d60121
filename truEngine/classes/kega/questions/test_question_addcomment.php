<?php

class test_question_addcomment_gClass extends source_gClass
{
    protected function getData()
    {
        if(!isset($this->params['item_id']) || !is_numeric($this->params['item_id'])) {
            return(false);
        }

        if(!isset($this->params['text']) || empty($this->params['text'])) {
            return(false);
        }

        $this->params['text'] = nl2br($this->params['text']);
        items_gClass::addItemComment_gFunc('test_questions', $this->params['item_id'], $this->params['text']);
        log_gClass::write_gFunc('TEST_QUESTION_COMMENT', $this->params['item_id'], false, true);
        return(true);
    }
}

class test_question_addcomment extends test_question_addcomment_gClass {}
