<?php

class test_question_log_gClass extends source_gClass
{
    protected function getData()
    {
        if(!isset($this->params['item_id']) || !is_numeric($this->params['item_id'])) {
            return(array());
        }

        $log_pVar = array();
        $log_pVar = items_gClass::getLog_gFunc('test_questions', $this->params['item_id'], array(), '0');

        // ziskam idcka odpovedi
        $ids_pVar = items_gClass::getItemsIds_gFunc('test_answers', array('test_question'=>$this->params['item_id']));
        // a pridam logy odpovedi
        $log_pVar = items_gClass::getLog_gFunc('test_answers', $ids_pVar, $log_pVar, '1');

        return($log_pVar);
    }
}

class test_question_log extends test_question_log_gClass {}
