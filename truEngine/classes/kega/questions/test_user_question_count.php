<?php

class test_user_question_count_gClass extends table_gClass
{
    protected function initTable_gFunc()
    {
        if(!session_gClass::userHasRightsAccessAction_gFunc(s_test_show_users_count)) {
            $data_pVar = array();
        }
        else {
            $data_pVar = kega_gClass::getUsersQuestionsCount_gFunc($this->params);
        }
        $this->setData_gFunc($data_pVar);
        if(isset($this->params['columns'])) {
            $this->setColumnsFromString_gFunc($this->params['columns']);
        }
    }
}

class test_user_question_count extends test_user_question_count_gClass {}
