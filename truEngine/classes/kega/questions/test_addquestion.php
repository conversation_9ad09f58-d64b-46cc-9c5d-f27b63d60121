<?php


class test_addquestion_gClass extends additem_gClass
{
    protected function initParams_gFunc()
    {
        $this->setParam_gFunc('formtype-add','add_item');
        $this->setParam_gFunc('formtype-edit', 'edit_item');
        $this->setParam_gFunc('itemtype', 'test_questions');
    }

    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
//        if(isset($this->params['item_id']) && $this->params['item_id']) {
//            kega_gClass::recountQuestionStats_gFunc($this->params['item_id']);
//        }
        parent::initForm_gFunc();
        $this->setFieldPrefix_gFunc('test_questions_');
        if(!$this->getFieldValue_gFunc('item_id')) {

            $saveVars_pVar = array('modul','program','predmet','kategoria','podkategoria',
                'sk_keywords','en_keywords',
                'literatura',
                'literatura_strana',
                'sk_vysvetlenie',
                'en_vysvetlenie',
                'dolezitost',
                'typ',
                'sk_otazka','en_otazka');
            foreach ($saveVars_pVar as $var_pVar) {
                $tmp_pVar = main_gClass::getSessionData_gFunc('test_questions_'.$var_pVar);
                if($tmp_pVar !== null) {
                    $this->setFieldDefaultValue_gFunc($var_pVar, $tmp_pVar);
                }
            }

            $test_questions_navrhovatel_pVar = main_gClass::getSessionData_gFunc('test_questions_navrhovatel');
            if(!$test_questions_navrhovatel_pVar) {
                $test_questions_navrhovatel_pVar = '@user=' . session_gClass::getUserDetail_gFunc('login');
                main_gClass::setPhpSessionVar_gFunc('test_questions_navrhovatel', $test_questions_navrhovatel_pVar);
            }
            $this->setFieldDefaultValue_gFunc('navrhovatel', $test_questions_navrhovatel_pVar);

        }
        else {
            $this->replaceContact_gFunc('navrhovatel');
            $this->replaceContact_gFunc('autorske_prava');

            if($this->getFieldValue_gFunc('status') == 'rejected' && !session_gClass::userHasRightsInfo_gFunc(s_test_edit_question)) {
                $options_pVar = $this->getFieldOptions_gFunc('main', 'status');
                unset($options_pVar['active']);
                $this->setFieldOptions_gFunc('main', 'status', $options_pVar);
            }
            if($this->getFieldValue_gFunc('status') == 'waiting' && !session_gClass::userHasRightsInfo_gFunc(s_test_edit_question)) {
                $options_pVar = $this->getFieldOptions_gFunc('main', 'status');
                unset($options_pVar['active']);
                unset($options_pVar['rejected']);
                $this->setFieldOptions_gFunc('main', 'status', $options_pVar);
            }
            $this->addField_gFunc('stats', 'reset_stats', 'enum', 'Vynulovať štatistiky');
            $this->addFieldOption_gFunc('stats', 'reset_stats', 'no', 'nie');
            $this->addFieldOption_gFunc('stats', 'reset_stats', 'yes', 'áno');
        }
        $this->setFieldPrefix_gFunc('');
    }

    protected function getData()
    {
        $field_pVar = $this->getField_gFunc('test_questions_navrhovatel');
        if($field_pVar['editable']) {
            main_gClass::setPhpSessionVar_gFunc('test_questions_navrhovatel', $this->getFieldValue_gFunc('test_questions_navrhovatel'));
        }

        $saveVars_pVar = array('modul','program','predmet','kategoria','podkategoria',
            'sk_keywords','en_keywords',
            'literatura',
            'literatura_strana',
            'sk_vysvetlenie',
            'en_vysvetlenie',
            'dolezitost',
            'typ',
            'sk_otazka','en_otazka');

        foreach($saveVars_pVar as $var_pVar) {
            main_gClass::setPhpSessionVar_gFunc('test_questions_'.$var_pVar, $this->getFieldValue_gFunc('test_questions_'.$var_pVar));
        }

        return(parent::getData());
    }

    protected function saveData_gFunc()
    {
        $extraFields_pVar = array();

        $reset_stats_pVar = $this->getFieldValue_gFunc('test_questions_reset_stats');
        if($reset_stats_pVar == 'yes') {
            $reset_value_pVar = 0;
            $extraFields_pVar['obtiaznost'] = $reset_value_pVar;
            $extraFields_pVar['nekorektnost'] = $reset_value_pVar;
            $extraFields_pVar['spravne'] = $reset_value_pVar;
            $extraFields_pVar['nespravne'] = $reset_value_pVar;
            $extraFields_pVar['spravne_test'] = $reset_value_pVar;
            $extraFields_pVar['nespravne_test'] = $reset_value_pVar;
        }

        $literatura_pVar = $this->getFieldValue_gFunc('test_questions_literatura');
        if(preg_match('/(\d{4})/', $literatura_pVar, $matches_pVar)) {
            $extraFields_pVar['aktualnost'] = $matches_pVar[1];
        }

        $extraFields_pVar['literatura_md5'] = md5($literatura_pVar);

        $questionId_pVar = $this->getVar_gFunc('item_id');
        $status_pVar = $this->getFieldValue_gFunc('test_questions_status');
        $autor_pVar = array('primary'=>array(), 'added'=>array(), 'updated'=>array());
        $info_pVar = db_items_gClass::getInfo_gFunc('test_questions');
        $autor_data_pVar = array();

        if($questionId_pVar) {
            $oldQuestion_pVar = items_gClass::getItem_gFunc('test_questions', $questionId_pVar);
            $oldQuestion_pVar['autorske_prava'] = str_replace('autor=', 'autor*=', $oldQuestion_pVar['autorske_prava']);
            $autor_data_pVar = explode(',', $oldQuestion_pVar['autorske_prava']);

            if($status_pVar == 'active' && $oldQuestion_pVar['status'] != 'active' && empty($oldQuestion_pVar['autorske_prava'])) {
                // zmena statusu na active, ak este nema autora
                // ziskam prava na uz existujuce jaz. mutacie
                foreach($info_pVar['languages'] as $language_pVar) {
                    if(!empty($oldQuestion_pVar[$language_pVar . '_otazka'])) {
                        $autor_pVar['primary'][] = $language_pVar;
                    }
                }
            }

            // a ziskam prava aj na novo zadane mutacie
            foreach($info_pVar['languages'] as $language_pVar) {
                $tmp1_pVar = $this->getFieldValue_gFunc('test_questions_' . $language_pVar . '_otazka');
                if(empty($oldQuestion_pVar[$language_pVar . '_otazka'])) {
                    if(!empty($tmp1_pVar)) {
                        $autor_pVar['added'][] = $language_pVar;
                    }
                }
                else {
                    if($tmp1_pVar != $oldQuestion_pVar[$language_pVar . '_otazka']) {
                        $autor_pVar['updated'][] = $language_pVar;
                    }
                }
            }
        }
        else {
            if($status_pVar == 'active') {
                // vlozenie novej otazky (nie predotazky)
                // ziskam prava na nove jaz. mutacie
                foreach($info_pVar['languages'] as $language_pVar) {
                    $tmp1_pVar = $this->getFieldValue_gFunc('test_questions_' . $language_pVar . '_otazka');
                    if(!empty($tmp1_pVar)) {
                        $autor_pVar['primary'][] = $language_pVar;
                    }
                }
            }
        }

        foreach($autor_pVar['primary'] as $tmp3_pVar) {
            // primarny autor
            $autor_data_pVar[] = '@user/' . $tmp3_pVar . '*=' . session_gClass::getUserDetail_gFunc('login');
        }
        foreach($autor_pVar['added'] as $tmp3_pVar) {
            // prekladatel
            $autor_data_pVar[] = '@user/' . $tmp3_pVar . '+=' . session_gClass::getUserDetail_gFunc('login');
        }
        foreach($autor_pVar['updated'] as $tmp3_pVar) {
            // editor
            $autor_data_pVar[] = '@user/' . $tmp3_pVar . '#=' . session_gClass::getUserDetail_gFunc('login');
        }

        $extraFields_pVar['autorske_prava'] = test_addquestion_gClass::userNameDuplicity_gFunc(implode(',', $autor_data_pVar));


        items_gClass::editItemByForm_gFunc($this->params['itemtype'], $this, $extraFields_pVar);
        log_gClass::write_gFunc('TEST_QUESTION_UPDATED', $this->getVar_gFunc('item_id'), false, true);

        literatura_gClass::insert_gFunc($literatura_pVar);
    }

    static public function userNameDuplicity_gFunc($value_pVar)
    {
        return($value_pVar);
    }

    static public function replaceUserName_gFunc($value_pVar, $details_pVar = false)
    {
        if(preg_match_all('/@user(\/(.{2}))?([\*\+\#])?=([a-zA-Z0-9_@.]+)/', $value_pVar, $matches_pVar)) {
            $data_pVar = array();
            foreach($matches_pVar[0] as $k_pVar=>$v_pVar) {
                $autor_pVar = false;
                if($matches_pVar[3][$k_pVar] != ''
                    && $matches_pVar[3][$k_pVar] != '*') {
                    if(!$details_pVar) {
                        continue;
                    }
                }
                else {
                    $autor_pVar = true;
                }

                if(!isset($data_pVar[$matches_pVar[4][$k_pVar]])) {
                    $data_pVar[$matches_pVar[4][$k_pVar]] = array();
                    $data_pVar[$matches_pVar[4][$k_pVar]]['languages'] = array();
                    $data_pVar[$matches_pVar[4][$k_pVar]]['autor'] =  false;
                }
                $data_pVar[$matches_pVar[4][$k_pVar]]['languages'][] = $matches_pVar[2][$k_pVar];
                $data_pVar[$matches_pVar[4][$k_pVar]]['autor'] = $data_pVar[$matches_pVar[4][$k_pVar]]['autor'] || $autor_pVar;
            }

            $value_pVar = array();
            foreach($data_pVar as $user_login_pVar=>$props_pVar) {
                if(is_numeric($user_login_pVar)) {
                    $userItem_pVar = items_gClass::getItems_gFunc('users', array('item_id'=>$user_login_pVar));
                }
                else {
                    $userItem_pVar = items_gClass::getItems_gFunc('users', array('login'=>$user_login_pVar));
                }
                unset($userItem_pVar['filter']);
                if(count($userItem_pVar)) {
                    $first = reset($userItem_pVar);
                }
                if(count($userItem_pVar) && isset($first['item_id'])) {
                    $userItem_pVar = reset($userItem_pVar);
                    $user_contact_pVar = '';
                    //$user_contact_pVar = $userItem_pVar['email'];
                    //if(!empty($user_contact_pVar)) {
                    //	$user_contact_pVar = ' (' . $user_contact_pVar . ')';
                    //}

                    if(isset($userItem_pVar['item_id'])) {
                        $user_contact_pVar = session_session_gClass::getUserDetailStatic_gFunc($userItem_pVar['item_id'], 'real_name') . $user_contact_pVar;
                    }

                    if(isset($userItem_pVar['item_id'])) {
                        $uid_pVar = uniqid('img_');
                        $useFoto_pVar = false;
                        if($props_pVar['autor']) {
                            if(isset($userItem_pVar['foto']) && is_array($userItem_pVar['foto']) && count($userItem_pVar['foto'])) {
                                $useFoto_pVar = $userItem_pVar['foto'][0];
                            }
                            elseif(isset($userItem_pVar['foto_rec']) && is_array($userItem_pVar['foto_rec']) && count($userItem_pVar['foto_rec'])) {
                                $useFoto_pVar = $userItem_pVar['foto_rec'][0];
                            }
                        }

                        $formatedText_pVar = '<a href="'.main_gClass::makeUrl_gFunc('/pouzivatelia/editovat-pouzivatela?item_id='.$userItem_pVar['item_id']).'" onmouseover="showFoto(\''.$uid_pVar.'\');" onmouseout="hideFoto(\''.$uid_pVar.'\');">';

                        if($props_pVar['autor']) { $formatedText_pVar .= '<strong style="position:relative;">'; }
                        $formatedText_pVar .= $user_contact_pVar;
                        if($useFoto_pVar !== false) {
                            $formatedText_pVar .= '<div id="'.$uid_pVar.'" style="display:none; z-index:100; position:absolute; left:0px; top:20px;"><img src="/?doc='.$useFoto_pVar['th1_']['src'].'" style="';
                            if(isset($useFoto_pVar['th1_']['_width'])) {
                                $formatedText_pVar .= 'width:'.$useFoto_pVar['th1_']['_width'].'px;';
                            }
                            if(isset($useFoto_pVar['th1_']['_height'])) {
                                $formatedText_pVar .= 'height:'.$useFoto_pVar['th1_']['_height'].'px;';
                            }
                            $formatedText_pVar .= 'border:1px solid black;"/></div>';
                        }
                        if($props_pVar['autor']) { $formatedText_pVar .= '</strong>'; }
                        if($details_pVar && count($props_pVar['languages']) &&
                            (count($props_pVar['languages']) != 1 || !empty($props_pVar['languages'][0]))) {
                            $formatedText_pVar .= ' ('.strtoupper(implode(',', array_unique($props_pVar['languages']))).')';
                        }
                        $formatedText_pVar .= '</a>';
                        $value_pVar[] = $formatedText_pVar;
                    }
                }
                else {
                    $value_pVar[] = $user_login_pVar;
                }
            }
            return(implode(', ', $value_pVar));
        }
        return($value_pVar);
    }

    private function replaceContact_gFunc($fieldName_pVar)
    {
        $field_pVar = $this->getField_gFunc($fieldName_pVar);
        $value_pVar = trim($field_pVar['value']);
        if($fieldName_pVar == 'autorske_prava') {
            $value_pVar = test_addquestion_gClass::replaceUserName_gFunc($value_pVar, true);
        }
        else {
            $value_pVar = self::replaceUserName_gFunc($value_pVar);
        }
        if(!$field_pVar['editable']) {
            $this->setFieldDefaultValue_gFunc($fieldName_pVar, $value_pVar);
        }
    }
}

class test_addquestion extends test_addquestion_gClass {}
