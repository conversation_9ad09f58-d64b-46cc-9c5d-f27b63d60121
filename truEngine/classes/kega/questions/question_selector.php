<?php

class question_selector_gClass
{
    static function selectQuestionsIdFromTest_gFunc($test_id_pVar, $settings_pVar)
    {
        $sql_pVar = 'SELECT `db_question_id`, `question_order`, `db_answer_ids` FROM `%ttests__questions` WHERE `test_id` = %d';
        if($settings_pVar['miesanie_otazok'] == 'yes') {
            $sql_pVar .= ' ORDER BY rand()';
        }
        else {
            $sql_pVar .= ' ORDER BY `question_order`';
        }
        $questions_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $test_id_pVar, 'db_question_id');

        $i_pVar = 0;
        if($settings_pVar['miesanie_odpovedi'] == 'yes') {
            foreach($questions_pVar as $k_pVar=>$v_pVar) {
                $questions_pVar[$k_pVar]['question_order'] = ++$i_pVar;
                $answers_pVar = explode(',', $v_pVar['db_answer_ids']);
                shuffle($answers_pVar);
                $answers_pVar = implode(',', $answers_pVar);
                $questions_pVar[$k_pVar]['db_answer_ids'] = $answers_pVar;
            }
        }
        else {
            foreach($questions_pVar as $k_pVar=>$v_pVar) {
                $questions_pVar[$k_pVar]['question_order'] = ++$i_pVar;
            }
        }
        return($questions_pVar);
    }

    static function selectQuestionsIdFromTemplate_gFunc($template_id_pVar, $settings_pVar)
    {
        $sql_pVar = 'SELECT `db_question_id`, `question_order`, `db_answer_ids` FROM `%ttemplate_questions` WHERE `template_id` = %d';
        if($settings_pVar['miesanie_otazok'] == 'yes') {
            $sql_pVar .= ' ORDER BY rand()';
        }
        else {
            $sql_pVar .= ' ORDER BY `question_order`';
        }
        $questions_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $template_id_pVar, 'db_question_id');

        $i_pVar = 0;
        if($settings_pVar['miesanie_odpovedi'] == 'yes') {
            foreach($questions_pVar as $k_pVar=>$v_pVar) {
                $questions_pVar[$k_pVar]['question_order'] = ++$i_pVar;
                $answers_pVar = explode(',', $v_pVar['db_answer_ids']);
                shuffle($answers_pVar);
                $answers_pVar = implode(',', $answers_pVar);
                $questions_pVar[$k_pVar]['db_answer_ids'] = $answers_pVar;
            }
        }
        else {
            foreach($questions_pVar as $k_pVar=>$v_pVar) {
                $questions_pVar[$k_pVar]['question_order'] = ++$i_pVar;
            }
        }
        return($questions_pVar);
    }


    static function selectQuestionsId_gFunc($settings_pVar)
    {
        if(isset($settings_pVar['categories']) && !empty($settings_pVar['categories'])) {
            $useSettings_pVar = $settings_pVar;
            unset($useSettings_pVar['categories']);
            $categories_pVar = explode("\n", $settings_pVar['categories']);

            $categoriesData_pVar = array();
            $categoriesCount_pVar = array();
            $tree_pVar = db_items_gClass::getTreeDef_gFunc('test_questions', 'zaradenie');
            $tree_pVar = explode(',', $tree_pVar['tree_def']);
            if(!isset($settings_pVar['sql_filter'])) {
                $settings_pVar['sql_filter'] = '';
                $settings_pVar['sql_filter_data'] = array();
            }
            $data_pVar = array();

            foreach($categories_pVar as $k=>$category_pVar) {
                if(empty($category_pVar)) {
                    unset($categories_pVar[$k]);
                }
            }
            $ncat_pVar = count($categories_pVar);
            foreach($categories_pVar as $category_pVar) {
                $ncat_pVar--;
                $useSettings_pVar['sql_filter'] = $settings_pVar['sql_filter'];
                $useSettings_pVar['sql_filter_data'] = $settings_pVar['sql_filter_data'];
                $category_pVar = trim($category_pVar);
                if(empty($category_pVar)) {
                    continue;
                }

                $tmp_pVar = explode(' ', $category_pVar);
                if($tmp_pVar[0] === 'questions') {
                    unset($tmp_pVar[0]);
                    $tmp_pVar = implode(' ', $tmp_pVar);
                    $tmp_pVar = explode(',', $category_pVar);
                    foreach($tmp_pVar as $k_pVar=>$v_pVar) {
                        $tmp_pVar[$k_pVar] = trim($v_pVar);
                    }
                    if(!empty($useSettings_pVar['sql_filter'])) {
                        $useSettings_pVar['sql_filter'] .= ' AND ';
                    }
                    $useSettings_pVar['sql_filter'] .= '`A`.`item_id` IN (%ad)';
                    $useSettings_pVar['sql_filter_data'][] = $tmp_pVar;

                    if(isset($settings_pVar['pocet_otazok']) && intval($settings_pVar['pocet_otazok']) > 0) {
                        $count_pVar = intval($settings_pVar['pocet_otazok']);
                    }
                    else {
                        $count_pVar = count($tmp_pVar);
                    }
                }
                else {
                    $n_pVar = floatval($tmp_pVar[0]);
                    unset($tmp_pVar[0]);
                    $category_pVar = implode(' ', $tmp_pVar);
                    $tmp_pVar = explode('/', $category_pVar);
                    foreach($tree_pVar as $xkey_pVar=>$key_pVar) {
                        if(empty($tmp_pVar[$xkey_pVar])) {
                            continue;
                        }
                        if(!empty($useSettings_pVar['sql_filter'])) {
                            $useSettings_pVar['sql_filter'] .= ' AND ';
                        }
                        $useSettings_pVar['sql_filter'] .= '`' . $key_pVar . '` = %s';
                        $useSettings_pVar['sql_filter_data'][] = $tmp_pVar[$xkey_pVar];
                    }

                    if(isset($settings_pVar['pocet_otazok']) && intval($settings_pVar['pocet_otazok']) > 0) {
                        $count_pVar = intval((intval($settings_pVar['pocet_otazok']) / 100.0) * $n_pVar);
                    }
                    else {
                        $count_pVar = intval($n_pVar);
                    }
                }

                if($settings_pVar['pocet_otazok'] !== -1) {
                    $useSettings_pVar['pocet_otazok'] = $count_pVar;
                }
                else {
                    $useSettings_pVar['pocet_otazok'] = -1;
                }

                if(!$ncat_pVar && $settings_pVar['pocet_otazok'] !== -1) {
                    // fix poctu otazok pri poslednej kategorii...
                    if((count($data_pVar) + $count_pVar) < $settings_pVar['pocet_otazok']) {
                        $count_pVar = $settings_pVar['pocet_otazok'] - count($data_pVar);
                        $useSettings_pVar['pocet_otazok'] = $count_pVar;
                    }
                }

                $tmpData_pVar = self::selectQuestionsId_gFunc($useSettings_pVar);

//dd($tmpData_pVar);
//                [2682] => Array
//                (
//                    [db_question_id] => 2682
//                    [question_order] => 1
//                    [db_answer_ids] => 20574,20575,20571,20576,20570
//                )
//
//            [159] => Array
//                (
//                    [db_question_id] => 159
//                    [question_order] => 2
//                    [db_answer_ids] => 1247,1245,1252,1248,1246
//                )


                if(0 && $settings_pVar['pocet_otazok'] !== -1) {
                    /// hmmm... teraz neriesim, ze co ak je v danej kategorii menej otazok - ze by som doplnil ine otazky..
                    /// ak je v niektorej kategorii malo otazok, tak vratim false
                    if(!is_array($tmpData_pVar) || !count($tmpData_pVar) || ($count_pVar && count($tmpData_pVar) != $count_pVar)) {
                        if(is_array($tmpData_pVar)) {
                            echo '<div class="teststat">(1)' . count($tmpData_pVar) . ' ' . $count_pVar . ' ';
                        }
                        else {
                            echo '<div class="teststat">(1)' . dump($tmpData_pVar) . ' ' . $count_pVar . ' ';
                        }
                        print_r($useSettings_pVar);
                        echo ' </div>';
                        return(false);
                    }
                }
                if(is_array($tmpData_pVar)) {
//                    echo '<div class="teststat">(category OK)' . count($tmpData_pVar) . ' ' . $count_pVar . ' ';
                }
                else {
//                    echo '<div class="teststat">(category OK)' . dump($tmpData_pVar) . ' ' . $count_pVar . ' ';
                }
//                print_r($useSettings_pVar);
                echo ' </div>';

                foreach($tmpData_pVar as $k_pVar=>$v_pVar) {
                    $data_pVar[$k_pVar] = $v_pVar;
                }

            }
            $count_pVar = intval($settings_pVar['pocet_otazok']);
            if($settings_pVar['pocet_otazok'] !== -1) {
                if(!count($data_pVar) || ($count_pVar && count($data_pVar) != $count_pVar)) {
                    echo '<div class="teststat">(2)'.count($data_pVar).' ' . $count_pVar. ' ';
                    print_r($settings_pVar);
                    echo ' </div>';
                    return(false);
                }
            }
            echo '<div class="teststat">(3)OK'.count($data_pVar).' ' . $count_pVar. ' ';
            print_r($settings_pVar);
            echo ' </div>';

            return($data_pVar);
        }

        unset($settings_pVar['categories']);

        $lng_pVar = isset($settings_pVar['language']) ? $settings_pVar['language']: 'sk';

        if(isset($settings_pVar[$lng_pVar . '_preferred_keywords'])
            && !empty($settings_pVar[$lng_pVar . '_preferred_keywords'])) {
            $keywords_pVar = true;
        }
        else {
            $keywords_pVar = false;
        }
        if(isset($settings_pVar['preferred_authors'])
            && !empty($settings_pVar['preferred_authors'])) {
            $authors_pVar = true;
        }
        else {
            $authors_pVar = false;
        }

        if($keywords_pVar || $authors_pVar) {
            if(!isset($settings_pVar['sql_filter'])) {
                $settings_pVar['sql_filter'] = '';
                $settings_pVar['sql_filter_data'] = array();
            }
            $data_pVar = array();
            if($keywords_pVar && $authors_pVar) {
                $useSettings_pVar = $settings_pVar;
                unset($useSettings_pVar[$lng_pVar . '_preferred_keywords']);
                unset($useSettings_pVar['preferred_authors']);
                $keywords_data_pVar = explode(',', $settings_pVar[$lng_pVar . '_preferred_keywords']);
                $tmp_sql_pVar = array();
                foreach($keywords_data_pVar as $k_pVar=>$v_pVar) {
                    $v_pVar = trim($v_pVar);
                    if(empty($v_pVar)) {
                        continue;
                    }
                    $tmp_sql_pVar[] =  '`' . $lng_pVar . '_keywords` LIKE %s';
                    $useSettings_pVar['sql_filter_data'][] = '%'.$v_pVar . '%';
                }
                if(count($tmp_sql_pVar)) {
                    if(!empty($useSettings_pVar['sql_filter'])) {
                        $useSettings_pVar['sql_filter'] .= ' AND ';
                    }
                    $useSettings_pVar['sql_filter'] .= '(' . implode(' OR ', $tmp_sql_pVar) . ')';
                }

                $authors_data_pVar = explode(',', $settings_pVar['preferred_authors']);
                foreach($authors_data_pVar as $k_pVar=>$v_pVar) {
                    if(preg_match('/.*\((\d+)\).*/i', trim($v_pVar), $matches_pVar)) {
                        $authors_data_pVar[$k_pVar] = $matches_pVar[1];
                    }
                    else {
                        $authors_data_pVar[$k_pVar] = db_session_gClass::getUserIdByLogin_gFunc($v_pVar);
                    }

                    if(empty($authors_data_pVar[$k_pVar])
                        || !intval($authors_data_pVar[$k_pVar])
                        || !is_numeric($authors_data_pVar[$k_pVar])) {
                        unset($authors_data_pVar[$k_pVar]);
                    }
                }
                if(count($authors_data_pVar)) {
                    if(!empty($useSettings_pVar['sql_filter'])) {
                        $useSettings_pVar['sql_filter'] .= ' AND ';
                    }
                    $useSettings_pVar['sql_filter'] .= '`A`.`owner_id` IN (%as)';
                    $useSettings_pVar['sql_filter_data'][] = $authors_data_pVar;
                }


                $tmpData_pVar = self::selectQuestionsId_gFunc($useSettings_pVar);
                if(is_array($tmpData_pVar)) {
                    foreach($tmpData_pVar as $k_pVar=>$v_pVar) {
                        $data_pVar[$k_pVar] = $v_pVar;
                    }
                }
            }

            $count_pVar = intval($settings_pVar['pocet_otazok']);
            if(!count($data_pVar) || ($count_pVar && count($data_pVar) != $count_pVar)) {
                if($keywords_pVar) {
                    $useSettings_pVar = $settings_pVar;
                    unset($useSettings_pVar[$lng_pVar . '_preferred_keywords']);
                    unset($useSettings_pVar['preferred_authors']);
                    $keywords_data_pVar = explode(',', $settings_pVar[$lng_pVar . '_preferred_keywords']);
                    $tmp_sql_pVar = array();
                    foreach($keywords_data_pVar as $k_pVar=>$v_pVar) {
                        $v_pVar = trim($v_pVar);
                        if(empty($v_pVar)) {
                            continue;
                        }
                        $tmp_sql_pVar[] = '`' . $lng_pVar . '_keywords` LIKE %s';
                        $useSettings_pVar['sql_filter_data'][] = '%'.trim($v_pVar) . '%';
                    }
                    if(count($tmp_sql_pVar)) {
                        if(!empty($useSettings_pVar['sql_filter'])) {
                            $useSettings_pVar['sql_filter'] .= ' AND ';
                        }
                        $useSettings_pVar['sql_filter'] .= '(' . implode(' OR ', $tmp_sql_pVar) . ')';
                    }
                    $tmpData_pVar = self::selectQuestionsId_gFunc($useSettings_pVar);
                    if(is_array($tmpData_pVar)) {
                        foreach($tmpData_pVar as $k_pVar=>$v_pVar) {
                            $data_pVar[$k_pVar] = $v_pVar;
                        }
                    }
                }
            }

            $count_pVar = intval($settings_pVar['pocet_otazok']);
            if(!count($data_pVar) || ($count_pVar && count($data_pVar) != $count_pVar)) {
                if($authors_pVar) {
                    $useSettings_pVar = $settings_pVar;
                    unset($useSettings_pVar[$lng_pVar . '_preferred_keywords']);
                    unset($useSettings_pVar['preferred_authors']);
                    $authors_data_pVar = explode(',', $settings_pVar['preferred_authors']);
                    foreach($authors_data_pVar as $k_pVar=>$v_pVar) {
                        if(preg_match('/.*\((\d+)\).*/i', trim($v_pVar), $matches_pVar)) {
                            $authors_data_pVar[$k_pVar] = $matches_pVar[1];
                        }
                        else {
                            $authors_data_pVar[$k_pVar] = db_session_gClass::getUserIdByLogin_gFunc($v_pVar);
                        }

                        if(empty($authors_data_pVar[$k_pVar])
                            || !intval($authors_data_pVar[$k_pVar])
                            || !is_numeric($authors_data_pVar[$k_pVar])) {
                            unset($authors_data_pVar[$k_pVar]);
                        }
                    }
                    if(count($authors_data_pVar)) {
                        if(!empty($useSettings_pVar['sql_filter'])) {
                            $useSettings_pVar['sql_filter'] .= ' AND ';
                        }
                        $useSettings_pVar['sql_filter'] .= '`A`.`owner_id` IN (%as)';
                        $useSettings_pVar['sql_filter_data'][] = $authors_data_pVar;
                    }


                    $tmpData_pVar = self::selectQuestionsId_gFunc($useSettings_pVar);
                    if(is_array($tmpData_pVar)) {
                        foreach($tmpData_pVar as $k_pVar=>$v_pVar) {
                            $data_pVar[$k_pVar] = $v_pVar;
                        }
                    }
                }
            }

            $count_pVar = intval($settings_pVar['pocet_otazok']);
            if(!count($data_pVar) || ($count_pVar && count($data_pVar) != $count_pVar)) {
                $useSettings_pVar = $settings_pVar;
                unset($useSettings_pVar[$lng_pVar . '_preferred_keywords']);
                unset($useSettings_pVar['preferred_authors']);
                $tmpData_pVar = self::selectQuestionsId_gFunc($useSettings_pVar);
                if(is_array($tmpData_pVar)) {
                    foreach($tmpData_pVar as $k_pVar=>$v_pVar) {
                        $data_pVar[$k_pVar] = $v_pVar;
                    }
                }
            }

            $count_pVar = intval($settings_pVar['pocet_otazok']);
            if(!count($data_pVar) || ($count_pVar && count($data_pVar) != $count_pVar)) {
                echo '<div class="teststat">(4)'.count($tmpData_pVar).' ' . $count_pVar. ' ';
                print_r($settings_pVar);
                echo ' </div>';
                return(false);
            }

            //echo '<pre>'; print_r($data_pVar); echo '</pre>';
            //echo implode(',', array_keys($data_pVar));

            echo '<div class="teststat">(5)OK '.count($tmpData_pVar).' ' . $count_pVar. ' ';
            print_r($settings_pVar);
            echo ' </div>';

            return($data_pVar);
        }

        if(isset($settings_pVar['index_obtiaznosti']) && floatval($settings_pVar['index_obtiaznosti']) > 0) {
            $index_obtiaznosti_pVar = floatval($settings_pVar['index_obtiaznosti']);
        }
        else {
            $index_obtiaznosti_pVar = 0;
        }

        if(isset($settings_pVar['moznosti_min']) && intval($settings_pVar['moznosti_min']) > 0) {
            $min_coun_answ_pVar = intval($settings_pVar['moznosti_min']);
        }
        else {
            $min_coun_answ_pVar = 0;
        }
        if(isset($settings_pVar['moznosti_max']) && intval($settings_pVar['moznosti_max']) > 0) {
            $max_coun_answ_pVar = intval($settings_pVar['moznosti_max']);
        }
        else {
            $max_coun_answ_pVar = 0;
        }
        if(isset($settings_pVar['pocet_otazok']) && intval($settings_pVar['pocet_otazok']) > 0) {
            $count_pVar = intval($settings_pVar['pocet_otazok']);
        }
        else {
            $count_pVar = 0;
        }

        $sql_params_pVar = array();

        $lng_pVar = isset($settings_pVar['language']) ? $settings_pVar['language']: 'sk';
        $lng_pVar = explode(',', $lng_pVar);
        $lngWhereQ_pVar = '';
        $lngWhereA_pVar = '';
        foreach($lng_pVar as $lngv_pVar) {
            $lngWhereQ_pVar .= ' AND `A`.`' . $lngv_pVar . '_otazka` IS NOT NULL AND `A`.`' . $lngv_pVar . '_otazka` <> \'\' ';
            $lngWhereA_pVar .= ' AND `B`.`' . $lngv_pVar . '_odpoved` IS NOT NULL AND `B`.`' . $lngv_pVar . '_odpoved` <> \'\' ';
        }

        $whereMedia_pVar = '';
        if(!isset($settings_pVar['media']) || $settings_pVar['media'] !== 'yes') {
            foreach($lng_pVar as $lngv_pVar) {
                $whereMedia_pVar .= ' AND (`A`.`' . $lngv_pVar . '_otazka_media` IS NULL OR (`A`.`' . $lngv_pVar . '_otazka_media` NOT LIKE \'%r\' AND `A`.`' . $lngv_pVar . '_otazka_media` NOT LIKE \'%r\'))';
                $whereMedia_pVar .= ' AND (`B`.`' . $lngv_pVar . '_odpoved_media` IS NULL OR (`B`.`' . $lngv_pVar . '_odpoved_media` NOT LIKE \'%r\' AND `B`.`' . $lngv_pVar . '_odpoved_media` NOT LIKE \'%r\'))';
                $sql_params_pVar[] = '%.swf' . NL . '%';
                $sql_params_pVar[] = '%.flv' . NL . '%';
                $sql_params_pVar[] = '%.swf' . NL . '%';
                $sql_params_pVar[] = '%.flv' . NL . '%';
            }
        }

        if($min_coun_answ_pVar > 0){
            $tmp_min_coun_answ_pVar = $min_coun_answ_pVar;
        }
        else {
            $tmp_min_coun_answ_pVar = 1;
        }

        $whereFilter_pVar = '';
        if(isset($settings_pVar['sql_filter']) && !empty($settings_pVar['sql_filter'])) {
            $whereFilter_pVar = ' AND ' . $settings_pVar['sql_filter'];
            foreach($settings_pVar['sql_filter_data'] as $v_pVar) {
                $sql_params_pVar[] = $v_pVar;
            }
        }

        if($index_obtiaznosti_pVar) {
            $whereFilter_pVar .= ' AND `A`.`obtiaznost` >= %f';
            $sql_params_pVar[] = $index_obtiaznosti_pVar;
        }

        $sql_pVar = 'SELECT `A`.`item_id` AS `item_id` FROM  `%titems_test_questions__data` as `A`
						LEFT JOIN `%titems_test_answers__data` as `B` ON `A`.`item_id` = `B`.`test_question`
						WHERE `A`.`status` = \'active\'
							 ' . $lngWhereQ_pVar . $lngWhereA_pVar . $whereMedia_pVar . $whereFilter_pVar . '
						GROUP BY `A`.`item_id` HAVING count(`A`.`item_id`) > %d
						ORDER BY rand()';
        $sql_params_pVar[] = $tmp_min_coun_answ_pVar;

        if($count_pVar) {
            $sql_pVar .= ' LIMIT 0, %d';
            $sql_params_pVar[] = $count_pVar;
        }
        //echo '<hr><pre>' . $sql_pVar;
        //print_r($sql_params_pVar);
        //echo  '</pre><hr />';

     //   dd($sql_pVar, $sql_params_pVar);

        $questions_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $sql_params_pVar);

        if(!count($questions_pVar) || ($count_pVar && count($questions_pVar) != $count_pVar)) {
            return(false);
        }

        foreach($questions_pVar as $k_pVar=>$v_pVar) {
            if(is_array($v_pVar)) {
                $questions_pVar[] = $v_pVar['item_id'];
                unset($questions_pVar[$k_pVar]);
            }
        }

        $sql_pVar = 'SELECT `item_id`, `test_question` FROM `%titems_test_answers__data` AS `B`
			WHERE `test_question` in (%ad)
			AND `status` = \'active\'
			' . $lngWhereA_pVar . '
			ORDER BY rand()';
        $answers_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($questions_pVar));

        $data_pVar = array();
        foreach($answers_pVar as $v_pVar) {
            if(!isset($data_pVar[$v_pVar['test_question']])) {
                $data_pVar[$v_pVar['test_question']] = array();
            }
            $data_pVar[$v_pVar['test_question']][] = $v_pVar['item_id'];
        }

        $order_pVar = 0;
        foreach($data_pVar as $k_pVar=>$v_pVar) {
            $order_pVar++;
            if($max_coun_answ_pVar && count($v_pVar) > $max_coun_answ_pVar) {
                $n_pVar = count($v_pVar) - $max_coun_answ_pVar;
                while($n_pVar) {
                    array_pop($v_pVar);
                    $n_pVar--;
                }
            }
            $data_pVar[$k_pVar] = array('db_question_id'=>$k_pVar, 'question_order'=>$order_pVar, 'db_answer_ids'=>implode(',', $v_pVar));
        }
        return($data_pVar);
    }

    static function selectQuestions_gFunc($refTable_pVar, $keyField_pVar, $keyValue_pVar, $random_questions_order_pVar = false, $random_answers_order_pVar = false, $language_pVar = false)
    {
        if($language_pVar === false) {
            $language_pVar = main_gClass::getLanguage_gFunc();
        }

        $test_pVar = array();
        $sql_pVar = 'SELECT * FROM `' . $refTable_pVar . '`  as `trq`
						LEFT JOIN `%titems_test_questions__data` as `tqd` ON `tqd`.`item_id`=`trq`.`db_question_id`
						WHERE `trq`.`' . $keyField_pVar . '` = %d ORDER BY ';
        if(!$random_questions_order_pVar) {
            $sql_pVar .= '`trq`.`question_order`';
        }
        else {
            $sql_pVar .= 'rand()';
        }

        $test_pVar['questions'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $keyValue_pVar, 'item_id');

        $answers_ids_pVar = array();
        $i_pVar = 0;
        foreach($test_pVar['questions'] as $k_pVar=>$v_pVar) {
            if($random_answers_order_pVar) {
                $tmp_pVar = explode(',', $v_pVar['db_answer_ids']);
                shuffle($tmp_pVar);
                $test_pVar['questions'][$k_pVar]['db_answer_ids'] = implode(',', $tmp_pVar);
            }
            $answers_ids_pVar[] = $v_pVar['db_answer_ids'];
            $i_pVar++;
            $test_pVar['questions'][$k_pVar]['index'] = $i_pVar;
        }

        $answers_ids_pVar = explode(',', implode(',', $answers_ids_pVar));

        $sql_pVar = 'SELECT * FROM `%titems_test_answers__data` WHERE `item_id` IN (%ad)';
        $test_pVar['answers'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($answers_ids_pVar), 'item_id');


        // nastavim media
        $question_ids_pVar = array();
        $answer_ids_pVar = array();
        foreach($test_pVar['questions'] as $k_pVar=>$v_pVar) {
            $question_ids_pVar[] = $v_pVar['item_id'];
            $answers_pVar = explode(',', $v_pVar['db_answer_ids']);
            $answer_ids_pVar = array_merge($answer_ids_pVar, $answers_pVar);
        }
        $files_pVar = main_gClass::getFiles_gFunc('items_' . 'test_questions' . '_'  . $language_pVar . '_' . 'otazka_media', $question_ids_pVar);
        foreach($files_pVar as $v_pVar) {
            if(isset($test_pVar['questions'][$v_pVar['ref_id']]) && !is_array($test_pVar['questions'][$v_pVar['ref_id']][$language_pVar . '_' . 'otazka_media'])) {
                $test_pVar['questions'][$v_pVar['ref_id']][$language_pVar . '_' . 'otazka_media'] = $v_pVar;
            }
        }
        $files_pVar = main_gClass::getFiles_gFunc('items_' . 'test_answers' . '_'  . $language_pVar . '_' . 'odpoved_media', $answer_ids_pVar);
        foreach($files_pVar as $v_pVar) {
            if(isset($test_pVar['answers'][$v_pVar['ref_id']]) && !is_array($test_pVar['answers'][$v_pVar['ref_id']][$language_pVar . '_' . 'odpoved_media'])) {
                $test_pVar['answers'][$v_pVar['ref_id']][$language_pVar . '_' . 'odpoved_media'] = $v_pVar;
            }
        }

        return($test_pVar);
    }
}

