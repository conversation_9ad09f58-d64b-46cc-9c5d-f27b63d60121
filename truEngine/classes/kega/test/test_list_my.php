<?php

class test_list_my_gClass extends source_gClass
{

    protected function getData()
    {
        $delete_id_pVar = 0;
        if(key_exists('delete_id', $this->params)) {
            // preco tato pomienka nefunguje?????? nevadi, nahradil som ju s key_exists
            // if(isset($this->params['delete_id'])) {
            $delete_id_pVar = intval($this->params['delete_id']);
            unset($this->params['delete_id']);
        }
        $delete_data_pVar = false;
        if(key_exists('delete_data', $this->params)) {
            if($this->params['delete_data'] === 'true') {
                $delete_data_pVar = true;
            }
            unset($this->params['delete_data']);
        }

        if($delete_id_pVar) {
            if($delete_data_pVar) {
                if(session_gClass::userHasRightsAccess_gFunc(s_test_delete_test_data)) {
                    $sql_pVar = 'UPDATE `%ttests` SET `status` = \'deleted\' WHERE `id` = %d';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($delete_id_pVar));
                }
            }
            else {
                if(session_gClass::userHasRightsAccess_gFunc(s_test_delete_test)
                    || session_gClass::userHasRightsAccess_gFunc(s_test_delete_test_data)) {
                    $sql_pVar = 'SELECT count(`id`) FROM `%ttests_running` WHERE `source_test_id` = %d';
                    if(!db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $delete_id_pVar)) {
                        $sql_pVar = 'UPDATE `%ttests` SET `status` = \'deleted\' WHERE `id` = %d';
                        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($delete_id_pVar));
                    }
                }
            }
        }

        $adapter_pVar = items_gClass::getAdapter_gFunc('test_my');
        $data_pVar = $adapter_pVar->getItems_gFunc($this->params);

        return($data_pVar);
    }

}

class test_list_my extends test_list_my_gClass {}
