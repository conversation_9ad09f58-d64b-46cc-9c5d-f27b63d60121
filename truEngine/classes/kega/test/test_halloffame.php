<?php


class test_halloffame_gClass extends table_gClass
{
    protected function initTable_gFunc()
    {
        if(isset($this->params['archive']) && $this->params['archive'] === 'true') {
            if(session_gClass::userHasRightsAccess_gFunc(s_test_hall_of_fame_archive)) {
                $type = 'test';
                if(isset($this->params['template_id']) && $this->params['template_id'] > 0) {
                    $test_id = $this->params['template_id'];
                    $type = 'template';
                }
                else {
                    $test_id = $this->params['test_id'];
                }
                $sql_pVar = 'UPDATE `%ttest_hall_of_fame` SET `status` = \'archived\', `archive_date` = now() WHERE `type` = %s AND test_id = %d AND `status` = \'active\'';
                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($type, $test_id));
            }
        }
        unset($this->params['archive']);
        $adapter_pVar = items_gClass::getAdapter_gFunc('test_halloffame');
        $params_pVar = $this->params;
        unset($params_pVar['columns']);
        unset($params_pVar['data_format']);
        if(isset($params_pVar['test_id']) && intval($params_pVar['test_id'])) {
            unset($params_pVar['template_id']);
            $params_pVar['type'] = 'test';
        }
        elseif(isset($params_pVar['template_id']) && intval($params_pVar['template_id'])) {
            $params_pVar['test_id'] = $params_pVar['template_id'];
            $params_pVar['type'] = 'template';
            unset($params_pVar['template_id']);
        }
        else {
            // ??
        }
        $data_pVar = $adapter_pVar->getItems_gFunc($params_pVar);
        unset($data_pVar['filter']);

        $this->setData_gFunc($data_pVar);
        if(isset($this->params['columns'])) {
            $this->setColumnsFromString_gFunc($this->params['columns']);
        }
    }

}

class test_halloffame extends test_halloffame_gClass {}

