<?php


class test_generate_print extends form_gClass
{
    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        $this->addHiddenField_gFunc('test_id', $this->params['test_id']);
        $this->addHiddenField_gFunc('template_id', $this->params['template_id']);

        $this->addField_gFunc('', 'count', 'int', 'Počet testov', true, '/[0-9]+/');
        $this->setFieldDefaultValue_gFunc('count', 1);

        if($this->getFieldValue_gFunc('template_id')) {
            $this->addField_gFunc('', 'versions', 'int', 'Počet sérií', true, '/[0-9]+/');
            $this->setDefaultValue_pVar('', 'versions', 1);
        }

        if(isset($this->params['submit_button_title_add'])) {
            $this->setVar_gFunc('submit_button_title', $this->params['submit_button_title_add'], false);
        }
    }

    protected function getData()
    {
        $data_pVar = $this->getFormData_gFunc();
        if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($data_pVar);
        }

        ob_start();

        $test_id_pVar = $this->getFieldValue_gFunc('test_id');
        $count_pVar = $this->getFieldValue_gFunc('count');

        if($test_id_pVar) {
            $sql_pVar = 'SELECT * FROM `%ttests` WHERE `id` = %d AND `status` <> \'deleted\'';
            $test_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $test_id_pVar);

            $data_pVar['test_data'] = array();
            $test_status_pVar = array('display'=>true, 'edit'=>false, 'update'=>false, 'print'=>true);
            if(is_array($test_pVar)) {
                $settings_pVar = kega_gClass::explodeSettings_gFunc($test_pVar['settings']);

                $miesanie_otazok_pVar = false;
                $miesanie_odpovedi_pVar = false;
                if($settings_pVar['miesanie_otazok'] == 'yes') {
                    $miesanie_otazok_pVar = true;
                }
                if($settings_pVar['miesanie_odpovedi'] == 'yes') {
                    $miesanie_odpovedi_pVar = true;
                }

                $versions_pVar = 1;

                for($n_pVar = 0; $n_pVar < $versions_pVar; $n_pVar++) {
                    $data_pVar['test_data'][$n_pVar] = array();
                    if($miesanie_otazok_pVar || $miesanie_odpovedi_pVar) {
                        for($i_pVar = 0; $i_pVar < $count_pVar; $i_pVar++) {
                            $questions_pVar = question_selector_gClass::selectQuestions_gFunc('%ttests__questions', 'test_id', $test_id_pVar, $miesanie_otazok_pVar, $miesanie_odpovedi_pVar, isset($settings_pVar['language'])?$settings_pVar['language']:false);
                            $questions_pVar['data'] = $test_pVar;
                            $questions_pVar['data']['settings'] = $settings_pVar;

                            $data_pVar['test_data'][$n_pVar][$i_pVar] = $questions_pVar;
                            $data_pVar['test_data'][$n_pVar][$i_pVar]['test_status'] = $test_status_pVar;
                            $data_pVar['test_data'][$n_pVar][$i_pVar]['current_question'] = 0;
                        }
                    }
                    else {
                        // toto je to ste ako vetva if, akturat optimalnejsie
                        $questions_pVar = question_selector_gClass::selectQuestions_gFunc('%ttests__questions', 'test_id', $test_id_pVar, $miesanie_otazok_pVar, $miesanie_odpovedi_pVar, isset($settings_pVar['language'])?$settings_pVar['language']:false);
                        $questions_pVar['data'] = $test_pVar;
                        $questions_pVar['data']['settings'] = $settings_pVar;
                        for($i_pVar = 0; $i_pVar < $count_pVar; $i_pVar++) {
                            $data_pVar['test_data'][$n_pVar][$i_pVar] = $questions_pVar;
                            $data_pVar['test_data'][$n_pVar][$i_pVar]['test_status'] = $test_status_pVar;
                            $data_pVar['test_data'][$n_pVar][$i_pVar]['current_question'] = 0;
                        }
                    }
                }
            }
        }
        else {
            $template_id_pVar = $this->getFieldValue_gFunc('template_id');
            if($template_id_pVar) {
                $versions_pVar = $this->getFieldValue_gFunc('versions');

                $template_pVar = db_items_gClass::getItem_gFunc('test_templates', $template_id_pVar);
                $template_pVar['official'] = 'no';

                $data_pVar['test_data'] = array();
                $test_status_pVar = array('display'=>true, 'edit'=>false, 'update'=>false, 'print'=>true);
                if(is_array($template_pVar)) {
                    $miesanie_otazok_pVar = false;
                    $miesanie_odpovedi_pVar = false;
                    if($template_pVar['miesanie_otazok'] == 'yes') {
                        $miesanie_otazok_pVar = true;
                    }
                    if($template_pVar['miesanie_odpovedi'] == 'yes') {
                        $miesanie_odpovedi_pVar = true;
                    }

                    for($n_pVar = 0; $n_pVar < $versions_pVar; $n_pVar++) {
                        $data_pVar['test_data'][$n_pVar] = array();

                        $tmp_pVar = new test_prepare_gClass();
                        $tmp_pVar->setParam_gFunc('template_id', $template_id_pVar);
                        $result_pVar = $tmp_pVar->getData();

                        if($miesanie_otazok_pVar || $miesanie_odpovedi_pVar) {
                            for($i_pVar = 0; $i_pVar < $count_pVar; $i_pVar++) {
                                $questions_pVar = question_selector_gClass::selectQuestions_gFunc('%ttests_running__questions', 'running_test_id', $result_pVar['running_test_id'], $miesanie_otazok_pVar, $miesanie_odpovedi_pVar, isset($settings_pVar['language'])?$settings_pVar['language']:false);
                                $questions_pVar['data'] = $template_pVar;
                                $questions_pVar['data']['settings'] = $template_pVar;

                                $data_pVar['test_data'][$n_pVar][$i_pVar] = $questions_pVar;
                                $data_pVar['test_data'][$n_pVar][$i_pVar]['test_status'] = $test_status_pVar;
                                $data_pVar['test_data'][$n_pVar][$i_pVar]['current_question'] = 0;
                            }
                        }
                        else {
                            // toto je to ste ako vetva if, akturat optimalnejsie
                            $questions_pVar = question_selector_gClass::selectQuestions_gFunc('%ttests_running__questions', 'running_test_id', $result_pVar['running_test_id'], $miesanie_otazok_pVar, $miesanie_odpovedi_pVar, isset($settings_pVar['language'])?$settings_pVar['language']:false);
                            $questions_pVar['data'] = $template_pVar;
                            $questions_pVar['data']['settings'] = $template_pVar;
                            for($i_pVar = 0; $i_pVar < $count_pVar; $i_pVar++) {
                                $data_pVar['test_data'][$n_pVar][$i_pVar] = $questions_pVar;
                                $data_pVar['test_data'][$n_pVar][$i_pVar]['test_status'] = $test_status_pVar;
                                $data_pVar['test_data'][$n_pVar][$i_pVar]['current_question'] = 0;
                            }
                        }

                        if($result_pVar['running_test_id']) {
                            $sql_pVar = 'DELETE FROM `%ttests_running__questions` WHERE `running_test_id` = %d';
                            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['running_test_id']));
                            $sql_pVar = 'DELETE FROM `%ttests_running` WHERE `id` = %d';
                            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($result_pVar['running_test_id']));
                        }
                    }
                }
            }
        }

        ob_end_clean();
        return($data_pVar);
    }
}

