<?php

class kega_gClass
{

    static public function authUser_gFunc($sessionObject_pVar, &$loginPrompt_pVar)
    {
        if(isset($loginPrompt_pVar['login_kega_pVar'])) {
            if(empty($loginPrompt_pVar['login_kega_pVar']) || empty($loginPrompt_pVar['password_pVar'])) {
                main_gClass::$mainVars_pVar['_login_msg'] = 'no_loginname_or_password';
                return(false);
            }

            // overim heslo.
            $data_pVar = db_session_gClass::getUserDataByLogin_gFunc($loginPrompt_pVar['login_kega_pVar']);
            if(!is_array($data_pVar) ||
                !db_session_gClass::comparePasswords($loginPrompt_pVar['login_kega_pVar'], $loginPrompt_pVar['password_pVar'], $data_pVar['password'])
            ) {
                // nespravny login alebo heslo
                main_gClass::$mainVars_pVar['_login_msg'] = 'bad_login_or_password';
                log_gClass::write_gFunc('LOGIN_BAD_PASSWORD', $loginPrompt_pVar['login_kega_pVar']);
                main_gClass::sleep_delayed(10);
                return(false);
            }

            // prihlasenie musi vzdy trvat nejaky cas (security)
            main_gClass::sleep_delayed(1);

            // teraz mam v $data_pVar profil usera, ktoreho mam prihlasit (aj item_id)
        }
        else {
            if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
                $loginPrompt_pVar['login_isic_pVar'] = cdouk_gClass::formatIsicSnr_gFunc($loginPrompt_pVar['login_isic_pVar']);
            }

            if(!isset($loginPrompt_pVar['login_isic_pVar'])
                || !isset($loginPrompt_pVar['login_first_name_pVar'])
                || !isset($loginPrompt_pVar['login_last_name_pVar'])
                || (empty($loginPrompt_pVar['login_isic_pVar']) && !strlen($loginPrompt_pVar['login_isic_pVar']))
                || empty($loginPrompt_pVar['login_first_name_pVar'])
                || empty($loginPrompt_pVar['login_last_name_pVar'])) {
                main_gClass::$mainVars_pVar['_login_msg'] = 'no_isicsrn_or_name';
                return(false);
            }


            if(strlen($loginPrompt_pVar['login_isic_pVar']) != 10 || !is_numeric($loginPrompt_pVar['login_isic_pVar'])) {
                main_gClass::$mainVars_pVar['_login_msg'] = 'no_isicsrn_or_name';
                return(false);
            }

            $filter_pVar = array('isic'=>$loginPrompt_pVar['login_isic_pVar'], 'last_name'=>'LIKE('.strtolower($loginPrompt_pVar['login_last_name_pVar'][0]).'%)', 'first_name'=>'LIKE('.strtolower($loginPrompt_pVar['login_first_name_pVar'][0]).'%)');
            if($loginPrompt_pVar['login_isic_pVar'] === '0000000000') {
                $filter_pVar = array('isic'=>$loginPrompt_pVar['login_isic_pVar'], 'last_name'=>$loginPrompt_pVar['login_last_name_pVar'], 'first_name'=>$loginPrompt_pVar['login_first_name_pVar']);
            }

            // selectnem si userov s takymto isicom a inicialami. Ak ich najdem viac, vyberiem toho najvhodnejsieho..
            // teda usera s najnizsou rolou (ale minimalne tester). Ak bude viac, pouzijem prveho.
            session_gClass::giveMeFullAccess_gFunc();
            $users_pVar = items_gClass::getItems_gFunc('users', $filter_pVar);
            session_gClass::revokeMeFullAccess_gFunc();
            unset($users_pVar['filter']);
            foreach($users_pVar as $k_pVar=>$v_pVar) {
                if(substr($k_pVar, 0, 1) == '_') {
                    unset($users_pVar[$k_pVar]);
                }
            }
            if(!count($users_pVar)) {
                session_gClass::giveMeFullAccess_gFunc();
                // vytvorim noveho usera
                $user_pVar = array('status'=>'active',
                    'login_enabled'=>'disabled',
                    'user_role'=>'tester',
                    'isic'=>$loginPrompt_pVar['login_isic_pVar'],
                    'last_name'=>$loginPrompt_pVar['login_last_name_pVar'],
                    'first_name'=>$loginPrompt_pVar['login_first_name_pVar'],
                    'nick'=>$loginPrompt_pVar['login_first_name_pVar'] . ' ' . $loginPrompt_pVar['login_last_name_pVar']);
                items_gClass::saveOrUpdateItem_gFunc('users', $user_pVar);

                // a znovu nacitam.
                $users_pVar = items_gClass::getItems_gFunc('users', $filter_pVar);
                unset($users_pVar['filter']);
                foreach($users_pVar as $k_pVar=>$v_pVar) {
                    if(substr($k_pVar, 0, 1) == '_') {
                        unset($users_pVar[$k_pVar]);
                    }
                }

                session_gClass::revokeMeFullAccess_gFunc();
            }
            $roles_order_pVar = array('tester', 'student', 'student_navrhovatel', 'pedagog', 'admin', 'superuser');
            $tmp_user_pVar = false;
            $tmp_user_role_index_pVar = false;
            if(count($users_pVar) > 1) {
                foreach($users_pVar as $k_pVar=>$v_pVar) {
                    if($tmp_user_pVar === false) {
                        $tmp_user_pVar = $v_pVar;
                        $tmp_user_role_index_pVar = array_search($v_pVar['user_role'], $roles_order_pVar);
                        continue;
                    }
                    $new_index_pVar = array_search($v_pVar['user_role'], $roles_order_pVar);
                    if($new_index_pVar !== false
                        && ($tmp_user_role_index_pVar === false || $new_index_pVar < $tmp_user_role_index_pVar)) {
                        $tmp_user_pVar = $v_pVar;
                        $tmp_user_role_index_pVar = $new_index_pVar;
                    }
                }
            }
            if($tmp_user_pVar !== false) {
                $users_pVar = array($v_pVar);
            }
            else {
                $users_pVar = array(reset($users_pVar));
            }

            if(!count($users_pVar)) {
                error_gClass::fatal_gFunc(__FILE__, __LINE__, count($users_pVar));
            }

            // overim isic
            if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
                $cdo_pVar = new cdouk_gClass();
                //23.3.2014 Kanitra -- zakomentovane kvoli nefungujucemu serveru
                //$data_pVar = $cdo_pVar->getData_gFunc($users_pVar[0]['isic'], false, false, true);
            }

            // prihlasenie musi vzdy trvat nejaky cas (security)
            main_gClass::sleep_delayed(3);

            $data_pVar = reset($users_pVar);
            // teraz mam v $data_pVar profil usera, ktoreho mam prihlasit (aj item_id)
        }

        // teraz mam v $data_pVar profil usera, ktoreho mam prihlasit (aj item_id)

        ////////////////////////////////////////
        //// prihlasim pouzivatela, vsetko je ok

        // reset session
        main_gClass::regenerateSessionId_gFunc();
        $sessionId_pVar = $sessionObject_pVar->generateSessionId_gFunc();

        $sessionObject_pVar->setUserId_gFunc($data_pVar['item_id']);
        $sessionObject_pVar->setRight_gFunc(s_system_logged_on);

        log_gClass::write_gFunc('SESSION_USER_ID', $data_pVar['item_id']);
        log_gClass::write_gFunc('SESSION_TESTER_ID', $data_pVar['item_id']);

        // insertnem do onlineusers
        list($rights_pVar, $groups_pVar, $from_pVar) = session_session_gClass::getUserRightsFromDb_gFunc('tester');

        $onlineData_pVar = db_session_gClass::createSessionOnlineusersRights_gFunc($data_pVar['item_id'], $sessionId_pVar, 'test', '');
        $cachedRights_pVar = db_session_gClass::cacheUserRights_gFunc($onlineData_pVar['online_user_id'], $rights_pVar, array());
        $onlineData_pVar['rights'] = $cachedRights_pVar;
        $sessionObject_pVar->setData_gFunc($onlineData_pVar);

        $sessionObject_pVar->restoreCachedRights_gFunc($cachedRights_pVar);

        main_gClass::$doc_pVar = main_gClass::getLanguage_gFunc() . '/testy/oficialne-testy';
        main_gClass::$mainVars_pVar['_login_msg'] = 'logged_in';
        main_gClass::$mainVars_pVar['_login_form_disabled'] = true;
        return(true);
    }

    /**
     * Prepocita pouzivatelove statistiky.
     * Ak $user_id = 0, tak prepocita vsetkyh pouzivatelov.
     *
     * @param unknown_type $user_id
     * @return unknown
     */
    static public function recount_user_stats_gFunc($user_id = 0)
    {

    }

    static public function explodeSettings_gFunc($settings_str_pVar)
    {
        $data_pVar = array();
        $tmp_settings_pVar = explode(NL, $settings_str_pVar);
        foreach($tmp_settings_pVar as $vv_pVar) {
            $tmp_pVar = explode('=', $vv_pVar);
            if(count($tmp_pVar) != 2) {
                continue;
            }
            $data_pVar[trim($tmp_pVar[0])] = str_replace('\\n', NL, $tmp_pVar[1]);
        }

        $data_pVar['_tmp_fields'] = array();

        foreach($data_pVar as $kk_pVar=>$vv_pVar) {
            if(isset($kk_pVar[2]) && $kk_pVar[2] == '_' && substr($kk_pVar, 0, 2) == main_gClass::getLanguage_gFunc()) {
                $data_pVar[substr($kk_pVar, 3)] = $vv_pVar;
                $data_pVar['_tmp_fields'][] = substr($kk_pVar, 3);
            }
        }

        return($data_pVar);
    }

    static public function implodeSettings_gFunc($settings_arr_pVar)
    {
        $str_pVar = '';
        foreach($settings_arr_pVar as $k_pVar=>$v_pVar) {
            if($k_pVar == '_tmp_fields') {
                continue;
            }
            if(isset($settings_arr_pVar['_tmp_fields']) && in_array($k_pVar, $settings_arr_pVar['_tmp_fields'])) {
                continue;
            }
            if(empty($v_pVar)) {
                continue;
            }
            if(!empty($str_pVar)) {
                $str_pVar .= NL;
            }
            $str_pVar .= $k_pVar . '=' . str_replace(NL, '\\n', $v_pVar);
        }
        return($str_pVar);
    }

    static function getUsersQuestionsCount_gFunc($filter_pVar = array())
    {

        if(isset($filter_pVar['pager'])) {
            $pager_pVar = explode(',', $filter_pVar['pager']);
            if(intval($pager_pVar[1])) {
                $pager_pVar[1] = intval($pager_pVar[1]) - 1;
            }
            unset($filter_pVar['pager']);
        }
        else {
            $pager_pVar = false;
        }

        if(is_array($pager_pVar) && count($pager_pVar) == 2 && intval($pager_pVar[0]) > 0) {
            $page_pVar = intval($pager_pVar[1]);
            $pageLen_pVar = intval($pager_pVar[0]);
            $offset_pVar = $pageLen_pVar * $page_pVar;
            $limit_str_pVar = ' LIMIT ' . $offset_pVar . ', ' . $pageLen_pVar;
        }
        else {
            $limit_str_pVar = '';
            $pager_pVar = false;
        }

        $_order_by_fields = array(
            'last_name'=>'Meno',
            'n'=>'Počet otázok',
            'n1'=>'Počet odpovedí'
        );

        $sql_filter_pVar = '';
        if(isset($filter_pVar['filter'])) {
            $filter_params_pVar = array();
            parse_str($filter_pVar['filter'], $filter_params_pVar);
            if(count($filter_params_pVar)) {
                foreach($filter_params_pVar as $k_pVar=>$v_pVar) {
                    $sql_filter_pVar .= ' AND ';
                    $sql_filter_pVar .= '`' . addslashes($k_pVar) . '` = \'' . addslashes($v_pVar) . '\'';
                }
            }
        }


        if(isset($filter_pVar['_order_by'])) {
            $order_by_pVar = explode(',', $filter_pVar['_order_by']);
            foreach ($order_by_pVar as $k_pVar=>$v_pVar) {
                if(strpos($v_pVar, '.') === false) {
                    if(empty($v_pVar)) {
                        unset($order_by_pVar[$k_pVar]);
                        continue;
                    }
                    $order_rule_pVar = explode('/', trim($v_pVar));
                    if(empty($order_rule_pVar[0])) {
                        unset($order_by_pVar[$k_pVar]);
                        continue;
                    }
                    if(!isset($order_rule_pVar[1])) {
                        $order_rule_pVar[1] = 'ASC';
                    }
                    if($order_rule_pVar[1] != 'ASC' && $order_rule_pVar[1] != 'DESC') {
                        $order_rule_pVar[1] = 'ASC';
                    }
                    $order_by_pVar[$k_pVar] = '`' . $order_rule_pVar[0] . '` ' . $order_rule_pVar[1];
                }
            }
            $order_by_data_pVar = implode(',', $order_by_pVar);
            if(!empty($order_by_data_pVar)) {
                $order_by_pVar = ' ORDER BY ' . $order_by_data_pVar;
            }
            else {
                $order_by_pVar = ' ORDER BY `last_name`';
            }
            unset($filter_pVar['_order_by']);
        }
        else {
            $order_by_pVar = '';
        }



        $sql_pVar = 'select u.item_id as `user_id`, CONCAT_WS(\' \', u.last_name, u.first_name) as `name`,
    					count(distinct o.item_id) as `n`,
    					count(a.item_id) as `n1`
    					FROM `%titems_test_questions__data` as `o`
    					LEFT JOIN `%titems_test_answers__data` as `a` ON `a`.`test_question` = `o`.`item_id`
						LEFT JOIN `%titems_users__data` as `u` ON `u`.`item_id`=`o`.`owner_id`
						WHERE `o`.`status` <> \'deleted\'
						' . $sql_filter_pVar . '
						group by `o`.`owner_id`
						' . $order_by_pVar
            . $limit_str_pVar;

        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, 'user_id');

        if($pager_pVar !== false) {
            $sql_pVar = 'select count(DISTINCT u.item_id) as n
    					FROM `%titems_test_questions__data` as `o`
						LEFT JOIN `%titems_users__data` as `u` ON `u`.`item_id`=`o`.`owner_id`
						WHERE `o`.`status` <> \'deleted\'
						' . $sql_filter_pVar;
            $pager_pVar[2] = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__);

            $data_pVar['_pager'] = array();
            $data_pVar['_pager']['pageLen'] = $pager_pVar[0];
            $data_pVar['_pager']['currentPage'] = (int)$pager_pVar[1] + 1;
            $data_pVar['_pager']['totalItems'] = $pager_pVar[2];
            $data_pVar['_pager']['totalPages'] = ceil($data_pVar['_pager']['totalItems'] / $data_pVar['_pager']['pageLen']);
        }

        $data_pVar['_order_by_fields'] = $_order_by_fields;

        return($data_pVar);
    }

//    // @TODO: Tuto funkciu prepoctu neskor buem pouzivat len na servisne ucely. Budem prepocitavat inline.
//    static public function recountQuestionStats_gFunc($question_id_pVar)
//    {
//        if(!main_gClass::getCacheTagStatus_gFunc('test_questions_stats', $question_id_pVar)
//            || !main_gClass::getCacheTagStatus_gFunc('test_answers_stats', $question_id_pVar)) {
//
//            $sql_pVar = 'SELECT
//								`t`.`id`, `q`.`db_question_id`, `t`.`official`, `q`.`db_answer_ids`, `q`.`answer_results`
//							FROM `%ttests_running__questions` as `q`
//							LEFT JOIN `%ttests_running` as `t` ON `q`.`running_test_id` = `t`.`id`
//							WHERE `t`.`in_stats` = \'yes\' AND `db_question_id` = %d AND `answer_results` IS NOT NULL';
//            $questions_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $question_id_pVar);
//
//            $n_answers_ok_pVar = 0;
//            $n_answers_fail_pVar = 0;
//            $n_answers_ok_test_pVar = 0;
//            $n_answers_fail_test_pVar = 0;
//            $db_answer_stats_pVar = array();
//            foreach($questions_pVar as $k_pVar=>$question_pVar) {
//                $db_answer_ids_pVar = explode(',', $question_pVar['db_answer_ids']);
//                if(empty($question_pVar['answer_results'])) {
//                    continue;
//                }
//                $tmp_pVar = explode('/', $question_pVar['answer_results']);
//                $tmp_pVar[0] = str_replace(',', '', $tmp_pVar[0]);
//                $tmp_pVar[1] = str_replace(',', '', $tmp_pVar[1]);
//                $len_pVar = strlen($tmp_pVar[0]);
//                for($i_pVar = 0; $i_pVar < $len_pVar; $i_pVar++) {
//                    if(!isset($db_answer_stats_pVar[$db_answer_ids_pVar[$i_pVar]])) {
//                        $db_answer_stats_pVar[$db_answer_ids_pVar[$i_pVar]] = array('spravne'=>0, 'nespravne'=>0, 'spravne_test'=>0, 'nespravne_test'=>0);
//                    }
//                    if($tmp_pVar[1][$i_pVar] == 'S') {
//                        if($question_pVar['official'] === 'yes') {
//                            $n_answers_ok_test_pVar++;
//                            $db_answer_stats_pVar[$db_answer_ids_pVar[$i_pVar]]['spravne_test']++;
//
//                        }
//                        else {
//                            $n_answers_ok_pVar++;
//                            $db_answer_stats_pVar[$db_answer_ids_pVar[$i_pVar]]['spravne']++;
//                        }
//                    }
//                    else {
//                        if($question_pVar['official'] === 'yes') {
//                            $n_answers_fail_test_pVar++;
//                            $db_answer_stats_pVar[$db_answer_ids_pVar[$i_pVar]]['nespravne_test']++;
//                        }
//                        else {
//                            $n_answers_fail_pVar++;
//                            $db_answer_stats_pVar[$db_answer_ids_pVar[$i_pVar]]['nespravne_test']++;
//                        }
//                    }
//                }
//            }
//
//            // vypocet obtiaznosti
//            if($n_answers_ok_pVar + $n_answers_fail_pVar) {
//                $indexObtiaznosti_pVar = round($n_answers_fail_pVar/($n_answers_ok_pVar+$n_answers_fail_pVar), 2);
//            }
//            else {
//                $indexObtiaznosti_pVar = 0;
//            }
//            $indexObtiaznosti_pVar *= 100;
//
//            // vynylyjem statistiky odpovedi
//            $sql_pVar = 'UPDATE `%titems_test_answers__data` SET
//							`spravne` = 0, `nespravne` = 0, `spravne_test` = 0, `nespravne_test` = 0
//							WHERE `test_question` = %d';
//            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($question_id_pVar));
//
//            foreach($db_answer_stats_pVar as $answer_id_pVar=>$answer_pVar) {
//                $sql_pVar = 'UPDATE `%titems_test_answers__data` SET
//								`spravne` = %d, `nespravne` = %d, `spravne_test` = %d, `nespravne_test` = %d
//								WHERE `item_id` = %d';
//                db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($answer_pVar['spravne'], $answer_pVar['nespravne'], $answer_pVar['spravne_test'], $answer_pVar['nespravne_test'], $answer_id_pVar));
//            }
//
//            $sql_pVar = 'UPDATE `%titems_test_questions__data` SET
//						`spravne` = %d, `nespravne` = %d, `spravne_test` = %d, `nespravne_test` = %d, `obtiaznost` = %f
//						WHERE `item_id` = %d';
//            db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($n_answers_ok_pVar, $n_answers_fail_pVar, $n_answers_ok_test_pVar, $n_answers_fail_test_pVar, $indexObtiaznosti_pVar, $question_id_pVar));
//
//            main_gClass::updateCacheTagStatus_gFunc('test_questions_stats', $question_id_pVar);
//            main_gClass::updateCacheTagStatus_gFunc('test_answers_stats', $question_id_pVar);
//        }
//    }
}
