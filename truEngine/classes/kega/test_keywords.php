<?php

class test_keywords_gClass extends source_gClass
{
    protected function getData()
    {
        if(!session_gClass::userHasRightsAccess_gFunc(s_test_keywords_organize)) {
            return(array());
        }

        $vars_pVar = main_gClass::getInputVarNames_gFunc();

        if(in_array('delete_keywords', $vars_pVar) || in_array('rename_keywords', $vars_pVar)) {
            $sign_pVar = main_gClass::getInputString_gFunc('sign', main_gClass::SRC_POST_pVar);
            if(!empty($sign_pVar)) {
                $keywords_data_pVar = main_gClass::getSessionData_gFunc('edit_keywords');
                if(is_array($keywords_data_pVar) && $sign_pVar == $keywords_data_pVar['sign']) {
                    $keywords_pVar = array();
                    foreach($vars_pVar as $var_pVar) {
                        if(substr($var_pVar, 0, 8) == 'keyword_') {
                            $lng_pVar = substr($var_pVar, 8, 2);
                            if(!isset($keywords_pVar[$lng_pVar])) {
                                $keywords_pVar[$lng_pVar] = array();
                            }
                            $keywords_pVar[$lng_pVar][] = $keywords_data_pVar[$lng_pVar][substr($var_pVar, 11)];
                        }
                    }

                    foreach($keywords_pVar as $lng_pVar=>$kwords_pVar) {
                        $lng_pVar = substr($lng_pVar, 0, 2);
                        if(strlen($lng_pVar) !== 2 || !ctype_alpha($lng_pVar[0]) || !ctype_alpha($lng_pVar[1])) {
                            continue;
                        }
                        foreach($kwords_pVar as $keyword_pVar) {
                            $old_value_pVar = $keyword_pVar;
                            if(in_array('delete_keywords', $vars_pVar)) {
                                $new_value_pVar = '';
                            }
                            if(in_array('rename_keywords', $vars_pVar)) {
                                $new_value_pVar = main_gClass::getInputString_gFunc('new_keyword_name', main_gClass::SRC_POST_pVar);
                                if(empty($new_value_pVar)) {
                                    continue;
                                }
                            }

                            // update databazy
                            $sql_pVar = 'SELECT `item_id`, `' . $lng_pVar . '_keywords` as `keywords` FROM `%titems_test_questions__data` WHERE `' . $lng_pVar . '_keywords` LIKE (\'%%%r%%\')';
                            $items_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $old_value_pVar);
                            foreach($items_pVar as $item_pVar) {
                                $tmp_pVar = explode(',', $item_pVar['keywords']);
                                foreach($tmp_pVar as $k_pVar=>$v_pVar) {
                                    $tmp_pVar[$k_pVar] = trim($v_pVar);
                                }
                                $key_pVar = array_search($old_value_pVar, $tmp_pVar);
                                if($key_pVar !== false) {
                                    if($new_value_pVar == '') {
                                        unset($tmp_pVar[$key_pVar]);
                                    }
                                    else {
                                        $tmp_pVar[$key_pVar] = $new_value_pVar;
                                    }
                                    $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `' . $lng_pVar . '_keywords` = %s WHERE `item_id` = %d';
                                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(implode(', ', $tmp_pVar), $item_pVar['item_id']));
                                }
                            }

                        }
                    }

                }
            }
        }


        $info_pVar = db_items_gClass::getInfo_gFunc('test_questions');
        $keywords_pVar = array();
        foreach($info_pVar['languages'] as $language_pVar) {
            $sql_pVar = 'SELECT DISTINCT `'.$language_pVar.'_keywords` FROM `%titems_test_questions__data`';
            $result_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

            $keywords_pVar[$language_pVar] = array();
            foreach($result_pVar as $v_pVar) {
                $tmp_pVar = explode(',', $v_pVar[$language_pVar . '_keywords']);
                foreach($tmp_pVar as $vv_pVar) {
                    if(empty($vv_pVar)) {
                        continue;
                    }
                    $keywords_pVar[$language_pVar][] = trim($vv_pVar);
                }
            }

            $keywords_pVar[$language_pVar] = array_unique($keywords_pVar[$language_pVar]);
            sort($keywords_pVar[$language_pVar]);
        }

        $keywords_pVar['sign'] = uniqid('keyword');
        main_gClass::setPhpSessionVar_gFunc('edit_keywords', $keywords_pVar);

        return($keywords_pVar);
    }

}

class test_keywords extends test_keywords_gClass {}
