<?php


class test_list_templates_my_gClass extends test_list_templates_gClass
{

    protected function getData()
    {
        $copy_id = main_gClass::getInputString_gFunc('copy_id', main_gClass::SRC_GET_pVar);
        if($copy_id) {
            self::copyTest($copy_id);
        }

        $isic_ok = session_gClass::getUserDetail_gFunc('isic_ok');
        $rocnik = session_gClass::getUserDetail_gFunc('rocnik');
        $smer = session_gClass::getUserDetail_gFunc('smer');

        if($isic_ok !== 'yes') {
            $this->params['rocnik-smer'] = array(
                array('rocnik'=>NULL, 'smer'=>NULL, '_operator'=>'AND', '__operator'=>'IS'),
                '_operator'=>'OR');
        }
        elseif(!empty($smer) && !empty($rocnik)) {
            $this->params['rocnik-smer'] = array(
                array('rocnik'=>$rocnik, 'smer'=>$smer, '_operator'=>'AND'),
                array('rocnik'=>NULL, 'smer'=>NULL, '_operator'=>'AND', '__operator'=>'IS'),
                array('rocnik'=>$rocnik, 'smer'=>array('smer'=>NULL, '__operator'=>'IS'), '_operator'=>'AND'),
                array('rocnik'=>array('rocnik'=>NULL, '__operator'=>'IS'), 'smer'=>$smer, '_operator'=>'AND'),
                '_operator'=>'OR');
        }
        elseif(!empty($smer)) {
            $this->params['smer'] = array('smer'=>$smer, 'smer'=>NULL, '_operator'=>'OR');
        }
        elseif(!empty($rocnik)) {
            $this->params['rocnik'] = $rocnik;
        }

        $templates_pVar = items_gClass::getItems_gFunc('test_templates', $this->params);
        unset($templates_pVar['filter']);

        $this->formatData_gFunc($templates_pVar);
        return($templates_pVar);
    }

    protected function formatData_gFunc(&$data_pVar)
    {
        foreach ($data_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 1) === '_') {
                continue;
            }
        }
    }
}

class test_list_templates_my extends test_list_templates_my_gClass {}

