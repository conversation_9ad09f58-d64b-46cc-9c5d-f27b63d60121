<?php

class bug_report extends form_gClass
{
    protected function initForm_gFunc($multiedit_pVar = false, $initFormRef_pVar = true)
    {
        $this->addField_gFunc('', 'type', 'enum', 'Typ problému', true);
        $types_pVar = array(
            ''=> 'vyberte typ problému',
            'other'=> 'iný',
            'technical'=>'Technický problém (nahlásenie chyby)'
        );
        $this->setFieldOptions_gFunc('', 'type', $types_pVar);
        $this->addField_gFunc('', 'name', 'varchar', 'Moje meno', true);
        $this->addField_gFunc('', 'email', 'varchar', 'Môj email', true);
        $this->addField_gFunc('', 'subject', 'varchar', 'Subject', true);
        $this->addField_gFunc('', 'text', 'text', 'Popis problému:', true);
        $this->setFieldDefaultValue_gFunc('name', session_gClass::getUserDetail_gFunc('real_name'));
        $this->setFieldDefaultValue_gFunc('email', session_gClass::getUserDetail_gFunc('email'));
    }

    protected function getData()
    {
        $data_pVar = $this->getFormData_gFunc();
        if($data_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return($data_pVar);
        }

        $type_pVar = $this->getFieldValue_gFunc('type');
        switch($type_pVar) {
            case 'technical':
                email_gClass::mailHtml_gFunc('<EMAIL>',
                    $this->getFieldValue_gFunc('email'),
                    'KEGA BUG: ' . $this->getFieldValue_gFunc('subject'),
                    'Zaslal používateľ: ' . $this->getFieldValue_gFunc('name')
                    . ' (user_id=' . session_gClass::getUserDetail_gFunc('user_id') . ')<br /><br />'
                    . nl2br($this->getFieldValue_gFunc('text')));
                break;
            case 'other':
                email_gClass::mailHtml_gFunc('<EMAIL>',
                    $this->getFieldValue_gFunc('email'),
                    'KEGA: ' . $this->getFieldValue_gFunc('subject'),
                    'Zaslal používateľ: ' . $this->getFieldValue_gFunc('name')
                    . ' (user_id=' . session_gClass::getUserDetail_gFunc('user_id') . ')<br /><br />'
                    . nl2br($this->getFieldValue_gFunc('text')),
                    array('Bcc' => '<EMAIL>'));
                break;
        }

        $data_pVar = $this->getFormData_gFunc();
        return($data_pVar);
    }
}
