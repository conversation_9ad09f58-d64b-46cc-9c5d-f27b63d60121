<?php

class documentCache_gClass extends document_gClass
{
    private $evaluatedContent_pVar;

    function __construct($docName_pVar, $docType_pVar = document_gClass::DOCTYPE_DOCUMENT)
    {
        parent::__construct($docName_pVar, $docType_pVar);
        $this->evaluatedContent_pVar = '';
    }

    public static function getCacheObject_gFunc($docName_pVar, $docType_pVar = document_gClass::DOCTYPE_DOCUMENT)
    {
        $fileName_pVar = self::getCacheFileName_gFunc($docName_pVar);

        if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
            $cacheObject_pVar = new documentCache_gClass($docName_pVar, $docType_pVar);
            callStack_gClass::setDocObject_gFunc($cacheObject_pVar);
            if($cacheObject_pVar->loadFile_gFunc($fileName_pVar) !== true) {
                return(null);
            }
            return($cacheObject_pVar);
        }
        else {
            return(null);
        }
    }

    private function loadFile_gFunc($fileName_pVar)
    {
        $ret_pVar = true;
        ob_start();
        $ret_pVar = include($fileName_pVar);
        $this->evaluatedContent_pVar = ob_get_contents();
        ob_end_clean();
        if($ret_pVar !== true) {
//            error_gClass::warning_gFunc(__FILE__,__LINE__,$fileName_pVar);
        }
        return($ret_pVar);
    }

    public function execute_gFunc()
    {
        return($this->evaluatedContent_pVar);
    }

    static public function updateDoc_gFunc($docName_pVar, $docContent_pVar)
    {
        $fileName_pVar = self::getCacheFileName_gFunc($docName_pVar);
        if(!file_put_contents($fileName_pVar, $docContent_pVar)) {
            error_gClass::warning_gFunc(__FILE__, __LINE__, $fileName_pVar);
        }
    }

}
