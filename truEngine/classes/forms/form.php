<?php

class form_gClass extends source_gClass {
    private $formData_pVar;
    private $formType_pVar;
    private $formId_pVar;
    private $isData_pVar;
    private $initData_pVar;
    private $uploadResults_pVar;
    private $fieldNamePrefixes_pVar;
    private $forceOk_pVar;
    private $forceError_pVar;
    private $preparedOptions_pVar;
    private $prefix_fieldset_pVar;
    private $prefix_field_pVar;
    private $multiedit_pVar;
    private $initArray_pVar;

    const RESULT_OK_pVar = 'ok';
    const RESULT_ERROR_pVar = 'error';
    const RESULT_INPUT_pVar = 'input';
    const RESULT_VISIBLE_pVar = 'visible';
    const RESULT_HIDDEN_pVar = 'hidden';
    const RESULT_DISABLED_pVar = 'disabled';

    function __construct($action_pVar = 'get', $formId_pVar = false)
    {
        $this->formData_pVar = array();
        $this->formData_pVar['hidden_fields'] = array();
        $this->formData_pVar['hidden_fields_code'] = '';
        $this->formData_pVar['fieldsets'] = array();
        $this->formData_pVar['fields_map'] = array();
        $this->formData_pVar['vars'] = array();
        $this->formData_pVar['js'] = '';
        $this->uploadResults_pVar = array();
        $this->fieldNamePrefixes_pVar = array();
        $this->forceOk_pVar = false;
        $this->forceError_pVar = false;
        $this->preparedOptions_pVar = array();
        $this->multiedit_pVar = false;
        $this->javaScript_pVar = false;

        parent::__construct($action_pVar);
        $this->formType_pVar = $this->getClassName_gFunc();
        if($formId_pVar === false) {
            $this->formId_pVar = $this->formType_pVar;
        }
        else {
            $this->formId_pVar = $formId_pVar;
        }
        $this->isData_pVar = main_gClass::getInputBoolean_gFunc($this->formId_pVar.'_data', main_gClass::SRC_REQUEST_pVar, false);
        $this->initData_pVar = main_gClass::getInputBoolean_gFunc($this->formId_pVar.'_init', main_gClass::SRC_REQUEST_pVar, false);
        if(!$this->isData_pVar) {
            $this->submitDisable_gFunc(false);
        }
        if($this->submitIsDisabled_gFunc()) {
            $this->isData_pVar = false;
        }

        $this->prefix_fieldset_pVar = '';
        $this->prefix_field_pVar = '';

        $this->initArray_pVar = false; //array();
    }



    function getFormId_gFunc()
    {
        return($this->formId_pVar);
    }

    public function isData_gFunc()
    {
        return($this->isData_pVar);
    }

    public function handleAction_gFunc()
    {
        $this->initForm_gFunc();
        return(parent::handleAction_gFunc());
    }

    private function submitDisable_gFunc($disable_pVar = true)
    {
        if($disable_pVar === true) {
            $value_pVar = '0';
        }
        else {
            $value_pVar = '1';
        }
        main_gClass::setPhpSessionVar_gFunc('form_'.$this->formId_pVar.'_submit_enabled', $value_pVar);
    }

    private function submitIsDisabled_gFunc()
    {
        $submit_enabled_pVar = main_gClass::getSessionData_gFunc('form_'.$this->formId_pVar.'_submit_enabled', '1');
        if($submit_enabled_pVar !== '1') {
            return(true);
        }
        return(false);
    }

    protected function initForm_gFunc($multiedit_pVar = false)
    {
        if($multiedit_pVar) {
            if($this->multiedit_pVar === false) {
                $this->multiedit_pVar = 0;
            }
            $this->setFieldsetPrefix_gFunc('a' . $this->multiedit_pVar . '_');
            $this->setFieldPrefix_gFunc('a' . $this->multiedit_pVar . '_');
            $this->multiedit_pVar++;
        }
        return;
    }

    /**
     * Prida Fieldset, alebo ak existuje, tak nastavi jeho parametre (label)
     */
    function addFieldset_gFunc($fieldsetName_pVar, $legend_pVar = false)
    {
        if(!isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar])) {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar] = array();
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'] = array();
        }
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['name'] = $this->prefix_fieldset_pVar . $fieldsetName_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['prefix'] = $this->prefix_fieldset_pVar;

        if(!empty($legend_pVar)) {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['legend'] = $legend_pVar;
        }
    }

    /**
     * Prida hidden polozku do formulara
     */
    function addHiddenField_gFunc($fieldName_pVar, $defaultValue_pVar = null, $match_pVar = null, $value_pVar = null)
    {
        $fixedValue_pVar = false;
        if(isset($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            if(isset($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['fixed'])
                && $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['fixed']) {
                $fixedValue_pVar = true;
            }
        }
        else {
            $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar] = array();
            $fixedValue_pVar = false;
        }
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['name'] = $this->prefix_field_pVar . $fieldName_pVar;
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['dbname'] = $fieldName_pVar;
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['match'] = $match_pVar;
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['default_value'] = $defaultValue_pVar;
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'] = array();
        if(!$fixedValue_pVar) {
            if($value_pVar !== null) {
                $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $value_pVar;
            }
            else {
                if($this->isData_pVar || $this->initData_pVar) {
                    $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $this->getFieldValueFromRequest_gFunc(false, $fieldName_pVar);
                }
                else {
                    $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $defaultValue_pVar;
                }
            }
        }
    }

    /**
     * Prid polozku do formulara
     */
    function addField_gFunc($fieldsetName_pVar, $fieldName_pVar, $fieldType_pVar, $fieldLabel_pVar, $required_pVar = false, $match_pVar = null, $editable_pVar = true)
    {
        if($fieldType_pVar === 'password' && strpos($fieldLabel_pVar, '|') !== false) {
            // password prida dve polia (jedno na verifikaciu, so suffixom _2
            $fieldLabel_pVar = explode('|', $fieldLabel_pVar);
            $this->addField_gFunc($fieldsetName_pVar, $fieldName_pVar, $fieldType_pVar, $fieldLabel_pVar[0], $required_pVar, $match_pVar, $editable_pVar);
            $this->addField_gFunc($fieldsetName_pVar, $fieldName_pVar . '_2', $fieldType_pVar, $fieldLabel_pVar[1], $required_pVar, $match_pVar, $editable_pVar);
            return;
        }

        if(!isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar])) {
            $this->addFieldset_gFunc($fieldsetName_pVar);
        }

        // nastavim default match pre rozne typy
        if($match_pVar === null) {
            if($fieldType_pVar === 'email') {
                $match_pVar = '/^[_a-z0-9-]+(\.[_a-z0-9-]+)*@[a-z0-9-]+(\.[a-z0-9-]+)*(\.[a-z]{2,3})$/i';
            }
            if($fieldType_pVar === 'url') {
                $match_pVar = '/^((https?|ftp|gopher|telnet|file|notes|ms-help):((/'.'/)|(\\\\))+[\w\d:#@%/;$()~_?\+-=\\\.&]*)$/';
            }
        }

        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['name'] = $this->prefix_field_pVar . $fieldName_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['dbname'] = $fieldName_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['label'] = $fieldLabel_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'] = $fieldType_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['required'] = $required_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['match'] = $match_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['error_code'] = self::RESULT_OK_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['error_message'] = '';
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = null;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['default_value'] = null;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['attributes_ok'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['attributes_error'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['attributes_layout'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['editable'] = $editable_pVar?true:false;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['confirm_set'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['confirm_unset'] = array();
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['info'] = '';
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['events'] = array();
        $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar] = $this->prefix_fieldset_pVar . $fieldsetName_pVar;

        // inicializujem z requestu
        $this->initField_gFunc($fieldsetName_pVar, $fieldName_pVar);
    }


    function deleteField_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        unset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]);
        unset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]);
    }

    function moveFieldBefore_gFunc($fieldsetName_pVar, $fieldName_pVar, $targetFieldset_pVar, $beforeField_pVar)
    {
        $field_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar];
        $this->deleteField_gFunc($fieldsetName_pVar, $fieldName_pVar);
        $fieldset_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $targetFieldset_pVar]['fields'];
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $targetFieldset_pVar]['fields'] = array();
        foreach($fieldset_pVar as $k_pVar=>$v_pVar) {
            if($k_pVar == $this->prefix_field_pVar . $beforeField_pVar) {
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $targetFieldset_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar] = $field_pVar;
                $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar] = $this->prefix_fieldset_pVar . $targetFieldset_pVar;
            }
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $targetFieldset_pVar]['fields'][$k_pVar] = $v_pVar;
        }
    }

    /**
     * Nastavi ConfirmSet hlasku, alebo ConfirmUnset hlasku.
     * Alebo obe.
     *
     * @param unknown_type $fieldsetName_pVar
     * @param unknown_type $fieldName_pVar
     * @param unknown_type $optionValue_pVar
     * @param unknown_type $confirmSetStr_pVar
     * @param unknown_type $confirmUnsetStr_pVar
     */
    function setFieldOptionConfirm_gFunc($fieldsetName_pVar, $fieldName_pVar, $optionValue_pVar, $confirmSetStr_pVar = false, $confirmUnsetStr_pVar = false)
    {
        if($confirmSetStr_pVar !== false) {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['confirm_set'][$optionValue_pVar] = $confirmSetStr_pVar;
        }
        if($confirmUnsetStr_pVar !== false) {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['confirm_unset'][$optionValue_pVar] = $confirmUnsetStr_pVar;
        }
    }

    function setFieldEvent_gFunc($fieldsetName_pVar, $fieldName_pVar, $eventName_pVar, $eventCode_pVar)
    {
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['events'][$eventName_pVar] = $eventCode_pVar;
    }

    /**
     * Inicializujem hodnotu polozky z requestu
     *
     * @param unknown_type $fieldsetName_pVar
     * @param unknown_type $fieldName_pVar
     */
    function initField_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        if(!isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar])) {
            return;
        }
        if(!isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return;
        }

        // inicializujem hodnotu z requestu, ak je pritomna
        if($this->isData_pVar || $this->initData_pVar) {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $this->getFieldValueFromRequest_gFunc($fieldsetName_pVar, $fieldName_pVar);
        }

        if(!$this->isData_gFunc() && is_array($this->initArray_pVar)) {
            if(isset($this->initArray_pVar[$fieldName_pVar])) {

                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $this->initArray_pVar[$fieldName_pVar];
            }
        }

        $this->fixField_gFunc($fieldsetName_pVar, $fieldName_pVar);
    }

    /**
     * Po nastaveni hodnoty musim zavolat tuto funkciu. Inicializuje vsetky potrebne premenne podla novej hodnoty
     */
    function fixField_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        // inicializujem multiple hodnoty
        if($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'] === 'set'
            || $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'] === 'itemlist') {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value'] = array_gClass::setKeysFromValues_gFunc(explode(',', $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value']));
        }

        // inicializujem intervalove hodnoty
        if(substr($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'], -8) === 'Interval') {
            $values_pVar = explode(',', $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value']);
            if(isset($values_pVar[0])) {
                $values_pVar['min'] = $values_pVar[0];
            }
            else {
                $values_pVar['min'] = false;
            }
            if(isset($values_pVar[1])) {
                $values_pVar['max'] = $values_pVar[1];
            }
            else {
                $values_pVar['max'] = false;
            }
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value'] = array('min'=>$values_pVar['min'], 'max'=>$values_pVar['max']);
        }

        // inicializujem datum
        if($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'] === 'date') {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value'] =  array('d'=>'', 'm'=>'', 'y'=>'');
            $value_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'];
            $t_pVar = strtotime($value_pVar);
            if($t_pVar) {
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['y'] = date('Y', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['m'] = date('m', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['d'] = date('d', $t_pVar);
            }
        }

        // inicializujem datum-cas
        if($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'] === 'datetime') {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value'] =  array('d'=>'', 'm'=>'', 'y'=>'', 'h'=>'', 'i'=>'', 's'=>'');
            $value_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'];
            $t_pVar = strtotime($value_pVar);
            if($t_pVar) {
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['y'] = date('Y', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['m'] = date('m', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['d'] = date('d', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['h'] = date('H', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['i'] = date('i', $t_pVar);
                $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']['s'] = date('s', $t_pVar);
            }
        }

    }

    /**
     * Nastavim pre polozku hodnotu option $optionValue_pVar, pre kluz $optionKey_pVar
     */
    function addFieldOption_gFunc($fieldsetName_pVar, $fieldName_pVar, $optionKey_pVar, $optionValue_pVar)
    {
        // nastavim option
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'][$optionKey_pVar] = $optionValue_pVar;

        $this->initField_gFunc($fieldsetName_pVar, $fieldName_pVar);
    }

    /**
     * Nastavim pre polozku cele pole option. Povodne pole (ak bolo nastavene) bude premazane tymto.
     */
    function setFieldOptions_gFunc($fieldsetName_pVar, $fieldName_pVar, $optionsArray_pVar)
    {
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'] = $optionsArray_pVar;

        $this->initField_gFunc($fieldsetName_pVar, $fieldName_pVar);
    }

    function getFieldOptions_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        if(isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar])
            && isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar])
            && isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'])) {
            return($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options']);
        }
        else {
            return(array());
        }
    }

    /**
     * Nastavim defaultnu hodnotu pre polozku.
     * Ak neboli odoslane data, iez inicializujem hodnotu.
     */
    function setDefaultValue_pVar($fieldsetName_pVar, $fieldName_pVar, $value_pVar, $useFieldsetPrefix_pVar = true)
    {
        // ak polozka neexistuje, nenastavujem ju
        if(!isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]) || !isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }

        // nastavim default hodnotu
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['default_value'] = $value_pVar;

        // inicializujem hodnotu, ak neboli prijate ata
        if(!$this->isData_pVar && !$this->initData_pVar) {
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $value_pVar;
        }

        // updatnem premenne
        $this->fixField_gFunc($fieldsetName_pVar, $fieldName_pVar);

        return(true);
    }

    /**
     * Nastavim defaultnu hodnotu pre polozku.
     * Ak neboli odoslane data, tiez inicializujem hodnotu.
     */
    function setFieldDefaultValue_gFunc($fieldName_pVar, $value_pVar)
    {
        // ak polozka neexistuje, nenastavujem ju
        if(!isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }

        $fieldsetName_pVar = $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar];

        if(strlen($this->prefix_fieldset_pVar) && substr($fieldsetName_pVar, 0, strlen($this->prefix_fieldset_pVar)) == $this->prefix_fieldset_pVar) {
            $fieldsetName_pVar = substr($fieldsetName_pVar, strlen($this->prefix_fieldset_pVar));
        }
        return($this->setDefaultValue_pVar($fieldsetName_pVar, $fieldName_pVar, $value_pVar));
    }

    function rewriteFieldValue_gFunc($fieldName_pVar, $value_pVar)
    {
        // ak polozka neexistuje, nenastavujem ju
        if(!isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }

        $fieldsetName_pVar = $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar];

        if(strlen($this->prefix_fieldset_pVar) && substr($fieldsetName_pVar, 0, strlen($this->prefix_fieldset_pVar)) == $this->prefix_fieldset_pVar) {
            $fieldsetName_pVar = substr($fieldsetName_pVar, strlen($this->prefix_fieldset_pVar));
        }

        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $value_pVar;
        $this->fixField_gFunc($fieldsetName_pVar, $fieldName_pVar);
        return($value_pVar);
    }

    public function setFieldEditable_gFunc($fieldName_pVar, $editable_pVar = true)
    {
        // ak polozka neexistuje, nenastavujem ju
        if(!isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }

        $fieldsetName_pVar = $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar];

        if(strlen($this->prefix_fieldset_pVar) && substr($fieldsetName_pVar, 0, strlen($this->prefix_fieldset_pVar)) == $this->prefix_fieldset_pVar) {
            $fieldsetName_pVar = substr($fieldsetName_pVar, strlen($this->prefix_fieldset_pVar));
        }

        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['editable'] = $editable_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] =
            $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['default_value'];
        $this->fixField_gFunc($fieldsetName_pVar, $fieldName_pVar);
        return(true);
    }

    public function setFieldLng_gFunc($fieldName_pVar, $lng_pVar = 'sk', $isDefault_pVar = true, $languages_pVar = array())
    {
        // ak polozka neexistuje, nenastavujem ju
        if(!isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }

        $fieldsetName_pVar = $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar];

        if(strlen($this->prefix_fieldset_pVar) && substr($fieldsetName_pVar, 0, strlen($this->prefix_fieldset_pVar)) == $this->prefix_fieldset_pVar) {
            $fieldsetName_pVar = substr($fieldsetName_pVar, strlen($this->prefix_fieldset_pVar));
        }

        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['lng'] = $lng_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['lng_default'] = $isDefault_pVar;
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['lngs'] = $languages_pVar;
        return(true);
    }


    /**
     * nastavim defaultnu hodnotu pre hidden polozku
     * Ak neexistuje, vytvorim a nastavim
     */
    function setHiddenFieldDefaultValue_gFunc($fieldName_pVar, $value_pVar, $fixValue_pVar = false)
    {
        // ak polozka neexistuje, nenastavujem hodnotu
        if(!isset($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['name'] = $this->prefix_field_pVar . $fieldName_pVar;
            $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['dbname'] = $fieldName_pVar;
        }

        // nastavim hodnotu
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['default_value'] = $value_pVar;

        // pre hidden field hodnotu vzdy overwritnem, ignorujem vstup
        $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $value_pVar;

        if($fixValue_pVar) {
            $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['fixed'] = true;
        }
        return(true);
    }

    function setFieldInfo_gFunc($fieldName_pVar, $infoText_pVar)
    {
        // ak polozka neexistuje, nenastavujem ju
        if(!isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }

        $fieldsetName_pVar = $this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar];

        if(strlen($this->prefix_fieldset_pVar) && substr($fieldsetName_pVar, 0, strlen($this->prefix_fieldset_pVar)) == $this->prefix_fieldset_pVar) {
            $fieldsetName_pVar = substr($fieldsetName_pVar, strlen($this->prefix_fieldset_pVar));
        }
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['info'] = $infoText_pVar;
        return(true);
    }

    function setInitArray_gFunc($data_pVar = array())
    {
        $this->initArray_pVar = $data_pVar;
    }

    /**
     * ziskam hodnotu z requestu.
     */
    protected function getFieldValueFromRequest_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        // ak nie su data, vratim null
        if(!$this->isData_pVar && !$this->initData_pVar) {
            return(null);
        }

        // hidden field
        if($fieldsetName_pVar === false && isset($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            $varName_pVar = $this->prefix_field_pVar . $fieldName_pVar;
            $match_pVar = $this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['match'];
            if($match_pVar === null) {
                $match_pVar = false;
            }
            $requestValue_pVar = main_gClass::getInputString_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar , $match_pVar, null);
            return($requestValue_pVar);
        }

        // a teraz idem podla typu.
        $fieldType_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'];
        $varName_pVar = $this->prefix_field_pVar . $fieldName_pVar;
        $match_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['match'];
        if($match_pVar === null) {
            $match_pVar = false;
        }

        switch ($fieldType_pVar) {
            case 'integer':
                $requestValue_pVar = main_gClass::getInputInteger_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar, null);
                break;
            case 'float':
                $requestValue_pVar = main_gClass::getInputFloat_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar, null);
                break;
            case 'unsigned':
                $requestValue_pVar = main_gClass::getInputInteger_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar , null);
                if($requestValue_pVar < 0) {
                    $requestValue_pVar = null;
                }
                break;
            case 'select':
                if($match_pVar === false) {
                    $match_pVar = '/[0-9a-zA-Z_-]*/';
                }
                $requestValue_pVar = main_gClass::getInputString_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar, $match_pVar, null);
                $options_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'];
                if(!$this->valueIsInSelect_gFunc($options_pVar, $requestValue_pVar)) {
                    $requestValue_pVar = null;
                }
                if(is_numeric($requestValue_pVar)) {
                    $requestValue_pVar = intval($requestValue_pVar);
                }
                break;
            case 'set':
                if($match_pVar === false) {
                    $match_pVar = '/[0-9a-zA-Z_-]*/';
                }
                $vars_pVar = main_gClass::getInputVarNames_gFunc(main_gClass::SRC_REQUEST_pVar);
                $values_pVar = array();
                $varLen_pVar = strlen($varName_pVar);
                foreach ($vars_pVar as $k_pVar=>$v_pVar) {
                    if(substr($v_pVar, 0, $varLen_pVar + 1) !== $varName_pVar . '_') {
                        continue;
                    }
                    $value_pVar = substr($v_pVar, $varLen_pVar + 1);
                    if(!empty($match_pVar) && !preg_match($match_pVar, $value_pVar)) {
                        $value_pVar = null;
                    }

                    $options_pVar = $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'];
                    if(!$this->valueIsInSelect_gFunc($options_pVar, $value_pVar)) {
                        $value_pVar = null;
                    }
                    if(is_numeric($value_pVar)) {
                        $value_pVar = intval($value_pVar);
                    }

                    if($value_pVar !== null) {
                        $values_pVar[] = $value_pVar;
                    }
                }
                $requestValue_pVar = implode(',', $values_pVar);
                break;
            case 'itemlist':
                $requestValue_pVar = main_gClass::getInputArray_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar, '/[0-9]+/');
                if(is_array($requestValue_pVar)) {
                    $requestValue_pVar = implode(',', $requestValue_pVar);
                }
                break;
            case 'intInterval':
                $requestValue1_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_min', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue2_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_max', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue_pVar = array($requestValue1_pVar, $requestValue2_pVar);
                $requestValue_pVar = implode(',', $requestValue_pVar);
                break;
            case 'floatInterval':
                $requestValue1_pVar = main_gClass::getInputFloat_gFunc($varName_pVar . '_min', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue2_pVar = main_gClass::getInputFloat_gFunc($varName_pVar . '_max', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue_pVar = array($requestValue1_pVar, $requestValue2_pVar);
                $requestValue_pVar = implode(',', $requestValue_pVar);
                break;
            case 'date':
                $requestValue_pVar = '';
                $requestValue1_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_d', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue2_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_m', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue3_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_y', main_gClass::SRC_REQUEST_pVar, null);
                if(empty($requestValue1_pVar) && empty($requestValue2_pVar) && empty($requestValue3_pVar)) {
                    $requestValue_pVar = null;
                }
                else {
                    $date_pVar = strtotime(sprintf('%04d-%02d-%02d', $requestValue3_pVar, $requestValue2_pVar, $requestValue1_pVar));
                    $requestValue_pVar = date('Y-m-d', $date_pVar);
                }
                break;
            case 'datetime':
                $requestValue_pVar = '';
                $requestValue1_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_d', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue2_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_m', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue3_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_y', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue4_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_h', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue5_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_i', main_gClass::SRC_REQUEST_pVar, null);
                $requestValue6_pVar = main_gClass::getInputInteger_gFunc($varName_pVar . '_s', main_gClass::SRC_REQUEST_pVar, null);
                if(empty($requestValue1_pVar) && empty($requestValue2_pVar) && empty($requestValue3_pVar)) {
                    $requestValue_pVar = null;
                }
                else {
                    $date_pVar = strtotime(sprintf('%04d-%02d-%02d %02d:%02d:%02d', $requestValue3_pVar, $requestValue2_pVar, $requestValue1_pVar, $requestValue4_pVar, $requestValue5_pVar, $requestValue6_pVar));
                    $requestValue_pVar = date('Y-m-d H:i:s', $date_pVar);
                }
                break;
            case 'password':
                $requestValue_pVar = main_gClass::getInputString_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar , $match_pVar, null);
                if(isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar . '_2'])) {
                    // ak je aj kontrola hesla, a kontrola hesla je prazdna, formular spracujem akoby bolo aj toto pole prazdne...
                    // to je ochrana proti tomu, ze prehliadac automaticky doplna heslo. A vzdy ho musim vymazavat.
                    $pass_2_value_pVar = $this->getFieldValueFromRequest_gFunc($fieldsetName_pVar, $fieldName_pVar . '_2');
                    if(empty($pass_2_value_pVar)) {
                        $requestValue_pVar = '';
                    }
                }
                break;
            default: // retazcove veci
                $requestValue_pVar = main_gClass::getInputString_gFunc($varName_pVar, main_gClass::SRC_REQUEST_pVar , $match_pVar, null);
                break;
        }
        return($requestValue_pVar);
    }

    public function getFieldValue_gFunc($fieldName_pVar)
    {
        if(isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {

            if(array_search($this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'], array('set','itemlist')) !== false) {
//				return($this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['multi_value']);
            }
            return($this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value']);
        }
        else {
            // este to moze byt hidden field
            if(isset($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
                return($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]['value']);
            }
            else {
                return(false);
            }
        }
    }

    public function getField_gFunc($fieldName_pVar)
    {
        if(isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return($this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]);
        }
        else {
            // este to moze byt hidden field
            if(isset($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
                return($this->formData_pVar['hidden_fields'][$this->prefix_field_pVar . $fieldName_pVar]);
            }
            else {
                return(false);
            }
        }
    }

    public function getFieldsNames_gFunc($hidden_pVar = false, $prefixedNames_pVar = false)
    {
        $fieldsNames_pVar = array();
        if(!$hidden_pVar) {
            foreach ($this->formData_pVar['fieldsets'] as $k_pVar=>$v_pVar) {
                foreach($this->formData_pVar['fieldsets'][$k_pVar]['fields'] as $kk_pVar=>$vv_pVar) {
                    if($prefixedNames_pVar) {
                        $fieldsNames_pVar[] = $vv_pVar['name'];
                    }
                    else {
                        $fieldsNames_pVar[] = $vv_pVar['dbname'];
                    }
                }
            }
        }
        else {
            foreach($this->formData_pVar['hidden_fields'] as $kk_pVar=>$vv_pVar) {
                if($prefixedNames_pVar) {
                    $fieldsNames_pVar[] = $vv_pVar['name'];
                }
                else {
                    $fieldsNames_pVar[] = $vv_pVar['dbname'];
                }
            }
        }
        return($fieldsNames_pVar);
    }

    private function valueIsInSelect_gFunc($selectData_pVar, $value_pVar)
    {
        if(isset($selectData_pVar[$value_pVar]) && (!is_array($selectData_pVar[$value_pVar]) || !key_exists('optgroup', $selectData_pVar[$value_pVar]))) {
            return(true);
        }
        foreach ($selectData_pVar as $k_pVar=>$v_pVar) {
            if(isset($v_pVar[$value_pVar])) {
                return(true);
            }
        }
        return(false);
    }

    function setCurrentValue_pVar($fieldsetName_pVar, $fieldName_pVar, $value_pVar)
    {
        if(!isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]) || !isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(false);
        }
        $this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['value'] = $value_pVar;
        return(true);
    }

    function buildHiddenFieldsCode_gFunc()
    {
        $this->formData_pVar['hidden_fields_code'] = '';
        foreach ($this->formData_pVar['hidden_fields'] as $field_pVar) {
            $this->formData_pVar['hidden_fields_code'] .= '<input type="hidden" name="' . $field_pVar['name'] . '" id="' . $field_pVar['name'] . '" value="' . $field_pVar['value'] . '" />';
        }
    }

    protected function getData()
    {
        return($this->getFormData_gFunc());
    }

    function getFormData_gFunc()
    {
        if($this->forceOk_pVar) {
            $this->formData_pVar['error_code'] = self::RESULT_OK_pVar;
            $this->formData_pVar['visibility'] = self::RESULT_HIDDEN_pVar;
            return($this->formData_pVar);
        }


        $tmpPrefix_field_pVar = $this->prefix_field_pVar;
        $this->prefix_field_pVar = '';
        $this->addHiddenField_gFunc($this->formId_pVar.'_data', 1, '/[01]/', 1);
        $this->prefix_field_pVar = $tmpPrefix_field_pVar;

        /*
        $this->addHiddenField_gFunc($this->formId_pVar.'_formfields', 0, '/[a-zA-Z0-9_,]*'.'/');
        $allFields_pVar = array_merge(array_keys($this->formData_pVar['fields_map']), array_keys($this->formData_pVar['hidden_fields']));
        $this->setHiddenFieldDefaultValue_gFunc($this->formId_pVar.'_formfields', implode(',', $allFields_pVar));
        */

        $this->buildHiddenFieldsCode_gFunc();

        $this->formData_pVar['form_id'] = $this->formId_pVar;
        $this->validateForm_gFunc();

        if(!isset($this->formData_pVar['attributes'])) {
            $this->formData_pVar['attributes'] = array();
        }
        if(!isset($this->formData_pVar['attributes']['method'])) {
            $this->formData_pVar['attributes']['method'] = 'post';
        }
        if(!isset($this->formData_pVar['attributes']['doc'])) {
            $this->formData_pVar['attributes']['doc'] = '#';
        }

        $this->formData_pVar['attributes']['action'] = main_gClass::makeUrl_gFunc($this->formData_pVar['attributes']['doc']);

        $data_pVar = $this->formData_pVar;
        unset($data_pVar['attributes']['doc']);

        return($data_pVar);
    }

    function saveDataToDb_gFunc($tableName_pVar)
    {
        if(!modules_gClass::isModuleRegistred_gFunc('db')) {
            return(false);
        }

        return(true);
    }

    /**
     * Nastavi error hlasku.
     * Ak $fieldName_pVar === false, tak nastavi hlasku pre formular
     *   inak nastavi hlasku pre aktualny field
     *
     * @param unknown_type $errorMessage_pVar
     * @param unknown_type $fieldName_pVar
     */
    public function setError_gFunc($errorMessage_pVar = '', $fieldName_pVar = false, $useDefaultMessage_pVar = false)
    {
        if($fieldName_pVar === false) {
            $this->formData_pVar['error_code'] = self::RESULT_ERROR_pVar;
            $this->formData_pVar['visibility'] = self::RESULT_VISIBLE_pVar;
            $this->forceError_pVar = true;
        }
        else {
            if($useDefaultMessage_pVar !== false) {
                $errorMessage_pVar = $this->getErrMessageForFieldType_gFunc($this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['type'], $useDefaultMessage_pVar);
            }
            $this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['error_code'] = self::RESULT_ERROR_pVar;
            $this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['error_message'] = $errorMessage_pVar;
            $this->formData_pVar['error_code'] = self::RESULT_ERROR_pVar;
            $this->formData_pVar['visibility'] = self::RESULT_VISIBLE_pVar;
        }
    }

    protected function validateForm_gFunc()
    {
        if($this->isData_pVar) {
            if(!$this->forceError_pVar) {
                $this->formData_pVar['error_code'] = self::RESULT_OK_pVar;
                $this->formData_pVar['visibility'] = self::RESULT_HIDDEN_pVar;
            }

            if(isset($this->params['itemtype'])) {
                if($this->getFieldValue_gFunc($this->params['itemtype'] . '_status') == 'deleted') {
                    return;
                }
            }

            /*			echo '<hr />';
                        echo '<pre>';
                        print_r($this->formData_pVar['fieldsets']);
                        echo '</pre>';*/

            foreach ($this->formData_pVar['fieldsets'] as $fieldset_pVar) {
                foreach ($fieldset_pVar['fields'] as $field_pVar) {
                    $this->validateField_gFunc($fieldset_pVar['name'],$field_pVar['name'], false);
                }
            }
            if($this->formData_pVar['error_code'] === self::RESULT_ERROR_pVar) {
                $this->formData_pVar['visibility'] = self::RESULT_VISIBLE_pVar;
            }
            else {
                $this->submitDisable_gFunc();
            }
        }
        else {
            if($this->submitIsDisabled_gFunc()) {
                $this->formData_pVar['error_code'] = self::RESULT_DISABLED_pVar;
                $this->formData_pVar['visibility'] = self::RESULT_DISABLED_pVar;
            }
            else {
                $this->formData_pVar['error_code'] = self::RESULT_INPUT_pVar;
                $this->formData_pVar['visibility'] = self::RESULT_VISIBLE_pVar;
            }
        }

        if($this->forceError_pVar) {
            $this->submitDisable_gFunc(false);
        }
    }

    protected function validateField_gFunc($fieldsetName_pVar, $fieldName_pVar, $applyPrefix_pVar = true)
    {
        if($this->getMultieditLevel_gFunc() !== false) {
            $fieldsetPrefix_pVar = $this->formData_pVar['fieldsets'][($applyPrefix_pVar?$this->prefix_fieldset_pVar:'') . $fieldsetName_pVar]['prefix'];

            // @TODO: poriesit to tak, aby to nemuselo byt zavysle od nastavenia ITEMTYPE
            $itemType_pVar = $this->getVar_gFunc('itemtype', false);
            if(!empty($itemType_pVar) && !empty($fieldsetPrefix_pVar)) {
                if(!$this->getFieldValue_gFunc($fieldsetPrefix_pVar . $itemType_pVar . '_active_formpart')) {
                    return(true);
                }
            }
        }

        $field_pVar = &$this->formData_pVar['fieldsets'][($applyPrefix_pVar?$this->prefix_fieldset_pVar:'') . $fieldsetName_pVar]['fields'][($applyPrefix_pVar?$this->prefix_field_pVar:'') . $fieldName_pVar];

        $ret_pVar = true;

        if(!$field_pVar['editable']) {
            unset($field_pVar);
            return(true);
        }

        $emptyValue_pVar = false;
        if($field_pVar['required']) {
            if($field_pVar['value'] === null || !strlen($field_pVar['value'])
                || ($field_pVar['type'] === 'enum' && $field_pVar['value'] == -1)) {
                $field_pVar['error_code'] = self::RESULT_ERROR_pVar;
                $field_pVar['error_message'] = $this->getErrMessageForFieldType_gFunc($field_pVar['type'], 'require');
                $ret_pVar = false;
                $emptyValue_pVar = true;
            }
        }
        else {
            if($field_pVar['value'] === null || !strlen($field_pVar['value'])
                || ($field_pVar['type'] === 'enum' && $field_pVar['value'] == -1)) {
                $emptyValue_pVar = true;
            }
        }

        if($field_pVar['match'] !== null && !$emptyValue_pVar) {
            if(!preg_match($field_pVar['match'], $field_pVar['value'])) {
                $field_pVar['error_code'] = self::RESULT_ERROR_pVar;
                $field_pVar['error_message'] = $this->getErrMessageForFieldType_gFunc($field_pVar['type'], 'match');
                $ret_pVar = false;
            }
        }

        if($field_pVar['type'] === 'password') {
            if(isset($this->formData_pVar['fieldsets'][($applyPrefix_pVar?$this->prefix_fieldset_pVar:'') . $fieldsetName_pVar]['fields'][$field_pVar['name'] . '_2'])) {
                if($this->formData_pVar['fieldsets'][($applyPrefix_pVar?$this->prefix_fieldset_pVar:'') . $fieldsetName_pVar]['fields'][$field_pVar['name']]['value']
                    !== $this->formData_pVar['fieldsets'][($applyPrefix_pVar?$this->prefix_fieldset_pVar:'') . $fieldsetName_pVar]['fields'][$field_pVar['name'] . '_2']['value']) {
                    $ret_pVar = false;
                    $field_pVar['error_code'] = self::RESULT_ERROR_pVar;
                    $field_pVar['error_message'] = $this->getErrMessageForFieldType_gFunc($field_pVar['type'], 'unequal');
                }
            }
        }

        if($ret_pVar === false) {
            $this->formData_pVar['error_code'] = self::RESULT_ERROR_pVar;
        }

        unset($field_pVar);
        return($ret_pVar);
    }

    protected function getErrMessageForFieldType_gFunc($fieldType_pVar, $fieldError_pVar)
    {
        switch ($fieldError_pVar) {
            case 'require':
                switch ($fieldType_pVar) {
                    case 'email':
                        return(string_gClass::get('str_forms_fielderr_require_email_pVar'));
                    default:
                        return(string_gClass::get('str_forms_fielderr_require_pVar'));
                }
                break;
            case 'match':
                switch ($fieldType_pVar) {
                    case 'email':
                        return(string_gClass::get('str_forms_fielderr_match_email_pVar'));
                    default:
                        return(string_gClass::get('str_forms_fielderr_match_pVar'));
                }
                break;
            case 'unequal':
                switch ($fieldType_pVar) {
                    case 'password':
                        return(string_gClass::get('str_forms_fielderr_unequal_password_pVar'));
                    default:
                        return(string_gClass::get('str_forms_fielderr_unequal_pVar'));
                }
                break;

        }
        error_gClass::warning_gFunc(__FILE__, __LINE__, $fieldType_pVar . ' ' .$fieldError_pVar);
        return('error');
    }

    public function isFieldset_gFunc($fieldsetName_pVar)
    {
        if(isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar])) {
            return(true);
        }
        else {
            return(false);
        }
    }

    public function isField_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        if(!$this->isFieldset_gFunc($fieldsetName_pVar)) {
            return(false);
        }

        if(isset($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar])) {
            return(true);
        }
        else {
            return(false);
        }
    }

    public function isFieldInRequest_gFunc($fieldName_pVar, $match_pVar = null)
    {
        $requestValue_pVar = main_gClass::getInputString_gFunc($this->prefix_field_pVar . $fieldName_pVar, main_gClass::SRC_REQUEST_pVar , $match_pVar, null);
        if($requestValue_pVar === null) {
            return(false);
        }
        else {
            return(true);
        }
    }

    public function countOptions_gFunc($fieldsetName_pVar, $fieldName_pVar)
    {
        return(count($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options']));
    }

    public function setVar_gFunc($varName_pVar, $varValue_pVar, $useFieldPrefix_pVar = false)
    {
        $this->formData_pVar['vars'][($useFieldPrefix_pVar?$this->prefix_field_pVar:'') . $varName_pVar] = $varValue_pVar;
    }

    public function getVar_gFunc($varName_pVar, $useFieldPrefix_pVar = false)
    {
        if(isset($this->formData_pVar['vars'][($useFieldPrefix_pVar?$this->prefix_field_pVar:'') . $varName_pVar])) {
            return($this->formData_pVar['vars'][($useFieldPrefix_pVar?$this->prefix_field_pVar:'') . $varName_pVar]);
        }
        else {
            return(false);
        }
    }

    public function setFormAttribute_gFunc($attributeName_pVar, $attributeValue_pVar)
    {
        if($attributeName_pVar === 'action') {
            $attributeName_pVar = 'doc';
        }
        $this->formData_pVar['attributes'][$attributeName_pVar] = $attributeValue_pVar;
    }

    /**
     * uploadne subory
     * - $fieldsList_pVar moze byt pole nazvov fieldov, ktore sa maju spracovat.. Ak to nie je pole, spracuje sa cely formular naraz.
     * - Ak sa niektory field nepodari uploadnut, funkcia zrusi vsetky uploady v tomto formulari (nie len z tejto davky, ale vsetky)
     * - pri multiedit forme riesi iba aktualnu cast formulara
     *
     * @param unknown_type $refPrefix_pVar
     * @param unknown_type $refId_pVar
     * @param unknown_type $fieldsList_pVar
     * @return unknown
     */
    public function uploadFiles_gFunc($refPrefix_pVar, $location_pVar, $refId_pVar = 0, $fieldsList_pVar = false)
    {
        $this->uploadResults_pVar = array();

        if($this->formData_pVar['error_code'] !== self::RESULT_OK_pVar) {
            return(false);
        }

        $success_pVar = false; // bol uploadnuty aspon jeden file
        $error_pVar = false; // nevznikla ziadna chyba

        foreach ($this->formData_pVar['fieldsets'] as $kFieldset_pVar=>$fieldset_pVar)
        {
            foreach ($this->formData_pVar['fieldsets'][$kFieldset_pVar]['fields'] as $kField_pVar=>$field_pVar) {
                // filter fieldov
                if($field_pVar['type'] !== 'imagelist' && $field_pVar['type'] !== 'filelist'
                    && $field_pVar['type'] !== 'ximagelist' && $field_pVar['type'] !== 'xfilelist') {
                    continue;
                }

                // ak je multiedit, pustim iba polia z aktualnej casti formulara
                if($this->getMultieditLevel_gFunc() !== false) {
                    if(substr($kField_pVar, 0, strlen($this->prefix_field_pVar)) !== $this->prefix_field_pVar) {
                        continue;
                    }
                }

                if(is_array($fieldsList_pVar)) {
                    if(array_search($field_pVar['name'], $fieldsList_pVar) === false) {
                        continue;
                    }
                }

                if(isset($field_pVar['uploaded_pVar'])) { // pole uz bolo uploadovane, preskocim ho
                    continue;
                }

                // detekcia typu suboru
                if($field_pVar['type'] === 'filelist' || $field_pVar['type'] === 'xfilelist') {
                    $fileType_pVar = 'file';
                }
                else {
                    $fileType_pVar = 'image';
                }

                $result_pVar = main_gClass::move_uploaded_file_gFunc($field_pVar['name'], $refPrefix_pVar . $field_pVar['dbname'], $refId_pVar, $location_pVar, false, false, $fileType_pVar);
                $result_pVar['field_key'] = $kField_pVar;
                $this->uploadResults_pVar[] = $result_pVar;
                if($result_pVar['success']) {
                    $success_pVar = true;
                }
                if($result_pVar['error']) {
                    $error_pVar = true;
                    break;
                }
            }

            if($error_pVar) {
                $this->cancelAllUploads_gFunc();
                return(false);
            }
        }

        /*
                if($success_pVar) {
                    // nastavim do fieldov. Ma to vyznam iba ak bol uploadovany aspon jeden file. Formular to potom ulozi do DB.
                    $files_pVar = array();
                    foreach ($this->uploadResults_pVar as $result_pVar) {
                        $value_pVar = array();
                        foreach ($result_pVar['files'] as $k_pVar=>$v_pVar) {
                            $value_pVar[] = $v_pVar['id']; // id-cka pre toto pole
                            $files_pVar[] = $v_pVar['id']; // idc-ka pre cely formular
                        }
                        $currentValue_pVar = $this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$result_pVar['field_key']]]['fields'][$result_pVar['field_key']]['value'];
                        $currentValue_pVar = explode(',', $currentValue_pVar);
                        foreach ($value_pVar as $k_pVar=>$v_pVar) {
                            if(array_search($v_pVar, $currentValue_pVar) === false) {
                                $currentValue_pVar[] = $v_pVar;
                            }
                        }

                        $value_pVar = implode(',', $value_pVar);
                        $this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$result_pVar['field_key']]]['fields'][$result_pVar['field_key']]['value'] = $value_pVar;
                    }
                }
        */

        return(true);
    }

    /**
     * Zrusi vsetky uploady, ktore boli v tomto formulari robene. (to sa vyuzije v pripade, ze sa jeden z nich nepodaril)
     *
     */
    private function cancelAllUploads_gFunc()
    {
        // zmazem z db
        foreach ($this->uploadResults_pVar as $result_pVar) {
            $value_pVar = array();
            foreach ($result_pVar['files'] as $k_pVar=>$v_pVar) {
                main_gClass::deleteUploadedFile_gFunc($v_pVar['id']);
            }
        }

        /*
                // zmazem z fieldov
                $files_pVar = array();
                foreach ($this->uploadResults_pVar as $result_pVar) {
                    foreach ($result_pVar['files'] as $k_pVar=>$v_pVar) {
                        $files_pVar[] = $v_pVar['id'];
                    }
                }
                foreach ($this->formData_pVar['fieldsets'] as $kFieldset_pVar=>$fieldset_pVar)
                {
                    foreach ($this->formData_pVar['fieldsets'][$kFieldset_pVar]['fields'] as $kField_pVar=>$field_pVar) {
                        // filter fieldov
                        if($field_pVar['type'] !== 'imagelist' && $field_pVar['type'] !== 'filelist') {
                            continue;
                        }

                        $value_pVar = explode(',', $field_pVar['value']);
                        $value_pVar = array_diff($value_pVar, $files_pVar);
                        $value_pVar = implode(',', $value_pVar);
                        $this->formData_pVar['fieldsets'][$kFieldset_pVar]['fields'][$kField_pVar]['value'] = $value_pVar;
                    }
                }
        */

        // nastavim chybu
        $this->formData_pVar['error_code'] = self::RESULT_ERROR_pVar;
    }

    public function extractItemValues_gFunc($systemName_pVar)
    {
        $data_pVar = array();
        $sLen_pVar = strlen($systemName_pVar) + 1 + strlen($this->prefix_field_pVar);
        foreach ($this->formData_pVar['fieldsets'] as $kFieldset_pVar=>$fieldset_pVar) {
            foreach ($fieldset_pVar['fields'] as $kField_pVar=>$field_pVar) {
                if(substr($field_pVar['name'], 0, $sLen_pVar) !== ($this->prefix_field_pVar . $systemName_pVar . '_')) {
                    continue;
                }
                if(!$field_pVar['editable']) {
                    continue;
                }
                $value_pVar = $field_pVar['value'];
                if($field_pVar['type'] === 'password') {
                    if(substr($field_pVar['name'], -2) == '_2') {
                        continue;
                    }
                    $loginField_pVar = substr($field_pVar['name'], 0, $sLen_pVar) . 'login';
                    if(isset($this->generatedLoginName_pVar)) {
                        $loginValue_pVar = $this->generatedLoginName_pVar;
                    }
                    else if(isset($this->formData_pVar['fields_map'][$loginField_pVar])) {
                        $loginValue_pVar = $this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$loginField_pVar]]['fields'][$loginField_pVar]['value'];
                    }
                    else {
                        $loginValue_pVar = '';
                    }

                    $pass2_field_pVar = $fieldset_pVar['fields'][$kField_pVar . '_2'];
                    if(empty($field_pVar['value']) || $pass2_field_pVar['value'] !== $field_pVar['value']) {
                        $value_pVar = null;
                    }
                    else {
                        if(!empty($field_pVar['value']) && !empty($loginValue_pVar)) {
                            $value_pVar = db_session_gClass::encodePassword_gFunc($loginValue_pVar, $field_pVar['value']);
                        }
                        else {
                            $value_pVar = uniqid('nopass-');
                        }
                    }
                }
                $data_pVar[substr($field_pVar['name'], $sLen_pVar)] = $value_pVar;
            }
        }

        foreach ($this->formData_pVar['hidden_fields'] as $kField_pVar=>$field_pVar) {
            if(substr($field_pVar['name'], 0, $sLen_pVar) !== ($this->prefix_field_pVar . $systemName_pVar . '_')) {
                continue;
            }
            $data_pVar[substr($field_pVar['name'], $sLen_pVar)] = $field_pVar['value'];
        }

        unset($data_pVar[substr($this->formId_pVar, $sLen_pVar) . '_data']);
        unset($data_pVar['url_filter']);

        // uploadnute subory (ked vkladam produkt, musim updatnut ich ID)
        $data_pVar['te_uploaded_files'] = array();
        if(count($this->uploadResults_pVar)) {
            foreach ($this->uploadResults_pVar as $k_pVar=>$v_pVar) {
                foreach ($v_pVar['files'] as $vv_pVar) {
                    $data_pVar['te_uploaded_files'][] = $vv_pVar['id'];
                }
            }
        }

        // zmazane subory
        $data_pVar['te_deleted_files'] = array();
        foreach ($this->formData_pVar['hidden_fields'] as $kField_pVar=>$field_pVar) {
            if(!preg_match('/'.$systemName_pVar.'_.*_delete_\d+/', $field_pVar['name'])) {
                continue;
            }
            if(isset($data_pVar[substr($field_pVar['name'], $sLen_pVar)])) {
                unset($data_pVar[substr($field_pVar['name'], $sLen_pVar)]);
            }
            if(!$field_pVar['value']) {
                continue;
            }
            $data_pVar['te_deleted_files'][] = $field_pVar['value'];
        }

        return($data_pVar);
    }

    function fieldIsEmpty_gFunc($fieldName_pVar)
    {
        $fieldValue_pVar = $this->getFieldValue_gFunc($fieldName_pVar);
        if(!strlen($fieldValue_pVar)) {
            return(true);
        }
        return(false);
    }

    function addFieldNamePrefix_gFunc($prefix_pVar)
    {
        $this->fieldNamePrefixes_pVar[$prefix_pVar . '_'] = true;
    }

    function removeFieldNamePrefix_gFunc($fieldName_pVar)
    {
        foreach ($this->fieldNamePrefixes_pVar as $k_pVar=>$v_pVar) {
            if(substr($fieldName_pVar, 0, strlen($k_pVar)) === $k_pVar) {
                return(substr($fieldName_pVar, strlen($k_pVar)));
            }
        }
    }

    function forceOk_gFunc()
    {
        $this->forceOk_pVar = true;
    }

    function prepareOptionsForField_gFunc($fieldName_pVar, $options_pVar = array())
    {
        $this->preparedOptions_pVar[$this->prefix_field_pVar . $fieldName_pVar] = $options_pVar;
    }

    function usePreparedOtionsForField_gFunc($fieldName_pVar)
    {
        if(!isset($this->preparedOptions_pVar[$this->prefix_field_pVar . $fieldName_pVar])) {
            $options_pVar = array();
        }
        else {
            $options_pVar = $this->preparedOptions_pVar[$this->prefix_field_pVar . $fieldName_pVar];
        }

        if(isset($this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar])) {
            $this->formData_pVar['fieldsets'][$this->formData_pVar['fields_map'][$this->prefix_field_pVar . $fieldName_pVar]]['fields'][$this->prefix_field_pVar . $fieldName_pVar]['options'] = $options_pVar;
        }

        return;
    }

    function setFieldsetPrefix_gFunc($prefix_pVar)
    {
        $this->prefix_fieldset_pVar = $prefix_pVar;
    }

    function getFieldsetPrefix_gFunc($fieldsetName_pVar)
    {
        if(!empty($fieldsetName_pVar) || !isset($$this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar])) {
            return($this->formData_pVar['fieldsets'][$this->prefix_fieldset_pVar . $fieldsetName_pVar]['prefix']);
        }
        return($this->prefix_fieldset_pVar);
    }

    function setFieldPrefix_gFunc($prefix_pVar)
    {
        $this->prefix_field_pVar = $prefix_pVar;
    }

    function getFieldPrefix_gFunc()
    {
        return($this->prefix_field_pVar);
    }

    /**
     * Ak nie je multiedit, tak vracia false..
     * Inak vrati posledny index multieditu.
     *
     * @return unknown
     */
    function getMultieditLevel_gFunc()
    {
        return($this->multiedit_pVar);
    }

    function addJavaScript_gFunc($js_pVar)
    {
        $this->formData_pVar['js'] .= $js_pVar;
    }
}
