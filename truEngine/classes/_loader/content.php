<?php

class content_gClass
{
    static protected $contents_pVar = array();
    static protected $displayed_pVar = array();

//    private $name_pVar;
//    private $value_pVar;

    private function __construct()
    {

    }

    static public function setContent_gFunc($contentName_pVar, $contentValue_pVar)
    {
        $contentName_pVar = strtolower($contentName_pVar);
        self::$contents_pVar[$contentName_pVar] = $contentValue_pVar;
        unset(self::$displayed_pVar[$contentName_pVar]);
    }

    static public function unsetContent_gFunc($contentName_pVar)
    {
        $contentName_pVar = strtolower($contentName_pVar);
        unset(self::$contents_pVar[$contentName_pVar]);
        unset(self::$displayed_pVar[$contentName_pVar]);
    }

    static public function displayContent_gFunc($contentName_pVar)
    {
        return(self::getContent_gFunc($contentName_pVar, true));
    }

    static public function getOthersContents_gFunc()
    {
        $ret_pVar = self::$contents_pVar;
        foreach (self::$displayed_pVar as $k_pVar=>$v_pVar) {
            unset($ret_pVar[$k_pVar]);
        }
        return($ret_pVar);
    }

    static public function getContent_gFunc($contentName_pVar, $display_pVar = false)
    {
        $contentName_pVar = strtolower($contentName_pVar);

        if(strpos($contentName_pVar,':')) {
            $content_pVar = explode(':', $contentName_pVar);
            if($display_pVar && isset(self::$contents_pVar[$content_pVar[0]])) {
                self::$displayed_pVar[$content_pVar[0]] = true;
            }
            $c_pVar = self::$contents_pVar;
            foreach ($content_pVar as $v_pVar) {
                if(!isset($c_pVar[$v_pVar])) {
                    return '';
                }
                $c_pVar = $c_pVar[$v_pVar];
            }
            return($c_pVar);
        }
        if(!isset(self::$contents_pVar[$contentName_pVar]))
        {
            return '';
        }
        else {
            self::$displayed_pVar[$contentName_pVar] = true;
            return self::$contents_pVar[$contentName_pVar];
        }
    }
}
