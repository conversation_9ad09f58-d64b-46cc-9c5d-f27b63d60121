<?php


class fileSafe_gClass
{
    const FILESAFESTREAM_PROTOCOL_pVar = 'fs';
    private $handle_pVar;
    private $fileName_pVar, $tempName_pVar;
    private $startPos_pVar = 0;
    private $writeError_pVar = false;

    static function register_gFunc()
    {
        return stream_wrapper_register(self::FILESAFESTREAM_PROTOCOL_pVar, __CLASS__);
    }

    function stream_open($path_pVar, $mode_pVar, $options_pVar, &$opened_path_pVar)
    {
        $fileName_pVar = substr($path_pVar, strlen(self::FILESAFESTREAM_PROTOCOL_pVar) + 3);  // trim protocol://

        $flag_pVar = trim($mode_pVar, 'rwax+');  // text | binary mode
        $mode_pVar = trim($mode_pVar, 'tb');     // mode
        $use_path_pVar = (bool)(STREAM_USE_PATH & $options_pVar); // use include_path?

        $append_pVar = false;

        switch ($mode_pVar) {
            case 'r':
            case 'r+':
                // enter critical section: open and lock EXISTING file for reading/writing
                $handle_pVar = @fopen($fileName_pVar, $mode_pVar . $flag_pVar, $use_path_pVar); // @ is needed
                if (!$handle_pVar) {
                    return(false);
                }
                if (flock($handle_pVar, $mode_pVar == 'r' ? LOCK_SH : LOCK_EX)) {
                    $this->handle_pVar = $handle_pVar;
                    return(true);
                }
                fclose($handle_pVar);
                return(false);
            case 'a':
            case 'a+': $append_pVar = true;
            case 'w':
            case 'w+':
                // try enter critical section: open and lock EXISTING file for rewriting
                error_gClass::disableReporting_gFunc();
                $handle_pVar = @fopen($fileName_pVar, 'r+' . $flag_pVar, $use_path_pVar); // @ is needed
                error_gClass::enableReporting_gFunc();

                if ($handle_pVar) {
                    if (flock($handle_pVar, LOCK_EX)) {
                        if ($append_pVar) {
                            fseek($handle_pVar, 0, SEEK_END);
                            $this->startPos_pVar = ftell($handle_pVar);
                        } else {
                            ftruncate($handle_pVar, 0);
                        }
                        $this->handle_pVar = $handle_pVar;
                        return(true);
                    }
                    fclose($handle_pVar);
                }
                // file doesn't exists, continue...
                $mode_pVar[0] = 'x'; // x || x+

            case 'x':
            case 'x+':
                if (file_exists($fileName_pVar)) {
                    return(false);
                }

                // create temporary file in the same directory
                $tmp_pVar = string_gClass::formatAsDirectory_gFunc(dirname($fileName_pVar)) . uniqid("__", true) . '.tmp';

                // enter critical section: create temporary file
                $handle_pVar = @fopen($tmp_pVar, $mode_pVar . $flag_pVar, $use_path_pVar); // @ is needed
                if ($handle_pVar) {
                    if (flock($handle_pVar, LOCK_EX)) {
                        $this->handle_pVar = $handle_pVar;
                        error_gClass::disableReporting_gFunc();
                        if (!@rename($tmp_pVar, $fileName_pVar)) { // @ is needed
                            // rename later - for windows
                            $this->tempName_pVar = realpath($tmp_pVar);
                            $this->fileName_pVar = substr($this->tempName_pVar, 0, -strlen($tmp_pVar)) . $fileName_pVar;
                        }
                        error_gClass::enableReporting_gFunc();
                        return(true);
                    }
                    fclose($handle_pVar);
                    unlink($tmp_pVar);
                }
                return(false);

            default:
                error_gClass::fatal_pVar(__FILE__, __LINE__, string_gClass::get('str___loader_err_filemode_sVar', $mode_pVar));
                return(false);
        } // switch
    } // stream_open

    function stream_close()
    {
        if ($this->writeError_pVar)
            ftruncate($this->handle_pVar, $this->startPos_pVar);

        fclose($this->handle_pVar);

        // are we working with temporary file?
        if ($this->tempName_pVar) {
            // try to rename temp file, otherwise delete temp file
            if (!@rename($this->tempName_pVar, $this->fileName_pVar)) // @ is needed
                unlink($this->tempName_pVar);
        }
    }

    function stream_read($length_pVar)
    {
        $ret_pVar=fread($this->handle_pVar, $length_pVar);
        return($ret_pVar);
    }


    function stream_write($data_pVar)
    {
        $len_pVar = string_gClass::getStringWidth_gFunc($data_pVar);
        $res_pVar = fwrite($this->handle_pVar, $data_pVar);

        if ($res_pVar !== $len_pVar) {// disk full?
            $this->writeError_pVar = true;
        }

        return($res_pVar);
    }

    function stream_tell()
    {
        $ret_pVar=ftell($this->handle_pVar);
        return($ret_pVar);
    }

    function stream_eof()
    {
        $ret_pVar=feof($this->handle_pVar);
        return($ret_pVar);
    }

    function stream_seek($offset_pVar, $whence_pVar)
    {
        $ret_pVar=fseek($this->handle_pVar, $offset_pVar, $whence_pVar);// === 0; // ???
        return($ret_pVar);
    }

    function stream_stat()
    {
        $ret_pVar=fstat($this->handle_pVar);
        return($ret_pVar);
    }

    function url_stat($path_pVar, $flags_pVar=0)
    {
        if(!$flags_pVar) {
            $ret_pVar=0;
            error_gClass::disableReporting_gFunc();
            $ret_pVar=@stat(fileSafe_gClass::unsafePath_gFunc($path_pVar)); // @ is needed
            error_gClass::enableReporting_gFunc();
            return($ret_pVar);
        }

        ////error_gClass::warning_gFunc(__FILE__,__LINE__,string_gClass::get('str___loader_err_notsafefaccess_sVar'));
        $path_pVar = substr($path_pVar, strlen(self::FILESAFESTREAM_PROTOCOL_pVar)+3);
        $ret_pVar=($flags_pVar & STREAM_URL_STAT_LINK) ? @lstat($path_pVar) : @stat($path_pVar); // @ is needed
        return($ret_pVar);
    }

    static function unsafePath_gFunc($path_pVar)
    {
        if(substr($path_pVar,0,strlen(self::FILESAFESTREAM_PROTOCOL_pVar )+3)==(self::FILESAFESTREAM_PROTOCOL_pVar .':/'.'/')) $path_pVar=substr($path_pVar,strlen(self::FILESAFESTREAM_PROTOCOL_pVar)+3);
        return($path_pVar);
    }

    public function stream_set_option($option, $arg1, $arg2)
    {
        return true;
    }


    static public function safePath_gFunc($path_pVar)
    {
        // ci ide o protokol xxx://, alebo safe protokol
        $p = strpos($path_pVar,':/'.'/');
        if($p !== false) {
            $str_pVar = substr($path_pVar, 0, $p);
            $p = strpos($str_pVar, '.'); // ak je v nazve  protokolu znak ., tak asi nejde o protokol
            if($p === false) return($path_pVar);
        }

        $path_pVar=self::FILESAFESTREAM_PROTOCOL_pVar . ':/'.'/' . $path_pVar;
        return($path_pVar);
    }

    static function file_exists_gFunc($fileName_pVar)
    {
        $fileName_pVar=fileSafe_gClass::unsafePath_gFunc($fileName_pVar);
        return(file_exists($fileName_pVar));
    }

}

