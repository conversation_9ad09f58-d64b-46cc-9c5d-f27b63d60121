<?php

class main_gClass extends truEngineBaseClass_gClass
{
    private static $instance = false;
    public static $doc_pVar = false; // hlavny dokument
    public static $mainVars_pVar = array('_login_msg'=>''); // premenne pre hlavny dokument
    private static $sleep_delayed_pVar = 0;
    private static $http_codes_gFunc = array();
    private static $cache_pVar = array();
    private static $trashLevel_pVar = 0;
    private static $logElementsCache_pVar = false;

    const SRC_REQUEST_pVar = 1;
    const SRC_GETPOST_pVar = 2;
    const SRC_POST_pVar = 3;
    const SRC_GET_pVar = 4;
    const SRC_COOKIE_pVar = 5;
    const SRC_SESSION_pVar = 6;
    const SRC_SERVER_pVar = 7;
    const SRC_ENV_pVar = 8;
    const SRC_FILES_pVar = 9;

    const SRC_FLAG_ADDSLASHES_pVar = 1;
    const SRC_FLAG_STRIPTAGS_pVar = 2;
    const SRC_FLAG_ALL_pVar = 3;

    static private $_REQUEST_pVar = false;
    static private $_GET_pVar = false;
    static private $_POST_pVar = false;
    static private $_SERVER_pVar = false;
    static private $_ENV_pVar = false;
    static private $_FILES_pVar = false;
    static private $_SESSION_pVar = false;
    static private $_SESSION_NAME_pVar = '';

    static private $main_currency_pVar;
    static private $main_currency_short_pVar;

    private $scriptStartTime_pVar;
    private $config_gVar;
    private $session_gVar;


    // The singleton method
    public static function getObject_gFunc()
    {
        if (self::$instance === false) {
            $c = __CLASS__;
            self::$instance = new $c;
            self::initInputs_gFunc();
        }

        return self::$instance;
    }

    // A private constructor; prevents direct creation of object
    private function __construct() {
        self::$instance = $this;

        $this->scriptStartTime_pVar = debug_gClass::getMicrotime_gFunc();
        if(isset($GLOBALS['scipt_start_time'])) {
            $this->scriptStartTime_pVar = $GLOBALS['scipt_start_time'];
        }
        else {
            error_gClass::warning_gFunc(__FILE__, __LINE__, 'Script start time is not defined.', array(), false);
        }
        if(isset($GLOBALS['scipt_start_time'])) {
            $this->scriptStartTime_pVar = $GLOBALS['scipt_start_time'];
        }
        $this->config_gVar = false;
        $this->session_gVar = false;
    }


    public static function timeExceeded_gFunc($sec_pVar)
    {
        $o_pVar = self::getObject_gFunc();
        if(time() - $o_pVar->scriptStartTime_pVar > $sec_pVar) {
            return(true);
        }
        else {
            return(false);
        }
    }

    // Prevent users to clone the instance
    public function __clone()
    {
        error_gClass::fatal_gFunc(__FILE__, __LINE__, 'Clone is not allowed.');
    }

    public static function getScriptTime_gFunc()
    {
        $m_pVar = main_gClass::getObject_gFunc();

        $currentTime_pVar = debug_gClass::getMicrotime_gFunc();
        return($currentTime_pVar - $m_pVar->scriptStartTime_pVar);
    }

    public static function getConfigVar_gFunc($varName_pVar, $sectionName_pVar = '')
    {
        $m_pVar = main_gClass::getObject_gFunc();

        if($m_pVar->config_gVar == false) return(false);
        return($m_pVar->config_gVar->getVar_gFunc($varName_pVar, $sectionName_pVar));
    }

    public static function getGuestDefaultRights_gFunc()
    {
        $m_pVar = main_gClass::getObject_gFunc();

        if($m_pVar->config_gVar == false) return(false);
        return($m_pVar->config_gVar->getGuestDefaultRights_gFunc());
    }

    public static function getUserDefaultRights_gFunc()
    {
        $m_pVar = main_gClass::getObject_gFunc();

        if($m_pVar->config_gVar == false) return(false);
        return($m_pVar->config_gVar->getUserDefaultRights_gFunc());
    }

    public static function getConfigSectionVar_gFunc($sectionName_pVar)
    {
        $m_pVar = main_gClass::getObject_gFunc();

        if($m_pVar->config_gVar == false) return(false);
        return($m_pVar->config_gVar->getVar_gFunc($sectionName_pVar));
    }

    public static function initInputs_gFunc()
    {
        if(self::$_REQUEST_pVar === false) {
            // inicializacia
            self::$_REQUEST_pVar = $_REQUEST;
            self::$_GET_pVar = $_GET;
            self::$_POST_pVar = $_POST;
            self::$_SERVER_pVar = $_SERVER;
            self::$_ENV_pVar = $_ENV;
            self::$_FILES_pVar = $_FILES;
        }
    }

    public static function initRights_gFunc($refresh_pVar = false)
    {
        $obj_pVar = self::getObject_gFunc();
        $obj_pVar->config_gVar->initRights_gFunc($refresh_pVar);
    }

    public static function initInputsSession_gFunc()
    {
        $ssid_pVar = session()->getId();
        //if(self::$_SESSION_NAME_pVar != $ssid_pVar) {
        //    self::$_SESSION_pVar = $_SESSION;
        //    self::$_SESSION_NAME_pVar = $ssid_pVar;
        //}
        if(strlen($ssid_pVar)) {
            self::$_SESSION_pVar = session()->all();
            self::$_SESSION_NAME_pVar = $ssid_pVar;
        }
    }

    public static function setSessionData_gFunc($varName_pVar, $varValue_pVar, $unslash_pVar = false, $decodeTags_pVar = false)
    {
        self::setPhpSessionVar_gFunc($varName_pVar, $varValue_pVar, $unslash_pVar, $decodeTags_pVar);
    }

    public static function setPhpSessionVar_gFunc($varName_pVar, $varValue_pVar, $unslash_pVar = false, $decodeTags_pVar = false)
    {
        if($unslash_pVar) {
            $varValue_pVar = string_gClass::stripSlashes_magic_gFunc($varValue_pVar);
        }
        if($decodeTags_pVar) {
            $varValue_pVar = htmlspecialchars_decode($varValue_pVar, ENT_QUOTES);
        }

        session()->put($varName_pVar, $varValue_pVar);
        self::$_SESSION_pVar[$varName_pVar] = $varValue_pVar;
    }

    public static function unsetPhpSessionVar_gFunc($varName_pVar)
    {
        session()->forget($varName_pVar);
        unset($_SESSION[$varName_pVar]);
        unset(self::$_SESSION_pVar[$varName_pVar]);
    }

    public static function getSessionData_gFunc($varname_pVar, $defaultValue_pVar = null)
    {
        return session()->get($varname_pVar, $defaultValue_pVar);
        // return(self::getInputVar_gFunc($varname_pVar, self::SRC_SESSION_pVar, false, $defaultValue_pVar, true, true));
    }

    public static function getServerVar_gFunc($varname_pVar, $disable_tags_pVar=true, $pregExp_pVar = false, $defaultValue_pVar = null, $enableSlashes_pVar = false)
    {
        return(self::getInputString_gFunc($varname_pVar, self::SRC_SERVER_pVar, $pregExp_pVar, $defaultValue_pVar, false, $enableSlashes_pVar));
    }

    public static function getInputBoolean_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $defaultValue_pVar = false)
    {
        $ret_pVar = self::getInputVar_gFunc($varname_pVar, $source_pVar, false, $defaultValue_pVar, true);
        if($ret_pVar === null || $ret_pVar === false) {
            return(false);
        }
        $ret_pVar = strtolower($ret_pVar);
        if($ret_pVar === 'off'
            || $ret_pVar === 'false'
            || $ret_pVar === 'no'
            || $ret_pVar === 'nie'
            || $ret_pVar === '0') {
            return(false);
        }
        else {
            return(true);
        }
    }

    public static function getInputInteger_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $defaultValue_pVar = null)
    {
        $ret_pVar = self::getInputVar_gFunc($varname_pVar, $source_pVar, false, $defaultValue_pVar, true);
        if($ret_pVar === null) {
            return(null);
        }
        if($ret_pVar === $defaultValue_pVar || !strlen($ret_pVar)) {
            return($defaultValue_pVar);
        }
        $ret_pVar = (int)$ret_pVar;
        return($ret_pVar);
    }

    public static function getInputFloat_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $defaultValue_pVar = null)
    {
        $ret_pVar = self::getInputVar_gFunc($varname_pVar, $source_pVar, false, $defaultValue_pVar, true);
        if($ret_pVar === null) {
            return(null);
        }
        if($ret_pVar === $defaultValue_pVar || !strlen($ret_pVar)) {
            return($defaultValue_pVar);
        }
        $ret_pVar = (float)$ret_pVar;
        return($ret_pVar);
    }

    public static function getInputString_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $pregExp_pVar = false, $defaultValue_pVar = null, $enableHtml_pVar = false, $enableSlashes_pVar = false)
    {
        if($varname_pVar === 'doc' && self::$doc_pVar !== false) {
            return(self::$doc_pVar); // vraciam hodnotu upraenu o lng prefix
        }
        $ret_pVar = self::getInputVar_gFunc($varname_pVar, $source_pVar, $pregExp_pVar, $defaultValue_pVar, $enableHtml_pVar, $enableSlashes_pVar);
        return($ret_pVar);
    }

    public static function getInputSqlString_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $pregExp_pVar = false, $defaultValue_pVar = null, $enableHtml_pVar = false)
    {
        $ret_pVar = self::getInputVar_gFunc($varname_pVar, $source_pVar, $pregExp_pVar, $defaultValue_pVar, $enableHtml_pVar, true);
        return($ret_pVar);
    }

    public static function getInputArray_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $pregExp_pVar = false, $defaultValue_pVar = null, $enableHtml_pVar = false, $enableSlashes_pVar = false)
    {
        $ret_pVar = self::getInputVar_gFunc($varname_pVar, $source_pVar, false, null, true, true);
        if(!is_array($ret_pVar)) {
            return($defaultValue_pVar);
        }

        // osetrim regularom
        if($pregExp_pVar !== false) {
            foreach ($ret_pVar as $k_pVar=>$v_pVar) {
                if(!preg_match($pregExp_pVar, $v_pVar)) {
                    unset($ret_pVar[$k_pVar]);
                }
            }
        }

        return($ret_pVar);
    }

    public static function getInputVarNames_gFunc($source_pVar = self::SRC_REQUEST_pVar)
    {
        if(self::$_REQUEST_pVar === false) {
            self::initInputs_gFunc();
        }
        if(self::$_SESSION_pVar === false) {
            self::initInputsSession_gFunc();
        }

        switch ($source_pVar) {
            case self::SRC_SERVER_pVar:
                return(array_keys(self::$_SERVER_pVar));
            case self::SRC_SESSION_pVar:
                return(array_keys(self::$_SESSION_pVar));
            case self::SRC_GET_pVar:
                return(array_keys(self::$_GET_pVar));
            case self::SRC_POST_pVar:
                return(array_keys(self::$_POST_pVar));
            case self::SRC_COOKIE_pVar:
                return(array_keys($_COOKIE));
            case self::SRC_REQUEST_pVar:
                return(array_keys(self::$_REQUEST_pVar));
            case self::SRC_GETPOST_pVar:
                $ret_pVar = array_keys(self::$_GET_pVar);
                $ret1_pVar = array_keys(self::$_POST_pVar);
                $ret_pVar = array_merge($ret_pVar, $ret1_pVar);
                return(array_unique($ret_pVar));
            default:
                return(array());
        }
    }

    private static function getInputVar_gFunc($varname_pVar, $source_pVar = self::SRC_REQUEST_pVar, $pregExp_pVar = false, $defaultValue_pVar = null, $enableHtml_pVar = false, $enableSlashes_pVar = false)
    {
        if(self::$_REQUEST_pVar === false) {
            self::initInputs_gFunc();
        }
        if(self::$_SESSION_pVar === false) {
            self::initInputsSession_gFunc();
        }

        switch ($source_pVar) {
            case self::SRC_SERVER_pVar:
                if(isset(self::$_SERVER_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_SERVER_pVar[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            case self::SRC_SESSION_pVar:
                if(isset(self::$_SESSION_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_SESSION_pVar[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            case self::SRC_GET_pVar:
                if(isset(self::$_GET_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_GET_pVar[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            case self::SRC_POST_pVar:
                if(isset(self::$_POST_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_POST_pVar[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            case self::SRC_COOKIE_pVar:
                if(isset($_COOKIE[$varname_pVar])) {
                    $ret_pVar = $_COOKIE[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            case self::SRC_REQUEST_pVar:
                if(isset(self::$_REQUEST_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_REQUEST_pVar[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            case self::SRC_GETPOST_pVar:
                if(isset(self::$_POST_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_POST_pVar[$varname_pVar];
                    break;
                }
                if(isset(self::$_GET_pVar[$varname_pVar])) {
                    $ret_pVar = self::$_GET_pVar[$varname_pVar];
                    break;
                }
                $ret_pVar = $defaultValue_pVar;
            default:
                $ret_pVar = $defaultValue_pVar;
        }

        // osetrim regularom
        if($pregExp_pVar !== false) {
            if(!preg_match($pregExp_pVar, $ret_pVar)) {
                return($defaultValue_pVar);
            }
        }

        if(is_null($ret_pVar) || is_bool($ret_pVar) || is_numeric($ret_pVar)) {
            return($ret_pVar);
        }

        if(!$enableHtml_pVar) {
            if(is_array($ret_pVar)) { // osetri iba 1D a 2D polia, ostatne nie je implementovane zatial.... :(
                reset($ret_pVar);
                while(list($k,$v) = each($ret_pVar)) {
                    $ret_pVar[$k]=htmlspecialchars($ret_pVar[$k], ENT_QUOTES);
                }
            }
            else {
                $ret_pVar=htmlspecialchars($ret_pVar, ENT_QUOTES);
            }
        }

        if(!$enableSlashes_pVar) {
            $ret_pVar = string_gClass::addSlashes_magic_gFunc($ret_pVar);
        }
        return($ret_pVar);
    }

    public static function getInputFile($uploadFileName_pVar)
    {
        if(isset(self::$_FILES_pVar[$uploadFileName_pVar])) {
            return(self::$_FILES_pVar[$uploadFileName_pVar]);
        }
        return(false);
    }

    public static function terminate_gFunc()
    {
        if(modules_gClass::isModuleLoaded_gFunc('db')) {
            mysql_connection_gClass::disconnectAll_gFunc(__FILE__, __LINE__);
        }
        error_gClass::restoreErrorHandler_gFunc();

        if(self::$sleep_delayed_pVar) {
            log_gClass::write_gFunc('SLEEPING', self::$sleep_delayed_pVar);
            sleep(self::$sleep_delayed_pVar);
        }

        //log_gClass::firePHPFlushGroups_gFunc();

        $output_pVar = ob_get_contents();
        ob_end_clean();

        $length_pVar = 0;

        if(ob_cache_gClass::isBuffers_gFunc()) {
            $o_pVar = explode('_OB_BUFFER_', $output_pVar);
            unset($output_pVar);

            foreach($o_pVar as $k_pVar=>$v_pVar) {
                if(!$k_pVar) {
                    echo $v_pVar;
                    $length_pVar += string_gClass::getStringWidth_gFunc($v_pVar);
                }
                else {
                    $p_pVar = strpos($v_pVar, '_');
                    $uid = substr($v_pVar, 0, $p_pVar);
                    $length_pVar += ob_cache_gClass::flushBuffer_gFunc($uid);
                    $str_pVar = substr($v_pVar, $p_pVar + 1);
                    echo $str_pVar;
                    $length_pVar += string_gClass::getStringWidth_gFunc($str_pVar);
                }
            }
            ob_cache_gClass::destroyBuffers_gFunc();
        }
        else {
            echo $output_pVar;
            $length_pVar = string_gClass::getStringWidth_gFunc($output_pVar);
        }

        log_gClass::write_gFunc('TOTAL_BYTES', $length_pVar);
        log_gClass::write_gFunc('MEMORY_REAL_USAGE', memory_get_peak_usage(true));
        log_gClass::write_gFunc('MEMORY_USAGE', memory_get_peak_usage());
        log_gClass::write_gFunc('TIME_TOTAL', self::getScriptTime_gFunc());
        log_gClass::close_gFunc();
    }
    /*
        static private function countBytes_gFunc(&$str_pVar)
        {
            $position_pVar = 0;
            $step_pVar = 10000;

            if(!isset($str_pVar[0])) {
                return(0);
            }
            while(1) {
                if(isset($str_pVar[$position_pVar + $step_pVar])) {
                    $position_pVar += $step_pVar;
                    continue;
                }
                else {
                    if($step_pVar == 1) {
                        return($position_pVar + 1);
                    }
                    $step_pVar = ceil($step_pVar/2);
                }
            }
        }
    */
    static public function sleep_delayed($sec_pVar)
    {
        self::$sleep_delayed_pVar += $sec_pVar;
    }

    static public function getPathForCache_gFunc()
    {
        return \Illuminate\Support\Facades\Storage::disk('truengine-legacy-data')->path('.cache/');
    }

    public function init()
    {
        // filesafe init
        fileSafe_gClass::register_gFunc();

        // nacitam config
        $this->config_gVar = new config_gClass();
        $this->config_gVar->load_gFunc();
        $this->config_gVar->initTimezone();
        $this->config_gVar->initRights_gFunc();

        // inicializujem error reporting podla configu
        error_gClass::init();

        // otvorim logovaci subor
        log_gClass::write_gFunc('start', false, true);

        // zistim ci je modul _loader registrovany
        if(!modules_gClass::isModuleRegistred_gFunc('_loader', true, error_gClass::E_FATAL )) {
            self::terminate_gFunc();
            return;
        }

        // debug on/off
        if($this->config_gVar->getVar_gFunc('enabled', 'debug')) {
            debug_gClass::enableDebug_gFunc();
        }

        // $this->handleRequestInstallApp();
        // $this->handlemaintainanceMode();
        // $this->handleCdoukChecker();
        // $this->initSessionOptions();
        // $this->handleCronBackslashCleaner();
        // $this->httpsCheck();
        // $this->handleSeoUrlRedirect(); // asi nepotrebujem

        $this->initPcid();


    }

    private function setLanguageFromRequest()
    {
        $language_pVar = self::getInputSqlString_gFunc('language', self::SRC_REQUEST_pVar);
        if(strlen($language_pVar)) {
            /**
             * Ak nastavujem language z requestu, tak hodnotu predhodim do session, a pouzije sa
             * ak url neobsahuje language (v url ma prednost).
             * Taketo nastavovanie je vyhodne len ak nechcem pouzit jazyk v url, a ni domenu.
             * (teda prepinac pomocou ?language=sk)
             */
            self::setPhpSessionVar_gFunc('language_pVar', $language_pVar);
        }
    }

    private function processDocumentNotDocumentType($doc_pVar)
    {
        /// nie je dokument
        // vytvorim session
        //$this->session_gVar = session_gClass::getSessionObject_gFunc();
        $ret404_pVar = true;

        $access = false;
        if(!session_gClass::userHasRightsInfo_gFunc(s_logged_on)) {
            $fileName_pVar = self::getFileNameByDocName_gFunc($doc_pVar);
            $p = explode('document_kega/', $fileName_pVar);
            if(isset($p[1])) {
                $access = \Mixedtype\Mixedtype\Components\Media::isPathAccessible($p[1]);
            }
        }


        if($access || session_gClass::userHasRightsInfo_gFunc(s_logged_on)) {
            if(strpos($doc_pVar, './') !== false || strpos($doc_pVar, '..') !== false) {
                error_gClass::error_gFunc(__FILE__, __LINE__, $doc_pVar);
            }
            else {
                $fileName_pVar = self::getFileNameByDocName_gFunc($doc_pVar);

                if (!empty($fileName_pVar)) {
                    if(file_exists($fileName_pVar)) {

                        $fileContent_pVar = file_get_contents($fileName_pVar);
                        $contentType_pVar = main_gClass::getMimeType_pVar($fileName_pVar);

                        if($contentType_pVar !== 'application/x-shockwave-flash'
                            && $contentType_pVar !== 'video/x-flv') {
                            $http_user_agent_pVar = main_gClass::getServerVar_gFunc('HTTP_USER_AGENT');
//								if (eregi('MSIE', $http_user_agent_pVar) && eregi('5.5', $http_user_agent_pVar))
//								{
//									header('Content-disposition: filename=' . basename($fileName_pVar));
//								}
//								else
//								{
                            \App\Legacy\LegacyApp::setHeader('Content-disposition', 'attachment; filename=' . basename($fileName_pVar));
//								}
                        }

                        \App\Legacy\LegacyApp::setHeader('Content-type', $contentType_pVar);
                        \App\Legacy\LegacyApp::setHeader('Content-Transfer-Encoding', 'binary');
                        \App\Legacy\LegacyApp::setHeader('Content-Length', filesize($fileName_pVar));
                        \App\Legacy\LegacyApp::setHeader('Connection', 'close');
                        echo $fileContent_pVar;
                        $ret404_pVar = false;
                    }
                }
            }
        }
        if($ret404_pVar) {
            header("HTTP/1.0 404 Not Found");
            echo "404 Not Found";
        }
    }

    private function processDocumentIsDocumentType($doc_pVar)
    {
        list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = self::removeLanguagePrefixFromDocName_gFunc($doc_pVar);

        $lngSectionName_pVar = strtolower('language_' . $lngStr_pVar);
        $lngSection_pVar = self::getConfigSectionVar_gFunc($lngSectionName_pVar);

// commented 2024-08-06 - multisite
//        /**
//         * dd($lngSection_pVar);
//         *
//         * array:3 [▼
//         *      "name" => "slovensky"
//         *      "url_prefix" => "1"
//         *      "domain" => "kega.sk"
//         * ]
//         *
//         */
//
//        /*** LANGUAGE REDIRECT, or add language prefix pre self::$doc_pVar  ***/
//
//        if($lngSection_pVar !== null) {
//            $language_pVar = $lngStr_pVar;
//            if(!$prefixEnabled_pVar) {
//                $lngDomain_pVar = self::getConfigVar_gFunc('domain', $lngSectionName_pVar);
//                if($lngDomain_pVar) {
//                    $p_pVar = strpos($lngDomain_pVar, '/');
//                    if($p_pVar !== false) {
//                        $lngDomain2_pVar = substr($lngDomain_pVar, 0, $p_pVar);
//                    }
//                    else {
//                        $lngDomain2_pVar = $lngDomain_pVar;
//                    }
//                    if($lngDomain2_pVar == self::getServerVar_gFunc('SERVER_NAME')) {
//                        // redirectujem na tuto domenu, takze odstranim lng prefix, aby som sa nezacyklil
//                        $doc_pVar = $docx_pVar;
//                    }
//
//                    $reirectUrl_pVar = 'http:/'.'/' . $lngDomain_pVar;
//                    if(substr($reirectUrl_pVar, -1) != '/') {
//                        $reirectUrl_pVar .= '/';
//                    }
//                    if(strpos($reirectUrl_pVar, '?') !== false) {
//                        $reirectUrl_pVar .= '&';
//                    }
//                    else {
//                        $reirectUrl_pVar .= '?';
//                    }
//                    $reirectUrl_pVar .= 'doc=' . $doc_pVar;
//                    if(substr($reirectUrl_pVar, -1) != '/') {
//                        $reirectUrl_pVar .= '/';
//                    }
//                }
//                //dd($reirectUrl_pVar, __LINE__);
//                header('HTTP/1.0 301 Moved Permanently');
//                header('Location: ' . $reirectUrl_pVar);
//                header("Connection: close");
//                echo 'Permanently moved to <a href="' . $reirectUrl_pVar . '">' . $reirectUrl_pVar . '</a>';
//                log_gClass::write_gFunc('LANGUAGE_REDIRECT1', 'HTTP/1.0 301 Moved Permanently ' . $reirectUrl_pVar);
//                self::terminate_gFunc();
//                exit;
//            }
//            else {
//                // prefix je pritomny, neodstranujem ho
//            }
//        }
//        else { // prefix nie je
//            // pridam lng prefix
//            list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = self::addLanguagePrefixToDocName_gFunc($doc_pVar);
//            $language_pVar = $lngStr_pVar;
//            if(!$prefixEnabled_pVar) {
//                // nema byt prefix, teda request bol spravny.. Pridam prefix
//                $doc_pVar = $lngStr_pVar . '/' . $docx_pVar;
//            }
//            else {
//                // prefix ma byt, cize request bol nespravny a musim spravit redirect
//                $docx_pVar = str_replace('index.php', '', $docx_pVar);
//                $reirectUrl_pVar = self::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . '?doc=' . $lngStr_pVar . '/' . $docx_pVar;
//                //dd($reirectUrl_pVar, __LINE__);
//                header('HTTP/1.0 301 Moved Permanently');
//                header('Location: ' . $reirectUrl_pVar);
//                header("Connection: close");
//                echo 'Permanently moved to <a href="' . $reirectUrl_pVar . '">' . $reirectUrl_pVar . '</a>';
//                log_gClass::write_gFunc('LANGUAGE_REDIRECT2', 'HTTP/1.0 301 Moved Permanently ' . $reirectUrl_pVar);
//                self::terminate_gFunc();
//                exit;
//            }
//        }


        // nastavim staticku vlastnost pre dalsie pouzitie
        self::$doc_pVar = $doc_pVar;

        if(isset($_REQUEST['ajax']) && $_REQUEST['ajax'] === 'true') {
            $language_pVar = session()->get('language_pVar', $lngStr_pVar);
        }
        else {
            $language_pVar = \Mixedtype\Multisite\Multisite::getLanguage(request());
        }




        // aktualizujem language v session
        self::setPhpSessionVar_gFunc('language_pVar', $language_pVar);

        // ak som presmeroval z HTTP na HTTPS, pregenerujem php session id
//        if(self::getInputBoolean_gFunc('regenerate_php_session', self::SRC_SESSION_pVar)) {
//            main_gClass::regenerateSessionId_gFunc();
//            self::setPhpSessionVar_gFunc('regenerate_php_session', false);
//        }

        // vytvorim session = AUTENTIFIKACIA !!!
        //$this->session_gVar = session_gClass::getSessionObject_gFunc();

        //echo $this->session_gVar->getClassName_gFunc();


        if(main_gClass::getInputString_gFunc('doc_edit', self::SRC_GETPOST_pVar) === 'true') {
            if(session_gClass::userHasRightsInfo_gFunc(s_document_edit)) {
                main_gClass::setPhpSessionVar_gFunc('doc_edit_mode', true);
            }
        }
        elseif(main_gClass::getInputString_gFunc('doc_edit', self::SRC_GETPOST_pVar) === 'false') {
            main_gClass::setPhpSessionVar_gFunc('doc_edit_mode', false);
        }

        //-layout
        $defaultLayout_pVar = main_gClass::getLayoutName();
        if(in_array($defaultLayout_pVar, array('pdf'))) {
            callStack_gClass::printStart_gFunc();
            \App\Legacy\LegacyApp::setPdfExport();
        }


        // pridam licencny obrazok
        if(((rand(0,100) % 100) < 10)) {
            $licenseContent_pVar = license_gClass::getCheckCode_gFunc();
        }
        else {
            $licenseContent_pVar = false;
        }



        if(session_gClass::ajaxRequest_gFunc()) {
            switch(session_gClass::ajaxFormat_gFunc()) {
                case 'xml':
                    \App\Legacy\LegacyApp::setHeader('Content-Type', 'text/xml; charset=utf-8');
                    echo '<' . '?xml version="1.0" encoding="utf-8" ?>' . NL . '<response>';
                    break;
                case 'xhtml':
                    \App\Legacy\LegacyApp::setHeader('Content-Type', 'text/xml; charset=utf-8');
                    echo '<' . '?xml version="1.0" encoding="utf-8" ?>' . NL . '<response><html>';
                    break;
                case 'text':
                    \App\Legacy\LegacyApp::setHeader('Content-Type', 'text/plain; charset=utf-8');
                    break;
                case 'json':
                    \App\Legacy\LegacyApp::setHeader('Content-Type', 'application/json; charset=utf-8');
                    break;
            }
        }
        else {
            \App\Legacy\LegacyApp::setHeader('Content-Type', 'text/html; charset=utf-8');
        }

        self::$mainVars_pVar['doc'] = self::$doc_pVar;
        self::$mainVars_pVar['REQUEST_URI'] = 'te://' . htmlspecialchars_decode(main_gClass::getServerVar_gFunc('REQUEST_URI', false));
        $lngData_pVar = self::addLanguagePrefixToDocName_gFunc('/');
        self::$mainVars_pVar['docroot'] = self::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . ($lngData_pVar[2]?$lngData_pVar[0].'/':'');
        ob_start();
        callStack_gClass::resetStacks_gFunc();
        varStack_gClass::$vars['requested_doc'] = self::$doc_pVar;
        varStack_gClass::registerGlobal_gFunc('requested_doc');

        // push layout
        callStack_gClass::pushLayout_gFunc($defaultLayout_pVar);

        //dd(self::$doc_pVar, $licenseContent_pVar, count(self::$mainVars_pVar)?self::$mainVars_pVar:false);

        callStack_gClass::execute_gFunc(self::$doc_pVar, $licenseContent_pVar, count(self::$mainVars_pVar)?self::$mainVars_pVar:false);
        callStack_gClass::destroyLastStack_gFunc();

        $output_pVar = ob_get_contents();
        ob_end_clean();
        self::sendHttpErrCodeHeader_gFunc(self::$doc_pVar);

        $output_pVar = self::iconv_gFunc($output_pVar);

        if(self::$trashLevel_pVar) {
            $p_pVar = strpos($output_pVar, '<te:trash />');
            if($p_pVar !== false) {
                $output_pVar = substr($output_pVar, 0, $p_pVar);
            }
        }

        if(session_gClass::ajaxRequest_gFunc()) {
            switch(session_gClass::ajaxFormat_gFunc()) {
                case 'xml':
                    echo $output_pVar;
                    echo '</response>';
                    break;
                case 'xhtml':
                    echo htmlspecialchars($output_pVar);
                    echo '</html></response>';
                    break;
                case 'text':
                case 'json':
                    echo $output_pVar;
                    break;
            }
        }
        else {
            echo $output_pVar;
        }
    }


    static public function getLayoutName()
    {
        $defaultLayout_pVar = \Mixedtype\Layouts\LayoutManager::getTheme();
        $defaultLayout_pVar = trim($defaultLayout_pVar, "\n\r /\\");

        if(!empty($defaultLayout_pVar)) {
            $defaultLayout_pVar = '.layouts/' . main_gClass::getLanguage_gFunc() . '_' . $defaultLayout_pVar;
        }

        return $defaultLayout_pVar;
    }

    public function go()
    {
        ob_start();
        ob_start();
        // set language from request
        $this->setLanguageFromRequest();

        // get document and detect document type
        $doc_pVar = self::getInputString_gFunc('doc');
        $doc_pVar = str_replace('---', ' ', $doc_pVar);
        $docType_pVar = self::getDocNameType_gFunc($doc_pVar);

        if($doc_pVar === 'sk/upload-file') {
//            if(!$this->uploadFile()) {
//                \App\Legacy\LegacyApp::setHttpStatus(404);
//                header("HTTP/1.0 404 Not Found");
//            }
        }
        else {

            if ($docType_pVar === 'document') {
                $this->processDocumentIsDocumentType($doc_pVar);
            } else {
                $this->processDocumentNotDocumentType($doc_pVar);
            }
        }

        self::terminate_gFunc();

        $legacyResult = ob_get_contents();
        ob_end_clean();
//dd(__LINE__, \App\Legacy\LegacyApp::isPdfExport());
        if(\App\Legacy\LegacyApp::isPdfExport()) {
            $response = response($legacyResult, 200);
            $response->header('Content-Type', 'application/pdf');
            $response->header('Content-Disposition', 'attachment; filename="export.pdf"');
            return $response;
        }
        else {
            $response = response($legacyResult, \App\Legacy\LegacyApp::getHttpStatus());
            foreach (\App\Legacy\LegacyApp::getHeaders() as $header => $value) {
                $response->header($header, $value);
            }
            return $response;
        }
    }

    private function uploadFile()
    {
        if(!session_gClass::userHasRightsAccess_gFunc(\s_test_edit_properties)) {
            return false;
        }

        foreach ($_FILES as $key=>$file) {
            $doc_root_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
            $fName_pVar = $doc_root_pVar . '.files/paste/' . $file['name'];
            if(!move_uploaded_file($file['tmp_name'], $fName_pVar)) {
                return false;
            }
        }

        return true;
    }

    private function handleRequestInstallApp()
    {
        $installKey_pVar = self::getInputString_gFunc('install', self::SRC_REQUEST_pVar);
        if(!empty($installKey_pVar) && $installKey_pVar === self::getConfigVar_gFunc('install_key', 'main')) {
            $install_enabled_time_pVar = strtotime(self::getConfigVar_gFunc('install_enabled', 'main'));
            \App\Legacy\LegacyApp::setHeader('Content-Type', 'text/html; charset="utf-8"');
            if($install_enabled_time_pVar < time() - 1800 || $install_enabled_time_pVar > time() + 1800) {

                echo string_gClass::get('str___loader_err_installation_disabled_sVar');
            }
            else {
                log_gClass::write_gFunc('SYSTEM_DOCUMENT', '1');
                if(include_once('install/_init.php')) {
                    echo string_gClass::get('str___loader_installation_completted_sVar');
                }
                else {
                    echo string_gClass::get('str___loader_err_installation_error_sVar');
                }
            }
            self::terminate_gFunc();
            return;
        }
    }

    private function handleMaintainanceMode()
    {
        if(self::getInputString_gFunc('maintainance', self::SRC_REQUEST_pVar) == 1) {
            log_gClass::write_gFunc('SYSTEM_DOCUMENT', '1');
            $deleteOld_pVar = true;
            if(modules_gClass::isModuleRegistred_gFunc('maintainance')) {
                if(!maintainance_gFunc::run_maintainance_gFunc()) {
                    $deleteOld_pVar = false;
                }
            }
            // vymazem stare logy
            if($deleteOld_pVar) {
                log_gClass::cleanOld_gFunc();
            }
            self::terminate_gFunc();
            return;
        }
    }

    private function handleCdoukChecker()
    {
        if(self::getInputString_gFunc('cron_cdouk_checker', self::SRC_REQUEST_pVar) == 1) {
            if(modules_gClass::isModuleRegistred_gFunc('cdouk')) {
                $cron_pVar = new cdouk_auto_checker_gClass();
                $cron_pVar->_run_gFunc();
            }
            self::terminate_gFunc();
            return;
        }
    }

    private function initSessionOptions()
    {
        // nastavim parametre pre session
        ini_set('session.name', 'teid');
        ini_set('session.use_cookies', '1');
        ini_set('session.use_only_cookies', '1');
        ini_set('session.referer_check', main_gClass::getServerVar_gFunc('SERVER_NAME'));
        session_set_cookie_params(0, main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar'), null, false, true);
    }

    private function handleCronBackslashCleaner()
    {
        if(self::getInputString_gFunc('cron_backslash_cleaner', self::SRC_REQUEST_pVar) == 1) {
            $cron_pVar = new cron_backslash_cleaner_gClass();
            $cron_pVar->_run_gFunc();
            self::terminate_gFunc();
            return;
        }
    }

    private function handleSeoUrlRedirect()
    {
        // SEO URL REDIRECT (az po kontrole HTTTPS)
        /**
         * a neviem kedy sa to vola, preoze rewrite mi vzdy nastavuje SEO=ON (seo=true)
         */
        if(!self::getInputBoolean_gFunc('seo')) {
            if(self::getConfigVar_gFunc('seo_url', 'main') && !session_gClass::ajaxRequest_gFunc()) {
                $reirectUrl_pVar = self::getInputString_gFunc('doc');
                if(isset($reirectUrl_pVar[0])) {
                    $reirectUrl_pVar = self::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . self::getInputString_gFunc('doc');
                    header('HTTP/1.0 301 Moved Permanently');
                    header('Location: ' . $reirectUrl_pVar);
                    header('Cache-Control: public, max-age=600');
                    header("Connection: close");
                    echo 'Permanently moved to <a href="' . $reirectUrl_pVar . '">' . $reirectUrl_pVar . '</a>';
                    log_gClass::write_gFunc('SEO_REDIRECT1', 'HTTP/1.0 301 Moved Permanently ' . $reirectUrl_pVar);
                    self::terminate_gFunc();
                    exit;
                }
            }
        }
    }

    private function httpsCheck()
    {
        // ak som prihlaseny, skontrolujem HTTPS
        if(main_gClass::getInputBoolean_gFunc('loggedon_pVar', main_gClass::SRC_COOKIE_pVar)) {
            if(modules_gClass::isModuleRegistred_gFunc('https')) {
                if(!https_gClass::check_gFunc()) {
                    self::terminate_gFunc();
                    exit;
                }
                https_gClass::secureSession_gFunc();
            }
        }
    }

    private function initPcid()
    {
        $pcid_pVar = self::getPcId_gFunc();
        if(!strlen($pcid_pVar) || rand(0,15) == 1) {
            if(!strlen($pcid_pVar)) {
                $pcid_pVar = sha1(time());
            }
            $_COOKIE['pcid'] = $pcid_pVar;
            setcookie('pcid', $pcid_pVar, time()+60*60*24*31*12*5, '/', null, null, true);
        }
    }

    static function iconv_gFunc($str_pVar, $outputCharset_pVar = false, $inputCharset_pVar = false)
    {
        if($inputCharset_pVar === false) {
            $inputCharset_pVar = 'utf-8';
        }
        if($outputCharset_pVar === false) {
            $outputCharset_pVar = 'utf-8';
        }

        if(strtolower($inputCharset_pVar) !== strtolower($outputCharset_pVar)) {
            $str_pVar = iconv($inputCharset_pVar, $outputCharset_pVar . '/'.'/TRANSLIT', $str_pVar);
        }
        return($str_pVar);
    }

    public static function getFileNameByDocName_gFunc($doc_pVar)
    {
        $files_pVar = main_gClass::getConfigSectionVar_gFunc('files');
        while($doc_pVar[0] == '/') {
            $doc_pVar = substr($doc_pVar, 1);
        }

        $fileName_pVar = false;
        $dname_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main') . $doc_pVar;

        foreach ($files_pVar as $k_pVar=>$v_pVar) {
            if(substr($dname_pVar, 0, strlen($v_pVar)) !== $v_pVar) {
                continue;
            }

            $file_pVar = db_public_gClass::files_get_file_name_gFunc($k_pVar, substr($dname_pVar, strlen($v_pVar)));
            if($file_pVar !== false) {
                $fullName = explode('/', $file_pVar['full_name']);
                $last = array_pop($fullName);
                $fileName_pVar = implode('/', $fullName) . '/' . $file_pVar['prefix'] . str_replace('.png', '.jpg', $last);

                $fileName_pVar = $v_pVar . $fileName_pVar;
                $file_pVar['location'] = $k_pVar;
                break;
            }
        }

        //dd($fileName_pVar, $file_pVar);


        if($fileName_pVar !== false && !file_exists($fileName_pVar)) {
            if(in_array(strtolower(substr($fileName_pVar, strrpos($fileName_pVar, '.') + 1)), array('jpg', 'png', 'gif', 'bmp', 'tif'))) {
                $origName_pVar = string_gClass::formatAsDirectory_gFunc(dirname($fileName_pVar)) . basename($file_pVar['full_name']);

                if(file_exists($origName_pVar) && modules_gClass::isModuleRegistred_gFunc('graphics')) {
                    $image_pVar = new gd_gClass();
                    $image_pVar->loadImage_gFunc($origName_pVar);
                    $image_pVar->initImage_gFunc($file_pVar['location']);
                }
            }
        }


        return str_replace('//', '/', $fileName_pVar);
    }

    public static function getSessionVars_gFunc()
    {
        $m_pVar =self::getObject_gFunc();
        if($m_pVar->session_gVar !== false) {
            return($m_pVar->session_gVar->getEnviroment_gFunc());
        }
    }

    public static function getInputKeys_gFunc()
    {
        $keys_pVar = array();
        foreach($_COOKIE as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 4) !== 'key_') {
                continue;
            }
            if(preg_match('/[0-9a-fA-F]{32}/', $v_pVar)) {
                $keys_pVar[] = $v_pVar;
            }
        }
        return($keys_pVar);
    }

    public static function makeUrl_gFunc($url_pVar, $escape_pVar = true)
    {
        $url_pVar = trim($url_pVar);
        if(empty($url_pVar)) {
            if($escape_pVar) {
                return(htmlspecialchars($url_pVar));
            }
            else {
                return($url_pVar);
            }
        }
        if(preg_match('/@\{.*\}/i', $url_pVar)) {
            // premenna v sablone
            return(PHP_OPEN . 'echo main_gClass::makeUrl_gFunc(\''. string_gClass::addSlashes_magic_gFunc($url_pVar) . '\''.($escape_pVar?'':', false').');' . PHP_CLOSE);
        }
        if(preg_match('/http\:\/\/.*|https\:\/\/.*|mailto\:.*|ftp\:\/\/.*|news\:.*|telnet\:\/\/.*|file\:\/\/.*|javascript\:.*|script\:.*/i', $url_pVar)) {
            // absolutna adresa
            $url_pVar = str_replace(' ', '---', $url_pVar);
            if($escape_pVar) {
                return(htmlspecialchars($url_pVar));
            }
            else {
                return($url_pVar);
            }
        }
        if($url_pVar[0] === '#') {
            if($escape_pVar) {
                return(htmlspecialchars($url_pVar));
            }
            else {
                return($url_pVar);
            }
        }

        if(substr($url_pVar, 0, 5) == 'te://') {
            $url_pVar = substr($url_pVar, 5);
            $url_pVar = str_replace(' ', '---', $url_pVar);
            if($escape_pVar) {
                return(htmlspecialchars($url_pVar));
            }
            else {
                return($url_pVar);
            }
        }

        $noDocument_pVar = false;
        $noLng_pVar = false;
        $docType_pVar = self::getDocNameType_gFunc($url_pVar);
        switch ($docType_pVar) {
            case 'document':
                break;
            case 'file':
                $noDocument_pVar = true;
                $noLng_pVar = true;
                break;
            case 'no_language':
                $noDocument_pVar = false;
                $noLng_pVar = true;
                break;
            case 'image':
                $noDocument_pVar = true;
                $noLng_pVar = false;
                break;
            default:
                break;
        }

        if(main_gClass::getConfigVar_gFunc('seo_url', 'main') || $noDocument_pVar) {
            if($url_pVar[0] !== '/') {
                $url_pVar = callStack_gClass::getDocPath_gFunc() . $url_pVar;
                if(!$noLng_pVar) {
                    $url_pVar = main_gClass::removeCurrentLanguagePrefixFromDocName_gFunc($url_pVar, !$noDocument_pVar);
                }
            }
            if($url_pVar[0] === '/' && isset($url_pVar[1])) {
                $url_pVar = substr($url_pVar, 1);
            }
            list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = self::addLanguagePrefixToDocName_gFunc($url_pVar);
            if(!$noLng_pVar && $prefixEnabled_pVar) {
                $url_pVar = $lngStr_pVar . '/' . $docx_pVar;
            }
            $url_pVar = main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . $url_pVar;
            $url_pVar = str_replace(' ', '---', $url_pVar);
            if($escape_pVar) {
                return(htmlspecialchars($url_pVar));
            }
            else {
                return($url_pVar);
            }
        }
        else {
            if($url_pVar[0] !== '/') {
                $url_pVar = callStack_gClass::getDocPath_gFunc() . $url_pVar;
                if(!$noLng_pVar) {
                    $url_pVar = main_gClass::removeCurrentLanguagePrefixFromDocName_gFunc($url_pVar);
                }
            }
            if($url_pVar[0] === '/' && isset($url_pVar[1])) {
                $url_pVar = substr($url_pVar, 1);
            }
            //if(is_array($url_pVar)) exit;
            list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = self::addLanguagePrefixToDocName_gFunc($url_pVar);
            if(!$noLng_pVar && $prefixEnabled_pVar) {
                $url_pVar = $lngStr_pVar . '/' . $docx_pVar;
            }
            $url_pVar = str_replace('?', '&', $url_pVar);
            $url_pVar = main_gClass::getConfigVar_gFunc('web_dir_gVar', 'runtime_pVar') . '?doc='. $url_pVar;
            $url_pVar = str_replace(' ', '---', $url_pVar);
            if($escape_pVar) {
                return(htmlspecialchars($url_pVar));
            }
            else {
                return($url_pVar);
            }
        }
    }


    public static function makePostUrl_gFunc($url_pVar)
    {
        $url_pVar = self::makeUrl_gFunc($url_pVar, false);

        $ret_pVar = array('action'=>'', 'vars'=>'');

        $components_pVar = parse_url($url_pVar);
        if(isset($components_pVar['path'])) {
            $ret_pVar['action'] = $components_pVar['path'];
        }
        else {
            $ret_pVar['action'] = $url_pVar;
        }

        $vars_pVar = array();
        $varsStr_pVar = '';
        if(isset($components_pVar['query'])) {
            parse_str($components_pVar['query'], $vars_pVar);
            foreach($vars_pVar as $k_pVar=>$v_pVar) {
                $varsStr_pVar .= '<input type="hidden" name="' . $k_pVar . '" value="' . $v_pVar . '" />';
            }
        }
        $ret_pVar['vars'] = $varsStr_pVar;
        return($ret_pVar);
    }

    public static function removeLanguagePrefixFromDocName_gFunc($doc_pVar)
    {
        $p_pVar = strpos($doc_pVar, '/');
        if($p_pVar) {
            $lngStr_pVar = substr($doc_pVar, 0, $p_pVar);
            $docx_pVar = substr($doc_pVar, $p_pVar + 1);
        }
        else {
            $lngStr_pVar = $doc_pVar;
            $docx_pVar = '';
        }
        $lngSectionName_pVar = strtolower('language_' . $lngStr_pVar);
        $lngSection_pVar = self::getConfigSectionVar_gFunc($lngSectionName_pVar);
        if($lngSection_pVar !== null) {
            $lngUrlPrefix_pVar = self::getConfigVar_gFunc('url_prefix', $lngSectionName_pVar);
            return(array($lngStr_pVar, $docx_pVar, $lngUrlPrefix_pVar));
        }
        return(array('', $doc_pVar, true));
    }

    public static function removeLngPrefix_gFunc($doc_pVar)
    {
        if(!strlen($doc_pVar)) {
            return($doc_pVar);
        }
        $prefix_pVar = '';
        if($doc_pVar[0] == '/') {
            $prefix_pVar = '/';
            $doc_pVar = substr($doc_pVar, 1);
        }
        $lng_pVar = substr($doc_pVar, 0,3);
        if($lng_pVar === 'sk/' || $lng_pVar === 'en/' || $lng_pVar === 'cz/') {
            $doc_pVar = substr($doc_pVar, 3);
        }
        $doc_pVar = $prefix_pVar . $doc_pVar;
        return($doc_pVar);
    }


    public static function removeCurrentLanguagePrefixFromDocName_gFunc($doc_pVar, $isDoc_pVar = true)
    {
        $currentPrefix_pVar = self::getInputString_gFunc('language_pVar', self::SRC_SESSION_pVar);
        $newDocName_pVar = $doc_pVar;
        if($newDocName_pVar[0] == '/') {
            $newDocName_pVar = substr($newDocName_pVar, 1);
        }
        if(strlen($newDocName_pVar) > strlen($currentPrefix_pVar)) {
            $currentPrefix_pVar .= '/';
        }
        $prefixLen_pVar = strlen($currentPrefix_pVar);

        if(substr($newDocName_pVar, 0, $prefixLen_pVar) == $currentPrefix_pVar) {
            $newDocName_pVar = substr($newDocName_pVar, $prefixLen_pVar);
        }

        if($newDocName_pVar[0] == '/') {
            $newDocName_pVar = substr($newDocName_pVar, 1);
        }
        if(!isset($newDocName_pVar[0])) {
            $newDocName_pVar='/';
        }
        if($isDoc_pVar && substr($newDocName_pVar, -1) !== '/') {
            $newDocName_pVar .= '/';
        }
        return($newDocName_pVar);
    }

    public static function addLngPrefixToDocName_gFunc($doc_pVar, $usePrefix_pVar = false)
    {
        list($lngStr_pVar, $docx_pVar, $prefixEnabled_pVar) = self::addLanguagePrefixToDocName_gFunc($doc_pVar, $usePrefix_pVar);
        if($prefixEnabled_pVar) {
            $docx_pVar = '/' . $lngStr_pVar . '/' . $docx_pVar;
        }
        return($docx_pVar);
    }

    public static function addLanguagePrefixToDocName_gFunc($doc_pVar, $usePrefix_pVar = false)
    {
        $prefixEnabled_pVar = true;

        list($lngPrefix_pVar, $docx_pVar) = self::removeLanguagePrefixFromDocName_gFunc($doc_pVar);
        if(strlen($docx_pVar) && $docx_pVar[0] == '/') {
            $docx_pVar = substr($docx_pVar, 1);
        }
        if($usePrefix_pVar !== false) {
            $lngPrefix_pVar = $usePrefix_pVar;
        }
        if(!strlen($lngPrefix_pVar)) {
            $lngPrefix_pVar = self::getInputString_gFunc('language_pVar', self::SRC_SESSION_pVar, '/[a-z]{0,3}/', false);
            if($lngPrefix_pVar === false) {
                $lngPrefix_pVar = self::getConfigVar_gFunc('default_language', 'main');
            }
        }
        $lngSectionName_pVar = strtolower('language_' . $lngPrefix_pVar);
        $lngSection_pVar = self::getConfigSectionVar_gFunc($lngSectionName_pVar);
        if($lngSection_pVar === null) {
            // nastavujem nedefinovany jazyk
            $lngPrefix_pVar = self::getConfigVar_gFunc('default_language', 'main');
            $lngSectionName_pVar = strtolower('language_' . $lngPrefix_pVar);
            $lngSection_pVar = self::getConfigSectionVar_gFunc($lngSectionName_pVar);
        }
        if($lngSection_pVar === null) {
            // nastavujem nedefinovany jazyk, ale neda sa uz nic robit...
        }
        else {
            $lngUrlPrefix_pVar = self::getConfigVar_gFunc('url_prefix', $lngSectionName_pVar);
            if(!$lngUrlPrefix_pVar) {
                $prefixEnabled_pVar = false;
            }
        }

        return(array($lngPrefix_pVar, $docx_pVar, $prefixEnabled_pVar));
    }

    static function getRequestedDocName_gFunc()
    {
        $doc_pVar = main_gClass::getInputString_gFunc('doc');
        if(!strlen($doc_pVar) || $doc_pVar[0] != '/') {
            $doc_pVar = '/' . $doc_pVar;
        }
        return($doc_pVar);
    }

    static function getPcId_gFunc()
    {
        $pcId_pVar = main_gClass::getInputString_gFunc('pcid', main_gClass::SRC_COOKIE_pVar, '/[a-hA-H0-9]{40}/i');
        return($pcId_pVar);
    }

    static function serializeRequestData_gFunc()
    {
        error_gClass::error_gFunc(__FILE__,__LINE__,'not implemented');
    }

    static function unserializeRequestData_gFunc()
    {
        error_gClass::error_gFunc(__FILE__,__LINE__,'not implemented');
    }

    static function isEncoded_gFunc()
    {
        if('isEncoded_pVar' === 'is'.'Encoded_pVar') {
            return(false);
        }
        else {
            return(true);
        }
    }

    static public function source_getData_gFunc($sourceClass_pVar, $sourceParams_pVar = array())
    {
        $moduleName_pVar = modules_gClass::findClass_gFunc($sourceClass_pVar);
        if($moduleName_pVar === false || !modules_gClass::isModuleRegistred_gFunc($moduleName_pVar)) {
            error_gClass::error_gFunc(__FILE__, __LINE__, $sourceData_pVar);
            return(array());
        }

        $source_pVar = new $sourceClass_pVar();
        $source_pVar->setParams_gFunc($sourceParams_pVar);
        return($source_pVar->handleAction_gFunc());
    }

    static public function setHttpErrCode_gFunc($docName_pVar, $code_pVar)
    {
        if(!empty($docName_pVar) && substr($docName_pVar, -1) !== '/') {
            $docName_pVar .= '/';
        }
        self::$http_codes_gFunc[$docName_pVar] = $code_pVar;
    }

    static private function sendHttpErrCodeHeader_gFunc($docName_pVar)
    {
        if(!empty($docName_pVar) && substr($docName_pVar, -1) !== '/') {
            $docName_pVar .= '/';
        }

        if(isset(self::$http_codes_gFunc[$docName_pVar])) {
            switch (self::$http_codes_gFunc[$docName_pVar])
            {
                case 404:
                    header("HTTP/1.0 404 Not Found");
                    break;
            }
        }
    }

    static public function move_uploaded_image_gFunc($upload_file_name_pVar, $ref_tag_pVar, $ref_id_pVar, $target_dir_index_pVar, $target_name_pVar = false, $upload_file_index_pVar = false)
    {
        return(self::move_uploaded_file_gFunc($upload_file_name_pVar, $ref_tag_pVar, $ref_id_pVar, $target_dir_index_pVar, $target_name_pVar, $upload_file_index_pVar, 'image'));
    }

    static public function move_uploaded_file_gFunc($upload_file_name_pVar, $ref_tag_pVar, $ref_id_pVar, $target_dir_index_pVar, $target_name_pVar = false, $upload_file_index_pVar = false, $fileType_pVar = 'file')
    {
        $ret_pVar = array('error'=>0, 'success'=>0, 'files' => array());

        $fileInfo_pVar = self::getInputFile($upload_file_name_pVar);
        //dd($fileInfo_pVar, $upload_file_name_pVar, $_FILES, ini_get('upload_max_filesize'));
        if(!is_array($fileInfo_pVar)) {
            // nezvacsim error++, lebo subor nebol uploadovany vobec
            //$ret_pVar['error']++;
            return($ret_pVar);
        }

        if(!is_array($fileInfo_pVar['error'])) {
            foreach ($fileInfo_pVar as $k_pVar=>$v_pVar) {
                $fileInfo_pVar[$k_pVar] = array(0 => $v_pVar);
            }
        }

        if($upload_file_index_pVar !== false) {
            if(!isset($fileInfo_pVar['error'][$upload_file_index_pVar])) {
                $ret_pVar['error']++;
                return($ret_pVar);
            }
            // zmazem vsetky ostatne zaznamy
            $fileInfo_pVar['error']=array($upload_file_index_pVar=>$fileInfo_pVar['error'][$upload_file_index_pVar]);
        }

        $target_dir_pVar = self::getConfigVar_gFunc($target_dir_index_pVar, 'files');
        if(empty($target_dir_pVar)) {
            $target_dir_pVar = $target_dir_index_pVar;
        }
        $target_dir_pVar = string_gClass::formatAsDirectory_gFunc($target_dir_pVar);

        foreach ($fileInfo_pVar['error'] as $key_pVar=>$file_error_pVar) {

            if($file_error_pVar == UPLOAD_ERR_NO_FILE) {
                continue;
            }
            if($file_error_pVar !== UPLOAD_ERR_OK) {
                $ret_pVar['error']++;
                continue;
            }

            // kontrola, ci je to spravny typ suboru
            switch ($fileType_pVar) {
                case 'image':
                    if(modules_gClass::isModuleRegistred_gFunc('graphics')) {
                        if(!gd_gClass::isImageName_gFunc($fileInfo_pVar['name'][$key_pVar])) {
                            $ret_pVar['error']++;
                            continue 2;
                        }
                    }
                    break;
                case 'file':
                    // vyhovuju vsetky subory
                    break;
                default:
                    $ret_pVar['error']++;
                    continue 2;
            }

            if($target_name_pVar !== false) {
                $tmp_target_name_pVar = $target_name_pVar;
            }
            else {
                $pathinfo_pVar = pathinfo($fileInfo_pVar['name'][$key_pVar]);
                $tmp_target_name_pVar = $pathinfo_pVar['basename'];
            }


            while(main_gClass::uploaded_file_exists_gFunc($target_dir_index_pVar, $target_dir_pVar, $tmp_target_name_pVar)) {
                $pathinfo_pVar = pathinfo($tmp_target_name_pVar);
                if($pathinfo_pVar['dirname'] === '.') {
                    unset($pathinfo_pVar['dirname']);
                }
                $basename_pVar = $pathinfo_pVar['filename'];
                if(substr($basename_pVar, -1) === ')') {
                    $p0_pVar = strrpos($basename_pVar, '(');
                    $n_pVar = substr($basename_pVar, $p0_pVar + 1, -1);
                    if(is_numeric($n_pVar)) {
                        $n_pVar = (int)$n_pVar + 1;
                        $tmp_target_name_pVar = '';
                        if(isset($pathinfo_pVar['dirname']) && !empty($pathinfo_pVar['dirname'])) {
                            $tmp_target_name_pVar .= string_gClass::formatAsDirectory_gFunc($pathinfo_pVar['dirname']);
                        }
                        $tmp_target_name_pVar .= substr($basename_pVar, 0, $p0_pVar) . '(' . $n_pVar . ')';
                        if(isset($pathinfo_pVar['extension'])) {
                            $tmp_target_name_pVar .= '.' . $pathinfo_pVar['extension'];
                        }
                    }
                    else {
                        $tmp_target_name_pVar = '';
                        if(isset($pathinfo_pVar['dirname']) && !empty($pathinfo_pVar['dirname'])) {
                            $tmp_target_name_pVar .= string_gClass::formatAsDirectory_gFunc($pathinfo_pVar['dirname']);
                        }
                        $tmp_target_name_pVar .= substr($basename_pVar, 0, $p0_pVar) . '(1)';
                        if(isset($pathinfo_pVar['extension'])) {
                            $tmp_target_name_pVar .= '.' . $pathinfo_pVar['extension'];
                        }
                    }
                }
                else {
                    $tmp_target_name_pVar = '';
                    if(isset($pathinfo_pVar['dirname']) && !empty($pathinfo_pVar['dirname'])) {
                        $tmp_target_name_pVar .= string_gClass::formatAsDirectory_gFunc($pathinfo_pVar['dirname']);
                    }
                    $tmp_target_name_pVar .= $basename_pVar . '(1)';
                    if(isset($pathinfo_pVar['extension'])) {
                        $tmp_target_name_pVar .= '.' . $pathinfo_pVar['extension'];
                    }
                }
            }

            $newPath_pVar = $target_dir_pVar . $tmp_target_name_pVar;


            if(modules_gClass::isModuleRegistred_gFunc('upload')) {
                if(upload_gClass::moveUploadedFile_gFunc($upload_file_name_pVar, $key_pVar, $target_dir_pVar, $target_name_pVar) === false) {
                    $ret_pVar['error']++;
                    continue;
                }
            }
            else {
                if(move_uploaded_file($fileInfo_pVar['tmp_name'][$key_pVar], $newPath_pVar) === false) {
                    $ret_pVar['error']++;
                    continue;
                }
            }

            // precitam copyright z requestu
            $copyright_pVar = self::getInputString_gFunc($upload_file_name_pVar . '_copyright');
            if(empty($copyright_pVar)) {
                $copyright_pVar = false;
            }

            $fileId_pVar = self::addFileToDb_gFunc($newPath_pVar, $target_dir_index_pVar, $ref_tag_pVar, $ref_id_pVar, $fileType_pVar, $tmp_target_name_pVar, $copyright_pVar);
            if(!$fileId_pVar) {
                $ret_pVar['error']++;
                continue;
            }

            // hotovo
            $ret_pVar['success']++;
            $ret_pVar['files'][] = array('name'=>$newPath_pVar, 'id'=>$fileId_pVar, 'file_name'=>$tmp_target_name_pVar);
        }
        return($ret_pVar);
    }

    static function addFileToDb_gFunc($newPath_pVar, $target_dir_index_pVar, $ref_tag_pVar, $ref_id_pVar, $fileType_pVar, $customName_pVar, $copyright_pVar = false)
    {
        $fileId_pVar = 0;
        // teraz zistim, ci dany subor uz neexistuje v databaze (podla md5)
        $original_md5_pVar = md5_file($newPath_pVar);
        $fileInfoOld_pVar = db_public_gClass::files_get_file_by_original_md5_gFunc($original_md5_pVar, $target_dir_index_pVar);
        if(is_array($fileInfoOld_pVar)) {
            // subor existuje, teda zmazem novopridany subor, a pouzijem povodny
            if(basename($newPath_pVar) != basename($fileInfoOld_pVar['full_name'])) {
                unlink($newPath_pVar);
            }
            $fileId_pVar = 0;
            if(modules_gClass::isModuleRegistred_gFunc('db')) {
                $fileId_pVar = db_public_gClass::files_insertFile_gFunc($ref_tag_pVar, $ref_id_pVar, $target_dir_index_pVar, $fileInfoOld_pVar['full_name'], $fileInfoOld_pVar['original_md5'], $fileInfoOld_pVar['md5'], $fileInfoOld_pVar['size'], $fileType_pVar, $customName_pVar);
                if($fileId_pVar === false) {
                    return(false);
                }
                db_public_gClass::files_log_gFunc('clone', $fileId_pVar);
            }
        }
        else {
            if($fileType_pVar === 'image' && modules_gClass::isModuleRegistred_gFunc('graphics')) {
                // obrazky konvertujem na jpeg format
                $img_pVar = new gd_gClass();
                $img_pVar->loadImage_gFunc($newPath_pVar);
                $tmpFileContent_pVar = file_get_contents($newPath_pVar);
                unlink($newPath_pVar);
                $tmpNewPath_pVar = $img_pVar->saveImage_gFunc();
                if($tmpNewPath_pVar !== false) {
                    $newPath_pVar = $tmpNewPath_pVar;
                    $img_pVar->initImage_gFunc($target_dir_index_pVar);
                    $customName_pVar = gd_gClass::getNameForJpegImage($customName_pVar);
                }
                else {
                    file_put_contents($newPath_pVar, $tmpFileContent_pVar);
                }
            }

            $fileId_pVar = 0;
            if(modules_gClass::isModuleRegistred_gFunc('db')) {
                $md5_pVar = md5_file($newPath_pVar);
                $size_pVar = filesize($newPath_pVar);
                $fileId_pVar = db_public_gClass::files_insertFile_gFunc($ref_tag_pVar, $ref_id_pVar, $target_dir_index_pVar, $customName_pVar, $original_md5_pVar, $md5_pVar, $size_pVar, $fileType_pVar, false, $copyright_pVar);
                if($fileId_pVar === false) {
                    unlink($newPath_pVar);
                    return(false);
                }
                db_public_gClass::files_log_gFunc('upload', $fileId_pVar);
            }
        }
        return($fileId_pVar);
    }

    static public function deleteUploadedFile_gFunc($fileId_pVar)
    {
        if($fileId_pVar <= 0) {
            return;
        }
        if(modules_gClass::isModuleRegistred_gFunc('db')) {
            $fileName_pVar = db_public_gClass::files_deleteFileRecord_gFunc($fileId_pVar, true);
            if($fileName_pVar !== false && file_exists($fileName_pVar)) {
                unlink($fileName_pVar);
            }
        }
    }

    static public function uploaded_file_exists_gFunc($target_dir_index_pVar, $target_dir_pVar, $target_name_pVar)
    {
        if(file_exists($target_dir_pVar . $target_name_pVar)) {
            return(true);
        }
        if(!modules_gClass::isModuleRegistred_gFunc('db')) {
            return(false);
        }
        return(db_public_gClass::files_file_exists_gFunc($target_dir_index_pVar, $target_name_pVar));
    }

    static public function getFiles_gFunc($ref_tag_pVar, $ref_id_pVar, $like_pVar = false)
    {
        if(!modules_gClass::isModuleRegistred_gFunc('db')) {
            return(array());
        }

        $files_pVar = array();
        $records_pVar = db_public_gClass::files_get_records_gFunc($ref_tag_pVar, $ref_id_pVar, $like_pVar);

        if(is_array($records_pVar)) {
            foreach ($records_pVar as $v_pVar) {
                $defs_pVar = self::getFileDefProperties_gFunc($v_pVar['location'], $v_pVar['file_type']);
                $file_pVar = array();
                $web_name_pVar = empty($v_pVar['custom_name'])?$v_pVar['full_name']:$v_pVar['custom_name'];
                $file_pVar['file_id'] = $v_pVar['file_id'];
                $file_pVar['ref_id'] = $v_pVar['ref_value'];
                $file_pVar['ref_tag'] = $v_pVar['ref_tag'];
                $file_pVar['src'] = $defs_pVar['web_dir'] . $web_name_pVar;
                $file_pVar['file'] = $defs_pVar['full_dir'] . $v_pVar['full_name'];
                $file_pVar['copyright'] = $v_pVar['copyright'];
                if(isset($defs_pVar['images'])) {
                    foreach ($defs_pVar['images'] as $kk_pVar=>$vv_pVar) {
                        if($kk_pVar === 0) {
                            $file_pVar['width'] = $vv_pVar['width'];
                            $file_pVar['height'] = $vv_pVar['height'];
                        }
                        $file_pVar[$kk_pVar] = $vv_pVar;
                        $file_pVar[$kk_pVar]['src'] = $defs_pVar['web_dir'] . $web_name_pVar;
                        $file_pVar[$kk_pVar]['file'] = $defs_pVar['full_dir'] . $v_pVar['full_name'];

                        if(modules_gClass::isModuleRegistred_gFunc('graphics')) {
                            $file_pVar[$kk_pVar]['src'] = gd_gClass::setFileNamePrefix_gFunc($kk_pVar, $file_pVar[$kk_pVar]['src']);
                            $file_pVar[$kk_pVar]['file'] = gd_gClass::setFileNamePrefix_gFunc($kk_pVar, $file_pVar[$kk_pVar]['file']);
                            if(file_exists($file_pVar[$kk_pVar]['file'])) {
                                $size_pVar = getimagesize($file_pVar[$kk_pVar]['file']);
                                if(is_array($size_pVar)) {
                                    $file_pVar[$kk_pVar]['_width'] = $size_pVar[0];
                                    $file_pVar[$kk_pVar]['_height'] = $size_pVar[1];
                                }
                            }

                        }

                        if($kk_pVar === 0) {
                            $file_pVar['src'] = $file_pVar[$kk_pVar]['src'];
                            $file_pVar['file'] = $file_pVar[$kk_pVar]['file'];
                        }
                    }
                }
                $files_pVar[$file_pVar['ref_tag'] . '_' . $file_pVar['ref_id']] = $file_pVar;
            }
        }
        return($files_pVar);
    }

    static public function getFileDefProperties_gFunc($location_pVar, $file_type_pVar = 'file')
    {
        $cacheName_pVar = 'filedef_' . $location_pVar . '_' . $file_type_pVar;
        if(isset(self::$cache_pVar[$cacheName_pVar])) {
            return(self::$cache_pVar[$cacheName_pVar]);
        }

        $fileDir_pVar = main_gClass::getConfigVar_gFunc($location_pVar, 'files');
        if(empty($fileDir_pVar)) {
            $data_pVar = false;
        }
        else {
            $documentsDir_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
            $pathWeb_pVar = main_gClass::getConfigVar_gFunc('path_web_gVar', 'runtime_pVar');
            $data_pVar['full_dir'] = $fileDir_pVar;
            if(substr($fileDir_pVar, 0, strlen($documentsDir_pVar)) != $documentsDir_pVar) {
                if(substr($fileDir_pVar, 0, strlen($pathWeb_pVar)) != $pathWeb_pVar) {
                    error_gClass::warning_gFunc(__FILE__, __LINE__, $fileDir_pVar);
                    $data_pVar['web_dir'] = '';
                }
                else {
                    $data_pVar['web_dir'] = '/' . substr($fileDir_pVar, strlen($pathWeb_pVar));
                }
            }
            else {
                // zatial docasne riesenie
                $data_pVar['web_dir'] = '/' . substr($fileDir_pVar, strlen($documentsDir_pVar));
            }
        }

        if($file_type_pVar === 'image') {
            $options_pVar = main_gClass::getConfigVar_gFunc($location_pVar, 'images');
            if(!empty($options_pVar)) {
                $options_pVar = explode(',', $options_pVar);
                foreach ($options_pVar as $k_pVar=>$v_pVar) {
                    $xdata_pVar = explode('=', $v_pVar);
                    if(count($xdata_pVar) !== 2) {
                        continue;
                    }
                    $xdata_pVar[0] = trim($xdata_pVar[0]);
                    $xdata_pVar[1] = explode('x',trim($xdata_pVar[1]));
                    $xdata_pVar[1][0] = trim($xdata_pVar[1][0]);
                    $xdata_pVar[1][1] = trim($xdata_pVar[1][1]);

                    if(empty($xdata_pVar[0])) {
                        $useKey_pVar = 0;
                    }
                    else {
                        $useKey_pVar = $xdata_pVar[0];
                    }
                    if(!isset($data_pVar['images'])) {
                        $data_pVar['images'] = array();
                    }
                    $data_pVar['images'][$useKey_pVar] = array('width'=>$xdata_pVar[1][0], 'height'=>$xdata_pVar[1][1]);
                }
            }
        }

        self::$cache_pVar[$cacheName_pVar] = $data_pVar;
        return($data_pVar);
    }

    static function getDocNameType_gFunc($docName_pVar)
    {
        $p_pVar = strrpos($docName_pVar, '.');
        if($p_pVar !== false) {
            $ext_pVar = strtolower(substr($docName_pVar, $p_pVar + 1));
            $pp_pVar = strpos($ext_pVar, '?');
            if($pp_pVar !== false) {
                $ext_pVar = substr($ext_pVar, 0, $pp_pVar);
            }
            switch ($ext_pVar) {
                case 'css':
                case 'pdf':
                case 'ico':
                case 'js':
                case 'swf':
                case 'flv':
                case 'mp4':
                    return('file');// document=0, language=0
                case 'jpg':
                case 'gif':
                case 'png':
                    $files_pVar = main_gClass::getConfigSectionVar_gFunc('files');
                    if($docName_pVar[0] == '/') {
                        $dname_pVar = substr($docName_pVar, 1);
                    }
                    else {
                        $dname_pVar = $docName_pVar;
                    }
                    $dname_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main') . $dname_pVar;
                    foreach ($files_pVar as $k_pVar=>$v_pVar) {
                        if($v_pVar === substr($dname_pVar, 0, strlen($v_pVar))) {
                            return('no_language'); // document=1, language=0
                        }
                    }
                    return('image');// document=0, language=1
                default:
                    return('document'); // document=1, language=1
            }
        }
        return('document');
    }

    static function getMimeType_pVar($fileName_pVar)
    {
        $pathInfo_pVar = pathinfo($fileName_pVar);

        switch (strtolower($pathInfo_pVar['extension'])) {
            case 'jpg':
                return('image/jpeg');
            case 'png':
                return('image/jpeg');
            case 'gif':
                return('image/gif');
            case 'pdf':
                return('application/pdf');
            case 'zip':
            case 'rar':
                return('application/octet-stream');
            case 'swf':
                return('application/x-shockwave-flash');
            case 'mp4':
                return('video/mp4');
            case 'flv':
                return('video/x-flv');
            default:
                return('text/plain');
        }
    }

    static function getMainCurrency_gFunc($short_pVar = false)
    {
        if(empty(self::$main_currency_pVar)) {
            self::$main_currency_pVar = main_gClass::getConfigVar_gFunc('main_currency', 'main');
            if(empty(self::$main_currency_pVar)) {
                self::$main_currency_pVar = 'EUR/€';
            }

            $tmp_pVar = explode('/', self::$main_currency_pVar);
            if(count($tmp_pVar) > 1) {
                self::$main_currency_pVar = $tmp_pVar[0];
                self::$main_currency_short_pVar = $tmp_pVar[1];
            }

            self::$main_currency_pVar = strtoupper(self::$main_currency_pVar);
        }

        if($short_pVar) {
            return(self::$main_currency_short_pVar);
        }
        return(self::$main_currency_pVar);
    }

    static function getLanguage_gFunc()
    {
        $language_pVar = self::getInputString_gFunc('language_pVar', self::SRC_SESSION_pVar, '/[a-z]{2}/i');
        if(empty($language_pVar)) {
            $language_pVar = 'sk';
        }
        return($language_pVar);
    }

    static function applyLanguageToArray_gFunc(&$array_pVar, $language_pVar)
    {
        $lngLen_pVar = strlen($language_pVar) + 1;
        foreach($array_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, $lngLen_pVar) == ($language_pVar . '_')) {
                $array_pVar[substr($k_pVar, $lngLen_pVar)] = $v_pVar;
            }
        }
    }

    static function regenerateSessionId_gFunc()
    {
        // request()->session()->regenerate();
        $language_pVar = main_gClass::getInputString_gFunc('language_pVar', self::SRC_SESSION_pVar);
        $_SESSION = array();
        self::initInputsSession_gFunc();

        main_gClass::setPhpSessionVar_gFunc('language_pVar', $language_pVar);
    }

    static function getVersion_gFunc()
    {
        $data = explode('#', trim(file_get_contents(base_path('.version'))));
        return [$data[1], $data[0]];
    }

    static function openTrash_gFunc()
    {
        self::$trashLevel_pVar++;
        echo '<te:trash />';
    }

    static function isLogEnabled_gFunc($elementName_pVar)
    {
        if(self::$logElementsCache_pVar === false) {
            if(self::$instance === false || self::$instance->config_gVar === false) {
                return(true);
            }
            $log_pVar = main_gClass::getConfigVar_gFunc('log', 'debug');
            self::$logElementsCache_pVar = explode(',', trim($log_pVar));
            if(!in_array('DB_QUERY', self::$logElementsCache_pVar)) {
                // ak je vypnute logovanie queries, tak logujem len pomale query.
                $slowQueryTime_pVar = intval(main_gClass::getConfigVar_gFunc('slow_query_time', 'debug'))/1000.0;
                if(!$slowQueryTime_pVar) {
                    $slowQueryTime_pVar = 3;
                }
                log_gClass::logQueriesOff_gFunc($slowQueryTime_pVar);
            }
        }
        return(in_array($elementName_pVar, self::$logElementsCache_pVar));
    }

    static function getCacheTagStatus_gFunc($tagName_pVar, $objectId_pVar = false, $objectTagLike_pVar = NULL)
    {
        $sqlData_pVar = array($tagName_pVar);
        $sql_pVar = 'SELECT * FROM `%tcache_tags` WHERE `tag` = %s';
        if($objectId_pVar !== false) {
            $sql_pVar .= ' AND `object_id` = %d';
            $sqlData_pVar[] = $objectId_pVar;
        }
        if($objectTagLike_pVar !== NULL) {
            $sql_pVar .= ' AND `object_tag` LIKE %s';
            $sqlData_pVar[] = $objectTagLike_pVar;
        }

        $sql_pVar .= ' AND cached_time > date_sub(now(), interval 10 day)';
        $data_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $sqlData_pVar);
        if(!is_array($data_pVar)) {
            return(false);
        }
        else {
            return(true);
        }
    }

    static function updateCacheTagStatus_gFunc($tagName_pVar, $objectId_pVar = false, $objectTagLike_pVar = NULL)
    {
        $sqlData_pVar = array($tagName_pVar);
        $sqlFormat_pVar = array('%s');
        $sqlFields_pVar = array('`tag`');
        $sql_pVar = 'REPLACE INTO `%tcache_tags` (';
        if($objectId_pVar !== false) {
            $sqlFormat_pVar[] = '%d';
            $sqlData_pVar[] = $objectId_pVar;
            $sqlFields_pVar[] = '`object_id`';
        }
        if($objectTagLike_pVar !== NULL) {
            $sqlFormat_pVar = '%xs';
            $sqlData_pVar[] = $objectTagLike_pVar;
            $sqlFields_pVar[] = '`object_tag`';
        }

        $sql_pVar .= implode(',', $sqlFields_pVar) . ', `cached_time`) VALUES(' . implode(',', $sqlFormat_pVar) . ', now())';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sqlData_pVar);
    }

    static function invalidateCacheTag_gFunc($tagName_pVar, $objectId_pVar = false, $objectTagLike_pVar = NULL)
    {
        $sql_pVar = 'DELETE FROM `%tcache_tags` WHERE `tag` = %s';
        $sqlData_pVar = array($tagName_pVar);
        if($objectId_pVar !== false) {
            $sql_pVar .= ' AND `object_id` = %d';
            $sqlData_pVar[] = $objectId_pVar;
        }
        if($objectTagLike_pVar !== NULL) {
            $sql_pVar .= ' AND `object_tag` LIKE %s';
            $sqlData_pVar[] = $objectTagLike_pVar;
        }
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $sqlData_pVar);
    }
}
