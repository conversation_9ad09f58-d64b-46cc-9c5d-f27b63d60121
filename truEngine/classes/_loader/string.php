<?php

class string_gClass
{

    static private $_cached_strings = [];

    private function __construct()
    {

    }

    static function formatAsDirectory_gFunc($string_pVar)
    {
        if(substr($string_pVar, -1) != '/') $string_pVar .= '/';
        $string_pVar = str_replace('\\', '/', $string_pVar);
        return($string_pVar);
    }

    static function get($str_id_pVar, $param1_pVar = false, $param2_pVar = false, $param3_pVar = false)
    {
        if(!defined($str_id_pVar)) return($str_id_pVar);
        return(sprintf(constant($str_id_pVar), $param1_pVar, $param2_pVar, $param3_pVar));
    }

    static function hex2bin_gFunc($str_pVar)
    {
        $out_pVar = "";
        while(strlen($str_pVar) > 1) {
            $s = substr($str_pVar, 0, 2);
            $str_pVar = substr($str_pVar, 2);
            $s = strtolower($s);
            $d = (ctype_digit( $s[0]) ? $s[0] : (ord($s[0]) - ord("a") + 10)) * 16;
            $d += (ctype_digit($s[1]) ? $s[1] : (ord($s[1]) - ord("a") + 10));
            $out_pVar .= chr($d);
        }
        return($out_pVar);
    }

    static function addSlashes_magic_gFunc($str_pVar)
    {
        return(addslashes($str_pVar));
    }

    static function stripSlashes_magic_gFunc($str_pVar)
    {
        return(stripslashes($str_pVar));
    }

    /**
     * multibyte string safe function
     */
    static function removeDiacritic_gFunc($str_pVar)
    {
        //// !!!! NEMOZEM POUZIT KLASICKYM SPOSOBOM STRTR(), PTRTOZE NIE JE KOMPATIBILNA S UTF-8 !!!!
        //// !!!! MUSIM TO SPRAVIT CEZ PARY.. TAK TO IDE STRTR(STR, ARRAY);

        $str_pVar = strtr($str_pVar, array(
            'á' => 'a',
            'ä' => 'a',
            'é' => 'e',
            'í' => 'i',
            'ó' => 'o',
            'ú' => 'u',
            'ý' => 'y',

            'Á' => 'A',
            'É' => 'E',
            'Í' => 'I',
            'Ó' => 'O',
            'Ú' => 'U',
            'Ý' => 'Y',

            'ď' => 'd',
            'ť' => 't',
            'ň' => 'n',
            'ľ' => 'l',

            'Ď' => 'D',
            'Ť' => 'T',
            'Ň' => 'N',
            'Ľ' => 'L',

            'ô' => 'o',
            'š' => 's',
            'ž' => 'z',
            'č' => 'c',

            'Ô' => 'O',
            'Š' => 'S',
            'Ž' => 'Z',
            'Č' => 'C',

            '€' => 'EUR'

        ));

        return($str_pVar);
    }

    static function getStringWidth_gFunc($str_pVar)
    {
        if(function_exists('mb_strwidth') && defined('MB_OVERLOAD_STRING') && MB_OVERLOAD_STRING) {
            return(mb_strwidth($str_pVar));
        }
        else {
            return(strlen($str_pVar));
        }
    }

    static function arrayToPhpCode_gFunc($array_pVar)
    {
        $str_pVar = 'array(';
        $n_pVar = 0;
        foreach ($array_pVar as $k_pVar=>$v_pVar)
        {
            if($n_pVar) {
                $str_pVar .= ', ';
            }
            $str_pVar .= '\'' . $k_pVar . '\'=>\'' . $v_pVar . '\'';
            $n_pVar++;
        }
        $str_pVar .= ')';
        return($str_pVar);
    }

    /**
     * Rozlozi pole do retazca rekurzivne. (vhodne na logovanie)
     * Ak $assoc_pVar = true, tak obrazi aj kluce.
     * Ak $assoc_pVar = true, a $replaceKeys_pVar obsahuje hodnoty kluc=>hodnota, tak
     * 	budu najdene kluce nahradene hodnotami z tohoto pola.
     *
     * @param unknown_type $array_pVar
     * @param unknown_type $assoc_pVar
     * @param unknown_type $replaceKeys_pVar
     * @return unknown
     */
    static function arrayToString_gFunc($array_pVar, $assoc_pVar = false, $replaceKeys_pVar = array())
    {
        return(serialize($array_pVar));

        /*
                foreach ($array_pVar as $k_pVar=>$v_pVar) {
                    if(is_array($v_pVar)) {
                        $array_pVar[$k_pVar] = '[' . self::arrayToString_gFunc($v_pVar, $assoc_pVar, $replaceKeys_pVar) . ']';
                    }
                    else {
                        $array_pVar[$k_pVar] = str_replace('\\', '\\1', $array_pVar[$k_pVar]);
                        $array_pVar[$k_pVar] = str_replace(',', '\\2', $array_pVar[$k_pVar]);
                        $array_pVar[$k_pVar] = str_replace('[', '\\3', $array_pVar[$k_pVar]);
                        $array_pVar[$k_pVar] = str_replace(']', '\\4', $array_pVar[$k_pVar]);
                        $array_pVar[$k_pVar] = str_replace('=>', '\\5', $array_pVar[$k_pVar]);
                    }
                }

                if(!$assoc_pVar) {
                    return(implode(',', $array_pVar));
                }
                else {
                    $ret_pVar = '';
                    foreach ($array_pVar as $k_pVar=>$v_pVar) {
                        if(!empty($ret_pVar)) {
                            $ret_pVar .= ',';
                        }
                        if(isset($replaceKeys_pVar[$k_pVar])) {
                            $k_pVar = $replaceKeys_pVar[$k_pVar];
                        }
                        $ret_pVar .= $k_pVar .'=>' . $v_pVar;
                    }
                    return($ret_pVar);
                }
                */
    }

    static function stringToArray_gFunc($str_pVar)
    {
        return(unserialize($str_pVar));
        /*
        $tmp_pVar = explode('[', $str_pVar);
        foreach ($tmp_pVar as $k_pVar=>$v_pVar) {
            $tmp_pVar[$k_pVar] = explode(']', $v_pVar);
        }

        echo '<pre>'; print_r($tmp_pVar); echo '<pre>';
        */
    }

    static function priceFormat_gFunc($value_pVar, $inCurrency_pVar=false, $outCurrency_pVar=false, $extraParams_pVar = array())
    {
        if(modules_gClass::isModuleRegistred_gFunc('currency')) {
            return(priceFormat_gClass::formatPriceEx_gFunc($value_pVar, $inCurrency_pVar, $outCurrency_pVar, $extraParams_pVar));
        }
        else {
            return($value_pVar . '&nbsp;' . $inCurrency_pVar);
        }
    }

    static function formatLinks_gFunc($str_pVar)
    {
        return(preg_replace_callback('/(https?:\/\/[^ ,]+)/i',array('string_gClass', 'formatLinksCallback_gFunc'), $str_pVar));
    }

    static function formatLinksCallback_gFunc($matches)
    {
        return('<a href="'.$matches[1].'" target="_blank">'.$matches[1].'</a>');
    }


    static function parseAccessString_gFunc($accessString_pVar)
    {
        if(empty($accessString_pVar)) {
            return('0');
        }

        $accessString_pVar = strtr($accessString_pVar, array('&'=>' & ', '|'=>' | ', '(' => ' ( ', ')'=>' ) '));
        $accessString_pVar = str_replace(TAB, ' ', $accessString_pVar);
        $ret_pVar = explode(' ', $accessString_pVar);

        foreach($ret_pVar as $k_pVar=>$v_pVar) {
            if(!empty($v_pVar) && isset($v_pVar[0])&& isset($v_pVar[1])) {
                if($v_pVar[0] === 's' && $v_pVar[1] === '_') {
                    $ret_pVar[$k_pVar] = ' session_gClass::userHasRightsInfo_gFunc('.$v_pVar.') ';
                }
                elseif($v_pVar[0] === '@' && $v_pVar[1] === '{') {
                    $ret_pVar[$k_pVar] = ' session_gClass::userHasRightsInfo_gFunc('.$v_pVar.') ';
                }
            }
        }

        return(implode(' ', $ret_pVar));
    }

    public static function getString_gFunc($id_pVar, $language_pVar = false, $cacheEnabled_pVar = true)
    {
        $ret_pVar = self::getStringId_gFunc($id_pVar, $language_pVar, $cacheEnabled_pVar);
        return($ret_pVar['str']);
    }

    public static function getStringId_gFunc($id_pVar, $language_pVar = false, $cacheEnabled_pVar = true)
    {
        $ret_pVar = array('str'=>$id_pVar, 'id'=>0);
        if(!modules_gClass::isModuleRegistred_gFunc('db')) {
            return($ret_pVar);
        }
        $cacheName_pVar = '_loader_string_pVar' . '_' . $language_pVar . '_' . $id_pVar;
        if($cacheEnabled_pVar) {
            $cache_ret_pVar = db_gClass::getCachedResult_gFunc($cacheName_pVar);
            if($cache_ret_pVar !== false) {
                return($cache_ret_pVar);
            }
        }
        if($language_pVar === false) {
            $language_pVar = main_gClass::getLanguage_gFunc();
        }
        if(!is_numeric($id_pVar)) {
            if(in_array(substr($id_pVar, 0, 3), array('sk_', 'en_'))) {
                $language_pVar = substr($id_pVar, 0, 2);
                $id_pVar = substr($id_pVar, 3);
            }

            if(empty(self::$_cached_strings)) {
                $sql_pVar = 'SELECT * FROM `%tstrings`';
                $allStrings = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
                foreach($allStrings as $row) {
                    if($row['sk_name'] !== null) self::$_cached_strings['sk_name_' . $row['sk_name']] = $row;
                    if($row['en_name'] !== null) self::$_cached_strings['en_name_' . $row['en_name']] = $row;
                    if($row['cz_name'] !== null) self::$_cached_strings['cz_name_' . $row['cz_name']] = $row;
                }
            }

            if(isset(self::$_cached_strings[$language_pVar . '_name_' . $id_pVar])) {
                $str_pVar = self::$_cached_strings[$language_pVar . '_name_' . $id_pVar];
            }
            else {
                $sql_pVar = 'SELECT * FROM `%tstrings` WHERE `' . $language_pVar . '_name` = %s';
                $str_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar);
            }

            if($str_pVar === true) {
                $sql_pVar = 'SELECT * FROM `%tstrings` WHERE `' . $language_pVar . '_name` IS NULL AND `sk_name`= %s';
                $str_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar);

                if($str_pVar === true) {
                    $sql_pVar = 'INSERT INTO `%tstrings` (`sk_name`, `sk_text`) VALUES (%s, %s)';
                    $id_pVar =  db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($id_pVar, $id_pVar), true);
                }
                else {
                    $id_pVar = $str_pVar['id'];
                }
            }
            else {
                $id_pVar = $str_pVar['id'];
            }
        }
        else {
            $sql_pVar = 'SELECT * FROM `%tstrings` WHERE `id` = %d';
            $str_pVar = db_public_gClass::getResult_gFunc($sql_pVar, __FILE__, __LINE__, $id_pVar);
        }

        $ret_pVar['id'] = $id_pVar;

        if(is_array($str_pVar) && isset($str_pVar[$language_pVar . '_text'])) {
            $ret_pVar['str'] = $str_pVar[$language_pVar . '_text'];
        }

        if($cacheEnabled_pVar === true) {
            db_gClass::cacheResult_gFunc($cacheName_pVar, $ret_pVar);
        }
        return($ret_pVar);
    }

    static function path_gFunc($str_pVar)
    {
        $str_pVar = str_replace('://', ':@@@@', $str_pVar);
        $str_pVar = str_replace('%3A//', ':####', $str_pVar);
        $str_pVar = str_replace('%3a//', ':####', $str_pVar);
        $str_pVar = str_replace('//', '/', $str_pVar);
        $str_pVar = str_replace('//', '/', $str_pVar);
        $str_pVar = str_replace('//', '/', $str_pVar);
        $str_pVar = str_replace(':@@@@', '://', $str_pVar);
        $str_pVar = str_replace(':####', '%3A//', $str_pVar);
        return($str_pVar);
    }

    static function interval($str)
    {
        $n = 0;
        while(1) {
            $str2 = self::_interval($str);
            if($str2 == $str) {
                return($str);
            }
            $str = $str2;
            $n++;
            if($n > 20) {
                return($str);
            }
        }
    }

    static private function _interval($str)
    {
        $ret = array();
        $a = explode(',', $str);
        foreach($a as $k=>$v) {
            $a[$k] = explode('-', $v);
            if(count($a[$k]) == 1) {
                $a[$k][1] = $a[$k][0];
            }

            $a[$k][0] = intval(trim($a[$k][0]));
            $a[$k][1] = intval(trim($a[$k][1]));

            foreach($a[$k] as $kk=>$vv) {
                $a[$k][$kk] = trim($vv);
            }
            foreach($ret as $kkk=>$vvv) {
                if($a[$k][0] >= $vvv[0] && $a[$k][1] <= $vvv[1]) { // je uz vramci existujuceho intervalu
                    continue 2;
                }
                if($a[$k][0] <= $vvv[0] && $a[$k][1] >= $vvv[1]) { // pohlti interval
                    $ret[$kkk] = array($a[$k][0], $a[$k][1]);
                    continue 2;
                }
                if($a[$k][0] == $vvv[1] + 1 && $a[$k][1] > $vvv[1]) { // spoji intervaly (pripoji vpravo)
                    $ret[$kkk] = array($vvv[0], $a[$k][1]);
                    continue 2;
                }
                if($a[$k][1] == $vvv[0] - 1 && $a[$k][0] < $vvv[0]) { // spoji intervaly (pripoji vlavo)
                    $ret[$kkk] = array($a[$k][0], $vvv[1]);
                    continue 2;
                }
                if($a[$k][0] <= $vvv[0] && $a[$k][1] <= $vvv[1] && $a[$k][1] >= $vvv[0]) { // rozsiri interval zlava
                    $ret[$kkk] = array($a[$k][0], $vvv[1]);
                    continue 2;
                }
                if($a[$k][0] >= $vvv[0] && $a[$k][1] >= $vvv[1] && $a[$k][0] <= $vvv[1]) { // rozsiri interval zprava
                    $ret[$kkk] = array($vvv[0], $a[$k][1]);
                    continue 2;
                }
            }
            $ret[] = array($a[$k][0], $a[$k][1]);
        }
        foreach($ret as $k=>$v) {
            if($ret[$k][0] == $ret[$k][1]) {
                if($ret[$k][0] == 0) {
                    unset($ret[$k]);
                    continue;
                }
                $ret[$k] = array($ret[$k][0]);
            }
            $ret[$k] = implode('-', $ret[$k]);
        }
        return(implode(', ', $ret));
    }
}

