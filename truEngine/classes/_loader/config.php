<?php


/**
 * konfiguracia

[path_web_gVar] => /hosting/elcoms.sk/demo/kega_test/
[document_root_gVar] => /hosting/elcoms.sk/
[web_dir_gVar] => /demo/kega_test/
[path_system_include_gVar] => /hosting/elcoms.sk/demo/kega_test/program/.lib/
[path_system_gVar] => /hosting/elcoms.sk/demo/kega_test/program/


 */
class config_gClass extends truEngineBaseClass_gClass
{
    const FORMAT_DIRECTORY_pVar = 1;
    const FORMAT_BOOLEAN_pVar = 2;
    const FORMAT_INTEGER_pVar = 3;
    const FORMAT_ENUM_pVar = 4;

    private $baseFileName_pVar = 'truEngine';
    /**
     * Poradie hladania premennej. Hlada sa vo vsetkych poliach, ak sa najde premenna,
     * prepise sa uz ziskana hodnota. Poradie prepisu vid v mergeVar_gFunc
     */
    private $vars_system_pVar = array();
    private $vars_default_master_pVar = array();
    private $vars_default_slave_pVar = array();
    private $vars_domain_master_pVar = array();
    private $vars_domain_slave_pVar = array();
    private $vars_merged_pVar = array();
    private $guestsDefaultRights_pVar = false; // defaultne prava pre neprihlaseneho pouziatela
    private $usersDefaultRights_pVar = false; // defaultne prava pre prihlaseneho pouzivatela (ak nie je mnodul rights)

    public function load_gFunc()
    {
        $this->vars_system_pVar = array();
        $this->vars_system_pVar['runtime_pVar'] = array();

        // adresar pre web (index.php)
        $this->vars_system_pVar['runtime_pVar']['path_web_gVar'] = string_gClass::formatAsDirectory_gFunc(dirname(main_gClass::getServerVar_gFunc('SCRIPT_FILENAME')));
        $this->vars_system_pVar['runtime_pVar']['document_root_gVar'] = string_gClass::formatAsDirectory_gFunc(main_gClass::getServerVar_gFunc('DOCUMENT_ROOT'));
        $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'] = string_gClass::formatAsDirectory_gFunc(substr($this->vars_system_pVar['runtime_pVar']['path_web_gVar'], strlen(main_gClass::getServerVar_gFunc('DOCUMENT_ROOT'))));
        $this->vars_system_pVar['runtime_pVar']['server_name_gVar'] = main_gClass::getServerVar_gFunc('SERVER_NAME');
        $this->vars_system_pVar['runtime_pVar']['language_gVar'] = main_gClass::getLanguage_gFunc();

        $httpsPort_pVar = main_gClass::getConfigVar_gFunc('port', 'https');
        if(main_gClass::getServerVar_gFunc('SERVER_PORT') != $httpsPort_pVar) {
            $this->vars_system_pVar['runtime_pVar']['server_protocol_gVar'] = 'http';
        }
        else {
            $this->vars_system_pVar['runtime_pVar']['server_protocol_gVar'] = 'https';
        }
        if($this->vars_system_pVar['runtime_pVar']['web_dir_gVar'][0] !== '/') {
            $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'] = '/' . $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'];
        }


        // zistim systemove adresare
        $arr_pVar=get_included_files();
        foreach($arr_pVar as $v_pVar) {
            if(basename($v_pVar)=='__loader.module.php') {
                $this->vars_system_pVar['runtime_pVar']['path_system_include_gVar'] = string_gClass::formatAsDirectory_gFunc(dirname($v_pVar));
                $path_pVar=rtrim(dirname($v_pVar));
                $p1_pVar = strrpos($path_pVar,'/');
                $p2_pVar = strrpos($path_pVar,'\\');
                $p_pVar = max($p1_pVar, $p2_pVar);
                $this->vars_system_pVar['runtime_pVar']['path_system_gVar'] = string_gClass::formatAsDirectory_gFunc(substr($path_pVar, 0, $p_pVar));
            }
        }

        $this->vars_system_pVar['runtime'] = array();
        $this->vars_system_pVar['runtime']['web_dir'] = $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'];
        $this->vars_system_pVar['runtime']['server_name'] = $this->vars_system_pVar['runtime_pVar']['server_name_gVar'];
        $this->vars_system_pVar['runtime']['server_protocol'] = $this->vars_system_pVar['runtime_pVar']['server_protocol_gVar'];
        $this->vars_system_pVar['runtime']['language'] = $this->vars_system_pVar['runtime_pVar']['language_gVar'];

        $ret_pVar = false; // navratova hodnota... bude true, ak sa podari nahrat default_master (ten je povinny)

        // default_master ini
        if(isset($GLOBALS['master_ini_dir'])) {
            $fileName_pVar = $GLOBALS['master_ini_dir'] . '.truengine.ini.php';
        }
        else {
            $fileName_pVar = $this->vars_system_pVar['runtime_pVar']['path_system_gVar'] . '.truengine.ini.php';
        }

        if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
            $this->vars_default_master_pVar = parse_ini_file($fileName_pVar, true);
            //dd($this->vars_default_master_pVar);
            if(is_array($this->vars_default_master_pVar)) {
                $ret_pVar = true;
            }
        }

        if($ret_pVar) {
            // cesty ku konfiguracnym suborom
            $config_pVar = array();
            if(isset($this->vars_default_master_pVar['config'])) {
                $config_pVar = $this->vars_default_master_pVar['config'];
            }

            $this->vars_default_master_pVar['main']['documents_dir'] = env('LEGACY_DOCUMENTS_DIR');
            $this->vars_default_master_pVar['main']['document_root'] = env('LEGACY_DOCUMENTS_ROOT');

            if(!isset($this->vars_default_master_pVar['main']['documents_dir'])
                || !strlen($this->vars_default_master_pVar['main']['documents_dir'])) {
                include_once('__loader.sk.str.php');
                error_gClass::error_gFunc(__FILE__,__LINE__, string_gClass::get('str___loader_err_config_documents_dir_sVar'));
                $ret_pVar = false;
            }

            // DEFAULT_SLAVE
            if(!isset($config_pVar['default_slave']) || !strlen($config_pVar['default_slave'])) {
                //nie je definovany, zadefinujem default
                $config_pVar['default_slave'] = string_gClass::formatAsDirectory_gFunc($this->vars_default_master_pVar['main']['documents_dir']) . '.truengine.ini.php';
                $required_pVar = false;
            }
            else {
                $required_pVar = true;
            }


//            if($config_pVar['default_slave'][0] == '/' || strpos($config_pVar['default_slave'], ':') !== false) {
//                // full path
//                $x_pVar = basename($config_pVar['default_slave']);
//                if($x_pVar[0] != '.') {
//                    // musi zacinat bodkou
//                    $ret_pVar = false;
//                }
//            }
//            else {
//                // filename
//                if(strpos($config_pVar['default_slave'], '/') !== false || strpos($config_pVar['default_slave'], '\\') !== false) {
//                    //cesta nemoze byt
//                    $ret_pVar = false;
//                }
//                if($config_pVar['default_slave'][0] != '.') {
//                    // musi zacinat bodkou
//                    $ret_pVar = false;
//                }
//                if($ret_pVar) {
//                    $config_pVar['default_slave'] = string_gClass::formatAsDirectory_gFunc($this->vars_default_master_pVar['main']['documents_dir']) . $config_pVar['default_slave'];
//                }
//            }

//            if($ret_pVar) {
//                if($required_pVar) {
//                    $ret_pVar = false; // nastavim na false, a uspesne nahratie zmeni zasa na true
//                }
//                $fileName_pVar = $config_pVar['default_slave'];
//                // default_slave ini
//                if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
//                	$this->vars_default_slave_pVar = parse_ini_file($fileName_pVar, true);
//                	if(is_array($this->vars_default_slave_pVar)) {
//                	    $ret_pVar = true;
//                	}
//                }
//            }


            if($ret_pVar) {
                // DOMAIN_MASTER
                if(!isset($config_pVar['master_' . main_gClass::getServerVar_gFunc('SERVER_NAME')]) || !strlen($config_pVar['master' . main_gClass::getServerVar_gFunc('SERVER_NAME')])) {
                    //nie je definovany, zadefinujem default
                    $config_pVar['master_' . main_gClass::getServerVar_gFunc('SERVER_NAME')] = $this->vars_system_pVar['runtime_pVar']['path_system_gVar'];
                    $required_pVar = false;
                }
                else {
                    $required_pVar = true;
                }

                // domain_master ini
                $fileName_pVar = string_gClass::formatAsDirectory_gFunc($config_pVar['master_' . main_gClass::getServerVar_gFunc('SERVER_NAME')]) . '.' . main_gClass::getServerVar_gFunc('SERVER_NAME') . '.ini.php';
                if($required_pVar) {
                    $ret_pVar = false;
                }
                $domain_master_ok_pVar = false;
                if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
                    $this->vars_domain_master_pVar = parse_ini_file($fileName_pVar, true);
                    if(is_array($this->vars_domain_master_pVar)) {
                        $domain_master_ok_pVar = true;
                        $ret_pVar = true;
                    }
                }

                if($required_pVar || $domain_master_ok_pVar) {
                    //// DOMAIN_SLAVE
                    if(!isset($this->vars_domain_master_pVar['main']['documents_dir'])
                        || !strlen($this->vars_domain_master_pVar['main']['documents_dir'])) {
                        include_once('__loader.sk.str.php');
                        error_gClass::error_gFunc(__FILE__,__LINE__, string_gClass::get('str___loader_err_config_documents_dir_sVar'));
                        $ret_pVar = false;
                    }

                    if($ret_pVar) {
                        if(!isset($config_pVar['slave_' . main_gClass::getServerVar_gFunc('SERVER_NAME')]) || !strlen($config_pVar['slave' . main_gClass::getServerVar_gFunc('SERVER_NAME')])) {
                            //nie je definovany, zadefinujem default
                            $config_pVar['slave_' . main_gClass::getServerVar_gFunc('SERVER_NAME')] = $this->vars_domain_master_pVar['main']['documents_dir'];
                            $required_pVar = false;
                        }
                        else {
                            $required_pVar = true;
                        }

                        // domain_slave ini
                        $fileName_pVar = string_gClass::formatAsDirectory_gFunc($config_pVar['slave_' . main_gClass::getServerVar_gFunc('SERVER_NAME')]) . '.' . main_gClass::getServerVar_gFunc('SERVER_NAME') . '.ini.php';
                        if($required_pVar) {
                            $ret_pVar = false;
                        }
                        if(fileSafe_gClass::file_exists_gFunc($fileName_pVar)) {
                            $this->vars_domain_slave_pVar = parse_ini_file($fileName_pVar, true);
                            if(is_array($this->vars_domain_slave_pVar)) {
                                $ret_pVar = true;
                            }
                        }
                    }

                }

            }
        }

        if($ret_pVar) {
            $this->merge_gFunc();
            $this->fix_document_root_gFunc();
            $this->initDefaultValues_gFunc();
            $this->vars_merged_pVar['config'] = $config_pVar;
            $this->formatValues_gFunc();
            $this->vars_system_pVar['runtime'] = array();
            $this->vars_merged_pVar['runtime']['path_web'] = $this->vars_system_pVar['runtime_pVar']['path_web_gVar'];
            $this->vars_merged_pVar['runtime']['document_root'] = $this->vars_system_pVar['runtime_pVar']['document_root_gVar'];
            $this->vars_merged_pVar['runtime']['web_dir'] = $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'];
            $this->replaceValues_gFunc();
            $this->formatValues_gFunc();
        }
        //echo '<pre>'; print_r($this->vars_merged_pVar); echo '</pre>';
        return($ret_pVar);
    }

    function fix_document_root_gFunc()
    {
        if(isset($this->vars_merged_pVar['main']['document_root'])) {
            $this->vars_system_pVar['runtime_pVar']['document_root_gVar'] = string_gClass::formatAsDirectory_gFunc($this->vars_merged_pVar['main']['document_root']);
            $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'] = string_gClass::formatAsDirectory_gFunc(substr($this->vars_system_pVar['runtime_pVar']['path_web_gVar'], strlen($this->vars_system_pVar['runtime_pVar']['document_root_gVar'])));

            $this->vars_merged_pVar['runtime_pVar']['document_root_gVar'] = $this->vars_system_pVar['runtime_pVar']['document_root_gVar'];
            $this->vars_merged_pVar['runtime_pVar']['web_dir_gVar'] = $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'];
            $this->vars_merged_pVar['runtime']['document_root'] = $this->vars_system_pVar['runtime_pVar']['document_root_gVar'];
            $this->vars_merged_pVar['runtime']['web_dir'] = $this->vars_system_pVar['runtime_pVar']['web_dir_gVar'];
        }
    }

    public function initTimezone()
    {
        // nastavenie timezone (koli date() warningom);

        date_default_timezone_set(main_gClass::getConfigVar_gFunc('timezone', 'main'));
    }

    public function initRights_gFunc($refresh_pVar = false)
    {
        $this->initTimezone();
        $cacheDir_pVar = main_gClass::getPathForCache_gFunc();
        $rightNamesFileUnsafe_pVar = $cacheDir_pVar . '.access_names.php';
        $rightNamesFile_pVar = fileSafe_gClass::safePath_gFunc($rightNamesFileUnsafe_pVar);

        if($refresh_pVar) {
            if(fileSafe_gClass::file_exists_gFunc($rightNamesFile_pVar)) {
                unlink($rightNamesFileUnsafe_pVar);
            }
        }

        if(fileSafe_gClass::file_exists_gFunc($rightNamesFile_pVar)) {
            if(!$refresh_pVar) {
                require_once($rightNamesFile_pVar);
            }
            else {
                error_gClass::reportOff_gFunc(true);
                require($rightNamesFile_pVar);
                error_gClass::reportOff_gFunc(false);
            }
        }
        else {
            // musim vygenerovat $rightNamesFile_pVar
            if(modules_gClass::isModuleRegistred_gFunc('db')) {
                db_public_gClass::initRightsNames_gFunc($rightNamesFile_pVar);
                if(!$refresh_pVar) {
                    require_once($rightNamesFile_pVar);
                }
                else {
                    error_gClass::reportOff_gFunc(true);
                    require($rightNamesFile_pVar);
                    error_gClass::reportOff_gFunc(false);
                }
            }
        }

        // guests
        $rights_pVar = $this->vars_merged_pVar['main']['default_guest_rights'];
        $rights_pVar = explode(',', $rights_pVar);
        $this->guestsDefaultRights_pVar = array();
        foreach ($rights_pVar as $v_pVar) {
            $v_pVar = trim($v_pVar);
            if(!strlen($v_pVar)) {
                continue;
            }
            if(!is_numeric($v_pVar)) {
                $v_pVar = constant($v_pVar);
                if($v_pVar === null) {
                    continue;
                }
            }
            $v_pVar = (int)$v_pVar;
            if(!$v_pVar) {
                continue;
            }
            $this->guestsDefaultRights_pVar[$v_pVar] = true;
        }

        // users
        $rights_pVar = $this->vars_merged_pVar['main']['default_user_rights'];
        $rights_pVar = explode(',', $rights_pVar);
        $this->usersDefaultRights_pVar = array();
        foreach ($rights_pVar as $v_pVar) {
            $v_pVar = trim($v_pVar);
            if(!strlen($v_pVar)) {
                continue;
            }
            if(!is_numeric($v_pVar)) {
                $v_pVar = constant($v_pVar);
                if($v_pVar === null) {
                    continue;
                }
            }
            $v_pVar = (int)$v_pVar;
            if(!$v_pVar) {
                continue;
            }
            $this->usersDefaultRights_pVar[$v_pVar] = true;
        }

    }

    function getGuestDefaultRights_gFunc()
    {
        if(!is_array($this->guestsDefaultRights_pVar)) {
            $this->initRights_gFunc();
        }
        return($this->guestsDefaultRights_pVar);
    }

    function getUserDefaultRights_gFunc()
    {
        if(!is_array($this->usersDefaultRights_pVar)) {
            $this->initRights_gFunc();
        }
        return($this->usersDefaultRights_pVar);
    }

    public function initDefaultValues_gFunc()
    {
        $this->initDefaultVarValue_gFunc('main', 'timezone', 'Europe/Bratislava');
        $this->initDefaultVarValue_gFunc('main', 'default_layout', 'default');
        $this->initDefaultVarValue_gFunc('main', 'log_dir_format', 'Y-m-d');
        $this->initDefaultVarValue_gFunc('main', 'max_errors', '5');
        $this->initDefaultVarValue_gFunc('main', 'error_display_mask', '8191');
        $this->initDefaultVarValue_gFunc('main', 'seo_url', 'on');
        $this->initDefaultVarValue_gFunc('', '', '');
        $this->initDefaultVarValue_gFunc('', '', '');
        $this->initDefaultVarValue_gFunc('', '', '');
        $this->initDefaultVarValue_gFunc('', '', '');
        $this->initDefaultVarValue_gFunc('', '', '');
        $this->initDefaultVarValue_gFunc('', '', '');
        $this->initDefaultVarValue_gFunc('', '', '');


        // seo URL
        $mod_rewrite_pVar = true;
        if(!function_exists('apache_get_modules')) {
            $mod_rewrite_pVar = false;
        }
        else {
            $x_pVar = apache_get_modules();
            if(array_search('mod_rewrite', $x_pVar, true) === false) {
                $mod_rewrite_pVar = false;
            }
        }
        if(!$mod_rewrite_pVar) {
            $this->initDefaultVarValue_gFunc('main', 'seo_url', 'off');
        }

        // languages
        foreach($this->vars_merged_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 9) !== 'language_') {
                continue;
            }
            if(isset($v_pVar['url_prefix'])) {
                continue;
            }
            $this->initDefaultVarValue_gFunc($k_pVar, 'url_prefix', 'on');
        }
    }

    private function formatValues_gFunc($section_pVar = false, $varname_pVar = false)
    {
        if($section_pVar === false || $section_pVar === 'main') {
            if($varname_pVar === false || $varname_pVar === 'documents_dir') {
                $this->formatVarValue_gFunc('main', 'documents_dir', self::FORMAT_DIRECTORY_pVar);
            }
            if($varname_pVar === false || $varname_pVar === 'max_errors') {
                $this->formatVarValue_gFunc('main', 'max_errors', self::FORMAT_INTEGER_pVar);
            }
            if($varname_pVar === false || $varname_pVar === 'error_display_mask') {
                $this->formatVarValue_gFunc('main', 'error_display_mask', self::FORMAT_INTEGER_pVar);
            }
            if($varname_pVar === false || $varname_pVar === 'seo_url') {
                $this->formatVarValue_gFunc('main', 'seo_url', self::FORMAT_BOOLEAN_pVar);
            }
        }
        if($section_pVar === false || $section_pVar === 'euro2008') {
            if($varname_pVar === false || $varname_pVar === 'enabled') {
                $this->formatVarValue_gFunc('euro2008', 'enabled', self::FORMAT_BOOLEAN_pVar);
            }
        }
        if($section_pVar === false || $section_pVar === 'access1_auth') {
            if($varname_pVar === false || $varname_pVar === 'enabled') {
                $this->formatVarValue_gFunc('access1_auth', 'enabled', self::FORMAT_BOOLEAN_pVar);
            }
        }
        if($section_pVar === false || $section_pVar === 'access2_session') {
            if($varname_pVar === false || $varname_pVar === 'enabled') {
                $this->formatVarValue_gFunc('access2_session', 'enabled', self::FORMAT_BOOLEAN_pVar);
            }
            if($varname_pVar === false || $varname_pVar === 'method') {
                $this->formatVarValue_gFunc('access2_session', 'method', self::FORMAT_ENUM_pVar, array('BOTH', 'BASIC', 'DIGEST'));
            }
        }

        // languages
        foreach($this->vars_merged_pVar as $k_pVar=>$v_pVar) {
            if(substr($k_pVar, 0, 9) !== 'language_') {
                continue;
            }
            if(isset($v_pVar['url_prefix'])) {
                continue;
            }
            if($section_pVar === false || $section_pVar === $k_pVar) {
                if($varname_pVar === false || $varname_pVar === 'url_prefix') {
                    $this->formatVarValue_gFunc($k_pVar, 'url_prefix', self::FORMAT_BOOLEAN_pVar);
                }
            }
        }
    }

    private function replaceValues_gFunc()
    {
        foreach ($this->vars_merged_pVar as $k_pVar=>$v_pVar) {
            foreach ($v_pVar as $kk_pVar=>$vv_pVar) {
                $p1_pVar = strpos($vv_pVar, '%%');
                if($p1_pVar === false) {
                    continue;
                }
                $p2_pVar = strpos($vv_pVar, '%%', $p1_pVar + 2);
                if($p1_pVar === false) {
                    continue;
                }

                $varname_pVar = substr($vv_pVar, $p1_pVar + 2, $p2_pVar - 2 - $p1_pVar);
                $varname_pVar = explode(':', $varname_pVar);
                $varValue_pVar = $this->vars_merged_pVar[$varname_pVar[0]];
                if(isset($varname_pVar[1])) {
                    $varValue_pVar = $varValue_pVar[$varname_pVar[1]];
                }
                $this->vars_merged_pVar[$k_pVar][$kk_pVar] = substr($vv_pVar, 0, $p1_pVar )
                    . $varValue_pVar
                    . substr($vv_pVar, $p2_pVar + 2);

            }
        }
    }

    private function formatVarValue_gFunc($sectionName_pVar, $varName_Var, $format_pVar, $data_pVar = false)
    {
        if(!is_array($data_pVar)) {
            $p1_pVar = strpos($data_pVar, '%%');
            if($p1_pVar !== false) {
                $p2_pVar = strpos($data_pVar, '%%', $p1_pVar + 2);
                if($p2_pVar !== false) {
                    return;
                }
            }
        }

        if(!strlen($sectionName_pVar) && !strlen($varName_Var)) {
            return;
        }
        if(!isset($this->vars_merged_pVar[$sectionName_pVar])) {
            return;
        }
        if(!isset($this->vars_merged_pVar[$sectionName_pVar][$varName_Var])) {
            return;
        }

        $value_pVar = $this->vars_merged_pVar[$sectionName_pVar][$varName_Var];
        switch ($format_pVar) {
            case self::FORMAT_DIRECTORY_pVar:
                $value_pVar = string_gClass::formatAsDirectory_gFunc($value_pVar);
                break;
            case self::FORMAT_INTEGER_pVar:
                $value_pVar = intval($value_pVar);
                break;
            case self::FORMAT_BOOLEAN_pVar:
                $value_pVar = trim(strtolower($value_pVar));
                {
                    switch ($value_pVar) {
                        case 'on':
                        case 'true':
                        case 'yes':
                        case 'ano':
                            $value_pVar = true;
                            break;
                        case 'off':
                        case 'false':
                        case 'no':
                        case 'nie':
                            $value_pVar = false;
                            break;
                        default:
                            if($value_pVar) {
                                $value_pVar = true;
                            }
                            else {
                                $value_pVar = false;
                            }
                    }
                }
                break;
            case self::FORMAT_ENUM_pVar:
                $k_pVar = array_search(trim(strtoupper($value_pVar)), $data_pVar);
                if(!$k_pVar) {
                    $value_pVar = $data_pVar[0];
                }
                else {
                    $value_pVar = $data_pVar[$k_pVar];
                }
                break;
            default;
                break;
        }
        $this->vars_merged_pVar[$sectionName_pVar][$varName_Var] = $value_pVar;
    }

    private function initDefaultVarValue_gFunc($sectionName_pVar, $varName_Var, $varValue_pVar)
    {
        if(!strlen($sectionName_pVar) && !strlen($varName_Var)) {
            return;
        }
        if(!isset($this->vars_merged_pVar[$sectionName_pVar])) {
            $this->vars_merged_pVar[$sectionName_pVar] = array();
        }
        if(!isset($this->vars_merged_pVar[$sectionName_pVar][$varName_Var])) {
            $this->vars_merged_pVar[$sectionName_pVar][$varName_Var] = $varValue_pVar;
        }
    }

    public function merge_gFunc()
    {
        // vynulujem
        $this->vars_merged_pVar = array();

        // zapamatam si config, a documents dir, pretoze tam plati specialne pravidlo pre merge
        if(isset($this->vars_default_master_pVar['config'])) {
            $config_pVar = $this->vars_default_master_pVar['config'];
        }
        if(isset($this->vars_default_master_pVar['main']) && isset($this->vars_default_master_pVar['main']['documents_dir'])) {
            $documents_dir_pVar = $this->vars_default_master_pVar['main']['documents_dir'];
        }
        if(isset($this->vars_domain_master_pVar['main']) && isset($this->vars_domain_master_pVar['main']['documents_dir'])) {
            $documents_dir_pVar = $this->vars_domain_master_pVar['main']['documents_dir'];
        }

        // default slave
        $this->mergeArray_gFunc($this->vars_default_slave_pVar, $this->vars_merged_pVar);

        // domain slave
        $this->mergeArray_gFunc($this->vars_domain_slave_pVar, $this->vars_merged_pVar);

        // default master
        $this->mergeArray_gFunc($this->vars_default_master_pVar, $this->vars_merged_pVar);

        // domain master
        $this->mergeArray_gFunc($this->vars_domain_master_pVar, $this->vars_merged_pVar);

        // system
        $this->mergeArray_gFunc($this->vars_system_pVar, $this->vars_merged_pVar);

        // obnvim zapamatane hodnoty
        if(isset($config_pVar)) {
            $this->vars_merged_pVar['config'] = $config_pVar;
        }
        if(isset($documents_dir_pVar)) {
            $this->vars_merged_pVar['main']['documents_dir'] = $documents_dir_pVar;
        }
    }

    private function mergeArray_gFunc(&$src_array_pVar, &$dest_array_pVar)
    {
        foreach($src_array_pVar as $k_pVar=>$v_pVar) {
            if(is_array($v_pVar)) { // sekcia
                if(!isset($dest_array_pVar[$k_pVar])) {
                    $dest_array_pVar[$k_pVar] = array();
                }
                foreach($v_pVar as $kk_pVar=>$vv_pVar) {
                    $dest_array_pVar[$k_pVar][$kk_pVar] = $vv_pVar;
                }
            }
            else {
                $dest_array_pVar[$k_pVar] = $v_pVar;
            }
        }
        $this->setVariables_gFunc();
    }

    public function getVar_gFunc($varName_pVar, $sectionName_pVar = '')
    {
        $value_pVar = null;

        if($sectionName_pVar == '') {
            if(isset($this->vars_merged_pVar[$varName_pVar])) {
                $value_pVar = $this->vars_merged_pVar[$varName_pVar];
            }
        }
        else {
            if(isset($this->vars_merged_pVar[$sectionName_pVar]) && isset($this->vars_merged_pVar[$sectionName_pVar][$varName_pVar])) {
                $value_pVar = $this->vars_merged_pVar[$sectionName_pVar][$varName_pVar];
            }
        }

        if($value_pVar == null) return(null);
        if(is_array($value_pVar)) {
            // toto moze nastat ked beriem celu sekciu
            return($value_pVar);
        }

        if(strtolower($value_pVar) === 'on' || strtolower($value_pVar) === 'true' || strtolower($value_pVar) === 'ano' || strtolower($value_pVar) === 'yes') $value_pVar = true;
        if(strtolower($value_pVar) === 'off' || strtolower($value_pVar) === 'false' || strtolower($value_pVar) === 'nie'  || strtolower($value_pVar) === 'no') $value_pVar = false;

        return($value_pVar);
    }

    private function setVariables_gFunc()
    {
        if(!isset($this->vars_merged_pVar['main'])) {
            $this->vars_merged_pVar['main'] = array();
        }
        if(!isset($this->vars_merged_pVar['main']['documents_dir'])) {
            $this->vars_merged_pVar['main']['documents_dir'] = $this->vars_system_pVar['runtime_pVar']['path_system_gVar'];
        }

        if(!isset($this->vars_merged_pVar['xml'])) {
            $this->vars_merged_pVar['xml'] = array();
        }

        if(!isset($this->vars_merged_pVar['database'])) {
            $this->vars_merged_pVar['database'] = array();
        }

        if(!isset($this->vars_merged_pVar['access1_auth'])) {
            $this->vars_merged_pVar['access1_auth'] = array();
        }
        if(!isset($this->vars_merged_pVar['access1_auth']['enabled'])) {
            $this->vars_merged_pVar['access1_auth']['enabled'] = 'false';
        }
        if(!isset($this->vars_merged_pVar['access1_auth']['method'])) {
            $this->vars_merged_pVar['access1_auth']['enabled'] = 'both';
        }

        if(!isset($this->vars_merged_pVar['access2_session'])) {
            $this->vars_merged_pVar['access2_session'] = array();
        }
        if(!isset($this->vars_merged_pVar['access2_session']['enabled'])) {
            $this->vars_merged_pVar['access2_session']['enabled'] = 'true';
        }
    }
}

