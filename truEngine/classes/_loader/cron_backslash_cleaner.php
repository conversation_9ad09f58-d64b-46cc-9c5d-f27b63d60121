<?php


class cron_backslash_cleaner_gClass extends cron_exec_gClass
{
    function __construct()
    {
        parent::__construct();

        $this->name_pVar = 'cron_backslash_cleaner';
    }

    protected function run_gFunc()
    {
        while(1) {
            if($this->timeExceeded_gFunc()) {
                return(0);
            }

            if(modules_gClass::isModuleRegistred_gFunc('kega')) {
                $fields_pVar = array('sk_odpoved', 'sk_odpoved_vysvetlenie', 'en_odpoved', 'en_odpoved_vysvetlenie');

                $sql_pVar = 'SELECT `item_id`, `' . implode('`, `', $fields_pVar) . '` FROM `%titems_test_answers__data` WHERE `' . implode('` LIKE \'%%\\\\\\%%\' OR `' , $fields_pVar) . '` LIKE \'%%\\\\\\%%\' LIMIT 0, 1000';
                $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

                if(!count($data_pVar)) {
                    break;
                }
                foreach($data_pVar as $k_pVar=>$v_pVar) {
                    $replace_pVar = false;

                    foreach($fields_pVar as $field_pVar) {
                        if(strpos($v_pVar[$field_pVar], '\\') !== false) {
                            $replace_pVar = true;
                            echo $v_pVar[$field_pVar] . '<br />';
                            $v_pVar[$field_pVar] = preg_replace('/\\\\+&quot;/', '&quot;', $v_pVar[$field_pVar]);
                            $v_pVar[$field_pVar] = preg_replace('/\\\\+&#039;/', '&#039;', $v_pVar[$field_pVar]);
                            echo $v_pVar[$field_pVar] . '<hr />';
                        }
                    }

                    if($replace_pVar) {
                        $sql_pVar = 'UPDATE `%titems_test_answers__data` SET `' . implode('` = %s, `', $fields_pVar) . '` = %s  WHERE `item_id` = %d';
                        $tmp_pVar = array();
                        foreach($fields_pVar as $field_pVar) {
                            $tmp_pVar[] = $v_pVar[$field_pVar];
                        }
                        $tmp_pVar[] = $v_pVar['item_id'];
                        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $tmp_pVar);

                    }
                }
            }
        }


        while(1) {
            if($this->timeExceeded_gFunc()) {
                return(0);
            }

            if(modules_gClass::isModuleRegistred_gFunc('kega')) {
                $fields_pVar = array('sk_otazka', 'sk_vysvetlenie', 'en_otazka', 'en_vysvetlenie');

                $sql_pVar = 'SELECT `item_id`, `' . implode('`, `', $fields_pVar) . '` FROM `%titems_test_questions__data` WHERE `' . implode('` LIKE \'%%\\\\\\%%\' OR `' , $fields_pVar) . '` LIKE \'%%\\\\\\%%\' LIMIT 0, 1000';
                $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);

                if(!count($data_pVar)) {
                    break;
                }
                foreach($data_pVar as $k_pVar=>$v_pVar) {
                    $replace_pVar = false;

                    foreach($fields_pVar as $field_pVar) {
                        if(strpos($v_pVar[$field_pVar], '\\') !== false) {
                            $replace_pVar = true;
                            echo $v_pVar[$field_pVar] . '<br />';
                            $v_pVar[$field_pVar] = preg_replace('/\\\\+&quot;/', '&quot;', $v_pVar[$field_pVar]);
                            $v_pVar[$field_pVar] = preg_replace('/\\\\+&#039;/', '&#039;', $v_pVar[$field_pVar]);
                            echo $v_pVar[$field_pVar] . '<hr />';
                        }
                    }

                    if($replace_pVar) {
                        $sql_pVar = 'UPDATE `%titems_test_questions__data` SET `' . implode('` = %s, `', $fields_pVar) . '` = %s  WHERE `item_id` = %d';
                        $tmp_pVar = array();
                        foreach($fields_pVar as $field_pVar) {
                            $tmp_pVar[] = $v_pVar[$field_pVar];
                        }
                        $tmp_pVar[] = $v_pVar['item_id'];
                        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, $tmp_pVar);

                    }
                }
            }
        }
        return(true);
    }
}


