<?php

class error_gClass {
    const E_OK = 0;
    const E_WARNING = E_NOTICE;
    const E_ERROR = E_WARNING;
    const E_FATAL = E_ERROR;

    // define an assoc array of error string
    static public $errortype_pVar = array (
        E_ERROR              => 'Error',
        E_WARNING            => 'Warning',
        E_PARSE              => 'Parsing Error',
        E_NOTICE             => 'Notice',
        E_CORE_ERROR         => 'Core Error',
        E_CORE_WARNING       => 'Core Warning',
        E_COMPILE_ERROR      => 'Compile Error',
        E_COMPILE_WARNING    => 'Compile Warning',
        E_USER_ERROR         => 'User Error',
        E_USER_WARNING       => 'User Warning',
        E_USER_NOTICE        => 'User Notice',
        E_STRICT             => 'Runtime Notice',
        E_RECOVERABLE_ERROR  => 'Catchable Fatal Error',
        E_DEPRECATED         => 'Deprecated ERROR'
    );

    private static $nErrors_pVar = 0;
    private static $nMaxErrors_pVar = 5;
    private static $displayErrorsLevelMask_pVar = E_ALL;
    private static $oldHandler_pVar = false;

    private static $disabledLevel_pVar = 0;

    private static $reportOff_pVar = 0;

    private function __construct()
    {

    }

    public static function init()
    {
        self::$displayErrorsLevelMask_pVar = main_gClass::getConfigVar_gFunc('error_display_mask', 'main');
        self::$nMaxErrors_pVar = main_gClass::getConfigVar_gFunc('max_errors', 'main');
    }

    static function reportOff_gFunc($off_pVar = true)
    {
        if($off_pVar) {
            self::$reportOff_pVar++;
        }
        else {
            self::$reportOff_pVar--;
            if(self::$reportOff_pVar < 0) {
                error_gClass::fatal_gFunc(__FILE__, __LINE__);
            }
        }
    }

    static function enableReporting_gFunc()
    {
        error_gClass::$disabledLevel_pVar--;
        if(error_gClass::$disabledLevel_pVar < 0) {
            error_gClass::$disabledLevel_pVar = 0;
        }
    }

    static function disableReporting_gFunc()
    {
        error_gClass::$disabledLevel_pVar++;
    }

    static function getErrorReportingStatus_gFunc()
    {
        if(error_gClass::$disabledLevel_pVar) {
            return(false);
        }
        else {
            return(true);
        }
    }

    static function _error_gFunc($err_type_pVar, $file_pVar, $line_pVar, $msg_pVar)
    {
        switch($err_type_pVar) {
            case self::E_OK:
                break;
            case self::E_WARNING:
                self::warning_gFunc($file_pVar, $line_pVar, $msg_pVar);
                break;
            case self::E_ERROR:
                self::error_gFunc($file_pVar, $line_pVar, $msg_pVar);
                break;
            case self::E_FATAL:
                self::fatal_gFunc($file_pVar, $line_pVar, $msg_pVar);
                break;
            default:
                self::warning_gFunc($file_pVar, $line_pVar, $err_type_pVar);
                break;
        }
    }

    static function _errorMessage_gFunc($err_type_pVar, $file_pVar, $line_pVar, $msg_pVar)
    {
        switch($err_type_pVar) {
            case self::E_OK:
                break;
            case self::E_WARNING:
                message_gClass::warning_gFunc('(' . $file_pVar . ':' . $line_pVar . ')' . $msg_pVar, true);
                break;
            case self::E_ERROR:
                message_gClass::error_gFunc('(' . $file_pVar . ':' . $line_pVar . ')' . $msg_pVar, true);
                break;
            case self::E_FATAL:
                message_gClass::fatal_gFunc('(' . $file_pVar . ':' . $line_pVar . ') ' . $msg_pVar, true);
                break;
            default:
                self::warning_gFunc($file_pVar, $line_pVar, $err_type_pVar);
                break;
        }
    }

    static function fatal_gFunc($file_pVar, $line_pVar, $msg_pVar='', $vars_pVar=array())
    {
        self::errorHandler_gFunc(self::E_FATAL, $msg_pVar, $file_pVar, $line_pVar, $vars_pVar);
        exit;
    }

    static function error_gFunc($file_pVar, $line_pVar, $msg_pVar='', $vars_pVar=array(), $no_count_pVar = false)
    {
        self::errorHandler_gFunc(self::E_ERROR, $msg_pVar, $file_pVar, $line_pVar, $vars_pVar, $no_count_pVar);
    }

    static function warning_gFunc($file_pVar, $line_pVar, $msg_pVar='', $vars_pVar=array(), $no_count_pVar = false)
    {
        self::errorHandler_gFunc(self::E_WARNING, $msg_pVar, $file_pVar, $line_pVar, $vars_pVar, $no_count_pVar);
    }

    static function deprecated_gFunc($file_pVar, $line_pVar, $msg_pVar='', $vars_pVar=array(), $no_count_pVar = false)
    {
        self::errorHandler_gFunc(self::E_WARNING, 'DEPRECATED: ' . $msg_pVar, $file_pVar, $line_pVar, $vars_pVar, $no_count_pVar);
    }

    static function errorHandler_gFunc($errno_pVar, $errmsg_pVar, $filename_pVar, $linenum_pVar, $vars_pVar=array(), $no_count_pVar = false)
    {
        throw new \Exception('(' . $errno_pVar . ') ' . $errmsg_pVar . ' ' . $filename_pVar . ' ' . $linenum_pVar . ' ' . var_export($vars_pVar, true) . ' ' . $no_count_pVar);

        if(self::$disabledLevel_pVar) return;

        if(self::$reportOff_pVar <= 0 && $no_count_pVar === false) {
            self::$nErrors_pVar++;
        }
//	    if(self::$nErrors_pVar > self::$nMaxErrors_pVar) {
//	        // too many errors
//	        self::disableReporting_gFunc();
//	        message_gClass::fatal_gFunc(string_gClass::get('str___loader_err_1_sVar', self::$nErrors_pVar, self::$nMaxErrors_pVar), true);
//	        self::enableReporting_gFunc();
//	        exit;
//	    }

        if(modules_gClass::isModuleLoaded_gFunc('db')) {
            mysql_connection_gClass::rollbackAllObjectsFromError_gFunc($filename_pVar, $linenum_pVar, true);
        }

        // pole MUSIM skopirovat. Inak to robi INTERNAL SERVER ERROR.. (v suvislosti s filesafe). A neviem preco. (v php4.. v php5 som neskusal bez tohto)

        //$myVars_pVar = $vars_pVar;
        //$myVars_pVar['DEBUG_BACKTRACE'] = debug_backtrace();


        // timestamp for the error entry
        $dt_pVar = date('Y-m-d H:i:s');

        // set of errors for which a var trace will be saved


        $err_pVar = $dt_pVar . ' (' . $errno_pVar . ') ' . self::$errortype_pVar[$errno_pVar]
            . ' ' .$filename_pVar . ':' . $linenum_pVar . ': ' .$errmsg_pVar;

        /*
        //$user_errors_pVar = array(E_ERROR);
        if (in_array($errno_pVar, $user_errors_pVar)) {
        	if(function_exists('wddx_serialize_value')) {
            $err_pVar .= TAB.'<vartrace>' . wddx_serialize_value($vars_pVar, 'Variables') . '</vartrace>'.LF;
        	}
        }
        */
        ob_start();
        debug_print_backtrace();
        $err_pVar .= "\n" . ob_get_contents();
        ob_end_clean();
        //$err_pVar .= string_gClass::arrayToString_gFunc(debug_backtrace(), true);

        // save to the error log, and e-mail me if there is a critical user error
        log_gClass::write_gFunc(self::$errortype_pVar[$errno_pVar], $err_pVar, true);

        //        error_log($err_pVar, 3,  . 'error.log');
        //    if ($errno_pVar == E_USER_ERROR) {
        //        mail('<EMAIL>', 'Critical User Error', $err);
        //    }

        if(self::$displayErrorsLevelMask_pVar && ($errno_pVar & self::$displayErrorsLevelMask_pVar)) {
            self::_errorMessage_gFunc($errno_pVar, $filename_pVar, $linenum_pVar, ' ' . self::$errortype_pVar[$errno_pVar] . ': ' . $errmsg_pVar);
        }
    }

    public static function registerErrorHandler_gFunc()
    {
//	   self::$oldHandler_pVar = set_error_handler('errorHandler_gFunc');
    }

    public static function restoreErrorHandler_gFunc()
    {
//	    if(self::$oldHandler_pVar === false) return;
//
//	    restore_error_handler();
//	    self::$oldHandler_pVar = false;
    }
}
