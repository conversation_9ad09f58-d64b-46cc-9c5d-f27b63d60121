<?php

class session_status_gClass extends source_gClass
{
    protected function getData()
    {
        $name_pVar = explode(';', $this->params['status-name']);
        $value_pVar = explode(';', $this->params['status-value']);
        foreach($name_pVar as $k_pVar=>$v_pVar) {
            session_gClass::saveSessionSettingsDetail_gFunc($name_pVar[$k_pVar], $value_pVar[$k_pVar]);
        }
        return(array('data'=>1));
    }
}

class session_status extends session_status_gClass {}
