<?php

abstract class source_gClass extends truEngineBaseClass_gClass {
    protected $params; // narocky neenkodovane
    protected $action_pVar;

    function __construct($action_pVar = 'get')
    {
        $this->action_pVar = $action_pVar;
    }

    function setParams_gFunc($params_pVar = array(), $initParams_pVar = true)
    {
        $this->params = $params_pVar;
        if($initParams_pVar) {
            $this->initParams_gFunc();
        }
    }

    function setParam_gFunc($paramName_pVar, $paramValue_pVar)
    {
        $this->params[$paramName_pVar] = $paramValue_pVar;
    }

    protected function getData_gFunc()
    {
        return($this->getData());
    }

    public function handleAction_gFunc()
    {
        return($this->handleAction($this->action_pVar));
    }

    protected function handleAction($action_pVar)
    {
        return($this->getData_gFunc());
    }

    protected function initParams_gFunc()
    {
        // mozem pretazit a zavolat metodu setParams/setParam
    }

    abstract protected function getData();
}
