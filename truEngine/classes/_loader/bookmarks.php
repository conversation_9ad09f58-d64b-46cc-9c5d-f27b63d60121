<?php

class bookmarks_gClass extends source_gClass
{
    protected function getData()
    {
        $doc_pVar = $this->params['doc'];
        $doc_pVar = main_gClass::removeLngPrefix_gFunc($doc_pVar);
        if(!isset($doc_pVar[0]) || $doc_pVar[0] != '/') {
            $doc_pVar = '/' . $doc_pVar;
        }

        $sql_pVar = 'SELECT bookmark_id FROM %tbookmarks_index where document = %s';
        $bookmark_id_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, $doc_pVar);

        $user_id_pVar = session_gClass::getUserDetail_gFunc('item_id');
        $ret_pVar = array('last'=>array(), 'top'=>array());
        if($user_id_pVar) {
            if($bookmark_id_pVar && $bookmark_id_pVar !== true) {
                $sql_pVar = 'SELECT bookmark_id from %tbookmarks
									WHERE user_id = %d AND bookmark_id = %d';
                $user_bookmark_id_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($user_id_pVar, $bookmark_id_pVar));

                if($user_bookmark_id_pVar && $user_bookmark_id_pVar !== true) {
                    $sql_pVar = 'UPDATE %tbookmarks SET score = score+1, last_access = now() where user_id = %d and bookmark_id = %d';
                    db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array($user_id_pVar, $bookmark_id_pVar));
                }
                else {
                    $sql_pVar= 'INSERT INTO %tbookmarks (user_id, bookmark_id, last_access, score)
									VALUES(%d, %d, now(), 1)';
                    db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($user_id_pVar, $bookmark_id_pVar));
                }
            }

            $sql_pVar = 'SELECT i.document, s.'.main_gClass::getLanguage_gFunc().'_text as `text` FROM %tbookmarks as b
							INNER JOIN %tbookmarks_index as i ON b.bookmark_id = i.bookmark_id
							INNER JOIN %tstrings as s ON i.string_id = s.id
							WHERE b.user_id = %d
							ORDER BY b.last_access desc
							LIMIT 0,4';
            $ret_pVar['last'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);
            $sql_pVar = 'SELECT i.document, s.'.main_gClass::getLanguage_gFunc().'_text as `text` FROM %tbookmarks as b
							INNER JOIN %tbookmarks_index as i ON b.bookmark_id = i.bookmark_id
							INNER JOIN %tstrings as s ON i.string_id = s.id
							WHERE b.user_id = %d
							ORDER BY b.score desc, b.last_access desc
							LIMIT 0,4';
            $ret_pVar['top'] = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, $user_id_pVar);
        }

        return($ret_pVar);
    }

    public static function addBookmarkIndex_gFunc($string_id_pVar, $url_pVar)
    {
        $url_pVar = main_gClass::removeLngPrefix_gFunc($url_pVar);
        if(!isset($url_pVar[0]) || $url_pVar[0] != '/') {
            $url_pVar = '/' . $url_pVar;
        }
        $sql_pVar = 'SELECT bookmark_id FROM `%tbookmarks_index` WHERE string_id = %d and document = %s';
        $bookmark_id_pVar = db_public_gClass::getField_gFunc($sql_pVar, __FILE__, __LINE__, array($string_id_pVar, $url_pVar));
        if(!$bookmark_id_pVar || $bookmark_id_pVar === true) {
            $sql_pVar = 'INSERT INTO %tbookmarks_index (string_id, document) VALUES(%d, %s)';
            $bookmark_id_pVar = db_public_gClass::insert_gFunc($sql_pVar, __FILE__, __LINE__, array($string_id_pVar, $url_pVar), true);
        }
    }
}

class bookmarks extends bookmarks_gClass {}
