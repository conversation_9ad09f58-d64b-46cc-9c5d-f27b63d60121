<?php


class license_gClass
{
    private function __construct()
    {

    }

    public static function getCheckCode_gFunc()
    {
        eval(string_gClass::hex2bin_gFunc('2478203d20273c696d67207372633d22687474703a2f2f7777772e656c636f6d732e736b2f636865636b2e7068703f646f6d61696e3d272e75726c656e636f6465286d61696e5f67436c6173733a3a6765745365727665725661725f6746756e6328275345525645525f4e414d452729292e2726616d703b616464723d272e75726c656e636f6465286d61696e5f67436c6173733a3a6765745365727665725661725f6746756e6328275345525645525f414444522729292e2726616d703b6b65793d272e75726c656e636f6465286d61696e5f67436c6173733a3a676574436f6e6669675661725f6746756e6328276c6963656e7365272c20276d61696e2729292e2726616d703b706174683d272e75726c656e636f6465286d61696e5f67436c6173733a3a676574436f6e6669675661725f6746756e632827706174685f7765625f67566172272c202772756e74696d655f705661722729292e2726616d703b6578743d2e6a706722207374796c653d2277696474683a203070783b206865696768743a203070783b20706f736974696f6e3a206162736f6c7574653b206c6566743a203070783b20746f703a203070783b223e273b'));
        //echo '#' . bin2hex('$x = \'<img src="http://www.elcoms.sk/check.php?domain=\'.urlencode(main_gClass::getServerVar_gFunc(\'SERVER_NAME\')).\'&amp;addr=\'.urlencode(main_gClass::getServerVar_gFunc(\'SERVER_ADDR\')).\'&amp;key=\'.urlencode(main_gClass::getConfigVar_gFunc(\'license\', \'main\')).\'&amp;path=\'.urlencode(main_gClass::getConfigVar_gFunc(\'path_web_gVar\', \'runtime_pVar\')).\'&amp;ext=.jpg" style="width: 0px; height: 0px; position: absolute; left: 0px; top: 0px;">\';') . '#';
        return($x);
    }

    /*
       static public function generateDomainKey_gFunc($serverName_pVar = false)
       {
           if($serverName_pVar === false) {
               $serverName_pVar = main_gClass::getServerVar_gFunc('S' . 'ER'. 'VE' .'R' . '_N' . 'AME');
               if($serverName_pVar === 'l'.'o'.'ca'.'l'.'host') {
                   return self::getDomainKey_gFunc();
               }
           }
           $key_pVar = $serverName_pVar; $n_pVar = strlen($serverName_pVar);
           for($i=0; $i < $n_pVar; $i++)
           {
               $key_pVar = self::encodeString_gFunc($key_pVar);

           }
           $n_pVar = 0;
           for($i_pVar = 0; isset($key_pVar[$i_pVar]); $i_pVar++) {
               $n_pVar += ord($key_pVar[$i_pVar]);
           }
           $key_pVar = sprintf('%c%s', ord('a') + $n_pVar%26, $key_pVar);

           return($key_pVar);
       }

       static public function getDomainKey_gFunc()
       {
           return main_gClass::getConfigVar_gFunc('l'.'i'.'cen'.'se', 'm'.'ain');
       }

       static private function encodeString_gFunc($str_pVar)
       {
           // predlzim/orezem na 32 znakov
           while (strlen($str_pVar) < 32) {
               $str_pVar .= $str_pVar;
           }
           $str_pVar = substr($str_pVar, 0, 32);

           for($i_pVar = 0; isset($str_pVar[$i_pVar]); $i_pVar++) {
               $str_pVar[$i_pVar] = self::rotateChar_gFunc($str_pVar[$i_pVar], (ord($str_pVar[$i_pVar]) %5 ) + $i_pVar);
           }

           return($str_pVar);
       }

       static private function rotateChar_gFunc($inputChar_pVar, $level_pVar)
       {
           $inputChar_pVar = strtolower($inputChar_pVar);
           $x_pVar = ord($inputChar_pVar);
           $x_pVar += $level_pVar;
           while($x_pVar > ord('z')) {
               $x_pVar = ord('a') + $x_pVar - ord('z');
           }
           $x_pVar = chr($x_pVar);
           return($x_pVar);
       }
       */
}


