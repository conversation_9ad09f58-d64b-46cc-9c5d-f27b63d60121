<?php


class json_gClass
{

    /**
     * Converts to and from JSON format.
     *
     * JSON (JavaScript Object Notation) is a lightweight data-interchange
     * format. It is easy for humans to read and write. It is easy for machines
     * to parse and generate. It is based on a subset of the JavaScript
     * Programming Language, Standard ECMA-262 3rd Edition - December 1999.
     * This feature can also be found in  Python. JSON is a text format that is
     * completely language independent but uses conventions that are familiar
     * to programmers of the C-family of languages, including C, C++, C#, Java,
     * JavaScript, Perl, TCL, and many others. These properties make JSON an
     * ideal data-interchange language.
     *
     * This package provides a simple encoder and decoder for JSON notation. It
     * is intended for use with client-side Javascript applications that make
     * use of HTTPRequest to perform server communication functions - data can
     * be encoded into JSON notation for use in a client-side javascript, or
     * decoded from incoming Javascript requests. JSON format is native to
     * Javascript, and can be directly eval()'ed with no further parsing
     * overhead
     *
     * All strings should be in ASCII or UTF-8 format!
     *
     * LICENSE: Redistribution and use in source and binary forms, with or
     * without modification, are permitted provided that the following
     * conditions are met: Redistributions of source code must retain the
     * above copyright notice, this list of conditions and the following
     * disclaimer. Redistributions in binary form must reproduce the above
     * copyright notice, this list of conditions and the following disclaimer
     * in the documentation and/or other materials provided with the
     * distribution.
     *
     * THIS SOFTWARE IS PROVIDED ``AS IS'' AND ANY EXPRESS OR IMPLIED
     * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
     * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN
     * NO EVENT SHALL CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT,
     * INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
     * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS
     * OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND
     * ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR
     * TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE
     * USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH
     * DAMAGE.
     *
     * @category
     * @package     Services_JSON
     * <AUTHOR> Migurski <<EMAIL>>
     * <AUTHOR> Knapp <mdknapp[at]gmail[dot]com>
     * <AUTHOR> Stimmerman <brettstimmerman[at]gmail[dot]com>
     * <AUTHOR> Dorn <<EMAIL>>
     * @copyright   2005 Michal Migurski
     * @version     CVS: $Id: JSON.php,v 1.31 2006/06/28 05:54:17 migurski Exp $
     * @license     http://www.opensource.org/licenses/bsd-license.php
     * @link        http://pear.php.net/pepr/pepr-proposal-show.php?id=198
     */


    /**
     * Keep a list of objects as we descend into the array so we can detect recursion.
     */
    private $json_objectStack_pVar = array();

    /**
     * convert a string from one UTF-8 char to one UTF-16 char
     *
     * Normally should be handled by mb_convert_encoding, but
     * provides a slower PHP-only method for installations
     * that lack the multibye string extension.
     *
     * @param    string  $utf8_pVar   UTF-8 character
     * @return   string  UTF-16 character
     * @access   private
     */
    private function json_utf82utf16_gFunc($utf8_pVar)
    {
        // oh please oh please oh please oh please oh please
        if(function_exists('mb_convert_encoding')) {
            return mb_convert_encoding($utf8_pVar, 'UTF-16', 'UTF-8');
        }

        switch(strlen($utf8_pVar)) {
            case 1:
                // this case should never be reached, because we are in ASCII range
                // see: http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                return $utf8_pVar;

            case 2:
                // return a UTF-16 character from a 2-byte UTF-8 char
                // see: http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                return chr(0x07 & (ord($utf8_pVar[0]) >> 2))
                    . chr((0xC0 & (ord($utf8_pVar[0]) << 6))
                        | (0x3F & ord($utf8_pVar[1])));

            case 3:
                // return a UTF-16 character from a 3-byte UTF-8 char
                // see: http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                return chr((0xF0 & (ord($utf8_pVar[0]) << 4))
                        | (0x0F & (ord($utf8_pVar[1]) >> 2)))
                    . chr((0xC0 & (ord($utf8_pVar[1]) << 6))
                        | (0x7F & ord($utf8_pVar[2])));
        }

        // ignoring UTF-32 for now, sorry
        return '';
    }

    /**
     * encodes an arbitrary variable into JSON format
     *
     * @param    mixed   $var    any number, boolean, string, array, or object to be encoded.
     *                           see argument 1 to Services_JSON() above for array-parsing behavior.
     *                           if var is a strng, note that encode() always expects it
     *                           to be in ASCII or UTF-8 format!
     *
     * @return   mixed   JSON string representation of input var or an error if a problem occurs
     * @access   public
     */
    public function json_encode_gFunc($var_pVar)
    {

        if(is_object($var_pVar)) {
            if(in_array($var_pVar,$this->json_objectStack_pVar)) {
                return '"** Recursion **"';
            }
        }

        switch (gettype($var_pVar)) {
            case 'boolean':
                return $var_pVar ? 'true' : 'false';

            case 'NULL':
                return 'null';

            case 'integer':
                return (int) $var_pVar;

            case 'double':
            case 'float':
                return (float) $var_pVar;

            case 'string':
                // STRINGS ARE EXPECTED TO BE IN ASCII OR UTF-8 FORMAT
                $ascii_pVar = '';
                $strlen_var_pVar = strlen($var_pVar);

                /*
                 * Iterate over every character in the string,
                 * escaping with a slash or encoding to UTF-8 where necessary
                 */
                for ($c = 0; $c < $strlen_var_pVar; ++$c) {

                    $ord_var_c_pVar = ord($var_pVar[$c]);

                    switch (true) {
                        case $ord_var_c_pVar == 0x08:
                            $ascii_pVar .= '\b';
                            break;
                        case $ord_var_c_pVar == 0x09:
                            $ascii_pVar .= '\t';
                            break;
                        case $ord_var_c_pVar == 0x0A:
                            $ascii_pVar .= '\n';
                            break;
                        case $ord_var_c_pVar == 0x0C:
                            $ascii_pVar .= '\f';
                            break;
                        case $ord_var_c_pVar == 0x0D:
                            $ascii_pVar .= '\r';
                            break;

                        case $ord_var_c_pVar == 0x22:
                        case $ord_var_c_pVar == 0x2F:
                        case $ord_var_c_pVar == 0x5C:
                            // double quote, slash, slosh
                            $ascii_pVar .= '\\'.$var_pVar[$c];
                            break;

                        case (($ord_var_c_pVar >= 0x20) && ($ord_var_c_pVar <= 0x7F)):
                            // characters U-00000000 - U-0000007F (same as ASCII)
                            $ascii_pVar .= $var_pVar[$c];
                            break;

                        case (($ord_var_c_pVar & 0xE0) == 0xC0):
                            // characters U-00000080 - U-000007FF, mask 110XXXXX
                            // see http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                            $char_pVar = pack('C*', $ord_var_c_pVar, ord($var_pVar[$c + 1]));
                            $c += 1;
                            $utf16_pVar = $this->json_utf82utf16_gFunc($char_pVar);
                            $ascii_pVar .= sprintf('\u%04s', bin2hex($utf16_pVar));
                            break;

                        case (($ord_var_c_pVar & 0xF0) == 0xE0):
                            // characters U-00000800 - U-0000FFFF, mask 1110XXXX
                            // see http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                            $char_pVar = pack('C*', $ord_var_c_pVar,
                                ord($var_pVar[$c + 1]),
                                ord($var_pVar[$c + 2]));
                            $c += 2;
                            $utf16_pVar = $this->json_utf82utf16_gFunc($char_pVar);
                            $ascii_pVar .= sprintf('\u%04s', bin2hex($utf16_pVar));
                            break;

                        case (($ord_var_c_pVar & 0xF8) == 0xF0):
                            // characters U-00010000 - U-001FFFFF, mask 11110XXX
                            // see http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                            $char_pVar = pack('C*', $ord_var_c_pVar,
                                ord($var_pVar[$c + 1]),
                                ord($var_pVar[$c + 2]),
                                ord($var_pVar[$c + 3]));
                            $c += 3;
                            $utf16_pVar = $this->json_utf82utf16_gFunc($char_pVar);
                            $ascii_pVar .= sprintf('\u%04s', bin2hex($utf16_pVar));
                            break;

                        case (($ord_var_c_pVar & 0xFC) == 0xF8):
                            // characters U-00200000 - U-03FFFFFF, mask 111110XX
                            // see http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                            $char_pVar = pack('C*', $ord_var_c_pVar,
                                ord($var_pVar[$c + 1]),
                                ord($var_pVar[$c + 2]),
                                ord($var_pVar[$c + 3]),
                                ord($var_pVar[$c + 4]));
                            $c += 4;
                            $utf16_pVar = $this->json_utf82utf16_gFunc($char_pVar);
                            $ascii_pVar .= sprintf('\u%04s', bin2hex($utf16_pVar));
                            break;

                        case (($ord_var_c_pVar & 0xFE) == 0xFC):
                            // characters U-04000000 - U-7FFFFFFF, mask 1111110X
                            // see http://www.cl.cam.ac.uk/~mgk25/unicode.html#utf-8
                            $char_pVar = pack('C*', $ord_var_c_pVar,
                                ord($var_pVar[$c + 1]),
                                ord($var_pVar[$c + 2]),
                                ord($var_pVar[$c + 3]),
                                ord($var_pVar[$c + 4]),
                                ord($var_pVar[$c + 5]));
                            $c += 5;
                            $utf16_pVar = $this->json_utf82utf16_gFunc($char_pVar);
                            $ascii_pVar .= sprintf('\u%04s', bin2hex($utf16_pVar));
                            break;
                    }
                }

                return '"'.$ascii_pVar.'"';

            case 'array':
                /*
                 * As per JSON spec if any array key is not an integer
                 * we must treat the the whole array as an object. We
                 * also try to catch a sparsely populated associative
                 * array with numeric keys here because some JS engines
                 * will create an array with empty indexes up to
                 * max_index which can cause memory issues and because
                 * the keys, which may be relevant, will be remapped
                 * otherwise.
                 *
                 * As per the ECMA and JSON specification an object may
                 * have any string as a property. Unfortunately due to
                 * a hole in the ECMA specification if the key is a
                 * ECMA reserved word or starts with a digit the
                 * parameter is only accessible using ECMAScript's
                 * bracket notation.
                 */

                // treat as a JSON object
                if (is_array($var_pVar) && count($var_pVar) && (array_keys($var_pVar) !== range(0, sizeof($var_pVar) - 1))) {

                    $this->json_objectStack_pVar[] = $var_pVar;

                    $properties_pVar = array_map(array($this, 'json_name_value_gFunc'),
                        array_keys($var_pVar),
                        array_values($var_pVar));

                    array_pop($this->json_objectStack_pVar);

                    foreach($properties_pVar as $property_pVar) {
                        if($property_pVar instanceof Exception) {
                            return $property_pVar;
                        }
                    }

                    return '{' . join(',', $properties_pVar) . '}';
                }

                $this->json_objectStack_pVar[] = $var_pVar;

                // treat it like a regular array
                $elements_pVar = array_map(array($this, 'json_encode_gFunc'), $var_pVar);

                array_pop($this->json_objectStack_pVar);

                foreach($elements_pVar as $element_pVar) {
                    if($element_pVar instanceof Exception) {
                        return $element_pVar;
                    }
                }

                return '[' . join(',', $elements_pVar) . ']';

            case 'object':
                $vars_pVar = self::encodeObject($var_pVar);

                $this->json_objectStack_pVar[] = $var_pVar;

                $properties_pVar = array_map(array($this, 'json_name_value_gFunc'),
                    array_keys($vars_pVar),
                    array_values($vars_pVar));

                array_pop($this->json_objectStack_pVar);

                foreach($properties_pVar as $property_pVar) {
                    if($property_pVar instanceof Exception) {
                        return $property_pVar;
                    }
                }

                return '{' . join(',', $properties_pVar) . '}';

            default:
                return null;
        }
    }

    /**
     * array-walking function for use in generating JSON-formatted name-value pairs
     *
     * @param    string  $name   name of key to use
     * @param    mixed   $value  reference to an array element to be encoded
     *
     * @return   string  JSON-formatted name-value pair, like '"name":value'
     * @access   private
     */
    private function json_name_value_gFunc($name_pVar, $value_pVar)
    {
        // Encoding the $GLOBALS PHP array causes an infinite loop
        // if the recursion is not reset here as it contains
        // a reference to itself. This is the only way I have come up
        // with to stop infinite recursion in this case.
        if($name_pVar=='GLOBALS'
            && is_array($value_pVar)
            && array_key_exists('GLOBALS',$value_pVar)) {
            $value_pVar['GLOBALS'] = '** Recursion **';
        }

        $encoded_value_pVar = $this->json_encode_gFunc($value_pVar);

        if($encoded_value_pVar instanceof Exception) {
            return $encoded_value_pVar;
        }

        return $this->json_encode_gFunc(strval($name_pVar)) . ':' . $encoded_value_pVar;
    }
}
