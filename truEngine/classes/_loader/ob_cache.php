<?php

class ob_cache_gClass
{
    static private $buffers_pVar = array();
    private $uid;

    function __construct()
    {
        $tmp_dir_pVar = main_gClass::getConfigVar_gFunc('documents_dir', 'main');
        $tmp_dir_pVar .= '.tmp/';
        $this->uid = uniqid('buffer-', true);
        self::$buffers_pVar[$this->uid] = array();
        self::$buffers_pVar[$this->uid]['cache'] = $tmp_dir_pVar . $this->uid;
        self::$buffers_pVar[$this->uid]['fd'] = fopen($tmp_dir_pVar . $this->uid, 'wb');
        self::$buffers_pVar[$this->uid]['fileName'] = $tmp_dir_pVar . $this->uid;
        self::$buffers_pVar[$this->uid]['encoding'] = false;

        echo '_OB_BUFFER_' . $this->uid . '_';
    }

    static function isBuffers_gFunc()
    {
        return(count(self::$buffers_pVar)?true:false);
    }

    function echo_gFunc($str_pVar)
    {
        if(self::$buffers_pVar[$this->uid]['fd']) {
            if(!empty(self::$buffers_pVar[$this->uid]['encoding'])) {
                $str_pVar = main_gClass::iconv_gFunc($str_pVar, self::$buffers_pVar[$this->uid]['encoding']);
            }
            fputs(self::$buffers_pVar[$this->uid]['fd'], $str_pVar);
        }
    }

    function echoCsv_gFunc($fields_pVar)
    {
        if(self::$buffers_pVar[$this->uid]['fd']) {
            if(!empty(self::$buffers_pVar[$this->uid]['encoding'])) {
                foreach($fields_pVar as $k_pVar=>$v_pVar) {
                    $fields_pVar[$k_pVar] = main_gClass::iconv_gFunc($v_pVar, self::$buffers_pVar[$this->uid]['encoding']);
                }
            }
            fputcsv(self::$buffers_pVar[$this->uid]['fd'], $fields_pVar);
        }
    }

    function setEncoding_gFunc($encoding_pVar)
    {
        self::$buffers_pVar[$this->uid]['encoding'] = $encoding_pVar;
    }

    function __destruct()
    {
        if(self::$buffers_pVar[$this->uid]['fd']) {
            fclose(self::$buffers_pVar[$this->uid]['fd']);
            self::$buffers_pVar[$this->uid]['fd'] = null;
        }
    }

    static function flushBuffer_gFunc($uid)
    {
        if(!isset(self::$buffers_pVar[$uid])) {
            return(0);
        }
        if(self::$buffers_pVar[$uid]['fd']) {
            fclose(self::$buffers_pVar[$uid]['fd']);
            self::$buffers_pVar[$uid]['fd'] = null;
        }

        $length_pVar = 0;

        $f_pVar = fopen(self::$buffers_pVar[$uid]['fileName'], 'rb');
        while(1) {
            $str_pVar = fread($f_pVar, 10000);
            if($str_pVar === false) {
                break;
            }
            echo $str_pVar;
            $length_pVar += string_gClass::getStringWidth_gFunc($str_pVar);
            if(feof($f_pVar)) {
                break;
            }
        }
        fclose($f_pVar);
        unlink(self::$buffers_pVar[$uid]['fileName']);
        unset(self::$buffers_pVar[$uid]);
        return($length_pVar);
    }

    static function destroyBuffers_gFunc()
    {
        foreach(self::$buffers_pVar as $k_pVar=>$v_pVar) {
            if($v_pVar['fd']) {
                fclose($v_pVar['fd']);
            }
            if(file_exists($v_pVar['fileName'])) {
                unlink($v_pVar['fileName']);
            }
            unset(self::$buffers_pVar[$k_pVar]);
        }
    }

}
