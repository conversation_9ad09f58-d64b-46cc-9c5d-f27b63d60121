<?php


class session_gClass extends truEngineBaseClass_gClass
{
    const AUTH_TYPE_NONE_pVar = 0;
    const AUTH_TYPE_HTTP_pVar = 1;
    const AUTH_TYPE_HTTP_BASIC_pVar = 2;
    const AUTH_TYPE_HTTP_DIGEST_pVar = 3;
    const AUTH_TYPE_SESSION_pVar = 4;

    const ACCESS_NONE_pVar = 0;
    const ACCESS_MESSAGE_FAIL_pVar = 1;
    const ACCESS_MESSAGE_OK_pVar = 2;
    const ACCESS_LOG_FAIL_pVar = 4;
    const ACCESS_LOG_OK_pVar = 8;
    const ACCESS_UNFINISHED_pVar = 16;
    const ACCESS_FINISHED_pVar = 32;
    const ACCESS_MESSAGE_pVar = 3; // ACCESS_MESSAGE_FAIL_pVar | ACCESS_MESSAGE_OK_pVar
    const ACCESS_LOG_pVar = 12; // ACCESS_LOG_FAIL_pVar | ACCESS_LOG_FAIL_pVar
    const ACCESS_FAIL_pVar = 5; // ACCESS_MESSAGE_FAIL_pVar | ACCESS_LOG_FAIL_pVar
    const ACCESS_OK_pVar = 10; // ACCESS_MESSAGE_OK_pVar | ACCESS_LOG_OK_pVar
    const ACCESS_ALL_pVar = 15; // ACCESS_MESSAGE_pVar | ACCESS_LOG_pVar

    private static $dont_check_rights_pVar = 0;
    protected static $instance = false;
    protected static $session_id_pVar = false;
    protected static $loginPrompt_pVar = false;
    private static $rights_pVar = array(
    );
    private static $temporary_rights_pVar = array();
    protected $userID_pVar = 0;
    protected $login_doc_pVar;

    // A private constructor; prevents direct creation of object
    protected function __construct() {
        $this->login_doc_pVar = '/' . main_gClass::getLanguage_gFunc() . '/log-in/';
        if(modules_gClass::isModuleRegistred_gFunc('kega', false)) {
            if(main_gClass::getInputString_gFunc('login_isic', main_gClass::SRC_GETPOST_pVar, false, null) !== null
                || main_gClass::getInputString_gFunc('login_kega', main_gClass::SRC_GETPOST_pVar, false, null) !== null) {
                if(main_gClass::getConfigVar_gFunc('isic_login_doc', 'kega')) {
                    $this->login_doc_pVar = '/' . main_gClass::getLanguage_gFunc() . main_gClass::getConfigVar_gFunc('isic_login_doc', 'kega');
                }
            }
        }
        self::$rights_pVar = main_gClass::getGuestDefaultRights_gFunc();
    }

    // Prevent users to clone the instance
    public function __clone()
    {
        error_gClass::fatal_gFunc(__FILE__, __LINE__, 'Clone is not allowed.');
    }

    /**
     *
     * VSTUPNY BOD PRE AUTENTIFIKACIU
     *
     * // The singleton method
     * Vytvorim objekt podla potreby..
     * Ak detekujem session, vytvorim typ objektu podla session (je jedno ci je autentifikovana, alebo timeoutovana)
     * Ak nedetekujem session, pozriem ci detekujem login udaje. Ak hej, vytvorim objekt podla udajov..
     * Ak nedetekujem nic, vytvorim obycajny session_gClass objekt
     *
     * Nahram prislusny modul.
     *
     * Ak detekujem sesion, nastavim aj session_id
     * @return unknown
     */
    static public function getSessionObject_gFunc()
    {
        if (self::$instance !== false) {
            return(self::$instance);
        }

        $class_pVar = false;
        $session_id_pVar = self::getSessionIdFromInput_gFunc();

        if(strlen($session_id_pVar) > 1) {
            // access2_session
            $class_pVar = 'session_session_gClass';
            self::isLoginPrompt_gFunc(self::AUTH_TYPE_SESSION_pVar);
        }
        elseif ($session_id_pVar === self::AUTH_TYPE_HTTP_BASIC_pVar) {
            $class_pVar = 'session_auth_basic_gClass';
            self::isLoginPrompt_gFunc(self::AUTH_TYPE_HTTP_BASIC_pVar);
        }
        elseif ($session_id_pVar === self::AUTH_TYPE_HTTP_DIGEST_pVar) {
            $class_pVar = 'session_auth_digest_gClass';
            self::isLoginPrompt_gFunc(self::AUTH_TYPE_HTTP_DIGEST_pVar);
        }

        // else
        if($class_pVar === false) {
            $loginPrompt_pVar = self::isLoginPrompt_gFunc();
            switch($loginPrompt_pVar) {
                case self::AUTH_TYPE_NONE_pVar:
                    $class_pVar = __CLASS__;
                    break;
                case self::AUTH_TYPE_SESSION_pVar:
                    $class_pVar = 'session_session_gClass';
                    break;
                case self::AUTH_TYPE_HTTP_BASIC_pVar:
                    $class_pVar = 'session_auth_basic_gClass';
                    break;
                case self::AUTH_TYPE_HTTP_DIGEST_pVar:
                    $class_pVar = 'session_auth_digest_gClass';
                    break;
                default:
                    $class_pVar = __CLASS__;
                    break;
            }
        }

        // nahram modul a vytvorim objekt
        if(($class_pVar == 'session_auth_basic_gClass' || $class_pVar == 'session_auth_digest_gClass')
            && modules_gClass::isModuleRegistred_gFunc('access1_auth')) {
            self::$instance = new $class_pVar;
        }
        elseif ($class_pVar == 'session_session_gClass' && modules_gClass::isModuleRegistred_gFunc('access2_session')) {
            self::$instance = new $class_pVar;
        }
        else {
            $class_pVar = __CLASS__;
            self::$instance = new $class_pVar;
        }


        main_gClass::$mainVars_pVar['_login_pass_min_len'] = 4;
        if(self::$instance->startTransaction_gFunc(__FILE__, __LINE__)) {

            $session_authentified = false;
            if($session_id_pVar !== false) {
                $session_authentified = self::$instance->authSession_gFunc();
            }

            if(!$session_authentified && auth()->check()) {
                self::$instance->authLaravelUser_gFunc();
            }

            if(!$session_authentified && self::$loginPrompt_pVar !== false) {
                self::$instance->authUser_gFunc();
            }
            self::$instance->commit_gFunc(__FILE__, __LINE__);
        }

        if($class_pVar == 'session_session_gClass') {
            // spravim logout, ak treba
            $logout_prompt_pVar = main_gClass::getInputBoolean_gFunc('logout');
            if($logout_prompt_pVar) {
                self::$instance->logout_gFunc();
                main_gClass::$mainVars_pVar['_logout_msg'] = 'logged_out';
            }
            else {
                $logout_prompt_pVar = main_gClass::getInputBoolean_gFunc('logout_session');
                if($logout_prompt_pVar) {
                    self::$instance->logout_gFunc();
                    main_gClass::$mainVars_pVar['_logout_msg'] = 'logged_out';
                }
            }
        }

        // ak je neovereny, tak vytvorim objekt session_gClass
        if(!self::$instance->isLoggedOn_gFunc() && self::$instance->getClassName() != 'session_gClass') {
            self::$instance = new session_gClass();
        }


        return(self::$instance);
    }


    static public function rightsLoaded_gFunc()
    {
        if(!self::$instance) {
            return(false);
        }
        return(self::$instance->_rightsLoaded_gFunc());
    }

    protected function _rightsLoaded_gFunc()
    {
        return(false);
    }

    public function setUserId_gFunc($user_id_pVar)
    {
        $this->userID_pVar = $user_id_pVar;
    }

    /**
     * Zisti, ci si prihlasovacie udaje. Ak existuju, ulozi ich do pola self::$loginPrompt_pVar
     * V pripade AUTH_DIGEST neulozi login ani heslo. Iba typ.
     * Nenahrava ziadne moduly.
     *
     * $promptType_pVar mozem vsugerovat, ze detehuje iba jeden typ autentifikacie.
     * Presmeruje na HTTPS, ak objavi prihlasovacie udaje.
     *
     * @param unknown_type $promptType_pVar
     * @return unknown
     */
    static protected function isLoginPrompt_gFunc($promptType_pVar = self::AUTH_TYPE_NONE_pVar)
    {
        // najskor session, ma vacsiu prioritu
        if(modules_gClass::isModuleRegistred_gFunc('access2_session', false)) {
            if($promptType_pVar == self::AUTH_TYPE_NONE_pVar || $promptType_pVar == self::AUTH_TYPE_SESSION_pVar) {
                if(main_gClass::getConfigVar_gFunc('enabled', 'access2_session')) {
                    $login_pVar = main_gClass::getInputString_gFunc('login', main_gClass::SRC_REQUEST_pVar);
                    $password_pVar = main_gClass::getInputString_gFunc('password', main_gClass::SRC_REQUEST_pVar);
                    if($login_pVar !== null) {
                        self::$loginPrompt_pVar = array();
                        self::$loginPrompt_pVar['login_pVar'] = $login_pVar;
                        self::$loginPrompt_pVar['password_pVar'] = $password_pVar;
                        self::$loginPrompt_pVar['type_pVar'] = self::AUTH_TYPE_SESSION_pVar;
                        if(modules_gClass::isModuleRegistred_gFunc('https')) {
                            https_gClass::check_gFunc();
                        }
                        return(self::AUTH_TYPE_SESSION_pVar);
                    }
                    else {
                        if(modules_gClass::isModuleRegistred_gFunc('kega', false)) {
                            $login_isic_pVar = main_gClass::getInputString_gFunc('login_isic', main_gClass::SRC_REQUEST_pVar);
                            $login_last_name_pVar = main_gClass::getInputString_gFunc('login_last_name', main_gClass::SRC_REQUEST_pVar);
                            $login_first_name_pVar = main_gClass::getInputString_gFunc('login_first_name', main_gClass::SRC_REQUEST_pVar);

                            $login_kega_pVar = main_gClass::getInputString_gFunc('login_kega', main_gClass::SRC_REQUEST_pVar);
                            $login_pass_pVar = main_gClass::getInputString_gFunc('password', main_gClass::SRC_REQUEST_pVar);

                            if($login_kega_pVar !== null) {
                                self::$loginPrompt_pVar['login_kega_pVar'] = $login_kega_pVar;
                                self::$loginPrompt_pVar['password_pVar'] = $login_pass_pVar;
                                self::$loginPrompt_pVar['type_pVar'] = self::AUTH_TYPE_SESSION_pVar;
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                return(self::AUTH_TYPE_SESSION_pVar);
                            }

                            if($login_isic_pVar !== null) {
                                self::$loginPrompt_pVar['login_isic_pVar'] = $login_isic_pVar;
                                self::$loginPrompt_pVar['login_last_name_pVar'] = $login_last_name_pVar;
                                self::$loginPrompt_pVar['login_first_name_pVar'] = $login_first_name_pVar;
                                self::$loginPrompt_pVar['type_pVar'] = self::AUTH_TYPE_SESSION_pVar;
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                return(self::AUTH_TYPE_SESSION_pVar);
                            }
                        }
                    }
                }
            }
        }

        // teraz auth
        if(modules_gClass::isModuleRegistred_gFunc('access1_auth', false)) {
            if($promptType_pVar == self::AUTH_TYPE_NONE_pVar || $promptType_pVar == self::AUTH_TYPE_HTTP_pVar
                || $promptType_pVar == self::AUTH_TYPE_HTTP_BASIC_pVar
                || $promptType_pVar == self::AUTH_TYPE_HTTP_DIGEST_pVar) {
                if(main_gClass::getConfigVar_gFunc('enabled', 'access1_auth')) {
                    if($promptType_pVar != self::AUTH_TYPE_HTTP_BASIC_pVar) {
                        // DIGEST
                        if(!main_gClass::getInputBoolean_gFunc('auth_logout_digest') && !main_gClass::getInputBoolean_gFunc('auth_logout')) {
                            if(main_gClass::getServerVar_gFunc('PHP_AUTH_DIGEST') !== null) {
                                self::$loginPrompt_pVar = array();
                                self::$loginPrompt_pVar['type_pVar'] = self::AUTH_TYPE_HTTP_DIGEST_pVar;
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                return(self::AUTH_TYPE_HTTP_DIGEST_pVar);
                            }
                        }
                    }
                    if($promptType_pVar != self::AUTH_TYPE_HTTP_DIGEST_pVar) {
                        // BASIC
                        if(!main_gClass::getInputBoolean_gFunc('auth_logout_basic') && !main_gClass::getInputBoolean_gFunc('auth_logout')) {
                            if(main_gClass::getServerVar_gFunc('PHP_AUTH_USER') !== null) {
                                self::$loginPrompt_pVar = array();
                                self::$loginPrompt_pVar['login_pVar'] = main_gClass::getServerVar_gFunc('PHP_AUTH_USER');
                                self::$loginPrompt_pVar['password_pVar'] = main_gClass::getServerVar_gFunc('PHP_AUTH_PW');
                                self::$loginPrompt_pVar['type_pVar'] = self::AUTH_TYPE_HTTP_BASIC_pVar;
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                return(self::AUTH_TYPE_HTTP_BASIC_pVar);
                            }
                        }
                    }
                }
            }
        }

        self::$loginPrompt_pVar = false;
        return(self::AUTH_TYPE_NONE_pVar);
    }

    static public function getSessionId_pVar()
    {
        return(self::$session_id_pVar);
    }

    /**
     * vrati session_ID
     * ak este nie je session id nastavene, tak ho nastavi a vrati nastavenu hodnotu
     * nenahrava ziadne moduly, co je dolezita vlastnost tejto metody.
     * Presmeruje na HTTPS, ak objavi existujucu session.
     *
     * @return session_ID
     */
    static protected function getSessionIdFromInput_gFunc($objectType_pVar = self::AUTH_TYPE_NONE_pVar, $reserved_pVar=false)
    {
        $sessionType_pVar = $reserved_pVar;

        if(self::$session_id_pVar !== false) {
            return(self::$session_id_pVar);
        }

        // access2_session ma prednost pred auth
        if(modules_gClass::isModuleRegistred_gFunc('access2_session', false)) { // modul som nenahral, iba zistujem ci je pouzitelny
            if($objectType_pVar === self::AUTH_TYPE_NONE_pVar || $objectType_pVar === self::AUTH_TYPE_SESSION_pVar) {
                if(main_gClass::getConfigVar_gFunc('enabled', 'access2_session')) {
                    if($sessionType_pVar === false) {
                        $sessionType_pVar = main_gClass::getConfigVar_gFunc('method', 'access2_session');
                    }

                    /**
                     * NATVRDO ZADEFINUJEM TYP AUTENTIFIKACIE.. OSTATNE (COOKIE a BOTH) su NEPOUZITELNE, pretoze
                     * sa mi zdaju zbytocne. V konfiguraku teda method nemusi byt definovana.
                     */
                    $sessionType_pVar = 'session';

                    $sessionType_pVar = strtolower($sessionType_pVar);
                    $sessionTypeByConfig_pVar = strtolower(main_gClass::getConfigVar_gFunc('method', 'access2_session'));

                    // dd($sessionType_pVar); // session

                    if($sessionTypeByConfig_pVar === $sessionType_pVar || $sessionTypeByConfig_pVar === 'both') {
                        if($sessionType_pVar === 'session') {
                            //$session_id_pVar = main_gClass::getInputString_gFunc('session_id_pVar', main_gClass::SRC_SESSION_pVar, '/[a-zA-Z0-9]*/');

                            $session_id_pVar = request()->session()->getId();


                            if(strlen($session_id_pVar)) {
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                self::$session_id_pVar = $session_id_pVar;
                                if(empty(self::$session_id_pVar)) {
                                    self::$session_id_pVar = false;
                                }
                                return(self::$session_id_pVar);
                            }
                        }
                        else if($sessionType_pVar === 'cookies') {
                            $cookieSessionId_pVar = main_gClass::getInputString_gFunc('session_id_pVar', main_gClass::SRC_COOKIE_pVar, '/[a-zA-Z0-9]*/');
                            if(strlen($cookieSessionId_pVar)) {
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                self::$session_id_pVar =  $cookieSessionId_pVar;
                                if(empty(self::$session_id_pVar)) {
                                    self::$session_id_pVar = false;
                                }
                                return(self::$session_id_pVar);
                            }
                        }
                        else  if($sessionType_pVar === 'both') {
                            $sesID1_pVar = self::getSessionIdFromInput_gFunc(self::AUTH_TYPE_SESSION_pVar, 'session');
                            self::$session_id_pVar = false;
                            $sesID2_pVar = self::getSessionIdFromInput_gFunc(self::AUTH_TYPE_SESSION_pVar, 'cookies');
                            self::$session_id_pVar = false;
                            if($sesID1_pVar !== false) {
                                if($sesID1_pVar === $sesID2_pVar) {
                                    self::$session_id_pVar = $sesID1_pVar;
                                }
                                else {
                                    self::$session_id_pVar = false;
                                }
                                return(self::$session_id_pVar);
                            }
                        }
                    }
                }
            }
            // nedam return, skusim este HTTP_AUTH
        }

        if($objectType_pVar === self::AUTH_TYPE_SESSION_pVar) {
            return(false);
        }

        if(modules_gClass::isModuleRegistred_gFunc('access1_auth', false)) { // modul som nenahral, iba zistujem ci je pouzitelny
            if(main_gClass::getConfigVar_gFunc('enabled', 'access1_auth')) {
                if($sessionType_pVar === false) {
                    $sessionType_pVar = main_gClass::getConfigVar_gFunc('method', 'access1_auth');
                }
                $sessionType_pVar = strtolower($sessionType_pVar);
                $sessionTypeByConfig_pVar = strtolower(main_gClass::getConfigVar_gFunc('method', 'access1_auth'));

                if($sessionTypeByConfig_pVar === $sessionType_pVar || $sessionTypeByConfig_pVar === 'both') {
                    if($objectType_pVar === self::AUTH_TYPE_NONE_pVar
                        || $objectType_pVar === self::AUTH_TYPE_HTTP_pVar
                        || $objectType_pVar === self::AUTH_TYPE_HTTP_DIGEST_pVar) {
                        if($sessionType_pVar === 'digest' || $sessionType_pVar === 'both') {
                            if(main_gClass::getServerVar_gFunc('PHP_AUTH_DIGEST') !== null
                                || main_gClass::getInputBoolean_gFunc('auth_login_digest')
                                || main_gClass::getInputBoolean_gFunc('auth_login')) {
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                self::$session_id_pVar = self::AUTH_TYPE_HTTP_DIGEST_pVar;
                                return(self::$session_id_pVar);
                            }
                        }
                    }
                    if($objectType_pVar === self::AUTH_TYPE_NONE_pVar
                        || $objectType_pVar === self::AUTH_TYPE_HTTP_pVar
                        || $objectType_pVar === self::AUTH_TYPE_HTTP_BASIC_pVar) {
                        if($sessionType_pVar === 'basic' || $sessionType_pVar === 'both') {
                            if(main_gClass::getServerVar_gFunc('PHP_AUTH_USER') !== null
                                || main_gClass::getInputBoolean_gFunc('auth_login_basic')
                                || main_gClass::getInputBoolean_gFunc('auth_login')) {
                                if(modules_gClass::isModuleRegistred_gFunc('https')) {
                                    https_gClass::check_gFunc();
                                }
                                self::$session_id_pVar = self::AUTH_TYPE_HTTP_BASIC_pVar;
                                return(self::$session_id_pVar);
                            }
                        }
                    }
                }
            }
        }
        return(false);
    }

    protected function startTransaction_gFunc($fileName_pVar, $lineNum_pVar)
    {
        return(true);
    }

    protected function commit_gFunc($fileName_pVar, $lineNum_pVar)
    {
        return(true);
    }

    protected function rollback_gFunc($fileName_pVar, $lineNum_pVar)
    {
        return(true);
    }

    public function isLoggedOn_gFunc()
    {
        return(false);
    }

    static public function giveMeFullAccess_gFunc()
    {
        self::$dont_check_rights_pVar++;
    }

    static public function revokeMeFullAccess_gFunc($total_pVar = false)
    {
        if($total_pVar) {
            self::$dont_check_rights_pVar = 0;
        }
        else {
            self::$dont_check_rights_pVar--;
            if(self::$dont_check_rights_pVar < 0) {
                self::$dont_check_rights_pVar = 0;
            }
        }
    }

    static public function isFullAccess_gFunc()
    {
        if(self::$dont_check_rights_pVar > 0) {
            return(true);
        }
        else {
            return(false);
        }
    }

    /**
     * abstraktna metoda..
     * pre session_gClass triedu nerobi nic.
     * pre ostatnych potomkov:
     * 		ak existuje session_id, tak ju autentifikuje...
     * 		ak neexistuje, a existuje loginprompt, tak overi heslo a vytvori session
     *
     * 	vysledkom je autentifikovany objekt (resp. staticka classa) s nastavenym userID
     * 		alebo neautentifikovany objekt bez userID
     */
    protected function authSession_gFunc()
    {
        return(false);
    }

    /**
     * abstraktna metoda..
     * pre session_gClass triedu nerobi nic.
     * pre ostatnych potomkov:
     * 		ak existuje loginprompt, tak overi heslo a vytvori session
     *
     * 	vysledkom je autentifikovany objekt (resp. staticka classa) s nastavenym userID
     * 		alebo neautentifikovany objekt bez userID
     */
    protected function authUser_gFunc()
    {
        return(false);
    }

    /**
     * Zrusi session...
     */
    protected function destroySession_gFunc()
    {
        // Implementacia v detskej triede.
    }

    static public function logout_gFunc()
    {
        if(self::$instance !== false) {
            self::$instance->destroySession_gFunc();
            self::$instance = false;
        }
        self::$instance = new session_gClass();
    }

    /**
     * $action_id_pVar je ACTION_ID, alebo je to pole, v ktorom je viacej action_id.
     * $objectID_pVar je asociativne pole, key je nazov vlastnosti, a value je ID objektu.
     *
     * Ak $actionID_pVar je pole, tak potom polia $actionID_pVar a $objectID_pVar prechadzam sucastne
     * pomocou foreach, a vzajomne parujem. Ak $actionID_pVar nie je pole, tak action sparujem so vsetkymi
     * polozkami v poli $objectID_pVar.
     *
     * Jednotlive vysledky $actionID_pVar spracujem pomocou operatora AND a podla toho vratim vysledok.
     * Prejdem vzdy vsetky hodnoty (nie len po prvy false). Ale zobrazim iba jednu hlasku? S zalogujem tiez jednu?
     *
     * ZATIAL NEIMPLEMENTUJEM $actionID_pVar AKO POLE... AZ KED TO BUDEM POTREBOVAT
     *
     * $logValue_pVar je hodnota, ktoru zalogujem (zasa asociativne pole).
     *
     */

    /**
     * budem pretazovat iba tuto metodu, ostatne budu volane cez session_gClass staticky, a zavolaju
     * self::$instance->_userHasRights_gFunc, teda pretazenu metodu.
     * pretazena metoda vyhodnoti prava, zavola session_gClass::_userHasRights_gFunc (ta zaloguje),
     * a potom vrati vyhodnotene prava (true/false)
     */
    protected function _userHasRights_gFunc($actionID_pVar, $objectID_pVar = false, $status_pVar = self::ACCESS_ALL_pVar, $logValue_pVar = false, $userID_pVar = false, $childResult_pVar = null)
    {
        if(self::$dont_check_rights_pVar) {
            return(true);
        }
        if($userID_pVar) {
            error_gClass::fatal_gFunc(__FILE__,__LINE__, 'Not implemented.');
        }
        if(is_array($actionID_pVar) || is_array($objectID_pVar) || is_array($logValue_pVar)) {
            error_gClass::fatal_gFunc(__FILE__,__LINE__,'Not implemented');
        }

        if($childResult_pVar === null) {
            if(!modules_gClass::isModuleRegistred_gFunc('access1_auth', false)
                && !modules_gClass::isModuleRegistred_gFunc('access2_session', false)) {
                // ak nie je ziadny autentifikacny modul instalovany, vraciam vzdy true
                $ret_pVar = true;
            }
            else {
                // ak je instalovany autentifikacny modul, overim prava neprihlaseneho pouzivatela,
                // pretoze ak to riesim cez tento objekt, znamena to ze pouzivatel nie je prihlaseny
                if(isset(self::$rights_pVar[$actionID_pVar]) && self::$rights_pVar[$actionID_pVar]) {
                    $ret_pVar = true;
                }
                else {
                    $ret_pVar = false;
                }
            }
        }
        else {
            $ret_pVar = $childResult_pVar;
        }

        if(isset(self::$temporary_rights_pVar[$actionID_pVar])) {
            $ret_pVar = self::$temporary_rights_pVar[$actionID_pVar]?true:false;
        }

        $logMessage_pVar = '';
        if($userID_pVar === false) {
            $userID_pVar = $this->userID_pVar;
        }
        $logMessage_pVar = 'userID=' . $userID_pVar . ',';
        $logMessage_pVar .= 'actionID='.$actionID_pVar;
        $logMessage_pVar .= ',status='.$status_pVar;
        if($logValue_pVar !== false) {
            $logMessage_pVar .= ',logValue='.$logValue_pVar;
        }

        if($ret_pVar) {
            log_gClass::write_gFunc('RIGHTS_OK', $logMessage_pVar);
        }
        else {
            log_gClass::write_gFunc('RIGHTS_FAIL', $logMessage_pVar);
        }
        return($ret_pVar);
    }

    static private function getActionId_gFunc($actionID_pVar)
    {
        if(!is_numeric($actionID_pVar)) {
            $actionID_pVar = constant($actionID_pVar);
        }
        return(intval($actionID_pVar));
    }

    static function userHasRightsInfo_gFunc($actionID_pVar, $objectID_pVar = false, $extendedStatus_pVar = 0, $logValue_pVar = false)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        $status_pVar = self::ACCESS_NONE_pVar | $extendedStatus_pVar;
        return(self::userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar));
    }

    static function userHasRightsAccess_gFunc($actionID_pVar, $objectID_pVar = false, $extendedStatus_pVar = 0, $logValue_pVar = false)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        $status_pVar = self::ACCESS_FAIL_pVar | $extendedStatus_pVar;
        return(self::userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar));
    }

    static function userHasRightsAccessAction_gFunc($actionID_pVar, $objectID_pVar = false, $extendedStatus_pVar = 0, $logValue_pVar = false)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        $status_pVar = self::ACCESS_ALL_pVar | $extendedStatus_pVar;
        return(self::userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar));
    }

    static function userHasRightsForUser_gFunc($actionID_pVar, $userID_pVar = false, $objectID_pVar = false, $status_pVar = self::ACCESS_NONE_pVar)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        return(self::userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, false, $userID_pVar));
    }

    static function userHasRights_gFunc($actionID_pVar, $objectID_pVar = false, $status_pVar = self::ACCESS_ALL_pVar, $logValue_pVar = false, $userID_pVar = false)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        if(self::$dont_check_rights_pVar) {
            return(true);
        }
        if(self::$instance === false) {
            self::$instance = new \App\Legacy\LegacySessionObject();
        }
        if(!$actionID_pVar) {
            // nulova hodnota, zakazana
            error_gClass::warning_gFunc(__FILE__, __LINE__, string_gClass::get('str___loader_err_rights_sVar'));
            return(false);
        }

        return(self::$instance->_userHasRights_gFunc($actionID_pVar, $objectID_pVar, $status_pVar, $logValue_pVar, $userID_pVar));
    }

    static function setTemporaryRights_gFunc($actionId_pVar, $access_pVar = true)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        self::$temporary_rights_pVar[$actionId_pVar] = $access_pVar?true:false;
    }

    static function unsetTemporaryRights_gFunc($actionId_pVar)
    {
        $actionID_pVar = self::getActionId_gFunc($actionID_pVar);
        unset(self::$temporary_rights_pVar[$actionId_pVar]);
    }

    function getEnviroment_gFunc()
    {
        return(array('loginname'=>'ADMIN'));
    }

    static function getUserDetail_gFunc($userDetailName_pVar)
    {
        if(!self::$instance) {
                self::$instance = new \App\Legacy\LegacySessionObject();
                if(!self::$instance) {
                    return (false);
                }
        }
        return(self::$instance->getUserDetailValue_gFunc($userDetailName_pVar));
    }

    static function getUserDetails_gFunc($user_id_pVar)
    {
        if(!self::$instance) {
            return(false);
        }
        return(self::$instance->getUserDetailsValue_gFunc($user_id_pVar));
    }

    protected function getUserDetailValue_gFunc($userDetailName_pVar)
    {
        return(false);
    }

    protected function getUserDetailsValue_gFunc($user_id_pVar)
    {
        return(false);
    }

    public static function getUserDetailStatic_gFunc($user_id_pVar, $detail_name_pVar)
    {
        if(modules_gClass::isModuleRegistred_gFunc('access2_session')) {
            return(session_session_gClass::getUserDetailStatic_gFunc($user_id_pVar, $detail_name_pVar));
        }
        return(false);
    }

    /**
     * Spojenie dvoch poli s pravami.
     * Ak je prvok v niektorom poli nastaveny na false, vo vyslednom poli bude false
     *
     * @param array $rights1_pVar
     * @param array $rights2_pVar
     */
    public static function mergeRights_gFunc(&$rights1_pVar = array(), $rights2_pVar = array())
    {
        foreach ($rights2_pVar as $k_pVar=>$v_pVar) {
            if(isset($rights1_pVar[$k_pVar]) && !$rights1_pVar[$k_pVar]) {
                continue;
            }
            $rights1_pVar[$k_pVar] = $v_pVar;
        }
    }

    public static function ajaxEnabled_gFunc()
    {
        return(true);
    }

    public static function ajaxRequest_gFunc()
    {
        if(main_gClass::getInputString_gFunc('ajax', main_gClass::SRC_GETPOST_pVar) == 'true') {
            return(true);
        }
        return(false);
    }

    public static function ajaxFormat_gFunc()
    {
        $format_pVar = main_gClass::getInputString_gFunc('ajaxFormat', main_gClass::SRC_GETPOST_pVar);
        switch($format_pVar) {
            case 'text': return('text');
            case 'xml': return('xml');
            case 'json': return('json');
            case 'xhtml': return('xhtml');
            default: return('xml');
        }
    }

    public static function isDocEdit_gFunc()
    {
        if(modules_gClass::isModuleRegistred_gFunc('cms_edit')) {
            return(cms_edit_gClass::isDocEdit_gFunc());
        }
        return(false);
    }

    public static function getSessionSettingsDetail_gFunc($detailName_pVar)
    {
        $data_pVar = main_gClass::getSessionData_gFunc('saved_session_settings', false);
        $user_id_pVar = main_gClass::getSessionData_gFunc('saved_session_settings_for_user_id', false);
        if($data_pVar === false || !$user_id_pVar) {
            return(false);
        }

        if(!isset($data_pVar[$detailName_pVar])) {
            return(false);
        }

        return($data_pVar[$detailName_pVar]);
    }

    public static function saveSessionSettingsDetail_gFunc($detailName_pVar, $detailValue_pVar)
    {
        $data_pVar = main_gClass::getSessionData_gFunc('saved_session_settings', false);
        $user_id_pVar = main_gClass::getSessionData_gFunc('saved_session_settings_for_user_id', false);

        if($data_pVar === false || !$user_id_pVar) {
            return(false);
        }

        if(isset($data_pVar[$detailName_pVar]) && $data_pVar[$detailName_pVar] === $detailValue_pVar) {
            return(true);
        }

        $data_pVar[$detailName_pVar] = $detailValue_pVar;

        main_gClass::setPhpSessionVar_gFunc('saved_session_settings', $data_pVar);

        $sql_pVar = 'update %titems_users__data SET session_settings = %s WHERE item_id = %d';
        db_public_gClass::execute_gFunc($sql_pVar, __FILE__, __LINE__, array(serialize($data_pVar), $user_id_pVar));

        return(true);
    }
}
