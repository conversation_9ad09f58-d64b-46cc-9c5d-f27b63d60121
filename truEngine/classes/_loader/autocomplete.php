<?php

use Mixedtype\Forms\Components\Userlist;

class autocomplete_gClass extends source_gClass
{
    public function getData()
    {

        $limit_pVar = 10;
        $value_pVar = main_gClass::getInputString_gFunc('value', main_gClass::SRC_POST_pVar);
        $value_pVar =str_replace('%', '%%', $value_pVar);

        $found_pVar = $this->getValues($this->params['type'], $value_pVar, $limit_pVar);

        $data_pVar = array('data'=>json_encode($found_pVar));
        return($data_pVar);
    }

    public function getValues($type, $query, $limit)
    {
        $found_pVar = array();

        switch($type) {
            case 'kniha':
            case 'items-test_questions-literatura':
            case 'literatura':
                $found_pVar = $this->autocomplete_items_test_questions_literatura_gFunc($query, $limit);
                break;
            case 'items-test_questions-sk_keywords':
            case 'items-test_templates-sk_preferred_keywords':
            case 'sk_keywords':
                $found_pVar = $this->autocomplete_items_test_questions_keywords_gFunc($query, $limit, 'sk');
                break;
            case 'items-test_questions-en_keywords':
            case 'items-test_templates-en_preferred_keywords':
            case 'en_keywords':
                $found_pVar = $this->autocomplete_items_test_questions_keywords_gFunc($query, $limit, 'en');
                break;
            case 'items-test_questions-cz_keywords':
            case 'items-test_templates-cz_preferred_keywords':
            case 'cz_keywords':

            $found_pVar = $this->autocomplete_items_test_questions_keywords_gFunc($query, $limit, 'cz');
                break;

            case 'items-test_templates-preferred_authors':
                $found_pVar = $this->autocomplete_items_test_templates_preferred_authors_gFunc($query, $limit);
                break;

            case 'items-test_templates-preferred_source_authors':
                $found_pVar = $this->autocomplete_items_test_templates_preferred_source_authors_gFunc($query, $limit);
                break;
        }

        return $found_pVar;
    }

    protected function autocomplete_items_test_questions_literatura_gFunc($value_pVar, $limit_pVar)
    {
        // tu nemusim osetrovat prava, pretoze ziskane data nemaju nejaku hodnotu.
        if(modules_gClass::isModuleRegistred_gFunc('items')
            && modules_gClass::isModuleRegistred_gFunc('kega')) {
            $sql_pVar = 'SELECT DISTINCT `search_name` as `str` FROM `%titems_literatura__data` WHERE `search_name` LIKE %s ORDER BY `search_name` LIMIT 0,%d';
            $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array('%' . $value_pVar . '%', $limit_pVar));

            $ret_pVar = array();
            foreach($data_pVar as $v_pVar) {
                $ret_pVar[] = $v_pVar['str'];
            }

            $ret_pVar = array_unique($ret_pVar);
            sort($ret_pVar);

            return($ret_pVar);
        }
        return(array());
    }

    protected function autocomplete_items_test_questions_keywords_gFunc($value_pVar, $limit_pVar, $language_pVar)
    {
        // tu nemusim osetrovat prava, pretoze ziskane data nemaju nejaku hodnotu.
        if(modules_gClass::isModuleRegistred_gFunc('items')
            && modules_gClass::isModuleRegistred_gFunc('kega')) {
            $sql_pVar = 'SELECT DISTINCT `' . $language_pVar . '_keywords` as `str` FROM `%titems_test_questions__data` WHERE `' . $language_pVar . '_keywords` LIKE %s ORDER BY `' . $language_pVar . '_keywords`';
            $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array('%' . $value_pVar . '%'));

            $ret_pVar = array();
            foreach($data_pVar as $v_pVar) {
                $tmp_pVar = explode(',', $v_pVar['str']);
                foreach($tmp_pVar as $vv_pVar) {
                    if(strpos($vv_pVar, $value_pVar) !== false) {
                        $ret_pVar[] = trim($vv_pVar);
                    }
                }
                $ret_pVar = array_unique($ret_pVar);
                sort($ret_pVar);
                $ret_pVar = array_slice($ret_pVar, 0, $limit_pVar);
            }
            return($ret_pVar);
        }
        return(array());
    }

    protected function autocomplete_items_test_templates_preferred_authors_gFunc($value_pVar, $limit_pVar)
    {
        // tu musim trosku aj prava osetrit
        if(!session_gClass::userHasRightsAccess_gFunc(s_users_show_userlist)) {
            return(array());
        }
        if(modules_gClass::isModuleRegistred_gFunc('items')
            && modules_gClass::isModuleRegistred_gFunc('kega')) {
            $sql_pVar = 'SELECT DISTINCT `u`.`item_id` as `user_id`, `u`.`login`, `u`.`first_name`, `u`.`last_name`
					FROM `%titems_test_questions__data` as `d`
					LEFT JOIN `%titems_users__data` as `u` ON (`u`.`item_id` = `d`.`owner_id` or `u`.`item_id` = `d`.`garant_id`)
					WHERE `u`.`item_id` IS NOT NULL
					AND (`u`.`last_name` LIKE %s OR `u`.`first_name` LIKE %s OR `u`.`login` LIKE %s)
					ORDER BY `u`.`last_name`, `u`.`first_name`';
            $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array('%' . $value_pVar . '%', '%' . $value_pVar . '%', '%' . $value_pVar . '%'));

            $ret_pVar = array();
            foreach($data_pVar as $v_pVar) {
                $ret_pVar[] = $v_pVar['first_name'] . ' ' . $v_pVar['last_name'] . ' (' . $v_pVar['user_id'] . ')';
            }

            return($ret_pVar);
        }
        return(array());
    }

    protected function autocomplete_items_test_templates_preferred_source_authors_gFunc($value_pVar, $limit_pVar)
    {
        // tu musim trosku aj prava osetrit
        if(!session_gClass::userHasRightsAccess_gFunc(s_users_show_userlist)) {
            return(array());
        }
        if(modules_gClass::isModuleRegistred_gFunc('items')
            && modules_gClass::isModuleRegistred_gFunc('kega')) {

            $sql_pVar = 'select l.item_id, l.autor FROM kega_items_literatura__data as l
			WHERE l.autor LIKE %s
			ORDER BY l.autor';

            $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array('%' . $value_pVar . '%'));

            $ret_pVar = array();
            foreach($data_pVar as $v_pVar) {
                $ret_pVar[] = $v_pVar['autor'];
            }

            return($ret_pVar);
        }
        return(array());
    }



}

class autocomplete extends autocomplete_gClass {}
