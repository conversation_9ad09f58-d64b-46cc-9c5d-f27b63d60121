<?php

class hLog_gClass extends source_gClass
{
    protected function getData()
    {
        if(!modules_gClass::isModuleRegistred_gFunc('db')) {
            return(array());
        }
        $sql_pVar = 'SELECT l.*, u.login FROM `%thumanlog` as `l`
						LEFT JOIN `%titems_users__data` as `u` ON `l`.`user_id` = `u`.`item_id`
						ORDER BY `event_time` DESC LIMIT 0,500';
        $data_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__);
        /*
        $last_pVar = array('event'=>false);
        foreach($data_pVar as $k_pVar=>$v_pVar) {
            $events = array('LOGIN_BAD_PASSWORD', 'LOGIN_BAD_LOGINNAME');
            if(in_array($v_pVar['event'] , $events)) {

            }
            else {
                if(isset($last_pVar['data'])) {
                    $data_pVar[$last_pVar['k']] = $last_pVar['data'];
                    $data_pVar[$last_pVar['k']]['n'] =  $last_pVar['n'];
                }
            }
        }*/


        foreach($data_pVar as $k_pVar=>$v_pVar) {
            if($v_pVar['event'] == 'LOGGED_IN') {
                $user_id_pVar = $data_pVar[$k_pVar]['param1'];
                $v_pVar['user_id'] = $user_id_pVar;
                $data_pVar[$k_pVar]['user_id'] = $user_id_pVar;
            }
            else {
                $user_id_pVar = $v_pVar['user_id'];
            }

            $data_pVar[$k_pVar]['event_text'] = $v_pVar['event'];
            $data_pVar[$k_pVar]['event_time'] = $data_pVar[$k_pVar]['event_time'];
            if(is_numeric($user_id_pVar) && intval($user_id_pVar)) {
                $data_pVar[$k_pVar]['login'] = session_gClass::getUserDetailStatic_gFunc($user_id_pVar, 'real_name');
                $data_pVar[$k_pVar]['foto_thumb'] = session_gClass::getUserDetailStatic_gFunc($user_id_pVar, 'foto_thumb');
            }
            else {
                $data_pVar[$k_pVar]['login'] = $user_id_pVar;
            }

            switch($v_pVar['event']) {
                case 'LOGGED_IN':
                    $data_pVar[$k_pVar]['login'] = self::getUserName_gFunc($data_pVar[$k_pVar]['param1']);
                    $data_pVar[$k_pVar]['event_text'] = __STR('Prihlásil/a sa.');
                    $motto_pVar = session_gClass::getUserDetailStatic_gFunc($user_id_pVar, 'motto');
                    if(!empty($motto_pVar)) {
                        $data_pVar[$k_pVar]['event_text'] .= ' ' . __STR('a vraví') . ' "' .$motto_pVar . '"';
                    }
                    else {
                        $data_pVar[$k_pVar]['event_text'] .= '.';
                    }
                    $data_pVar[$k_pVar]['event_label'] = __STR('Prihlásil/a sa.');
                    break;
                case 'TEST_QUESTION_UPDATED':
                    $sql_pVar = 'select * from %titems_test_questions__data WHERE item_id = %d';
                    $tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar['param1']));

                    if(is_array($tmp_pVar) && count($tmp_pVar)) {
                        $q_pVar = self::getField_gFunc($tmp_pVar[0], 'otazka');
                        $data_pVar[$k_pVar]['event_text'] = __STR('Aktualizoval/a') . ' <a href="' . main_gClass::makeUrl_gFunc('/otazky/editovat-otazku/&item_id=' . $v_pVar['param1']) . '">' . __STR('otázku') . '</a> ' . $q_pVar . ' (<a href="' . main_gClass::makeUrl_gFunc('/otazky/log-zmien-otazky&item_id=' . $v_pVar['param1']) . '">' . __STR('zmeny') . '</a>).';
                    }
                    else {
                        $data_pVar[$k_pVar]['event_text'] = __STR('Aktualizoval/a otázku.');
                    }
                    $data_pVar[$k_pVar]['event_label'] = __STR('Aktualizoval/a otázku.');
                    break;
                case 'TEST_QUESTION_COMMENT':
                    $sql_pVar = 'select * from %titems_test_questions__comments WHERE item_id = %d';
                    $tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar['param1']));

                    $data_pVar[$k_pVar]['event_text'] = __STR('Komentoval/a') . ' <a href="'.main_gClass::makeUrl_gFunc('/otazky/komentare-k-otazke&item_id=' . $v_pVar['param1']) . '">'. __STR('otázku') . '</a>: ' . $tmp_pVar[0]['comment_text'];
                    $data_pVar[$k_pVar]['event_label'] = __STR('Komentoval/a otázku.');
                    break;
                case 'TEST_QUESTION_TEMPLATE_UPDATE':
                    $data_pVar[$k_pVar]['event_text'] = __STR('Aktualizoval/a') . ' <a href="'.main_gClass::makeUrl_gFunc('/testy/editovat-sablonu/&item_id=' . $v_pVar['param1']) . '">'. __STR('šablónu testu') .'</a>.';
                    $data_pVar[$k_pVar]['event_label'] = __STR('Aktualizoval/a test.');
                    break;
                case 'TEST_QUESTION_TEST_UPDATE':
                    $data_pVar[$k_pVar]['event_text'] = __STR('Aktualizoval/a test.');
                    $data_pVar[$k_pVar]['event_label'] = __STR('Aktualizoval/a test.');
                    break;
                case 'TEST_PREPARE_FROM_TEST':
                    modules_gClass::isModuleRegistred_gFunc('items');
                    $info_pVar = db_items_gClass::getInfo_gFunc('test_questions');
                    $sql_pVar = 'SELECT * FROM `%ttests` WHERE `id` = %d';
                    $tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar['param1']));

                    $name_pVar = self::getField_gFunc($tmp_pVar[0], 'name');
                    $data_pVar[$k_pVar]['event_text'] = __STR('Spustil/a test') . ' ' . $name_pVar . '.';
                    $data_pVar[$k_pVar]['event_label'] = '<a href="'.main_gClass::makeUrl_gFunc('/testy/zoznam-pouzivatelovych-testovani?user_id='.$user_id_pVar).'">' . __STR('Spustil/a test') . '</a>';
                    break;
                case 'TEST_PREPARE_FROM_TEMPLATE':
                    $sql_pVar = 'SELECT * FROM `%titems_test_templates__data` WHERE `item_id` = %d';
                    $tmp_pVar = db_public_gClass::getResultArray_gFunc($sql_pVar, __FILE__, __LINE__, array($v_pVar['param1']));

                    if(!count($tmp_pVar)) {
                        unset($data_pVar[$k_pVar]);
                        continue 2;
                    }
                    $name_pVar = self::getField_gFunc($tmp_pVar[0], 'name');

                    $data_pVar[$k_pVar]['event_text'] = __STR('Spustil/a test') . ' ' . $name_pVar . '.';
                    $data_pVar[$k_pVar]['event_label'] = '<a href="'.main_gClass::makeUrl_gFunc('/testy/zoznam-pouzivatelovych-testovani?user_id='.$user_id_pVar).'">' . __STR('Spustil/a test') . '</a>';
                    break;
                case 'LOGIN_BAD_PASSWORD':
                case 'LOGIN_BAD_LOGINNAME':
                    $data_pVar[$k_pVar]['event_text'] = __STR('Neúspešné prihlásenie') . ': ' . $data_pVar[$k_pVar]['param1'];
                    $data_pVar[$k_pVar]['event_label'] = __STR('Neúspešné prihlásenie.');
                    break;
            }
            if(!session_gClass::userHasRightsInfo_gFunc(s_logged_on) && isset($data_pVar[$k_pVar]['event_text'])) {
                $data_pVar[$k_pVar]['event_text'] = strip_tags($data_pVar[$k_pVar]['event_text']);
            }
        }

        return($data_pVar);
    }

    private static function getField_gFunc($data_pVar, $field_pVar)
    {
        $languages_pVar = array('sk', 'cz', 'en');

        $name_pVar = '';
        if(isset($data_pVar[main_gClass::getLanguage_gFunc() . '_' . $field_pVar]) && !empty($data_pVar[main_gClass::getLanguage_gFunc() . '_' . $field_pVar])) {
            $name_pVar = $data_pVar[main_gClass::getLanguage_gFunc() . '_' . $field_pVar];
        }
        else {
            foreach($languages_pVar as $lng_pVar) {
                if(isset($data_pVar[$lng_pVar . '_' . $field_pVar]) && !empty($data_pVar[$lng_pVar . '_' . $field_pVar])) {
                    $name_pVar = $data_pVar[$lng_pVar . '_' . $field_pVar];
                    break;
                }
            }
        }
        return($name_pVar);
    }

    private function getUserName_gFunc($user_id_pVar)
    {
        if(!is_numeric($user_id_pVar)) {
            return($user_id_pVar);
        }
        if(!intval($user_id_pVar)) {
            return('');
        }
        $real_name_pVar = session_gClass::getUserDetailStatic_gFunc($user_id_pVar, 'real_name');
        return($real_name_pVar);
    }
}

class hlog extends hLog_gClass {}
